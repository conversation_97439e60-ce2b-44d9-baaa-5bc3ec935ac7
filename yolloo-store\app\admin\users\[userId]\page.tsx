"use client"

import { useState, useEffect } from "react"
import { notFound, useRouter } from "next/navigation"
import Link from "next/link"
import axios from "axios"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Icons } from "@/components/icons"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {
  Loader2,
  Calendar,
  Package,
  CreditCard,
  User,
  Pencil,
  Trash,
  Clock,
  LogOut,
  Globe,
  Smartphone
} from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { format } from "date-fns"
import { DateFormatter, DateUtils } from "@/lib/utils"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

const ORDER_STATUS_MAP = {
  PENDING: { label: "Pending", variant: "warning" as const },
  PROCESSING: { label: "Processing", variant: "secondary" as const },
  SHIPPED: { label: "Shipped", variant: "info" as const },
  DELIVERED: { label: "Delivered", variant: "success" as const },
  CANCELLED: { label: "Cancelled", variant: "destructive" as const },
  PAID: { label: "Paid", variant: "default" as const },
} as const

const USER_ROLE_MAP = {
  ADMIN: { label: "Admin", variant: "destructive" as const },
  CUSTOMER: { label: "Customer", variant: "secondary" as const },
  STAFF: { label: "Staff", variant: "outline" as const },
} as const

interface UserDetailsPageProps {
  params: {
    userId: string
  }
}

interface User {
  id: string
  name: string | null
  email: string
  image: string | null
  role: string
  createdAt: Date
  updatedAt: Date
  emailVerified: Date | null
  lastLoginTime: Date | null
  lastLoginIp: string | null
  lastLoginMethod: string | null
  hashedPassword: string | null
  accounts: {
    provider: string
    providerAccountId: string
  }[]
  loginHistory: {
    id: string
    loginTimestamp: Date
    ipAddress: string | null
    userAgent: string | null
    loginMethod: string | null
    isSuccessful: boolean
  }[]
  orders: Order[]
  addresses: Address[]
  _count?: {
    orders: number
    reviews: number
    wishlist: number
  }
}

interface Order {
  id: string
  status: string
  total: number
  createdAt: Date
  items: OrderItem[]
}

interface OrderItem {
  id: string
  quantity: number
  product: {
    id: string
    name: string
    price: number
    images: string[]
  }
}

interface Address {
  id: string
  name: string
  address1: string
  address2: string | null
  city: string
  state: string
  postalCode: string
  country: string
  phone: string
  isDefault: boolean
}

export default function UserDetailsPage({ params }: UserDetailsPageProps) {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [editFormData, setEditFormData] = useState({
    name: "",
    email: "",
    role: ""
  })

  // Fetch user data
  useEffect(() => {
    const fetchUser = async () => {
      try {
        setIsLoading(true)
        const response = await axios.get(`/api/admin/users/${params.userId}`)
        setUser(response.data)

        // Initialize edit form
        setEditFormData({
          name: response.data.name || "",
          email: response.data.email || "",
          role: response.data.role || ""
        })
      } catch (error) {
        console.error("Error fetching user:", error)
        toast.error("Failed to load user details")
        router.push("/admin/users")
      } finally {
        setIsLoading(false)
      }
    }

    fetchUser()
  }, [params.userId, router])

  const handleUpdateUser = async () => {
    if (!user) return

    try {
      setIsUpdating(true)
      const response = await axios.patch(`/api/admin/users/${user.id}`, editFormData)

      setUser({
        ...user,
        ...response.data
      })

      setIsEditDialogOpen(false)
      toast.success("User updated successfully")
    } catch (error) {
      console.error("Error updating user:", error)
      toast.error("Failed to update user")
    } finally {
      setIsUpdating(false)
    }
  }

  const handleDeleteUser = async () => {
    if (!user) return

    try {
      setIsDeleting(true)
      await axios.delete(`/api/admin/users/${user.id}`)

      toast.success("User deleted successfully")
      router.push("/admin/users")
    } catch (error) {
      console.error("Error deleting user:", error)
      toast.error("Failed to delete user")
      setIsDeleting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-[450px] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!user) {
    return notFound()
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2">
            <Link href="/admin/users">
              <Button variant="ghost" size="sm">
                <Icons.chevronLeft className="h-4 w-4 mr-2" />
                Back to Users
              </Button>
            </Link>
            <h2 className="text-2xl font-bold tracking-tight">User Details</h2>
          </div>
          <p className="text-muted-foreground">
            View and manage user information
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsEditDialogOpen(true)}
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit User
          </Button>
          <Button
            variant="destructive"
            onClick={() => setIsDeleteDialogOpen(true)}
          >
            <Trash className="h-4 w-4 mr-2" />
            Delete User
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>User Profile</CardTitle>
            <CardDescription>User account information and preferences</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-8">
              <div className="flex flex-col items-center gap-3">
                <Avatar className="h-24 w-24">
                  {user.image ? (
                    <AvatarImage src={user.image} alt={user.name || "User"} />
                  ) : (
                    <AvatarFallback className="text-xl">
                      {user.name ? user.name.substring(0, 2).toUpperCase() : <User className="h-12 w-12" />}
                    </AvatarFallback>
                  )}
                </Avatar>
                <Badge variant={USER_ROLE_MAP[user.role as keyof typeof USER_ROLE_MAP]?.variant || "secondary"} className="mt-1">
                  {USER_ROLE_MAP[user.role as keyof typeof USER_ROLE_MAP]?.label || user.role}
                </Badge>
              </div>

              <div className="flex-1 space-y-4">
                <div className="grid gap-1">
                  <p className="text-sm font-medium text-muted-foreground">Full Name</p>
                  <p className="text-xl font-semibold">{user.name || "Unnamed User"}</p>
                </div>
                <div className="grid gap-1">
                  <p className="text-sm font-medium text-muted-foreground">Email Address</p>
                  <p className="font-medium">{user.email}</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="grid gap-1">
                    <p className="text-sm font-medium text-muted-foreground">Created</p>
                    <p className="font-mono text-sm">
                      {DateFormatter.withTimezone(user.createdAt)}
                    </p>
                  </div>
                  <div className="grid gap-1">
                    <p className="text-sm font-medium text-muted-foreground">Account Age</p>
                    <p className="font-mono text-sm">
                      {DateUtils.daysBetween(user.createdAt, new Date())} days
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Created: {DateFormatter.withTimezone(user.createdAt)}
                    </p>
                  </div>
                  <div className="grid gap-1">
                    <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                    <p className="font-mono text-sm">
                      {DateFormatter.withTimezone(user.updatedAt)}
                    </p>
                  </div>
                  <div className="grid gap-1">
                    <p className="text-sm font-medium text-muted-foreground">Email Verified</p>
                    <p className="font-mono text-sm">
                      {user.emailVerified ? DateFormatter.withTimezone(user.emailVerified) : "Not verified"}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-6 grid-cols-1 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5 text-muted-foreground" />
                <CardTitle className="text-base">Orders</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{user._count?.orders || user.orders?.length || 0}</div>
              <p className="text-sm text-muted-foreground">Total orders placed</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-muted-foreground" />
                <CardTitle className="text-base">Addresses</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{user.addresses?.length || 0}</div>
              <p className="text-sm text-muted-foreground">Saved addresses</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-muted-foreground" />
                <CardTitle className="text-base">Last Login</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              {user.lastLoginTime ? (
                <>
                  <div className="text-md font-bold">
                    {DateFormatter.withTimezone(user.lastLoginTime)}
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {user.lastLoginMethod && `Via ${user.lastLoginMethod}`}
                    {user.lastLoginIp && ` from ${user.lastLoginIp}`}
                  </p>
                </>
              ) : (
                <>
                  <div className="text-md font-bold">Never</div>
                  <p className="text-sm text-muted-foreground">No login recorded</p>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="orders" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="orders" id="orders-tab">Orders</TabsTrigger>
            <TabsTrigger value="addresses" id="addresses-tab">Addresses</TabsTrigger>
            <TabsTrigger value="activity" id="activity-tab">Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="orders">
            <Card>
              <CardHeader>
                <CardTitle>Order History</CardTitle>
                <CardDescription>
                  Previous orders and purchases
                </CardDescription>
              </CardHeader>
              <CardContent>
                {user.orders.length === 0 ? (
                  <div className="text-center py-6">
                    <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No orders found</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {user.orders.map((order) => (
                      <div key={order.id} className="rounded-lg border p-4 transition-colors hover:bg-accent/5">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <p className="font-medium">Order #{order.id.slice(0, 8)}</p>
                            <p className="text-sm text-muted-foreground">
                              {DateFormatter.withTimezone(order.createdAt)}
                            </p>
                          </div>
                          <Badge variant={ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP]?.variant || "secondary"}>
                            {ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP]?.label || order.status}
                          </Badge>
                        </div>
                        <div className="space-y-2">
                          {order.items.map((item) => (
                            <div key={item.id} className="flex items-center gap-4">
                              {item.product.images?.[0] && (
                                <img
                                  src={item.product.images[0]}
                                  alt={item.product.name}
                                  className="h-10 w-10 rounded object-cover"
                                />
                              )}
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">
                                  {item.product.name}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  {item.quantity} × ${item.product.price.toFixed(2)}
                                </p>
                              </div>
                            </div>
                          ))}
                          <div className="flex justify-between pt-2 border-t">
                            <span className="font-medium">Total</span>
                            <span className="font-medium">
                              ${order.total.toFixed(2)}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="addresses">
            <Card>
              <CardHeader>
                <CardTitle>Saved Addresses</CardTitle>
                <CardDescription>
                  Customer shipping and billing addresses
                </CardDescription>
              </CardHeader>
              <CardContent>
                {user.addresses.length === 0 ? (
                  <div className="text-center py-6">
                    <Icons.mapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No addresses found</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {user.addresses.map((address) => (
                      <div key={address.id} className="rounded-lg border p-4">
                        <div className="flex items-center justify-between mb-2">
                          <p className="font-medium">{address.name}</p>
                          {address.isDefault && (
                            <Badge variant="secondary">Default</Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground space-y-1">
                          <p>{address.address1}</p>
                          {address.address2 && <p>{address.address2}</p>}
                          <p>
                            {address.city}, {address.state} {address.postalCode}
                          </p>
                          <p>{address.country}</p>
                          <p>Phone: {address.phone}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="activity">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  User's login history and activity
                </CardDescription>
              </CardHeader>
              <CardContent>
                {user.loginHistory.length === 0 ? (
                  <div className="text-center py-6">
                    <LogOut className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No login history available</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[140px]">Activity Type</TableHead>
                          <TableHead className="w-[200px]">Timestamp</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Login Method</TableHead>
                          <TableHead>IP Address</TableHead>
                          <TableHead className="hidden md:table-cell">User Agent</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {user.loginHistory.map((login) => (
                          <TableRow key={login.id}>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <LogOut className="h-4 w-4 text-muted-foreground" />
                                <span>Login</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className="font-mono text-sm">
                                {DateFormatter.withTimezone(login.loginTimestamp)}
                              </span>
                            </TableCell>
                            <TableCell>
                              <Badge variant={login.isSuccessful ? "success" : "destructive"}>
                                {login.isSuccessful ? "Success" : "Failed"}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {login.loginMethod || "Unknown"}
                            </TableCell>
                            <TableCell>
                              {login.ipAddress ? (
                                <div className="flex items-center gap-2">
                                  <Globe className="h-4 w-4 text-muted-foreground" />
                                  <span>{login.ipAddress}</span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground">N/A</span>
                              )}
                            </TableCell>
                            <TableCell className="hidden md:table-cell">
                              {login.userAgent ? (
                                <div className="flex items-center gap-2 max-w-[300px]">
                                  <Smartphone className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                                  <span className="truncate" title={login.userAgent}>
                                    {login.userAgent}
                                  </span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground">N/A</span>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and preferences
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={editFormData.name}
                onChange={(e) => setEditFormData({ ...editFormData, name: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={editFormData.email}
                onChange={(e) => setEditFormData({ ...editFormData, email: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="role">Role</Label>
              <Select
                value={editFormData.role}
                onValueChange={(value) => setEditFormData({ ...editFormData, role: value })}
              >
                <SelectTrigger id="role">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ADMIN">Admin</SelectItem>
                  <SelectItem value="CUSTOMER">Customer</SelectItem>
                  <SelectItem value="STAFF">Staff</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateUser} disabled={isUpdating}>
              {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Confirmation */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete User</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this user? This action cannot be undone and all associated data will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteUser}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete User
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}