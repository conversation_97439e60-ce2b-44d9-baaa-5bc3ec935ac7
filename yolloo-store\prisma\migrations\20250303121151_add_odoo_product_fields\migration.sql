/*
  Warnings:

  - A unique constraint covering the columns `[productCode]` on the table `Product` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[variantCode]` on the table `ProductVariant` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "country" TEXT,
ADD COLUMN     "countryCode" TEXT,
ADD COLUMN     "dataSize" DOUBLE PRECISION,
ADD COLUMN     "odooLastSyncAt" TIMESTAMP(3),
ADD COLUMN     "planType" TEXT,
ADD COLUMN     "productCode" TEXT;

-- AlterTable
ALTER TABLE "ProductVariant" ADD COLUMN     "duration" INTEGER,
ADD COLUMN     "durationType" TEXT,
ADD COLUMN     "variantCode" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "Product_productCode_key" ON "Product"("productCode");

-- CreateIndex
CREATE UNIQUE INDEX "ProductVariant_variantCode_key" ON "ProductVariant"("variantCode");
