export interface OrderItem {
  id: string
  quantity: number
  price: number
  productCode: string
  variantCode?: string | null
  variantText?: string | null
  product?: {
    id: string
    name: string
    images: string[]
  } | null
}

export interface ShippingAddress {
  id: string
  name: string
  phone: string
  address1: string
  address2: string | null
  city: string
  state: string
  postalCode: string
  country: string
}

export interface Order {
  id: string
  userId: string
  total: number
  status: string
  items: OrderItem[]
  shippingAddress: ShippingAddress
  user: {
    id: string
    name: string | null
    email: string | null
  }
  createdAt: Date
  updatedAt: Date
}

export interface Product {
  id: string
  name: string
  description: string
  price: number
  images: string[]
  categoryId: string
  stock: number
  specifications: Record<string, any>
  status: string
  sku: string
  category: {
    id: string
    name: string
  }
  createdAt: Date
  updatedAt: Date
}

export interface User {
  id: string
  name: string | null
  email: string | null
  image: string | null
  role: "ADMIN" | "USER"
  createdAt: Date
  updatedAt: Date
} 