import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for batch adding members
const batchMemberSchema = z.array(
  z.object({
    userId: z.string(),
    commissionRate: z.number().min(0).max(1).optional(),
    isAdmin: z.boolean().optional(),
  })
);

// POST - Batch add members to an organization (admin only)
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }
    
    const organizationId = params.id;
    
    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validationResult = batchMemberSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const membersToAdd = validationResult.data;
    
    // Check for duplicate user IDs in the batch
    const userIds = membersToAdd.map(member => member.userId);
    const uniqueUserIds = new Set(userIds);
    
    if (uniqueUserIds.size !== userIds.length) {
      return NextResponse.json(
        { error: "Duplicate user IDs found in batch" },
        { status: 400 }
      );
    }
    
    // Check if users exist
    const existingUsers = await prisma.user.findMany({
      where: { id: { in: userIds } },
      include: { affiliate: true },
    });
    
    if (existingUsers.length !== userIds.length) {
      const foundUserIds = existingUsers.map(user => user.id);
      const missingUserIds = userIds.filter(id => !foundUserIds.includes(id));
      
      return NextResponse.json(
        { 
          error: "Some users not found", 
          missingUserIds 
        },
        { status: 400 }
      );
    }
    
    // Check if any users are already in an organization
    const usersInOrg = existingUsers.filter(
      user => user.affiliate && user.affiliate.organizationId
    );
    
    if (usersInOrg.length > 0) {
      return NextResponse.json(
        { 
          error: "Some users are already in an organization", 
          userIds: usersInOrg.map(user => user.id) 
        },
        { status: 400 }
      );
    }
    
    // Process each member
    const results = await Promise.all(
      membersToAdd.map(async (memberData) => {
        const targetUser = existingUsers.find(user => user.id === memberData.userId);
        
        if (!targetUser) {
          return { error: `User ${memberData.userId} not found`, userId: memberData.userId };
        }
        
        try {
          // If user has an affiliate profile, add to organization
          if (targetUser.affiliate) {
            // Check if affiliate is already in an organization
            if (targetUser.affiliate.organizationId) {
              return { 
                error: `User ${memberData.userId} is already a member of an organization`,
                userId: memberData.userId 
              };
            }
            
            // Add affiliate to organization
            const updatedAffiliate = await prisma.affiliateProfile.update({
              where: { id: targetUser.affiliate.id },
              data: {
                organizationId,
                ...(memberData.commissionRate !== undefined && { commissionRate: memberData.commissionRate }),
                ...(memberData.isAdmin !== undefined && { isAdmin: memberData.isAdmin }),
              },
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true,
                  },
                },
              },
            });
            
            return { success: true, member: updatedAffiliate };
          }
          
          // If user doesn't have an affiliate profile, create one and add to organization
          // Generate a unique affiliate code
          const code = `${targetUser.name?.substring(0, 3) || "AFF"}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
          
          const newAffiliate = await prisma.affiliateProfile.create({
            data: {
              userId: targetUser.id,
              code,
              organizationId,
              ...(memberData.commissionRate !== undefined && { commissionRate: memberData.commissionRate }),
              ...(memberData.isAdmin !== undefined && { isAdmin: memberData.isAdmin }),
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true,
                },
              },
            },
          });
          
          return { success: true, member: newAffiliate };
        } catch (error) {
          console.error(`Error adding member ${memberData.userId}:`, error);
          return { 
            error: `Failed to add member ${memberData.userId}`, 
            userId: memberData.userId 
          };
        }
      })
    );
    
    // Extract successful members and errors
    const addedMembers = results
      .filter(result => result.success)
      .map(result => (result as any).member);
    
    const errors = results
      .filter(result => !result.success)
      .map(result => ({ userId: result.userId, error: result.error }));
    
    return NextResponse.json({
      addedMembers,
      errors,
      totalAdded: addedMembers.length,
      totalFailed: errors.length,
    });
  } catch (error) {
    console.error("Error batch adding members:", error);
    return NextResponse.json(
      { error: "Failed to batch add members" },
      { status: 500 }
    );
  }
} 