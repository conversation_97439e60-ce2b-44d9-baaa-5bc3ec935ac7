import { prisma } from "@/lib/prisma"
import { ProductForm } from "@/components/admin/product-form"

export const metadata = {
  title: "New Product",
  description: "Create a new product",
}

async function getCategories() {
  return await prisma.category.findMany({
    orderBy: {
      name: "asc",
    },
  })
}

export default async function NewProductPage() {
  const categories = await getCategories()

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">New Product</h3>
        <p className="text-sm text-muted-foreground">
          Add a new product to your store
        </p>
      </div>
      <ProductForm categories={categories} />
    </div>
  )
} 