import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";
import { OrderStatus } from "@prisma/client";
import { cookies } from "next/headers";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const page = Number(searchParams.get("page")) || 1;
  const limit = 10;
  const statusParam = searchParams.get("status") || "all";
  const sort = searchParams.get("sort") || "newest";
  const search = searchParams.get("search") || "";

  // Validate status is a valid OrderStatus enum value
  const isValidStatus = statusParam === "all" || Object.values(OrderStatus).includes(statusParam as OrderStatus);
  if (statusParam !== "all" && !isValidStatus) {
    return new NextResponse("Invalid status parameter", { status: 400 });
  }

  const where = {
    userId: session.user.id,
    ...(statusParam !== "all" && { status: statusParam as OrderStatus }),
    ...(search && {
      OR: [
        { id: { contains: search } },
        { items: {
          some: {
            OR: [
              // 使用 variantText 进行搜索，因为我们已经移除了 product 关系
              { variantText: { contains: search, mode: "insensitive" } }
            ]
          }
        } },
      ],
    }),
  };

  try {
    const [orders, filteredTotal, statusCounts] = await Promise.all([
      prisma.order.findMany({
        where,
        include: {
          items: {
            select: {
              id: true,
              quantity: true,
              price: true,
              productCode: true,
              variantCode: true,
              variantText: true,
              // 移除 product 关系查询，因为我们已经移除了外键关系
            },
          },
          payment: true,
        },
        orderBy: {
          createdAt: sort === "oldest" ? "asc" : "desc",
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.order.count({ where }),
      prisma.order.groupBy({
        by: ["status"],
        where: { userId: session.user.id },
        _count: true,
      })
    ]);

    // Get total count of all orders without filters
    const totalOrders = await prisma.order.count({
      where: { userId: session.user.id }
    });

    return NextResponse.json({
      orders,
      total: filteredTotal,
      totalOrders,
      statusCounts,
      totalPages: Math.ceil(filteredTotal / limit)
    });
  } catch (error) {
    console.error("[ORDERS_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // 从cookies中获取推广码
    const cookieStore = cookies();
    const referralCode = cookieStore.get('referralCode')?.value;
    console.log(`[ORDER_POST] Referral code from cookie: ${referralCode || 'none'}`);

    let body: any;
    try {
      body = await request.json();
      console.log('[ORDER_POST] Request body:', JSON.stringify(body));
    } catch (error) {
      console.error('[ORDER_POST] Error parsing request body:', error);
      return new NextResponse("Invalid request body", { status: 400 });
    }

    const { addressId, items } = body;

    if (!addressId) {
      return new NextResponse("Address ID is required", { status: 400 });
    }

    if (!items || !items.length) {
      return new NextResponse("Items are required", { status: 400 });
    }

    // 计算订单总金额
    const total = items.reduce((sum: number, item: any) => {
      return sum + item.price * item.quantity;
    }, 0);

    // 获取地址信息
    let address = await prisma.address.findUnique({
      where: {
        id: addressId,
      },
    });

    // 如果找不到地址，尝试使用系统默认地址或创建一个虚拟地址
    if (!address) {
      console.log(`Address not found with ID: ${addressId}, attempting to find system default address`);

      // 尝试获取系统默认地址
      const systemDefaultAddress = await prisma.address.findFirst({
        where: {
          userId: null,
          name: 'System Default Address',
        },
      });

      address = systemDefaultAddress;
    }

    // 批量查找本次下单涉及的商品和变体
    const productIds = [...new Set(items.map((i: any) => i.productId))] as string[];
    const variantIds = [...new Set(items.map((i: any) => i.variantId).filter(Boolean))] as string[];

    const products = await prisma.product.findMany({ where: { id: { in: productIds } } });
    const variants = await prisma.productVariant.findMany({ where: { id: { in: variantIds } } });

    const productMap = new Map(products.map(p => [p.id, p]));
    const variantMap = new Map(variants.map(v => [v.id, v]));

    // 创建订单 - 添加referralCode到订单数据
    const order = await prisma.order.create({
      data: {
        userId: session.user.id,
        addressId: address.id,
        total,
        ...(referralCode && { referralCode }), // 如果有推广码，添加到订单
        shippingAddressSnapshot: {
          name: address.name,
          phone: address.phone,
          address1: address.address1,
          address2: address.address2,
          city: address.city,
          state: address.state,
          postalCode: address.postalCode,
          country: address.country,
        },
        items: {
          create: items.map((item: any) => {
            const product = productMap.get(item.productId);
            const variant = item.variantId ? variantMap.get(item.variantId) : null;
            let variantText = product?.name || '';
            if (variant?.duration && variant?.durationType) {
              variantText += ` ${variant.duration} ${variant.durationType}`;
            }
            return {
              productCode: product?.sku,
              variantCode: variant?.variantCode,
              variantText,
              quantity: item.quantity,
              price: parseFloat(item.price),
              uid: item.uid,
              ...(item.lpaString && { lpaString: item.lpaString })
            };
          })
        }
      },
      include: {
        items: true
      }
    });

    // 为每个订单创建初始化的Odoo状态记录
    await prisma.odooOrderStatus.create({
      data: {
        orderId: order.id,
        variantCode: "default", // 添加默认的variantCode
        status: "pending",
        description: "Order created, waiting for payment",
        isDigital: false,
        deliveredQty: 0,
        lastCheckedAt: new Date(),
      }
    });

    // 减少产品库存
    for (const item of items) {
      try {
        await prisma.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity
            }
          }
        });
        console.log(`[ORDER_POST] Decreased stock for product ${item.productId} by ${item.quantity}`);
      } catch (stockError) {
        console.error(`[ORDER_POST] Failed to update stock for product ${item.productId}:`, stockError);
        // 不中断订单创建流程，继续处理其他产品
      }
    }

    return NextResponse.json(order);
  } catch (error) {
    console.error("[ORDER_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}