'use client'

import Image, { ImageProps } from 'next/image'
import { useState } from 'react'

interface ProductImageProps extends Omit<ImageProps, 'src' | 'alt'> {
  src: string
  alt: string
  fallbackSrc?: string
}

export function ProductImage({ src, alt, fallbackSrc = '/placeholder.jpg', ...props }: ProductImageProps) {
  const [imgSrc, setImgSrc] = useState(src)

  return (
    <Image
      {...props}
      src={imgSrc}
      alt={alt}
      onError={() => {
        setImgSrc(fallbackSrc)
      }}
    />
  )
} 