import { NextRequest, NextResponse } from 'next/server';
import { createOdooService } from '../../../services/odooService';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


const odooService = createOdooService({
    address: process.env.ODOO_ADDRESS || '',
    channelId: process.env.ODOO_CHANNEL_ID || '',
    channelLanguage: process.env.ODOO_CHANNEL_LANGUAGE || 'zh_CN',
    authSecret: process.env.ODOO_AUTH_SECRET || '',
    signMethod: process.env.ODOO_SIGN_METHOD || 'MD5',
});

export async function POST(request: NextRequest) {
    try {
        const params = await request.json();
        const response = await odooService.getInvoiceOrders(params);
        return NextResponse.json(response);
    } catch (error) {
        console.error('Error fetching invoice orders from Odoo:', error);
        return NextResponse.json(
            { 
                code: 500,
                msg: 'Failed to fetch invoice orders',
                data: null
            },
            { status: 500 }
        );
    }
} 