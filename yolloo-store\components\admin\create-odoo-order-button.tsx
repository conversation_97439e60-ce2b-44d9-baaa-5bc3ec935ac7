"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { toast } from "sonner"

interface CreateOdooOrderButtonProps {
  orderId: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
}

export function CreateOdooOrderButton({ orderId, variant = "secondary" }: CreateOdooOrderButtonProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  async function handleCreateOdooOrder() {
    setIsLoading(true)

    try {
      const response = await fetch(`/api/admin/orders/${orderId}/odoo`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || "Failed to create Odoo order")
      }

      // 获取使用的服务类型
      const serviceType = data.serviceType || "Standard Odoo Service"

      // 检查 Odoo 响应的状态
      if (data.odooResponse && data.odooResponse.status === "error") {
        // Odoo 返回了错误信息，但我们的 API 调用成功了
        toast.error(`Odoo returned error: ${data.odooResponse.message}`, {
          duration: 5000,
          description: `Using ${serviceType}. Order information has been updated with error details`
        })
      } else {
        // 成功创建 Odoo 订单
        toast.success("Odoo order created successfully", {
          description: `Using ${serviceType}. ${data.odooResponse?.message || "Order has been synced with Odoo"}`
        })
      }

      // 刷新页面以显示更新的信息
      router.refresh()
    } catch (error) {
      console.error(error)
      toast.error(`Failed to create Odoo order: ${error instanceof Error ? error.message : 'Unknown error'}`, {
        duration: 5000
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button
      onClick={handleCreateOdooOrder}
      disabled={isLoading}
      variant={variant}
    >
      {isLoading ? (
        <>
          <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          Creating...
        </>
      ) : (
        <>
          <Icons.refresh className="mr-2 h-4 w-4" />
          Create Odoo Order
        </>
      )}
    </Button>
  )
}