"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Icons } from "@/components/icons";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ArrowRight, CheckCircle, FileText } from "lucide-react";

interface InviteAcceptFormProps {
  inviteCode: string;
  email: string;
  userExists: boolean;
  isLoggedIn: boolean;
  isGeneralInvite?: boolean;
  currentUserEmail?: string;
  commissionRate?: number;
}

export function InviteAcceptForm({
  inviteCode,
  email,
  userExists,
  isLoggedIn,
  isGeneralInvite = false,
  currentUserEmail,
  commissionRate,
}: InviteAcceptFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [emailInput, setEmailInput] = React.useState<string>(email || "");

  // Determine initial tab based on user state
  const initialTab = isLoggedIn ? "accept" : (userExists ? "login" : "register");
  
  // Accept the invitation - for logged in users
  const acceptInviteLoggedIn = async () => {
    try {
      setIsLoading(true);
      
      // Use the specialized API for logged-in users
      const response = await fetch("/api/affiliate/invites/accept-logged-in", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          inviteCode,
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || "Failed to accept invitation");
      }
      
      toast.success(data.message || "Successfully joined organization");
      router.push("/affiliate");
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Failed to accept invitation");
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle registration and accept invite
  const handleRegister = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      const formData = new FormData(e.currentTarget);
      const name = formData.get("name") as string;
      const password = formData.get("password") as string;
      const confirmPassword = formData.get("confirmPassword") as string;
      const emailValue = isGeneralInvite ? formData.get("email") as string : email;
      
      if (!name || !password || !confirmPassword || (isGeneralInvite && !emailValue)) {
        toast.error("All fields are required");
        return;
      }
      
      if (password !== confirmPassword) {
        toast.error("Passwords do not match");
        return;
      }
      
      if (password.length < 8) {
        toast.error("Password must be at least 8 characters long");
        return;
      }
      
      // For general invites, validate email format
      if (isGeneralInvite) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailValue)) {
          toast.error("Please enter a valid email address");
          return;
        }
      }
      
      // Register and accept invite in one step
      const response = await fetch("/api/affiliate/invites/accept", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          inviteCode,
          name,
          password,
          ...(isGeneralInvite && { email: emailValue }),
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || "Failed to register and accept invitation");
      }
      
      toast.success("Successfully registered and joined organization");
      
      // Auto sign in after registration
      const signInResult = await signIn("credentials", {
        email: emailValue,
        password,
        redirect: false,
      });
      
      if (signInResult?.error) {
        toast.error("Account created but failed to sign in automatically");
        router.push("/auth/signin");
      } else {
        router.push("/affiliate");
      }
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Failed to register and accept invitation");
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle login and accept invite
  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      const formData = new FormData(e.currentTarget);
      const password = formData.get("password") as string;
      
      if (!password) {
        toast.error("Password is required");
        return;
      }
      
      // Sign in
      const signInResult = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });
      
      if (signInResult?.error) {
        throw new Error("Invalid email or password");
      }
      
      // Accept the invite using the logged-in API
      await acceptInviteLoggedIn();
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Failed to sign in");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="border-t-4 border-t-primary">
      <Tabs defaultValue={initialTab}>
        {!isLoggedIn && (
          <CardHeader>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="register">Register</TabsTrigger>
              <TabsTrigger value="login">Login</TabsTrigger>
            </TabsList>
          </CardHeader>
        )}
        {isLoggedIn && (
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-primary" />
              Accept Invitation
            </CardTitle>
            {currentUserEmail && (
              <CardDescription className="mt-1">
                Continue as <strong>{currentUserEmail}</strong> to join this organization
              </CardDescription>
            )}
          </CardHeader>
        )}
        
        {commissionRate && (
          <div className="px-6 pb-2">
            <div className="flex items-center justify-between rounded-md bg-muted/50 p-3 text-sm">
              <span className="text-muted-foreground">Your commission rate:</span>
              <Badge className="bg-primary/10 hover:bg-primary/20 text-primary font-medium">
                {(commissionRate * 100).toFixed(0)}%
              </Badge>
            </div>
          </div>
        )}
        
        <CardContent className="pt-6">
          {isLoggedIn ? (
            <div className="space-y-4">
              <div className="rounded-md bg-muted p-4">
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>You are logged in as <strong>{currentUserEmail}</strong></span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>You will become an affiliate member</span>
                  </div>
                  {commissionRate && (
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>You will earn <strong>{(commissionRate * 100).toFixed(0)}%</strong> commission on sales</span>
                    </div>
                  )}
                </div>
              </div>
              
              <Button 
                className="w-full group" 
                onClick={acceptInviteLoggedIn}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="mr-2 h-4 w-4" />
                )}
                Accept Invitation
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </div>
          ) : (
            <>
              <TabsContent value="register">
                <form onSubmit={handleRegister} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="Your full name"
                      required
                      disabled={isLoading}
                      autoComplete="name"
                    />
                  </div>
                  
                  {isGeneralInvite ? (
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="Your email address"
                        required
                        disabled={isLoading}
                        value={emailInput}
                        onChange={(e) => setEmailInput(e.target.value)}
                        autoComplete="email"
                        pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                        title="Please enter a valid email address in <NAME_EMAIL>"
                      />
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        disabled
                        readOnly
                        className="bg-muted/50"
                      />
                    </div>
                  )}
                  
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      name="password"
                      type="password"
                      placeholder="Create a password (min. 8 characters)"
                      required
                      disabled={isLoading}
                      minLength={8}
                      autoComplete="new-password"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm Password</Label>
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      placeholder="Confirm your password"
                      required
                      disabled={isLoading}
                      minLength={8}
                      autoComplete="new-password"
                    />
                  </div>
                  
                  {commissionRate && (
                    <div className="mt-4 rounded-md bg-muted/50 p-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">You will earn:</span>
                        <Badge className="bg-primary/10 hover:bg-primary/20 text-primary font-medium">
                          {(commissionRate * 100).toFixed(0)}% Commission
                        </Badge>
                      </div>
                    </div>
                  )}
                  
                  <Button 
                    type="submit" 
                    className="w-full mt-2" 
                    disabled={isLoading}
                  >
                    {isLoading && (
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Register & Accept Invitation
                  </Button>
                </form>
              </TabsContent>
              
              <TabsContent value="login">
                <form onSubmit={handleLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="login-email">Email Address</Label>
                    <Input
                      id="login-email"
                      type="email"
                      value={email}
                      disabled
                      readOnly
                      className="bg-muted/50"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="login-password">Password</Label>
                    <Input
                      id="login-password"
                      name="password"
                      type="password"
                      placeholder="Your password"
                      required
                      disabled={isLoading}
                      autoComplete="current-password"
                    />
                  </div>
                  
                  {commissionRate && (
                    <div className="mt-4 rounded-md bg-muted/50 p-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">You will earn:</span>
                        <Badge className="bg-primary/10 hover:bg-primary/20 text-primary font-medium">
                          {(commissionRate * 100).toFixed(0)}% Commission
                        </Badge>
                      </div>
                    </div>
                  )}
                  
                  <Button 
                    type="submit" 
                    className="w-full mt-2" 
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <FileText className="mr-2 h-4 w-4" />
                    )}
                    Login & Accept Invitation
                  </Button>
                </form>
              </TabsContent>
            </>
          )}
        </CardContent>
      </Tabs>
    </Card>
  );
} 