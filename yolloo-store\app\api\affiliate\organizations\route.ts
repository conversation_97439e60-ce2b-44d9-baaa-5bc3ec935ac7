import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for organization creation
const createOrgSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().optional(),
  logo: z.string().optional(),
  commissionRate: z.number().min(0).max(1).optional(),
  discountRate: z.number().min(0).max(1).optional(),
});

// GET - List organizations (admin sees all, org admin sees their own)
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Get user with role and affiliate info
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { 
        role: true, 
        affiliate: {
          include: {
            organization: true
          }
        } 
      },
    });
    
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Get user's affiliate profile
    const affiliateProfile = await prisma.affiliateProfile.findUnique({
      where: { userId: session.user.id },
    });
    
    if (!affiliateProfile) {
      // User has no affiliate profile, return empty array
      return NextResponse.json([]);
    }
    
    // Get organizations the user has joined
    const memberOrganizations = await prisma.affiliateOrganization.findMany({
      where: {
        members: {
          some: {
            id: affiliateProfile.id
          }
        }
      },
      include: {
        _count: {
          select: { members: true },
        },
        members: {
          select: { 
            id: true,
            createdAt: true,
            isAdmin: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: "desc" },
    });
    
    // Map the data to include membership join date
    const orgWithMembershipInfo = memberOrganizations.map(org => {
      // Find the current user's membership
      const currentUserMembership = org.members.find(
        member => member.id === affiliateProfile.id
      );
      const memberJoinDate = currentUserMembership?.createdAt;
      
      // Filter members who are admins and map to a simpler format
      const administrators = org.members
        .filter(member => member.isAdmin && member.user) 
        .map(admin => ({
          id: admin.user.id,
          name: admin.user.name,
          email: admin.user.email,
          image: admin.user.image
        }));
      
      // Remove members array and create clean response
      const { members, ...orgWithoutMembers } = org;
      
      return {
        ...orgWithoutMembers,
        memberSince: memberJoinDate,
        administrators
      };
    });
    
    return NextResponse.json(orgWithMembershipInfo);
  } catch (error) {
    console.error("[ORGANIZATIONS_GET]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

// POST - Create a new organization
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Get user with role
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true, affiliate: true },
    });
    
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Only admins or users with affiliate profiles can create organizations
    if (user.role !== "ADMIN" && !user.affiliate) {
      return NextResponse.json(
        { error: "You must be an affiliate to create an organization" },
        { status: 403 }
      );
    }
    
    const body = await req.json();
    
    // Validate input
    const validationResult = createOrgSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { name, description, logo, commissionRate, discountRate } = validationResult.data;
    
    // Generate a unique code for the organization
    const code = `ORG-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
    
    // Create the organization
    const organization = await prisma.affiliateOrganization.create({
      data: {
        name,
        description,
        logo,
        code,
        commissionRate: commissionRate ?? 0.12,
        discountRate: discountRate ?? 0.05,
        members: {
          // If user is not an admin creating for someone else
          ...(user.role !== "ADMIN" && {
            connect: {
              id: user.affiliate!.id,
            },
          }),
        },
      },
    });
    
    // If the user is not an admin and has an affiliate profile, make them an admin of the organization
    if (user.role !== "ADMIN" && user.affiliate) {
      await prisma.affiliateProfile.update({
        where: { id: user.affiliate.id },
        data: {
          organizationId: organization.id,
          isAdmin: true,
        },
      });
    }
    
    return NextResponse.json(organization, { status: 201 });
  } catch (error) {
    console.error("Error creating organization:", error);
    return NextResponse.json(
      { error: "Failed to create organization" },
      { status: 500 }
    );
  }
} 