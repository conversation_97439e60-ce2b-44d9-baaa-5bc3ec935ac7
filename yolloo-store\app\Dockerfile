# 构建阶段
FROM node:22-alpine AS builder
WORKDIR /app
RUN apk add --no-cache openssl
COPY ./package.json ./
COPY ./package-lock.json ./
COPY ../prisma ./prisma/
RUN npm install
COPY . .
RUN npm run build

# 运行阶段
FROM node:22-alpine AS runner
WORKDIR /app
RUN apk add --no-cache openssl postgresql-client
COPY --from=builder /app ./
COPY .env .env
EXPOSE 8000

# 启动脚本
RUN echo '#!/bin/sh' > /app/start.sh && \
    echo 'set -a' >> /app/start.sh && \
    echo '. /app/.env' >> /app/start.sh && \
    echo 'set +a' >> /app/start.sh && \
    echo 'npx prisma migrate deploy' >> /app/start.sh && \
    echo 'npx prisma generate' >> /app/start.sh && \
    echo 'npm run start' >> /app/start.sh && \
    chmod +x /app/start.sh

CMD ["/app/start.sh"] 