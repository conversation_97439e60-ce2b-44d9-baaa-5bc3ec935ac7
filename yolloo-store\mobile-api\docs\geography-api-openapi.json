{"openapi": "3.0.3", "info": {"title": "Geography Products API", "description": "地理位置商品API - 提供按大洲和国家筛选商品的功能", "version": "1.0.0"}, "servers": [{"url": "http://localhost:3001", "description": "Development server"}], "paths": {"/geography/continents": {"get": {"summary": "获取所有大洲列表", "description": "返回所有支持的大洲列表", "tags": ["Geography"], "responses": {"200": {"description": "成功返回大洲列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContinentsResponse"}}}}}}}, "/geography/continents/{continent}/countries": {"get": {"summary": "根据大洲获取国家列表", "description": "根据指定的大洲返回该大洲下的所有国家", "tags": ["Geography"], "parameters": [{"name": "continent", "in": "path", "required": true, "description": "大洲ID", "schema": {"type": "string", "enum": ["asia", "europe", "america", "oceania", "africa"]}}], "responses": {"200": {"description": "成功返回国家列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CountriesResponse"}}}}}}}, "/geography/countries/{countryCode}/products": {"get": {"summary": "根据国家获取商品列表", "description": "根据指定的国家代码返回该国家的商品列表", "tags": ["Geography"], "parameters": [{"name": "countryCode", "in": "path", "required": true, "description": "国家代码", "schema": {"type": "string", "example": "JP"}}, {"name": "planType", "in": "query", "required": false, "description": "商品类型", "schema": {"type": "string", "enum": ["Total", "Daily"]}}, {"name": "page", "in": "query", "required": false, "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "pageSize", "in": "query", "required": false, "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10}}], "responses": {"200": {"description": "成功返回商品列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductsResponse"}}}}}}}, "/geography/products": {"get": {"summary": "获取所有商品", "description": "获取所有国家的商品列表，不限制特定国家", "tags": ["Geography"], "parameters": [{"name": "planType", "in": "query", "required": false, "description": "商品类型", "schema": {"type": "string", "enum": ["Total", "Daily"]}}, {"name": "page", "in": "query", "required": false, "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "pageSize", "in": "query", "required": false, "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10}}], "responses": {"200": {"description": "成功返回商品列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductsResponse"}}}}}}}, "/geography/filters": {"get": {"summary": "获取商品筛选选项", "description": "返回可用的商品筛选选项", "tags": ["Geography"], "responses": {"200": {"description": "成功返回筛选选项", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FiltersResponse"}}}}}}}}, "components": {"schemas": {"Continent": {"type": "object", "properties": {"id": {"type": "string", "description": "大洲ID"}, "name": {"type": "string", "description": "大洲名称（根据语言显示）"}, "nameEn": {"type": "string", "description": "英文名称"}, "nameZh": {"type": "string", "description": "中文名称"}}}, "Country": {"type": "object", "properties": {"code": {"type": "string", "description": "国家代码"}, "name": {"type": "string", "description": "国家名称（根据语言显示）"}, "nameEn": {"type": "string", "description": "英文名称"}, "nameZh": {"type": "string", "description": "中文名称"}}}, "Product": {"type": "object", "properties": {"id": {"type": "string", "description": "商品ID"}, "name": {"type": "string", "description": "商品名称"}, "description": {"type": "string", "description": "商品描述"}, "price": {"type": "number", "description": "价格"}, "currency": {"type": "string", "description": "货币"}, "imageUrl": {"type": "string", "description": "商品图片URL"}, "dataSize": {"type": "number", "description": "流量大小（MB）"}, "planType": {"type": "string", "enum": ["Total", "Daily"], "description": "商品类型"}, "duration": {"type": "number", "description": "有效期（天）"}, "countries": {"type": "array", "items": {"type": "string"}, "description": "支持的国家列表"}, "countryCode": {"type": "string", "description": "主要国家代码"}, "rating": {"type": "number", "description": "评分"}, "reviewCount": {"type": "number", "description": "评论数量"}}}, "Context": {"type": "object", "properties": {"language": {"type": "string", "description": "语言"}, "theme": {"type": "string", "description": "主题"}, "currency": {"type": "string", "description": "货币"}}}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码"}, "pageSize": {"type": "integer", "description": "每页数量"}, "total": {"type": "integer", "description": "总数量"}, "totalPages": {"type": "integer", "description": "总页数"}}}, "ContinentsResponse": {"type": "object", "properties": {"continents": {"type": "array", "items": {"$ref": "#/components/schemas/Continent"}}, "context": {"$ref": "#/components/schemas/Context"}}}, "CountriesResponse": {"type": "object", "properties": {"continent": {"$ref": "#/components/schemas/Continent"}, "countries": {"type": "array", "items": {"$ref": "#/components/schemas/Country"}}, "context": {"$ref": "#/components/schemas/Context"}}}, "ProductsResponse": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "filters": {"type": "object", "properties": {"countryCode": {"type": "string"}, "planType": {"type": "string"}}}, "context": {"$ref": "#/components/schemas/Context"}}}, "FiltersResponse": {"type": "object", "properties": {"planTypes": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "string"}, "label": {"type": "string"}, "description": {"type": "string"}}}}, "dataSizes": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "string"}, "label": {"type": "string"}}}}, "sortOptions": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "string"}, "label": {"type": "string"}}}}, "context": {"$ref": "#/components/schemas/Context"}}}}}, "tags": [{"name": "Geography", "description": "地理位置相关的商品接口"}]}