# Yolloo Store

A modern e-commerce platform for eSIMs, devices, and mobile plans, built with Next.js 13 and TypeScript.

## Features

- 🌐 **eSIM Management**: Purchase and manage eSIMs for global connectivity
- 🛍️ **E-commerce Platform**: Full-featured online store with shopping cart and checkout
- 💳 **Secure Payments**: Integrated payment processing with Stripe
- 👥 **User Authentication**: Secure sign-up and login functionality
- 🤝 **Affiliate Program**: Built-in affiliate marketing system
- 📱 **Responsive Design**: Mobile-first approach with modern UI
- 🔄 **Odoo Integration**: Seamless integration with Odoo ERP system
- 👨‍💼 **Admin Dashboard**: Comprehensive admin panel for business management
- 💬 **Live Chat Support**: Real-time customer support integration

## Tech Stack

- **Frontend**: Next.js 13, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: NextAuth.js
- **State Management**: Zustand
- **UI Components**: <PERSON>dix <PERSON>, Lucide Icons
- **Payment Processing**: Stripe
- **Deployment**: Docker, Vercel

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm/yarn/pnpm
- PostgreSQL
- Redis (for rate limiting)

### Installation

1. Clone the repository:
```bash
git clone http://***********/baixiliang/esim-store-standalone.git
cd yolloo-store
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```
Edit `.env.local` with your configuration values.

4. Set up the database:
```bash
npx prisma generate
npx prisma db push
```

5. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

The application will be available at [http://localhost:8000](http://localhost:8000).

### Docker Deployment

To run the application using Docker:

```bash
docker-compose up -d
```

## API Documentation

The application includes several API endpoints:

### Production APIs

- Products:
  - `GET /api/odoo/products` - Get product list
  - `POST /api/odoo/products/price` - Get product prices

- Orders:
  - `POST /api/odoo/orders` - Create new order
  - `PUT /api/odoo/orders` - Update order status
  - `POST /api/odoo/orders/status` - Query order status
  - `POST /api/odoo/orders/qrcode` - Get order QR code

For detailed API documentation, visit the `/odoo-integration` page in the application.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is proprietary software. All rights reserved.

## Support

For support, please contact our <NAME_EMAIL>
