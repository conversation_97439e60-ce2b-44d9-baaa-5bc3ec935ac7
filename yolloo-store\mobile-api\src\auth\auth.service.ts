import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
  Logger,
  ConflictException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma.service';
import { SocialAuthService } from '../social-auth/social-auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { LoginCodeDto } from './dto/login-code.dto';
import { SocialLoginDto } from './dto/social-login.dto';
import * as bcrypt from 'bcrypt';
import { DateFormatter } from '../common/utils';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private socialAuthService: SocialAuthService,
  ) {}

  async register(registerDto: RegisterDto) {
    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email: registerDto.email },
    });

    if (existingUser) {
      throw new BadRequestException('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(registerDto.password, 10);

    // Create new user
    const user = await this.prisma.user.create({
      data: {
        email: registerDto.email,
        name: registerDto.name,
        hashedPassword: hashedPassword,
        role: 'CUSTOMER',
      },
    });

    // Generate JWT token
    const token = this.generateToken(user);

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      token,
    };
  }

  async login(loginDto: LoginDto) {
    // Find user by email
    const user = await this.prisma.user.findUnique({
      where: { email: loginDto.email },
    });

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Verify password
    if (!user.hashedPassword) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(
      loginDto.password,
      user.hashedPassword,
    );

    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Generate JWT token
    const token = this.generateToken(user);

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      image: user.image,
      token,
    };
  }

  async loginWithCode(loginCodeDto: LoginCodeDto) {
    if (loginCodeDto.action === 'request') {
      // 检查用户是否存在
      const user = await this.prisma.user.findUnique({
        where: { email: loginCodeDto.email },
      });

      if (!user) {
        // 如果用户不存在，可以选择创建一个新用户或返回错误
        // 这里我们选择返回成功消息，但在实际应用中可能需要不同的处理
      }

      // 在实际应用中，你会生成一个验证码并发送邮件
      // 这里我们可以在数据库中存储验证码，但为了简化，我们只返回成功消息
      return {
        status: 'sent',
        message: '验证码已发送至您的邮箱',
      };
    } else if (loginCodeDto.action === 'verify') {
      if (!loginCodeDto.code) {
        throw new BadRequestException('Verification code is required');
      }

      // 在实际应用中，你会验证用户提供的验证码
      // 这里我们接受任何验证码进行演示

      // 查找或创建用户
      let user = await this.prisma.user.findUnique({
        where: { email: loginCodeDto.email },
      });

      if (!user) {
        // 如果用户不存在，创建新用户
        user = await this.prisma.user.create({
          data: {
            email: loginCodeDto.email,
            name: loginCodeDto.email.split('@')[0], // 从邮箱生成默认名称
            hashedPassword: await bcrypt.hash(Math.random().toString(36), 10), // 随机密码
            role: 'CUSTOMER',
          },
        });
      }

      // 生成 JWT 令牌
      const token = this.generateToken(user);

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        token,
      };
    }

    throw new BadRequestException('Invalid action');
  }

  async socialLogin(socialLoginDto: SocialLoginDto) {
    const { provider, token } = socialLoginDto;

    try {
      let socialUserInfo;

      // 根据不同的社交平台验证token
      switch (provider) {
        case 'google':
          socialUserInfo = await this.socialAuthService.verifyGoogleToken(token);
          break;
        case 'facebook':
          socialUserInfo = await this.socialAuthService.verifyFacebookToken(token);
          break;
        case 'apple':
          socialUserInfo = await this.socialAuthService.verifyAppleToken(token);
          break;
        case 'wechat':
          socialUserInfo = await this.socialAuthService.verifyWechatCode(token);
          break;
        default:
          throw new BadRequestException('Unsupported social provider');
      }

      // 查找或创建用户
      const user = await this.socialAuthService.findOrCreateSocialUser(socialUserInfo);

      // 生成 JWT 令牌
      const jwtToken = this.generateToken(user);

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user.image,
        token: jwtToken,
      };
    } catch (error) {
      this.logger.error('Social login failed:', error);
      throw new UnauthorizedException('Social login failed');
    }
  }

  private generateToken(user: any) {
    const payload = { email: user.email, sub: user.id };
    return this.jwtService.sign(payload);
  }
}
