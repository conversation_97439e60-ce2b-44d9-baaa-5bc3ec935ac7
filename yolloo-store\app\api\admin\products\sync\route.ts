import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { ProductSyncManager, SyncEventType } from "@/app/services/productSyncManager";
import { PRODUCT_TYPES } from "@/app/config/odoo";

// Force dynamic rendering
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// Sync request interface
interface SyncRequest {
  productTypes?: string[];
  categoryOverride?: string;
  skipEmptyVariantsAndZeroPrice?: boolean;
  allProductTypes?: string[]; // All available product types
}

// Get sync status
export async function GET(req: NextRequest) {
  try {
    // Check user permissions
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get sync manager instance
    const syncManager = ProductSyncManager.getInstance();

    // Logic to get sync status can be implemented here
    // For example, query recent sync records from the database

    // Get custom product types from local storage or database
    // Here we only return default types because custom types are stored on the client
    return NextResponse.json({
      message: "Sync status query feature not yet implemented",
      availableTypes: PRODUCT_TYPES
    });
  } catch (error) {
    console.error("[SYNC_STATUS]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// Execute synchronization
export async function POST(req: NextRequest) {
  try {
    // Check user permissions
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Parse request body
    let requestData: SyncRequest = {};
    try {
      requestData = await req.json();
    } catch (e) {
      // If request body is empty or invalid JSON, use default values
    }

    const {
      productTypes,
      categoryOverride,
      skipEmptyVariantsAndZeroPrice = true,
      allProductTypes
    } = requestData;

    // If client provided custom product type list, log it
    if (allProductTypes && Array.isArray(allProductTypes) && allProductTypes.length > 0) {
      console.log(`[SYNC] Using custom product types: ${allProductTypes.join(', ')}`);
    }

    // Get sync manager instance
    const syncManager = ProductSyncManager.getInstance();

    // Execute synchronization
    const syncResult = await syncManager.syncProducts({
      productTypes,
      forceCategoryName: categoryOverride,
      skipEmptyVariantsAndZeroPrice,
      sourceType: 'standard',
      onlyUpdateOwnProducts: true
    });

    return NextResponse.json({
      message: "Product synchronization completed",
      stats: syncResult.stats,
      count: syncResult.products.length,
      errors: syncResult.errors.length > 0 ? syncResult.errors.map(e => e.message) : []
    });
  } catch (error) {
    console.error("[SYNC_PRODUCTS]", error);
    return new NextResponse(
      JSON.stringify({
        message: "Error occurred during synchronization",
        error: error instanceof Error ? error.message : "Unknown error"
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
