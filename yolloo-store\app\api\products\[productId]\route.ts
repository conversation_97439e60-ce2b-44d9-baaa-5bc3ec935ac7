import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// GET /api/products/[productId] - 获取单个产品详情
export async function GET(
  req: Request,
  { params }: { params: { productId: string } }
) {
  try {
    const product = await prisma.product.findUnique({
      where: {
        id: params.productId,
      },
      include: {
        category: true,
        reviews: {
          include: {
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
        variants: true,
      },
    });

    if (!product) {
      return new NextResponse("Product not found", { status: 404 });
    }

    // 修正：将所有 price 字段转为 number，避免 Decimal 传递到前端
    const safeProduct = {
      ...product,
      price: typeof product.price === 'number' ? product.price : Number(product.price),
      variants: Array.isArray(product.variants)
        ? product.variants.map(v => ({
            ...v,
            price: typeof v.price === 'number' ? v.price : Number(v.price),
          }))
        : [],
    };
    return NextResponse.json(safeProduct);
  } catch (error) {
    console.error("[PRODUCT_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// PATCH /api/products/[productId] - 更新产品
export async function PATCH(
  req: Request,
  { params }: { params: { productId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const {
      name,
      description,
      price,
      images,
      categoryId,
      stock,
      specifications,
      status,
    } = body;

    const product = await prisma.product.update({
      where: {
        id: params.productId,
      },
      data: {
        name: name === null ? undefined : name,
        description: description === null ? undefined : description,
        price: price !== undefined && price !== null ? 
          isNaN(parseFloat(String(price))) ? undefined : parseFloat(String(price)) : undefined,
        images: images === null ? undefined : images,
        categoryId: categoryId === null ? undefined : categoryId,
        stock: stock !== undefined && stock !== null ? 
          isNaN(parseInt(String(stock), 10)) ? undefined : parseInt(String(stock), 10) : undefined,
        specifications: specifications === null ? undefined : specifications,
        status: status === null ? undefined : status,
      },
    });

    return NextResponse.json(product);
  } catch (error) {
    console.error("[PRODUCT_PATCH]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// DELETE /api/products/[productId] - 删除产品
export async function DELETE(
  req: Request,
  { params }: { params: { productId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    await prisma.product.delete({
      where: {
        id: params.productId,
      },
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("[PRODUCT_DELETE]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
} 