import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for batch adding members
const batchMemberSchema = z.object({
  emails: z.array(z.string().email()),
  commissionRate: z.number().min(0).max(1).optional(),
  isAdmin: z.boolean().optional(),
});

// POST - Batch add members to an organization (organization admin only)
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const organizationId = params.id;
    
    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
      include: {
        members: {
          where: {
            userId: session.user.id,
            isAdmin: true,
          },
        },
      },
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Check if current user is an admin of this organization
    if (organization.members.length === 0) {
      return NextResponse.json({ error: "You must be an admin of this organization to add members" }, { status: 403 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validationResult = batchMemberSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { emails, commissionRate = 0.5, isAdmin = false } = validationResult.data;
    
    // Get existing users by emails
    const existingUsers = await prisma.user.findMany({
      where: { email: { in: emails } },
      include: {
        affiliate: true,
      },
    });
    
    // Prepare results arrays
    const addedMembers: any[] = [];
    const errors: { email: string; message: string }[] = [];
    
    // Process each email
    await Promise.all(
      emails.map(async (email) => {
        try {
          // Find user with this email
          const user = existingUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
          
          if (!user) {
            errors.push({ email, message: "User not found" });
            return;
          }
          
          // Check if user already has an affiliate profile
          if (user.affiliate) {
            // Check if already in an organization
            if (user.affiliate.organizationId) {
              // Check if already in this organization
              if (user.affiliate.organizationId === organizationId) {
                errors.push({ email, message: "User is already a member of this organization" });
              } else {
                errors.push({ email, message: "User is already a member of another organization" });
              }
              return;
            }
            
            // Add user to this organization
            const updatedAffiliate = await prisma.affiliateProfile.update({
              where: { id: user.affiliate.id },
              data: {
                organizationId,
                commissionRate,
                isAdmin,
              },
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true,
                  },
                },
              },
            });
            
            addedMembers.push(updatedAffiliate);
          } else {
            // User doesn't have an affiliate profile, create one
            // Generate a unique affiliate code
            const code = `${user.name?.substring(0, 3) || "AFF"}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
            
            const newAffiliate = await prisma.affiliateProfile.create({
              data: {
                userId: user.id,
                code,
                organizationId,
                commissionRate,
                isAdmin,
              },
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true,
                  },
                },
              },
            });
            
            addedMembers.push(newAffiliate);
          }
        } catch (error) {
          console.error(`Error processing email ${email}:`, error);
          errors.push({ email, message: "Failed to add member" });
        }
      })
    );
    
    return NextResponse.json({
      addedMembers,
      errors,
      totalAdded: addedMembers.length,
      totalFailed: errors.length,
    });
  } catch (error) {
    console.error("Error batch adding members:", error);
    return NextResponse.json(
      { error: "Failed to batch add members" },
      { status: 500 }
    );
  }
} 