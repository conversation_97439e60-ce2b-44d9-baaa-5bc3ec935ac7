import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { cleanCardNumber } from "@/lib/utils"
import { Prisma } from "@prisma/client"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 403 })
    }

    const json = await req.json()
    const { number, type, status, activationDate, expiryDate } = json

    // 清理卡号，只保留数字
    const cleanedNumber = cleanCardNumber(number)

    if (!cleanedNumber) {
      return new NextResponse("Card number is required and must contain digits", { status: 400 })
    }

    const card = await prisma.yollooCard.create({
      data: {
        number: cleanedNumber,
        type,
        status,
        activationDate,
        expiryDate,
      },
    })

    return NextResponse.json(card)
  } catch (error) {
    console.error("[CARDS_POST]", error)

    // 检查是否是唯一约束错误（卡片已存在）
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
      return new NextResponse("Card number already exists", { status: 400 })
    }

    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 403 })
    }

    const { searchParams } = new URL(req.url)
    const query = searchParams.get("query")

    const where = query
      ? {
          OR: [
            { number: { contains: query } },
            { type: { contains: query } },
            { status: { contains: query } },
          ],
        }
      : {}

    const cards = await prisma.yollooCard.findMany({
      where,
      include: {
        user: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    })

    return NextResponse.json(cards)
  } catch (error) {
    console.error("[CARDS_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 