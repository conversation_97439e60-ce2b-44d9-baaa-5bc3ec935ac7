"use client"

import Image from "next/image"
import Link from "next/link"
import { Command } from "lucide-react"
import { SignInForm } from "@/components/auth/signin-form"
import { useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useSession } from "next-auth/react"
import { Icons } from "@/components/icons"

const SignInPage = () => {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const callbackUrl = searchParams?.get("callbackUrl")
  const signUpUrl = callbackUrl ? `/auth/signup?callbackUrl=${encodeURIComponent(callbackUrl)}` : "/auth/signup"

  useEffect(() => {
    if (status === "authenticated") {
      const returnUrl = callbackUrl || "/account"
      
      try {
        new URL(returnUrl);
        window.location.href = returnUrl;
      } catch (e) {
        if (returnUrl.startsWith('/')) {
          router.replace(returnUrl);
        } else {
          router.replace(`/${returnUrl}`);
        }
      }
    }
  }, [status, router, callbackUrl])

  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col p-10 text-white lg:flex">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-700">
          <div className="absolute inset-0 bg-[url('/auth-bg-pattern.svg')] opacity-20" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/30" />
        </div>
        <div className="relative z-20 flex items-center text-lg font-medium">
          <div className="flex items-center space-x-2">
            <div className="rounded-lg bg-white/10 p-1.5 backdrop-blur-sm">
              <Icons.zap className="h-6 w-6" />
            </div>
            <span className="text-xl font-bold tracking-tight">
              Yolloo<span className="text-blue-200">Store</span>
            </span>
          </div>
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              "This store has transformed how I shop online. The experience is
              seamless and the products are amazing."
            </p>
            <footer className="text-sm">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center">
                  <Icons.user className="h-4 w-4" />
                </div>
                <div>
                  <p className="font-semibold">Sofia Davis</p>
                  <p className="text-xs text-blue-200">Happy Customer</p>
                </div>
              </div>
            </footer>
          </blockquote>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Welcome back
            </h1>
            <p className="text-sm text-muted-foreground">
              Sign in to your account to continue
            </p>
          </div>
          <SignInForm />
          <p className="px-8 text-center text-sm text-muted-foreground">
            Don't have an account?{" "}
            <Link
              href={signUpUrl}
              className="underline underline-offset-4 hover:text-primary"
            >
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

export default SignInPage 