"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

export function MainNav() {
  const pathname = usePathname()

  return (
    <nav className="flex items-center space-x-6 text-sm font-medium">
      <Link
        href="/admin"
        className={cn(
          "transition-colors hover:text-foreground/80",
          pathname === "/admin" ? "text-foreground" : "text-foreground/60"
        )}
      >
        Overview
      </Link>
      <Link
        href="/admin/products"
        className={cn(
          "transition-colors hover:text-foreground/80",
          pathname?.startsWith("/admin/products")
            ? "text-foreground"
            : "text-foreground/60"
        )}
      >
        Products
      </Link>
      <Link
        href="/admin/orders"
        className={cn(
          "transition-colors hover:text-foreground/80",
          pathname?.startsWith("/admin/orders")
            ? "text-foreground"
            : "text-foreground/60"
        )}
      >
        Orders
      </Link>
      <Link
        href="/admin/users"
        className={cn(
          "transition-colors hover:text-foreground/80",
          pathname?.startsWith("/admin/users")
            ? "text-foreground"
            : "text-foreground/60"
        )}
      >
        Users
      </Link>
    </nav>
  )
} 