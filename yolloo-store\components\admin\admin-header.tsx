import Link from "next/link"
import { UserNav } from "@/components/admin/user-nav"

interface AdminHeaderProps {
  user: {
    name?: string | null
    image?: string | null
    email?: string | null
  }
}

export function AdminHeader({ user }: AdminHeaderProps) {
  return (
    <header className="sticky top-0 z-40 border-b bg-background">
      <div className="container flex h-16 items-center justify-between py-4">
        <Link href="/admin" className="flex items-center space-x-2">
          <span className="font-bold">Yolloo Store Admin</span>
        </Link>
        <UserNav user={user} />
      </div>
    </header>
  )
} 