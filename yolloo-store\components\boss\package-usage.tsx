'use client';

import { formatDataUsage } from "@/lib/boss-utils";
import { Progress } from "@/components/ui/progress";

interface PackageUsageProps {
  usageData: any;
}

export function PackageUsage({ usageData }: PackageUsageProps) {
  if (!usageData) {
    return <div className="py-4 text-center text-gray-500">No usage data available</div>;
  }

  // Calculate usage percentage
  const usedBytes = parseInt(usageData.usageBytes || '0');
  const totalBytes = parseInt(usageData.totalBytes || '0');
  const usagePercentage = totalBytes > 0 ? Math.min(100, (usedBytes / totalBytes) * 100) : 0;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h3 className="text-sm font-medium">Order Number</h3>
          <p className="text-sm">{usageData.orderSn || '-'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Package Name</h3>
          <p className="text-sm">{usageData.packageName || '-'}</p>
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-medium">Data Usage</h3>
          <div className="text-sm">
            <span className="font-medium">{formatDataUsage(usageData.usageBytes)}</span>
            <span className="text-muted-foreground"> / {formatDataUsage(usageData.totalBytes)}</span>
          </div>
        </div>
        <Progress value={usagePercentage} className="h-2" />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h3 className="text-sm font-medium">Start Time</h3>
          <p className="text-sm">{usageData.startTime || '-'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">End Time</h3>
          <p className="text-sm">{usageData.endTime || '-'}</p>
        </div>
      </div>
    </div>
  );
}
