'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { setCookie } from 'cookies-next';

export function RefHandler() {
  const searchParams = useSearchParams();

  useEffect(() => {
    const ref = searchParams.get('ref');
    if (ref) {
      // 设置cookie，30天有效期
      setCookie('referralCode', ref, { 
        maxAge: 60 * 60 * 24 * 30, // 30天
        path: '/' 
      });
      
      console.log('Referral code saved to cookie:', ref);
      
      // 发送访问记录
      trackVisit(ref);
    }
  }, [searchParams]);
  
  const trackVisit = async (referralCode: string) => {
    try {
      // 获取来源信息
      let source = 'direct';
      let referrer = '';
      
      // 如果是从外部网站来的
      if (typeof document !== 'undefined' && document.referrer) {
        try {
          const referrerUrl = new URL(document.referrer);
          source = referrerUrl.hostname;
          referrer = document.referrer;
        } catch (e) {
          console.error('Invalid referrer URL:', e);
        }
      }
      
      // 如果URL中有utm参数
      const utmSource = searchParams.get('utm_source');
      if (utmSource) {
        source = utmSource;
      }
      
      const response = await fetch('/api/affiliate/visit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          referralCode,
          path: window.location.pathname,
          source,
          referrer,
          userAgent: navigator.userAgent,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to track visit: ${errorData.error || response.statusText}`);
      }
      
      console.log('Affiliate visit tracked successfully');
    } catch (error) {
      console.error('Failed to track affiliate visit:', error);
    }
  };

  return null; // 这是一个不可见的组件
} 