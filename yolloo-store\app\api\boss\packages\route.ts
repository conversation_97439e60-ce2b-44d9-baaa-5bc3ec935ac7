import { NextRequest, NextResponse } from 'next/server';
import { bossService } from '@/lib/boss';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Force dynamic rendering
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

/**
 * GET request - Get packages for a specific card UID
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the card ID from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const cardId = searchParams.get('cardId');
    const orderSn = searchParams.get('orderSn');
    const action = searchParams.get('action');

    if (!cardId && !orderSn) {
      return NextResponse.json(
        {
          resultCode: '400',
          resultMsg: 'Card ID or Order SN is required',
          data: null
        },
        { status: 400 }
      );
    }

    // If orderSn is provided, handle specific order actions
    if (orderSn) {
      // Verify the user has access to this order
      // This is a simplified check - in a real app, you might want to verify
      // that the order belongs to one of the user's cards

      let response;
      let error = null;

      try {
        switch (action) {
          case 'queryUsage':
            response = await bossService.queryUsageOrderPlan(orderSn);
            break;
          case 'queryDetails':
            response = await bossService.queryOrderPlan(orderSn);
            break;
          default:
            return NextResponse.json(
              {
                resultCode: '400',
                resultMsg: 'Invalid action',
                data: null
              },
              { status: 400 }
            );
        }
      } catch (err) {
        console.error(`Error executing Boss service action ${action}:`, err);
        error = {
          code: 'API_ERROR',
          message: err instanceof Error ? err.message : 'Error communicating with Boss service'
        };

        // 创建一个错误响应，但使用成功的状态码，以便客户端可以处理
        response = {
          resultCode: '10000', // 使用成功代码，但包含错误信息
          resultMsg: 'Error occurred but returning partial data',
          data: {
            hasError: true,
            errorDetails: error
          }
        };
      }

      return NextResponse.json(response);
    }

    // Handle card-based queries
    // Verify the card belongs to the current user
    const card = await prisma.yollooCard.findFirst({
      where: {
        AND: [
          { id: cardId },
          { userId: session.user.id }
        ]
      }
    });

    if (!card) {
      return NextResponse.json(
        {
          resultCode: '404',
          resultMsg: 'Card not found',
          data: null
        },
        { status: 404 }
      );
    }

    // Use the card number as the UID for the Boss service
    const uid = card.number;

    // 获取所有套餐数据（分页查询）
    let allPackages: any[] = [];
    let pageNum = 0;
    const pageSize = 100; // 使用较大的页面大小
    let hasMoreData = true;

    // 循环获取所有页面的数据
    let bossServiceError = null;

    try {
      while (hasMoreData) {
        const queryParams = {
          pageNum,
          pageSize,
          uid,
          queryParam: {
            uid
          }
        };

        // 调用Boss服务获取当前页的套餐
        const response = await bossService.getDeviceOrderPage(queryParams);

        if (response.resultCode === '10000' && response.data && response.data.list) {
          // 添加当前页的数据到总列表
          allPackages = [...allPackages, ...response.data.list];

          // 检查是否还有更多数据
          if (response.data.list.length < pageSize) {
            hasMoreData = false;
          } else {
            pageNum++;
          }
        } else {
          // 保存错误信息，但不中断整个请求
          bossServiceError = {
            code: response.resultCode || 'UNKNOWN',
            message: response.resultMsg || 'Unknown error from Boss service'
          };
          hasMoreData = false;
        }
      }
    } catch (error) {
      console.error('Error during Boss service pagination:', error);
      bossServiceError = {
        code: 'API_ERROR',
        message: error instanceof Error ? error.message : 'Error communicating with Boss service'
      };
    }

    // 创建一个包含所有数据的响应对象，即使有错误也返回成功状态
    // 客户端可以根据hasError和errorDetails字段来处理错误情况
    const response = {
      resultCode: '10000',
      resultMsg: bossServiceError ? 'Partial data available' : 'Success',
      data: {
        list: allPackages,
        total: allPackages.length,
        hasError: !!bossServiceError,
        errorDetails: bossServiceError
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching packages from Boss service:', error);
    return NextResponse.json(
      {
        resultCode: '500',
        resultMsg: 'Failed to fetch packages',
        data: null
      },
      { status: 500 }
    );
  }
}

/**
 * POST request - Perform actions on packages
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await request.json();
    const { action, orderSn, cardId, uid } = data;

    if (!action || !orderSn) {
      return NextResponse.json(
        {
          resultCode: '400',
          resultMsg: 'Action and Order SN are required',
          data: null
        },
        { status: 400 }
      );
    }

    // If cardId is provided, verify the card belongs to the current user
    if (cardId) {
      const card = await prisma.yollooCard.findFirst({
        where: {
          AND: [
            { id: cardId },
            { userId: session.user.id }
          ]
        }
      });

      if (!card) {
        return NextResponse.json(
          {
            resultCode: '404',
            resultMsg: 'Card not found',
            data: null
          },
          { status: 404 }
        );
      }
    }

    let response;

    try {
      // Execute the requested action
      switch (action) {
        case 'closeOrderPlan':
          response = await bossService.closeOrderPlan(orderSn);
          break;
        case 'toppingOrderPlan':
          if (!uid) {
            return NextResponse.json(
              {
                resultCode: '400',
                resultMsg: 'UID is required for toppingOrderPlan',
                data: null
              },
              { status: 400 }
            );
          }
          response = await bossService.toppingOrderPlan(orderSn, uid);
          break;
        default:
          return NextResponse.json(
            {
              resultCode: '400',
              resultMsg: 'Invalid action',
              data: null
            },
            { status: 400 }
          );
      }

      // 检查响应是否包含错误
      if (response.resultCode !== '10000') {
        console.warn(`Boss service action ${action} returned non-success code:`, response.resultCode, response.resultMsg);
      }

      return NextResponse.json(response);
    } catch (error) {
      console.error(`Error executing Boss service action ${action}:`, error);

      // 返回一个友好的错误响应
      return NextResponse.json({
        resultCode: '500',
        resultMsg: error instanceof Error ? error.message : 'Error communicating with Boss service',
        data: {
          action,
          orderSn,
          error: true,
          errorDetails: {
            message: error instanceof Error ? error.message : 'Unknown error occurred'
          }
        }
      });
    }
  } catch (error) {
    console.error('Error performing action on package:', error);
    return NextResponse.json(
      {
        resultCode: '500',
        resultMsg: 'Failed to perform action',
        data: null
      },
      { status: 500 }
    );
  }
}
