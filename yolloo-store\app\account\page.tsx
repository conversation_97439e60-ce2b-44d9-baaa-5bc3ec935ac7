import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { Separator } from "@/components/ui/separator"
import { AccountForm } from "@/components/account/account-form"
import { AddressForm } from "@/components/account/address-form"
import { PasswordForm } from "@/components/account/password-form"
import { prisma } from "@/lib/prisma"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

export const metadata = {
  title: "Account",
  description: "Manage your account settings",
}

async function getAccountData() {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      redirect("/auth/signin")
    }

    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
      include: {
        addresses: true,
      },
    })

    if (!user) {
      redirect("/auth/signin")
    }

    return user
  } catch (error) {
    console.error("Error in getAccountData:", error)
    redirect("/auth/signin")
  }
}

export default async function AccountPage() {
  const user = await getAccountData()

  return (
    <div className="container py-8">
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Account</h3>
          <p className="text-sm text-muted-foreground">
            Manage your account settings and set your preferred addresses.
          </p>
        </div>
        <Separator />
        <div className="grid gap-10">
          <AccountForm
            user={{
              id: user.id,
              name: user.name,
              email: user.email,
              image: user.image,
            }}
          />

          {user.hashedPassword && (
            <>
              <Separator />
              <PasswordForm />
            </>
          )}

          <Separator />
          <AddressForm
            userId={user.id}
            defaultAddresses={user.addresses}
          />
        </div>
      </div>
    </div>
  )
}