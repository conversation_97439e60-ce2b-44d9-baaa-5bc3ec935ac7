import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// Force dynamic rendering
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const productCode = searchParams.get('code')

    if (!productCode) {
      return NextResponse.json(
        { error: 'Product code is required' },
        { status: 400 }
      )
    }

    const product = await prisma.product.findFirst({
      where: {
        OR: [
          { sku: productCode },
          {
            specifications: {
              path: ['odooProductCode'],
              equals: productCode
            }
          }
        ]
      },
      select: {
        id: true,
        name: true,
      }
    })

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error("Error finding product by code:", error)
    return NextResponse.json(
      { error: 'Failed to find product' },
      { status: 500 }
    )
  }
}
