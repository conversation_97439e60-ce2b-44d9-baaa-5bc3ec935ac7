{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "target": "es2022", "strict": false, "noImplicitAny": false, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": false, "forceConsistentCasingInFileNames": true, "noUncheckedIndexedAccess": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "scripts/create-admin.js"], "exclude": ["node_modules"]}