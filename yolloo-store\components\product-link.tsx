"use client"

import { useEffect, useState } from "react"
import Link from "next/link"

interface ProductLinkProps {
  productCode: string | null
  children: React.ReactNode
}

export function ProductLink({ productCode, children }: ProductLinkProps) {
  const [productId, setProductId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!productCode) {
      setIsLoading(false)
      return
    }

    const fetchProductId = async () => {
      try {
        const response = await fetch(`/api/products/by-code?code=${encodeURIComponent(productCode)}`)
        if (response.ok) {
          const data = await response.json()
          setProductId(data.id)
        }
      } catch (error) {
        console.error("Error fetching product ID:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchProductId()
  }, [productCode])

  if (!productCode || isLoading || !productId) {
    return <span className="font-medium">{children}</span>
  }

  return (
    <Link 
      href={`/products/${productId}`} 
      target="_blank" 
      className="font-medium hover:text-primary hover:underline"
    >
      {children}
    </Link>
  )
}
