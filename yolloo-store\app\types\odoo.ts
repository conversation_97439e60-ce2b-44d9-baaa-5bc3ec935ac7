// Odoo商品相关类型
export interface OdooProduct {
    name: string;
    product_code: string;
    product_type: string;
    description_sale: string;
    website_description: string;
    product_attributes: OdooProductAttribute[];
    variants: OdooProductVariant[];
    product_parameters: OdooProductParameter[];
    off_shelve: boolean;
    photos_link: string;
    requiredUID: boolean;
    mcc?: string;
    accessory_data_product: any[];
}

export interface OdooProductAttribute {
    code: string;
    name: string;
    values: string[];
}

export interface OdooProductVariant {
    variant_code: string;
    variant_name: string;
    variant_attributes: OdooVariantAttribute[];
    supply_price: number;
    currency: string;
}

export interface OdooVariantAttribute {
    code: string;
    name: string;
    value: string;
}

export interface OdooProductParameter {
    code: string;
    name: string;
    value: string;
}

// 产品价格相关类型
export interface OdooProductPrice {
    product_code: string;
    product_name: string;
    price: number;
    currency: string;
    effective_date: string;
    expiry_date?: string;
}

// Odoo订单相关类型
export interface OdooOrder {
    customer_order_ref: string;
    shipping_address: OdooShippingAddress;
    payment_method: string;
    email: string;
    note?: string;
    order_lines: OdooOrderLine[];
}

export interface OdooOrderLine {
    customer_order_line_ref: string;
    product_code: string;
    card_uid?: string;
    product_uom_qty: number;
    lpa_string?: string;
}

export interface OdooAddress {
    name: string;
    street: string;
    city: string;
    state: string;
    country: string;
    zip: string;
    phone: string;
}

// Odoo API响应类型
export interface OdooResponse<T> {
    status: string;
    result: {
        status: string;
        message: string;
        data: T;
    }
}

// QR码响应类型
export interface OdooQRCodeResponse {
    customer_order_ref: string;
    qrcode_url: string;
    qrcode: Array<{
        uid: string;
        qrCodeContent: string;
    }>;
}

// 订单状态响应
export interface OdooOrderStatus {
    order_sn: string;
    status: string;
    iccid?: string;
    activation_code?: string;
    qr_code?: string;
    manual_url?: string;
}

// 结算单相关类型
export interface OdooInvoiceOrder {
    invoice_no: string;
    invoice_date: string;
    customer_order_ref: string;
    product_code: string;
    product_name: string;
    quantity: number;
    unit_price: number;
    currency: string;
    total_amount: number;
    payment_method: string;
    payment_status: string;
}

export interface OdooInvoiceSummary {
    total_orders: number;
    total_amount: number;
    currency: string;
}

export interface OdooInvoiceResponse {
    orders: OdooInvoiceOrder[];
    summary?: OdooInvoiceSummary;
}

export interface OdooShippingAddress {
    name: string;
    country: string;
    city: string;
    zip: string;
    address: string;
    address2?: string;
    phone: string;
}

export interface OdooOrderResponse {
    status: string;
    result: {
        status: string;
        message: string;
        data: {
            order_name: string;
            customer_order_ref: string;
            amount_untaxed: number;
            amount_tax: number;
            amount_total: number;
            currency: string;
        };
    };
}

export interface OdooOrderLineData {
  uid: string;
  plan_state: string;
}

export interface OdooOrderLineStatus {
  customer_order_line_ref: string;
  product_code: string;
  product_name: string;
  is_digital: boolean;
  status: string;
  description: string;
  delivered_qty: number;
  tracking_number: string;
  data: OdooOrderLineData[];
}

export interface OdooOrderStatusDetail {
  customer_order_ref: string;
  order_lines: OdooOrderLineStatus[];
}

export interface OdooOrderStatusResponse {
  status: string;
  result: {
    status: string;
    message: string;
    data: OdooOrderStatusDetail[];
  };
} 