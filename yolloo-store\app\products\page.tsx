"use client"

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useSearchPara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Input } from "@/components/ui/input"
import { Icons } from '@/components/icons'
import { cn } from '@/lib/utils'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination"
import { FormattedDescription } from "@/components/formatted-description"
import { Badge } from "@/components/ui/badge"
import { X, Search as SearchIcon } from "lucide-react"
import { parseProductBadge, formatProductName } from "@/lib/utils";
import { useDebounce } from '@/hooks/useDebounce'

const ITEMS_PER_PAGE = 12
const STORAGE_KEY = 'products_page_state'

interface Product {
  id: string
  name: string
  description: string
  websiteDescription: string
  price: number
  off_shelve: boolean
  parameters: {
    code: string
    name: string
    value: string
  }[]
  category: {
    name: string
  }
  country?: string | null
  countryCode?: string | null
  dataSize?: number | null
  planType?: string | null
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

interface ProductListResponse {
  products: Product[]
  pagination: PaginationInfo
}

interface Country {
  name: string
  count: number
}

export default function ProductsPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [products, setProducts] = useState<Product[]>([])
  const [pagination, setPagination] = useState<PaginationInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [countries, setCountries] = useState<Country[]>([])
  const [error, setError] = useState<string | null>(null)

  // Initialize state from URL parameters or localStorage
  const [currentPage, setCurrentPage] = useState(1)
  const [regionType, setRegionType] = useState("all") // "all", "single", "multi"
  const [countryFilter, setCountryFilter] = useState("") // Add country filter state
  const [sortBy, setSortBy] = useState("default")
  const [searchQuery, setSearchQuery] = useState("") // Add search query state
  const [searchInput, setSearchInput] = useState("") // For input field (before debounce)

  // 防抖搜索
  const debouncedSearchQuery = useDebounce(searchInput, 500)

  // 添加搜索状态
  const [countrySearchTerm, setCountrySearchTerm] = useState("")
  const [countryCodeSearchTerm, setCountryCodeSearchTerm] = useState("")

  // Load state from URL params or localStorage on initial render
  useEffect(() => {
    // First try to get from URL params
    const urlRegionType = searchParams.get("regionType") || "all"
    const urlCountry = searchParams.get("country") || "" // Get country from URL
    const urlSort = searchParams.get("sort") || "default"
    const urlPage = parseInt(searchParams.get("page") || "1", 10)
    const urlSearch = searchParams.get("search") || "" // Get search query from URL

    // Then check if we have saved state in localStorage
    try {
      const savedState = localStorage.getItem(STORAGE_KEY)
      if (savedState) {
        const parsedState = JSON.parse(savedState)

        // Use URL params if they exist, otherwise use localStorage values
        setRegionType(urlRegionType || parsedState.regionType || "all")
        setCountryFilter(urlCountry || parsedState.countryFilter || "") // Set country filter
        setSortBy(urlSort || parsedState.sortBy || "default")
        setCurrentPage(urlPage || parsedState.currentPage || 1)
        setSearchQuery(urlSearch || parsedState.searchQuery || "")
        setSearchInput(urlSearch || parsedState.searchQuery || "")
      } else {
        // If no localStorage data, just use URL params
        setRegionType(urlRegionType)
        setCountryFilter(urlCountry) // Set country filter
        setSortBy(urlSort)
        setCurrentPage(urlPage)
        setSearchQuery(urlSearch)
        setSearchInput(urlSearch)
      }
    } catch (error) {
      console.error('Error loading state from localStorage:', error)
      // Fallback to URL params
      setRegionType(urlRegionType)
      setCountryFilter(urlCountry) // Set country filter
      setSortBy(urlSort)
      setCurrentPage(urlPage)
      setSearchQuery(urlSearch)
      setSearchInput(urlSearch)
    }
  }, [searchParams])

  // Save state to localStorage whenever it changes
  useEffect(() => {
    if (loading) return // Don't save during initial loading

    try {
      const stateToSave = {
        regionType,
        countryFilter, // Save country filter
        sortBy,
        currentPage,
        searchQuery
      }
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave))
    } catch (error) {
      console.error('Error saving state to localStorage:', error)
    }
  }, [regionType, countryFilter, sortBy, currentPage, searchQuery, loading])

  // Function to update URL parameters
  const updateUrlParams = useCallback((params: {
    regionType?: string,
    country?: string,
    sort?: string,
    page?: number,
    search?: string
  }) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()))

    // Update parameters
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        current.set(key, value.toString())
      } else {
        current.delete(key)
      }
    })

    // Create new URL
    const search = current.toString()
    const query = search ? `?${search}` : ""

    // Update URL without refreshing the page
    router.push(`/products${query}`, { scroll: false })
  }, [searchParams, router])

  // Determine if a product is a single-region or multi-region package
  const isMultiRegionPackage = (product: Product) => {
    // Check if country or countryCode contains multiple countries (separated by semicolons)
    if (product.country) {
      const countries = product.country.split(/[,;]/).map(c => c.trim()).filter(Boolean)
      if (countries.length > 1) return true
    }

    if (product.countryCode) {
      const countryCodes = product.countryCode.split(/[,;]/).map(c => c.trim()).filter(Boolean)
      if (countryCodes.length > 1) return true
    }

    return false
  }

  // 使用服务端分页，不需要客户端过滤和分页
  const totalPages = pagination?.totalPages || 0
  const paginatedProducts = products // 直接使用从API获取的产品

  // 获取产品数据
  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: ITEMS_PER_PAGE.toString(),
      })

      if (searchQuery) params.append('search', searchQuery)
      if (regionType !== 'all') params.append('regionType', regionType)
      if (countryFilter) params.append('country', countryFilter)
      if (sortBy !== 'default') params.append('sort', sortBy)

      const response = await fetch(`/api/products/paginated?${params.toString()}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: ProductListResponse = await response.json()
      setProducts(data.products)
      setPagination(data.pagination)

    } catch (error) {
      console.error('Error fetching products:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch products')
      setProducts([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [currentPage, searchQuery, regionType, countryFilter, sortBy])

  // 获取国家列表
  const fetchCountries = useCallback(async () => {
    try {
      const response = await fetch('/api/products/countries')
      if (response.ok) {
        const data = await response.json()
        setCountries(data.countries)
      }
    } catch (error) {
      console.error('Error fetching countries:', error)
    }
  }, [])

  // 当URL参数变化时获取数据
  useEffect(() => {
    fetchProducts()
  }, [fetchProducts])

  // 初始化时获取国家列表
  useEffect(() => {
    fetchCountries()
  }, [fetchCountries])

  // 当搜索输入变化时更新URL
  useEffect(() => {
    if (debouncedSearchQuery !== searchQuery) {
      setSearchQuery(debouncedSearchQuery)
    }
  }, [debouncedSearchQuery, searchQuery])

  // 初始化搜索输入
  useEffect(() => {
    setSearchInput(searchQuery)
  }, [searchQuery])

  // Update URL when filters change
  useEffect(() => {
    if (loading) return // Don't update URL during initial loading

    updateUrlParams({
      regionType: regionType === "all" ? undefined : regionType,
      country: countryFilter || undefined, // Add country to URL params
      sort: sortBy === "default" ? undefined : sortBy,
      page: currentPage > 1 ? currentPage : undefined,
      search: searchQuery || undefined // Add search query to URL params
    })
  }, [regionType, countryFilter, sortBy, currentPage, searchQuery, updateUrlParams, loading])

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [regionType, countryFilter, sortBy, searchQuery])

  // 由于使用服务端分页，这些计数函数不再需要，但为了保持UI兼容性，返回空值
  const getRegionTypeCount = (type: string) => {
    return '' // 不显示计数，因为是服务端分页
  }

  // 从countries状态获取国家计数
  const getCountryCount = (country: string) => {
    const countryData = countries.find(c => c.name === country)
    return countryData ? countryData.count : 0
  }

  // 从countries状态获取唯一国家列表
  const getUniqueCountries = () => {
    return countries.map(c => c.name)
  };

  // Format data size to display in MB or GB
  const formatDataSize = (size: number | null | undefined): { value: string, unit: string } => {
    if (!size) return { value: "0", unit: "MB" };

    if (size >= 1024) {
      const gbValue = size / 1024;
      // Check if the value is a whole number
      return {
        value: Number.isInteger(gbValue) ? gbValue.toString() : gbValue.toFixed(2).replace(/\.00$/, ''),
        unit: "GB"
      };
    } else {
      // Check if the MB value is a whole number
      return {
        value: Number.isInteger(size) ? size.toString() : size.toFixed(2).replace(/\.00$/, ''),
        unit: "MB"
      };
    }
  };

  // Format data value without decimal places when possible
  const formatDataValue = (value: number): string => {
    if (Number.isInteger(value)) {
      return value.toString();
    } else {
      return value.toFixed(2).replace(/\.00$/, '');
    }
  };

  // 获取产品的 Plan Type (从参数或直接字段)
  const getProductPlanType = (product: Product): string | null => {
    // 如果产品直接有 planType 字段，优先使用
    if (product.planType) {
      return product.planType;
    }

    // 否则从参数中查找
    const planTypeParam = product.parameters.find(
      param => param.code.toLowerCase().includes('plan_type') ||
               param.name.toLowerCase().includes('plan type')
    );

    if (planTypeParam) {
      const value = planTypeParam.value.toLowerCase();
      if (value === 'daily' || value.includes('day')) {
        return 'Daily';
      } else if (value === 'total' || value.includes('total')) {
        return 'Total';
      }
      return planTypeParam.value;
    }

    return null;
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <Icons.spinner className="h-8 w-8 animate-spin" />
          <p className="mt-4 text-muted-foreground">Loading products...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-12 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center text-center">
          <div className="text-red-500 mb-4">
            <Icons.alertTriangle className="h-12 w-12" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Error Loading Products</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => fetchProducts()}>
            <Icons.refresh className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold mb-8">Our Packages</h1>

      {/* Search Bar */}
      <div className="relative mb-6">
        <div className="relative">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search by name, description, country, SKU or product code..."
            className="pl-10 pr-10"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
          />
          {searchInput && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-7 w-7"
              onClick={() => setSearchInput("")}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        <Select value={regionType} onValueChange={setRegionType}>
          <SelectTrigger>
            <SelectValue placeholder="Region Type" />
          </SelectTrigger>
          <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
            <SelectItem value="all">All Region Types</SelectItem>
            <SelectItem value="single">Local</SelectItem>
            <SelectItem value="multi">Regional</SelectItem>
          </SelectContent>
        </Select>

        <Select value={countryFilter || "all"} onValueChange={value => setCountryFilter(value === "all" ? "" : value)}>
          <SelectTrigger>
            <SelectValue placeholder="Country" />
          </SelectTrigger>
          <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
            <SelectItem value="all">All Destinations</SelectItem>
            {getUniqueCountries().map(country => (
              <SelectItem key={country} value={country}>
                {country} ({getCountryCount(country)})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger>
            <SelectValue placeholder="Sort By">
              {sortBy === "default" ? "Sort By" :
                sortBy === "price-asc" ? "Price: Low to High" :
                sortBy === "price-desc" ? "Price: High to Low" :
                sortBy === "name-asc" ? "Name: A to Z" :
                "Name: Z to A"
              }
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
            <SelectItem value="default">Sort By</SelectItem>
            <SelectItem value="price-asc">Price: Low to High</SelectItem>
            <SelectItem value="price-desc">Price: High to Low</SelectItem>
            <SelectItem value="name-asc">Name: A to Z</SelectItem>
            <SelectItem value="name-desc">Name: Z to A</SelectItem>
          </SelectContent>
        </Select>

        <Button
          variant="outline"
          onClick={() => {
            setRegionType("all")
            setCountryFilter("")
            setSortBy("default")
            setCurrentPage(1)
            setSearchInput("")
            setSearchQuery("")
          }}
        >
          <Icons.refresh className="mr-2 h-4 w-4" />
          Reset Filters
        </Button>
      </div>

      {/* Active Filters */}
      {(regionType !== "all" || countryFilter || sortBy !== "default" || searchQuery) && (
        <div className="flex flex-wrap gap-2 mb-6">
          <div className="text-sm text-muted-foreground mr-2 py-1">Active Filters:</div>
          {searchQuery && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: {searchQuery.length > 20 ? `${searchQuery.substring(0, 20)}...` : searchQuery}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => {
                  setSearchInput("")
                  setSearchQuery("")
                }}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {regionType !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Region Type: {regionType === "single" ? "Local" : "Regional"}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => setRegionType("all")}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {countryFilter && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Destination: {countryFilter}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => setCountryFilter("")}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {sortBy !== "default" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Sort: {sortBy.replace("-", " ").replace(/(^\w|\s\w)/g, c => c.toUpperCase())}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => setSortBy("default")}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}

      {/* Results count */}
      {pagination && (
        <div className="flex justify-between items-center mb-6 text-sm text-muted-foreground">
          <div>
            Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} products
          </div>
          <div>
            Page {pagination.page} of {pagination.totalPages}
          </div>
        </div>
      )}

      {paginatedProducts.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-12 text-center bg-white shadow-md rounded-lg">
          <Icons.package className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Products Found</h3>
          <p className="text-muted-foreground mb-4">Try adjusting your filters to find more products.</p>
          <Button
            onClick={() => {
              setRegionType("all")
              setCountryFilter("")
              setSortBy("default")
              setCurrentPage(1)
              setSearchInput("")
              setSearchQuery("")
            }}
          >
            Clear Filters
          </Button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            {paginatedProducts.map((product) => {
              const { badge, name } = parseProductBadge(product.name);
              return (
                <div
                  key={product.id}
                  className={cn(
                    "bg-white shadow-md rounded-lg p-6 relative transition-all hover:shadow-lg",
                    product.off_shelve && "opacity-60"
                  )}
                >
                  {product.off_shelve && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-lg">
                      <span className="bg-black/70 text-white px-4 py-2 rounded-full font-medium">
                        Off Shelf
                      </span>
                    </div>
                  )}
                  <div className="flex flex-col h-full">
                    <Link href={`/products/${product.id}`} className="block group">
                      <h2 className="text-xl font-semibold mb-2 flex items-center gap-2 truncate group-hover:text-primary transition-colors">
                        {badge && (
                          <span className="inline-block rounded-full bg-gradient-to-r from-primary to-primary/70 text-white font-bold px-3 py-1 text-xs shadow-sm">
                            {badge}
                          </span>
                        )}
                        {formatProductName(name)}
                      </h2>
                      <div className="mb-4 flex-grow">
                        <FormattedDescription
                          text={product.websiteDescription}
                          className="line-clamp-6"
                          plain
                        />
                      </div>
                    </Link>
                    <div className="space-y-2">
                      {/* Package Type - 固定显示 */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Package Type:</span>
                        <Badge variant="outline" className="text-xs">
                          {isMultiRegionPackage(product) ? 'Regional' : 'Local'}
                        </Badge>
                      </div>

                      {/* Data Size - 固定显示 */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Data Size:</span>
                        {product.dataSize ? (
                          <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-400 dark:border-blue-800">
                            {formatDataSize(product.dataSize).value} {formatDataSize(product.dataSize).unit}
                            {getProductPlanType(product) && ` (${getProductPlanType(product)})`}
                          </Badge>
                        ) : (
                          <span className="text-sm text-gray-400">-</span>
                        )}
                      </div>

                      {/* Coverage - 固定显示 */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Coverage:</span>
                        {product.country ? (
                          <TooltipProvider delayDuration={200}>
                            <Tooltip onOpenChange={(open) => {
                              if (!open) setCountrySearchTerm("");
                            }}>
                              <TooltipTrigger asChild>
                                <div className="flex flex-wrap justify-end gap-1 max-w-[180px]">
                                  {(() => {
                                    const countries = product.country.split(/[,;]/).map(c => c.trim()).filter(Boolean);
                                    if (countries.length <= 3) {
                                      return countries.map((country, index) => (
                                        <Badge key={index} variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800">
                                          {country}
                                        </Badge>
                                      ));
                                    } else {
                                      return (
                                        <>
                                          {countries.slice(0, 2).map((country, index) => (
                                            <Badge key={index} variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800">
                                              {country}
                                            </Badge>
                                          ))}
                                          <Badge variant="outline" className="text-xs bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700">
                                            +{countries.length - 2}
                                          </Badge>
                                        </>
                                      );
                                    }
                                  })()}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent side="top" className="max-w-[300px] p-2">
                                {(() => {
                                  const countries = product.country.split(/[,;]/).map(c => c.trim()).filter(Boolean);
                                  const filteredCountries = countries.filter(country =>
                                    country.toLowerCase().includes(countrySearchTerm.toLowerCase())
                                  );

                                  return (
                                    <>
                                      <div className="flex justify-between items-center mb-2 pb-1 border-b">
                                        <span className="font-medium"></span>
                                        <Badge variant="outline" className="text-xs">{countries.length}</Badge>
                                      </div>
                                      {countries.length > 10 && (
                                        <div className="mb-2">
                                          <Input
                                            type="text"
                                            placeholder="Search countries..."
                                            className="h-7 text-xs"
                                            value={countrySearchTerm}
                                            onChange={(e) => setCountrySearchTerm(e.target.value)}
                                          />
                                        </div>
                                      )}
                                      <div className="flex flex-wrap gap-1 max-h-[200px] overflow-y-auto pr-1 visible-scrollbar">
                                        {filteredCountries.length > 0 ? (
                                          filteredCountries.map((country, i) => (
                                            <Badge key={i} variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800">
                                              {country}
                                            </Badge>
                                          ))
                                        ) : (
                                          <div className="text-xs text-muted-foreground py-1">No countries found</div>
                                        )}
                                      </div>
                                    </>
                                  );
                                })()}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ) : (
                          <span className="text-sm text-gray-400">-</span>
                        )}
                      </div>

                      <div className="flex items-center justify-between pt-2">
                        <span className="text-lg font-bold">${product.price.toFixed(2)}</span>
                        <Button asChild>
                          <Link href={`/products/${product.id}`}>View Details</Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="w-full flex justify-center items-center my-8">
              <Pagination className="justify-center">
                <PaginationContent className="justify-center">
                  <PaginationItem>
                    <PaginationPrevious
                      href="#"
                      onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                      aria-disabled={!pagination?.hasPrev}
                    />
                  </PaginationItem>

                  {/* First page */}
                  {totalPages > 5 && currentPage > 3 && (
                    <PaginationItem>
                      <PaginationLink
                        href="#"
                        onClick={() => setCurrentPage(1)}
                      >
                        1
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Ellipsis at the beginning */}
                  {totalPages > 5 && currentPage > 3 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  {/* Page numbers */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter(page => {
                      if (totalPages <= 5) {
                        // Show all pages if 5 or fewer
                        return true;
                      } else {
                        // Show pages around current page
                        if (currentPage <= 3) {
                          // Near the beginning
                          return page <= 5;
                        } else if (currentPage >= totalPages - 2) {
                          // Near the end
                          return page > totalPages - 5;
                        } else {
                          // In the middle
                          return Math.abs(page - currentPage) <= 1;
                        }
                      }
                    })
                    .map((page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          href="#"
                          onClick={() => setCurrentPage(page)}
                          isActive={currentPage === page}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    ))}

                  {/* Ellipsis at the end */}
                  {totalPages > 5 && currentPage < totalPages - 2 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  {/* Last page */}
                  {totalPages > 5 && currentPage < totalPages - 2 && (
                    <PaginationItem>
                      <PaginationLink
                        href="#"
                        onClick={() => setCurrentPage(totalPages)}
                      >
                        {totalPages}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      href="#"
                      onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                      aria-disabled={!pagination?.hasNext}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </>
      )}
    </div>
  )
}

