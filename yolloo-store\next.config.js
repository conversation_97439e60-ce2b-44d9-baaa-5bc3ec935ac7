/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'lh3.googleusercontent.com',
      'avatars.githubusercontent.com',
      'cloud.simmesh.com',
      'nextcloud.nextcloud',
    ],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'cloud.simmesh.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'nextcloud.nextcloud',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // 移除 env 配置，让应用在运行时读取环境变量

  // 设置输出为独立构建，更好地处理服务器组件和中间件
  output: 'standalone',

  // 配置外部包
  experimental: {
    serverComponentsExternalPackages: ['bcrypt'],
  },

  // 配置路由处理方式
  async rewrites() {
    return [];
  },

  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Warning: This allows production builds to successfully complete even if
    // your project has type errors.
    ignoreBuildErrors: true,
  },

  // 添加这个配置来确保所有API路由都是动态的
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
          {
            key: 'X-Next-Dynamic-Rendering',
            value: 'true',
          },
        ],
      },
    ];
  },

  // 禁用严格模式
  reactStrictMode: false,
}

module.exports = nextConfig