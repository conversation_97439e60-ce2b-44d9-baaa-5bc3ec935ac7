"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Loader2, ChevronLeft } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

// Form schema
const formSchema = z.object({
  name: z.string().min(3, { message: "Organization name must be at least 3 characters" }),
  description: z.string().optional(),
  logo: z.string().optional(),
  commissionRate: z.coerce.number()
    .min(1, { message: "Commission rate must be at least 1%" })
    .max(70, { message: "Commission rate cannot exceed 70%" }),
  discountRate: z.coerce.number()
    .min(0, { message: "Discount rate must be at least 0%" })
    .max(50, { message: "Discount rate cannot exceed 50%" }),
});

type FormValues = z.infer<typeof formSchema>;

export default function EditOrganizationPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      logo: "",
      commissionRate: 10,
      discountRate: 5,
    },
  });

  // Fetch organization data
  useEffect(() => {
    const fetchOrganizationData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/affiliate/organizations/${params.id}`);
        
        // If user is not an admin, redirect back
        if (!response.data.isAdmin) {
          toast.error("You do not have permission to edit this organization");
          router.push(`/affiliate/organization/${params.id}`);
          return;
        }

        const org = response.data.organization;
        form.reset({
          name: org.name,
          description: org.description || "",
          logo: org.logo || "",
          commissionRate: org.commissionRate * 100,
          discountRate: org.discountRate * 100,
        });
      } catch (error) {
        console.error("Error fetching organization data:", error);
        toast.error("Failed to load organization data");
        router.push(`/affiliate/organization/${params.id}`);
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizationData();
  }, [params.id, router, form]);

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    try {
      setSubmitting(true);
      
      // Convert percentage values to decimal before sending to API
      const submissionData = {
        ...data,
        commissionRate: data.commissionRate / 100,
        discountRate: data.discountRate / 100
      };
      
      await axios.patch(`/api/affiliate/organizations/${params.id}`, submissionData);
      toast.success("Organization updated successfully");
      router.push(`/affiliate/organization/${params.id}`);
    } catch (error) {
      console.error("Error updating organization:", error);
      toast.error("Failed to update organization");
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/affiliate/organization/${params.id}`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Organization
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Edit Organization</CardTitle>
          <CardDescription>
            Update your organization's information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter organization name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your organization"
                        {...field}
                        rows={4}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide a brief description of your organization.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="logo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Logo URL</FormLabel>
                    <FormControl>
                      <Input placeholder="https://example.com/logo.png" {...field} />
                    </FormControl>
                    <FormDescription>
                      Enter the URL for your organization's logo.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="commissionRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Commission Rate (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.1"
                          min="1"
                          max="70"
                          placeholder="10"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        The maximum percentage commission your organization earns. Members with their own commission rates will receive their rate from this amount, and your organization keeps the difference.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="discountRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Discount Rate (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.1"
                          min="0"
                          max="50"
                          placeholder="5"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        The discount rate customers will receive when using your referral code (0-50%).
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
} 