interface SectionBackgroundProps {
  children: React.ReactNode
  className?: string
  withGradient?: boolean
  withGrid?: boolean
  withTopGradient?: boolean
  isDark?: boolean
  isWhite?: boolean
}

export function SectionBackground({ 
  children, 
  className = "", 
  withGradient = true,
  withGrid = true,
  withTopGradient = false,
  isDark = false,
  isWhite = true
}: SectionBackgroundProps) {
  return (
    <section className={`relative py-32 overflow-hidden ${
      isDark ? 'bg-[#1a1818]' : isWhite ? 'bg-white' : 'bg-[#f7f9fc]'
    } ${className}`}>
      {/* Background decoration */}
      <div className="absolute inset-0 -z-10">
        {/* Main gradient background */}
        {withGradient && (
          <div className={`absolute inset-0 bg-gradient-to-b ${
            isDark 
              ? 'from-[#F799A6]/30 via-transparent to-transparent'
              : 'from-[#F799A6]/15 via-transparent to-[#B82E4E]/15'
          }`} />
        )}
        
        {/* Top gradient transition */}
        {withTopGradient && (
          <div className={`absolute top-0 inset-x-0 h-32 bg-gradient-to-b ${
            isDark 
              ? 'from-[#151313] to-[#1a1818]'
              : isWhite 
                ? 'from-white to-white'
                : 'from-white to-[#f7f9fc]'
          }`} />
        )}
        
        {/* Enhanced glow effects */}
        <div className={`absolute -top-[30%] -left-[10%] h-[60%] w-[60%] 
        bg-[radial-gradient(circle,${isDark ? 'rgba(247,153,166,0.4)' : 'rgba(247,153,166,0.3)'},transparent_70%)] blur-3xl`} />
        
        <div className={`absolute -bottom-[20%] -right-[10%] h-[60%] w-[60%] 
        bg-[radial-gradient(circle,${isDark ? 'rgba(184,46,78,0.35)' : 'rgba(184,46,78,0.25)'},transparent_70%)] blur-3xl`} />
        
        <div className={`absolute top-[40%] left-[50%] -translate-x-1/2 -translate-y-1/2 h-[40%] w-[80%] 
        bg-[radial-gradient(ellipse,${isDark ? 'rgba(247,153,166,0.25)' : 'rgba(247,153,166,0.25)'},transparent_70%)] blur-3xl`} />
        
        {/* Enhanced decorative effects */}
        <div className={`absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,${
          isDark ? 'rgba(247,153,166,0.25)' : 'rgba(247,153,166,0.2)'
        },transparent_50%)]`} />
        <div className={`absolute inset-0 bg-[radial-gradient(circle_at_top_right,${
          isDark ? 'rgba(184,46,78,0.25)' : 'rgba(184,46,78,0.2)'
        },transparent_50%)]`} />
        
        {/* Grid effect */}
        {withGrid && (
          <div className={`absolute inset-0 ${
            isDark ? 'bg-grid-white/[0.03]' : 'bg-grid-[#B82E4E]/[0.05]'
          }`} />
        )}

        {/* Enhanced light streaks */}
        <div className="absolute inset-0">
          <div className={`absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent ${
            isDark ? 'via-[#F799A6]/20' : 'via-[#F799A6]/30'
          } to-transparent`} />
          <div className={`absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent ${
            isDark ? 'via-[#B82E4E]/20' : 'via-[#B82E4E]/30'
          } to-transparent`} />
        </div>

        {/* Contrast enhancement layer */}
        {!isDark && (
          <div className="absolute inset-0 bg-white/40 backdrop-blur-[1px]" />
        )}
      </div>

      {children}
    </section>
  )
} 