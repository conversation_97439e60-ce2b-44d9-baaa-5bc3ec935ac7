import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for verifying an invite
const verifyInviteSchema = z.object({
  inviteCode: z.string(),
});

// GET - Verify an invite code
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const inviteCode = searchParams.get("code");
    
    if (!inviteCode) {
      return NextResponse.json({ error: "Invite code is required" }, { status: 400 });
    }
    
    // Find the invite
    const invite = await prisma.organizationInvite.findUnique({
      where: { inviteCode },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            code: true,
            logo: true
          }
        },
      },
    });
    
    if (!invite) {
      return NextResponse.json({ 
        valid: false,
        error: "Invite not found" 
      }, { status: 404 });
    }
    
    // Check if invite is still valid
    if (invite.status !== "PENDING") {
      return NextResponse.json({
        valid: false,
        error: `Invite is ${invite.status.toLowerCase()}`
      }, { status: 400 });
    }
    
    // Check if invite has expired
    if (invite.expiresAt && invite.expiresAt < new Date()) {
      // Update invite status to EXPIRED
      await prisma.organizationInvite.update({
        where: { id: invite.id },
        data: { status: "EXPIRED" },
      });
      
      return NextResponse.json({
        valid: false,
        error: "Invite has expired"
      }, { status: 400 });
    }
    
    // For general invites (without email), just return isGeneralInvite=true
    const isGeneralInvite = !invite.email;
    
    // Check if user with this email already exists (only if specific email invite)
    const userExists = !isGeneralInvite && invite.email ? 
      await prisma.user.findUnique({ where: { email: invite.email }}) !== null : 
      false;
    
    return NextResponse.json({
      valid: true,
      invite: {
        code: invite.inviteCode,
        email: invite.email || null,
        organization: invite.organization,
        expires: invite.expiresAt,
        isGeneralInvite: isGeneralInvite
      },
      userExists
    });
  } catch (error) {
    console.error("Error verifying invite:", error);
    return NextResponse.json(
      { 
        valid: false,
        error: "Failed to verify invite" 
      },
      { status: 500 }
    );
  }
} 