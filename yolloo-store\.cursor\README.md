# Yolloo Store Cursor规则

这个目录包含了为Yolloo Store项目定制的Cursor规则集。这些规则旨在帮助维护代码质量、一致性，并遵循Next.js、React和TypeScript的最佳实践。

## 规则说明

### 导入与依赖
- **未使用的导入检测**: 检测代码中未被使用的导入语句
- **Next.js API导入错误**: 防止错误地导入Next.js API路由

### React和Next.js最佳实践
- **Image组件alt属性**: 确保所有Image组件都有alt属性，提高可访问性
- **避免直接访问document**: 防止在Next.js组件中直接访问document对象，避免SSR问题
- **使用client指令**: 确保使用React钩子的组件文件有'use client'指令
- **组件命名规范**: 确保React组件使用PascalCase命名规范

### TypeScript类型安全
- **避免使用any类型**: 鼓励使用更具体的类型，而不是any
- **明确函数返回类型**: 建议为函数添加明确的返回类型定义

### 错误处理和日志
- **异步函数错误处理**: 确保异步函数使用try-catch块处理潜在错误
- **Prisma查询错误处理**: 确保所有Prisma数据库查询都有适当的错误处理
- **避免使用console.log**: 避免在生产代码中使用console.log

### 样式和UI
- **避免内联样式**: 鼓励使用Tailwind类而不是内联样式
- **Tailwind类名使用**: 确保React组件使用className属性应用Tailwind样式

### 表单和用户输入
- **表单验证**: 建议为表单添加验证，如使用react-hook-form和zod

## 如何使用

1. Cursor会自动加载这些规则并在您编辑代码时实时检查
2. 规则检查结果会在编辑器中以波浪线标记显示
3. 鼠标悬停在标记上可查看详细的规则违反信息和建议修复方法

## 自定义规则

如果您想修改或添加新规则，请编辑`.cursor/rules.json`文件。每条规则包含以下属性：

- `id`: 规则的唯一标识符
- `pattern`: 用于匹配代码的正则表达式
- `description`: 规则的简短描述
- `severity`: 规则违反的严重程度（"error", "warning", "information"）
- `message`: 显示给用户的消息
- `filePattern`（可选）: 限制规则仅适用于特定文件类型
- `filterPattern`和`matchesFilter`（可选）: 用于细化匹配条件

## 禁用规则

如果您想在特定文件或代码块中禁用规则，可以添加以下注释：

```typescript
// cursor-disable-next-line: rule-id
const problematicCode = 'something';

// cursor-disable
// 这段代码会禁用所有规则检查
const moreProblematicCode = 'something else';
// cursor-enable
``` 