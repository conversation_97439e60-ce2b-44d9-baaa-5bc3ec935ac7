import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// POST /api/admin/products/bulk-price-update - 批量调价
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await req.json()
    const { 
      categoryIds, 
      adjustmentType, // 'increase' | 'decrease'
      adjustmentPercentage, // 数字，如 30 表示30%
      productIds // 可选，如果提供则只调整指定产品
    } = body

    // 验证输入
    if (!adjustmentType || !adjustmentPercentage) {
      return new NextResponse("Missing required fields", { status: 400 })
    }

    if (!['increase', 'decrease'].includes(adjustmentType)) {
      return new NextResponse("Invalid adjustment type", { status: 400 })
    }

    if (adjustmentPercentage <= 0 || adjustmentPercentage > 100) {
      return new NextResponse("Adjustment percentage must be between 1 and 100", { status: 400 })
    }

    // 构建查询条件
    const whereCondition: any = {
      status: 'ACTIVE', // 只调整活跃产品的价格
    }

    // 如果指定了产品ID，优先使用产品ID
    if (productIds && productIds.length > 0) {
      whereCondition.id = {
        in: productIds
      }
    } 
    // 否则使用分类筛选
    else if (categoryIds && categoryIds.length > 0) {
      whereCondition.categoryId = {
        in: categoryIds
      }
    } else {
      return new NextResponse("Must specify either categoryIds or productIds", { status: 400 })
    }

    // 获取需要调价的产品
    const products = await prisma.product.findMany({
      where: whereCondition,
      include: {
        category: true,
        variants: true
      }
    })

    if (products.length === 0) {
      return NextResponse.json({
        success: true,
        message: "No products found matching the criteria",
        updatedCount: 0,
        products: []
      })
    }

    // 计算调价比例
    const multiplier = adjustmentType === 'increase' 
      ? (1 + adjustmentPercentage / 100) 
      : (1 - adjustmentPercentage / 100)

    // 批量更新产品价格
    const updatePromises = products.map(async (product) => {
      const newPrice = Math.round(product.price * multiplier * 100) / 100 // 保留两位小数
      
      // 更新产品主价格
      const updatedProduct = await prisma.product.update({
        where: { id: product.id },
        data: { price: newPrice },
        include: {
          category: true,
          variants: true
        }
      })

      // 更新产品变体价格
      if (product.variants && product.variants.length > 0) {
        await Promise.all(
          product.variants.map(variant => {
            const newVariantPrice = Math.round(Number(variant.price) * multiplier * 100) / 100
            return prisma.productVariant.update({
              where: { id: variant.id },
              data: { price: newVariantPrice }
            })
          })
        )
      }

      return {
        id: product.id,
        name: product.name,
        category: product.category.name,
        oldPrice: product.price,
        newPrice: newPrice,
        variants: product.variants.length
      }
    })

    const updatedProducts = await Promise.all(updatePromises)

    return NextResponse.json({
      success: true,
      message: `Successfully updated ${updatedProducts.length} products`,
      updatedCount: updatedProducts.length,
      adjustmentType,
      adjustmentPercentage,
      products: updatedProducts
    })

  } catch (error) {
    console.error("[BULK_PRICE_UPDATE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
