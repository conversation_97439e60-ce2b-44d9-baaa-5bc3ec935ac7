import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { CartButton } from "./cart-button"

async function getCartData() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.id) {
    return null
  }

  const cartItems = await prisma.cartItem.findMany({
    where: {
      userId: session.user.id,
    },
    include: {
      product: true,
      variant: true,
    },
  })

  return cartItems
}

export default async function Cart() {
  const cartItems = await getCartData()

  return <CartButton initialItems={cartItems} />
} 