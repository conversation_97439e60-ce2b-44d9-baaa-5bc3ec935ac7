"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Icons } from "@/components/icons"

interface YollooCard {
  id: string
  number: string
  status: string
}

interface ProductUIDSelectorProps {
  cards: YollooCard[]
  onSelect: (cardId: string) => void
  selectedCardId?: string
}

export function ProductUIDSelector({ cards, onSelect, selectedCardId }: ProductUIDSelectorProps) {
  const activeCards = cards.filter(card => card.status === "Active")

  if (activeCards.length === 0) {
    return (
      <Alert>
        <Icons.warning className="h-4 w-4" />
        <AlertTitle>No Active Cards</AlertTitle>
        <AlertDescription>
          You need an active Yolloo Card to purchase this product. Please activate a card first.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="card-selector">Select Yolloo Card</Label>
        <Select
          value={selectedCardId}
          onValueChange={onSelect}
        >
          <SelectTrigger id="card-selector">
            <SelectValue placeholder="Select a card" />
          </SelectTrigger>
          <SelectContent>
            {activeCards.map((card) => (
              <SelectItem key={card.id} value={card.id}>
                Card: {card.number}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <p className="text-sm text-muted-foreground">
        This product requires a Yolloo Card UID. Please select the card you want to use.
      </p>
    </div>
  )
} 