"use client";

import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { formatDate, DateFormatter } from "@/lib/utils";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useSession } from "next-auth/react";
import { Icons } from "@/components/icons";
import { Input } from "@/components/ui/input";
import { Loader2, ArrowRight, Wallet } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { OrganizationIcons } from "@/components/organization-icons";
import axios from "axios";

interface AffiliateProfile {
  code: string;
  totalEarnings: number;
  commissionRate: number;
  discountRate: number;
  status: string;
}

interface AffiliateData {
  profile: AffiliateProfile;
  stats?: {
    totalReferrals: number;
    totalSubscribers: number;
    totalRegisteredUsers: number;
  };
  recentReferrals?: Array<{
    id: string;
    orderId: string;
    order: {
      total: number;
    };
    commissionAmount: number;
    status: string;
    createdAt: string;
  }>;
  recentWithdrawals?: Array<{
    id: string;
    amount: number;
    paymentMethod: string;
    status: string;
    createdAt: string;
  }>;
}

// Define types for the organization data
interface Organization {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  code: string;
  commissionRate: number;
  discountRate: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  memberSince: string;
  _count: {
    members: number;
  };
  administrators?: Array<{
    id: string;
    name: string;
    email: string;
    image?: string;
  }>;
}

export default function AffiliatePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("dashboard");

  // Personal affiliate data
  const [data, setData] = useState<AffiliateData | null>(null);
  const [loading, setLoading] = useState(true);
  const [copySuccess, setCopySuccess] = useState(false);

  // Organizations data
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loadingOrgs, setLoadingOrgs] = useState(true);

  // Generate the referral link
  const referralLink = typeof window !== "undefined"
    ? `${window.location.origin}/?ref=${data?.profile.code}`
    : "";

  // Handle copying the referral link
  const handleCopyLink = () => {
    if (referralLink) {
      navigator.clipboard.writeText(referralLink);
      setCopySuccess(true);
      toast.success("Referral link copied to clipboard");
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  // Handle selecting an organization
  const handleSelectOrganization = (id: string) => {
    router.push(`/affiliate/organization/${id}`);
  };

  // Check for tab query parameter
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const tabParam = params.get('tab');
      if (tabParam && ['dashboard', 'organizations', 'commissions'].includes(tabParam)) {
        setActiveTab(tabParam);
      }
    }
  }, []);

  // Fetch affiliate data
  useEffect(() => {
    const fetchAffilateData = async () => {
      try {
        setLoading(true);
        const response = await fetch("/api/affiliate/dashboard");

        if (!response.ok) {
          if (response.status === 404) {
            // No affiliate profile found
            setData(null);
            return;
          }
          throw new Error("Failed to fetch affiliate data");
        }

        const affiliateData = await response.json();
        console.log("Affiliate data loaded:", affiliateData); // 添加日志查看数据
        setData(affiliateData);
      } catch (error) {
        console.error("Error fetching affiliate data:", error);
        toast.error("Failed to fetch affiliate data");
        setData(null);
      } finally {
        setLoading(false);
      }
    };

    if (status === "authenticated") {
      fetchAffilateData();
    }
  }, [status]);

  // Fetch organizations data when on organization tab
  useEffect(() => {
    const fetchOrganizations = async () => {
      if (activeTab !== "organizations") return;

      try {
        setLoadingOrgs(true);
        const response = await axios.get("/api/affiliate/organizations");
        setOrganizations(response.data);
      } catch (error) {
        console.error("Error fetching organizations:", error);
        toast.error("Failed to fetch organizations");
      } finally {
        setLoadingOrgs(false);
      }
    };

    fetchOrganizations();
  }, [activeTab]);

  // Redirect to login if not authenticated
  if (status === "unauthenticated") {
    router.push("/auth/signin");
    return null;
  }

  // Show loading state while checking authentication
  if (status === "loading") {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Icons.spinner className="h-10 w-10 animate-spin" />
      </div>
    );
  }

  // Main content
  return (
    <div className="container py-8">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold">Affiliate Center</h1>
          <p className="text-muted-foreground">
            Manage your referrals and track your earnings
          </p>
        </div>
      </div>

      <Tabs defaultValue="dashboard" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid grid-cols-3 w-full max-w-md">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <Icons.cart className="h-4 w-4" />
            <span className="hidden sm:inline">Dashboard</span>
          </TabsTrigger>
          <TabsTrigger value="organizations" className="flex items-center gap-2">
            <Icons.users className="h-4 w-4" />
            <span className="hidden sm:inline">Organization</span>
          </TabsTrigger>
          <TabsTrigger value="commissions" className="flex items-center gap-2">
            <Wallet className="h-4 w-4" />
            <span className="hidden sm:inline">Commissions</span>
          </TabsTrigger>
        </TabsList>

        {/* Personal Affiliate Dashboard */}
        <TabsContent value="dashboard">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : !data ? (
            <Card>
              <CardHeader>
                <CardTitle>Join our Affiliate Program</CardTitle>
                <CardDescription>
                  You are not enrolled in our affiliate program yet. Join to start earning commissions.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="mt-2">Enroll Now</Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-6">
              {/* Referral Link Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Your Referral Link</CardTitle>
                  <CardDescription>
                    Share this link to earn commissions on referrals
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    <Input
                      value={referralLink}
                      readOnly
                      className="font-mono text-sm"
                    />
                    <Button
                      size="icon"
                      onClick={handleCopyLink}
                      aria-label="Copy referral link"
                    >
                      {copySuccess ? (
                        <Icons.check className="h-4 w-4" />
                      ) : (
                        <Icons.page className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  <div className="mt-4">
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralLink)}`, '_blank')}
                      >
                        <Icons.facebook className="h-4 w-4 mr-2" />
                        Share on Facebook
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`https://x.com/intent/tweet?url=${encodeURIComponent(referralLink)}&text=${encodeURIComponent('Check out this great service!')}`, '_blank')}
                      >
                        <Icons.twitter className="h-4 w-4 mr-2" />
                        Share on X
                      </Button>
                    </div>
                  </div>

                  <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardHeader className="p-3">
                        <CardDescription>Discount Rate for Customers</CardDescription>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <div className="text-2xl font-bold">
                          {(data.profile.discountRate * 100).toFixed(0)}%
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          Customers using your referral link will receive this discount on their purchase.
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="p-3">
                        <CardDescription>Commission Rate</CardDescription>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <div className="text-2xl font-bold">
                          {(data.profile.commissionRate * 100).toFixed(0)}%
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="p-3">
                        <CardDescription>Total Earnings</CardDescription>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <div className="text-2xl font-bold">
                          ${data.profile.totalEarnings.toFixed(2)}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                </CardContent>
              </Card>

              {/* Stats Card */}
              {data.stats && (
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Statistics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardHeader className="p-3">
                          <CardDescription>Total Referrals</CardDescription>
                        </CardHeader>
                        <CardContent className="p-3 pt-0">
                          <div className="text-2xl font-bold">
                            {data.stats.totalReferrals}
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="p-3">
                          <CardDescription>Subscribers</CardDescription>
                        </CardHeader>
                        <CardContent className="p-3 pt-0">
                          <div className="text-2xl font-bold">
                            {data.stats.totalSubscribers}
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="p-3">
                          <CardDescription>Registered Users</CardDescription>
                        </CardHeader>
                        <CardContent className="p-3 pt-0">
                          <div className="text-2xl font-bold">
                            {data.stats.totalRegisteredUsers}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Recent Referrals */}
              {data.recentReferrals && data.recentReferrals.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Referrals</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Order ID</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Commission</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Date</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {data.recentReferrals.map((referral) => (
                          <TableRow key={referral.id}>
                            <TableCell className="font-medium">
                              {referral.orderId.substring(0, 8)}...
                            </TableCell>
                            <TableCell>
                              ${referral.order.total.toFixed(2)}
                            </TableCell>
                            <TableCell>
                              ${referral.commissionAmount.toFixed(2)}
                            </TableCell>
                            <TableCell>
                              <span
                                className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                  referral.status === "APPROVED"
                                    ? "bg-green-100 text-green-800"
                                    : referral.status === "PENDING"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : referral.status === "PAID"
                                    ? "bg-blue-100 text-blue-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {referral.status}
                              </span>
                            </TableCell>
                            <TableCell>
                              {formatDate(new Date(referral.createdAt))}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" size="sm" className="ml-auto" onClick={() => setActiveTab("commissions")}>
                      View All Commissions
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        {/* Organizations Tab */}
        <TabsContent value="organizations">
          {loadingOrgs ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : organizations.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 bg-muted/50 rounded-lg border border-dashed">
              <div className="flex flex-col items-center justify-center space-y-4">
                <OrganizationIcons.users className="h-12 w-12 text-muted-foreground" />
                <div className="text-center space-y-2">
                  <h3 className="text-lg font-medium">No Organizations</h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    You haven&apos;t joined any organizations yet. Please contact your administrator for invitations.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {organizations.map((org) => (
                <Card
                  key={org.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleSelectOrganization(org.id)}
                >
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {org.logo ? (
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={org.logo} alt={org.name} />
                          <AvatarFallback>{org.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                      ) : (
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>{org.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                      )}
                      {org.name}
                    </CardTitle>
                    <CardDescription>Code: {org.code}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Members</span>
                        <span className="font-medium">{org._count.members}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Commission Rate</span>
                        <span className="font-medium">{(org.commissionRate * 100).toFixed(0)}%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Discount Rate</span>
                        <span className="font-medium">{(org.discountRate * 100).toFixed(0)}%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Member Since</span>
                        <span className="font-medium">{formatDistanceToNow(new Date(org.memberSince), { addSuffix: true })}</span>
                      </div>

                      {/* Administrators section */}
                      {org.administrators && org.administrators.length > 0 && (
                        <div>
                          <p className="text-sm font-medium mb-2">Administrators</p>
                          <div className="space-y-2">
                            {org.administrators.map((admin) => (
                              <div key={admin.id} className="flex items-center gap-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={admin.image} alt={admin.name} />
                                  <AvatarFallback>{admin.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                                </Avatar>
                                <span className="text-sm truncate">{admin.name}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <div className="text-xs text-muted-foreground">
                      Created {DateFormatter.relative(org.createdAt)}
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Commissions Tab */}
        <TabsContent value="commissions">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : !data ? (
            <Card>
              <CardHeader>
                <CardTitle>No Commission Data</CardTitle>
                <CardDescription>
                  Join our affiliate program to start earning commissions.
                </CardDescription>
              </CardHeader>
            </Card>
          ) : (
            <div className="grid gap-6">
              {/* Commission Summary */}
              <Card>
                <CardHeader>
                  <CardTitle>Commission Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardHeader className="p-3">
                        <CardDescription>Total Earnings</CardDescription>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <div className="text-2xl font-bold">
                          ${data.profile.totalEarnings.toFixed(2)}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="p-3">
                        <CardDescription>Pending</CardDescription>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <div className="text-2xl font-bold">
                          ${data.recentReferrals?.filter(r => r.status === "PENDING")
                            .reduce((sum, r) => sum + r.commissionAmount, 0)
                            .toFixed(2) || "0.00"}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="p-3">
                        <CardDescription>Available for Withdrawal</CardDescription>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <div className="text-2xl font-bold">
                          ${data.recentReferrals?.filter(r => r.status === "APPROVED")
                            .reduce((sum, r) => sum + r.commissionAmount, 0)
                            .toFixed(2) || "0.00"}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>

              {/* All Commissions */}
              <Card>
                <CardHeader>
                  <CardTitle>All Commissions</CardTitle>
                </CardHeader>
                <CardContent>
                  {data.recentReferrals && data.recentReferrals.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Order ID</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Commission</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Date</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {data.recentReferrals.map((referral) => (
                          <TableRow key={referral.id}>
                            <TableCell className="font-medium">
                              {referral.orderId.substring(0, 8)}...
                            </TableCell>
                            <TableCell>
                              ${referral.order.total.toFixed(2)}
                            </TableCell>
                            <TableCell>
                              ${referral.commissionAmount.toFixed(2)}
                            </TableCell>
                            <TableCell>
                              <span
                                className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                  referral.status === "APPROVED"
                                    ? "bg-green-100 text-green-800"
                                    : referral.status === "PENDING"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : referral.status === "PAID"
                                    ? "bg-blue-100 text-blue-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {referral.status}
                              </span>
                            </TableCell>
                            <TableCell>
                              {formatDate(new Date(referral.createdAt))}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      No commission data available yet.
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Recent Withdrawals */}
              {data.recentWithdrawals && data.recentWithdrawals.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Withdrawals</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>ID</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Payment Method</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Date</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {data.recentWithdrawals.map((withdrawal) => (
                          <TableRow key={withdrawal.id}>
                            <TableCell className="font-medium">
                              {withdrawal.id.substring(0, 8)}...
                            </TableCell>
                            <TableCell>
                              ${withdrawal.amount.toFixed(2)}
                            </TableCell>
                            <TableCell>{withdrawal.paymentMethod}</TableCell>
                            <TableCell>
                              <span
                                className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                  withdrawal.status === "COMPLETED"
                                    ? "bg-green-100 text-green-800"
                                    : withdrawal.status === "PENDING"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {withdrawal.status}
                              </span>
                            </TableCell>
                            <TableCell>
                              {formatDate(new Date(withdrawal.createdAt))}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}