import { QrCode, Download, Signal, Smartphone } from "lucide-react"
import { SectionBackground } from "@/components/ui/section-background"

const StepCard = ({ icon: Icon, step, title, description }: {
  icon: any,
  step: string,
  title: string,
  description: string
}) => (
  <div className="flex flex-col items-center text-center p-8 rounded-3xl bg-gradient-to-br from-[#F799A6]/10 to-[#B82E4E]/10 
  backdrop-blur-sm transition-all duration-500 hover:from-[#F799A6]/15 hover:to-[#B82E4E]/15 
  hover:shadow-[0_8px_32px_rgba(247,153,166,0.2)] hover:-translate-y-1 border border-[#F799A6]/30 
  hover:border-[#F799A6]/50 group">
    <div className="relative">
      <div className="w-20 h-20 flex items-center justify-center rounded-2xl bg-gradient-to-br from-[#B82E4E]/20 
      to-[#F799A6]/20 mb-6 shadow-[0_4px_16px_rgba(247,153,166,0.2)]">
        <Icon className="w-10 h-10 text-[#B82E4E] transition-transform duration-500 group-hover:scale-110" />
      </div>
    </div>
    <div className="px-4 py-1 bg-gradient-to-r from-[#F799A6]/20 to-[#B82E4E]/20 rounded-full text-[#B82E4E] 
    font-semibold mb-3 transition-all duration-500 shadow-sm shadow-pink-500/10 border border-[#F799A6]/30">
      {step}
    </div>
    <h3 className="font-semibold text-xl mb-3 text-gray-900 group-hover:text-[#B82E4E] transition-colors duration-300">
      {title}
    </h3>
    <p className="text-gray-600 leading-relaxed">
      {description}
    </p>
  </div>
)

export default function HowItWorks() {
  return (
    <SectionBackground isWhite={false}>
      <div className="container">
        <div className="text-center mb-20">
          <div className="inline-block px-6 py-2 bg-gradient-to-br from-[#F799A6]/20 to-[#B82E4E]/20 backdrop-blur-sm 
          rounded-full border border-[#F799A6]/30 text-[#B82E4E] font-semibold tracking-wide mb-4 shadow-lg shadow-pink-500/10">
            Simple Setup Process
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] 
          text-transparent bg-clip-text">
            Get Started in Minutes
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
            Our streamlined activation process ensures you can start using your eSIM 
            right away, with no technical expertise required.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          <StepCard
            icon={QrCode}
            step="Step 1"
            title="Choose Plan"
            description="Select your ideal data plan from our range of global coverage options"
          />
          <StepCard
            icon={Download}
            step="Step 2"
            title="Instant Delivery"
            description="Receive your eSIM QR code immediately in your email after purchase"
          />
          <StepCard
            icon={Smartphone}
            step="Step 3"
            title="Quick Install"
            description="Scan the QR code with your phone camera to install the eSIM profile"
          />
          <StepCard
            icon={Signal}
            step="Step 4"
            title="Ready to Go"
            description="Activate your eSIM with one tap and enjoy global connectivity"
          />
        </div>
      </div>
    </SectionBackground>
  )
} 