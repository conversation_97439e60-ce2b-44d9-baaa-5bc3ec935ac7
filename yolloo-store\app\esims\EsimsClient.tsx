"use client";

import { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import EsimProductCard from "./EsimProductCard";
import { CheckCircle, MapPin, Globe, Flag, SearchIcon, Package } from "lucide-react";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  stock: number;
  status: string;
  country: string | null;
  countryCode?: string | null;
  planType?: string | null;
  dataSize?: number | null;
  sku: string;
  variants?: {
    id: string;
    price: number;
    duration: number;
    durationType: string;
  }[];
}

interface EsimsClientProps {
  allProducts: Product[];
  localDestinations: string[];
  regionalDestinations: string[];
}

export default function EsimsClient({ allProducts, localDestinations, regionalDestinations }: EsimsClientProps) {
  const [type, setType] = useState<"Local" | "Regional">("Local");
  const [destination, setDestination] = useState<string>("");

  // 目的地选项
  const destinations = useMemo(() => {
    return type === "Local" ? localDestinations : regionalDestinations;
  }, [type, localDestinations, regionalDestinations]);

  // 当前筛选商品
  const filteredProducts = useMemo(() => {
    if (!destination) return [];
    if (type === "Local") {
      return allProducts.filter(
        (p) => p.country?.trim() === destination && p.country && p.country.split(/[,;]/).length === 1
      );
    } else {
      // Regional: 只要 destination 在 country 字段中即可
      return allProducts.filter(
        (p) =>
          p.country &&
          p.country
            .split(/[,;]/)
            .map((c) => c.trim())
            .includes(destination)
      );
    }
  }, [allProducts, type, destination]);

  return (
    <div className="space-y-12">
      {/* 步骤1：类型选择 */}
      <div className="flex flex-col sm:flex-row gap-6 items-center mb-6">
        {["Local", "Regional"].map((t) => (
          <button
            key={t}
            className={`group flex-1 rounded-2xl border-2 transition-all duration-200 px-0 py-0 bg-gradient-to-br from-white via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 shadow-md hover:shadow-xl flex flex-col items-center justify-center h-28 cursor-pointer relative ${type === t ? "border-blue-500 ring-2 ring-blue-400" : "border-transparent"}`}
            onClick={() => {
              setType(t as "Local" | "Regional");
              setDestination("");
            }}
            aria-pressed={type === t}
          >
            <span className="absolute top-3 right-3">
              {type === t && <CheckCircle className="text-blue-500 h-6 w-6" />}
            </span>
            <span className="mb-2">
              {t === "Local" ? (
                <MapPin className="h-8 w-8 text-blue-400 group-hover:scale-110 transition-transform" />
              ) : (
                <Globe className="h-8 w-8 text-violet-400 group-hover:scale-110 transition-transform" />
              )}
            </span>
            <span className="text-lg font-bold text-gray-900 dark:text-gray-100">{t}</span>
            <span className="text-xs text-muted-foreground mt-1">{t === "Local" ? "Single Country" : "Multi-country Region"}</span>
          </button>
        ))}
      </div>

      {/* 步骤2：选择 destination */}
      <div className="max-w-lg mx-auto">
        <div className="flex items-center gap-2 mb-2">
          <Flag className="h-5 w-5 text-blue-400" />
          <span className="font-medium text-base">Select Destination</span>
        </div>
        <Select value={destination} onValueChange={setDestination}>
          <SelectTrigger className="w-full h-14 text-base rounded-xl border-2 border-blue-100 dark:border-gray-800 bg-white dark:bg-gray-950 shadow-sm">
            <SelectValue placeholder={`Choose ${type === "Local" ? "Country" : "Region"}`} />
          </SelectTrigger>
          <SelectContent className="max-h-72 overflow-y-auto rounded-xl shadow-lg">
            {destinations.length === 0 ? (
              <div className="p-4 text-muted-foreground">No destinations available</div>
            ) : (
              destinations.map((d) => (
                <SelectItem key={d} value={d} className="truncate rounded-lg">
                  {d}
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
      </div>

      {/* 步骤3：商品列表 */}
      <div>
        {destination === "" ? (
          <div className="flex flex-col items-center justify-center py-16">
            <SearchIcon className="h-16 w-16 text-blue-200 mb-4" />
            <div className="text-lg text-muted-foreground font-medium">Please select a destination to view available eSIM plans.</div>
          </div>
        ) : filteredProducts.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-16">
            <Package className="h-16 w-16 text-blue-200 mb-4" />
            <div className="text-lg text-muted-foreground font-medium">No eSIM plans found for this destination.</div>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProducts.map((product) => (
              <EsimProductCard key={product.id} product={product} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 