'use client'

import Link from "next/link"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { UserNav } from "@/components/user-nav"
import { CreditCard, ShoppingCart, User } from "lucide-react"

export function HeaderActions() {
  const { data: session, status } = useSession()

  return (
    <div className="ml-auto flex items-center space-x-4">
      {/* My Cards Button */}
      <Button size="sm" asChild className="bg-gradient-to-r from-[#B82E4E] to-[#F799A6] hover:from-[#A02745] hover:to-[#E88A97] text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5">
        <Link href="/cards" className="flex items-center gap-2">
          <CreditCard className="h-4 w-4" />
          <span className="hidden sm:inline font-medium">My Cards</span>
        </Link>
      </Button>

      {/* Cart <PERSON> */}
      <Button variant="ghost" size="sm" asChild className="transition-all duration-300 hover:bg-[#F799A6]/10 hover:text-[#B82E4E]">
        <Link href="/cart" className="flex items-center gap-2">
          <ShoppingCart className="h-4 w-4" />
        </Link>
      </Button>

      {/* Language/Region Button */}
      <Button variant="ghost" size="sm" className="transition-all duration-300 hover:bg-[#F799A6]/10 hover:text-[#B82E4E]">
        <Icons.globe className="h-4 w-4" />
      </Button>

      {/* User Authentication */}
      {status === "loading" ? (
        <div className="h-8 w-8 animate-pulse rounded-full bg-muted" />
      ) : session ? (
        <UserNav user={session.user} />
      ) : (
        <Button variant="ghost" size="sm" asChild className="transition-all duration-300 hover:bg-[#F799A6]/10 hover:text-[#B82E4E] gap-2.5 px-4 py-2 rounded-full hover:shadow-sm">
          <Link href="/auth/signin" className="flex items-center gap-2 group">
            <User className="h-4 w-4 transition-transform duration-300 group-hover:scale-110 group-hover:text-[#B82E4E]" />
          </Link>
        </Button>
      )}
    </div>
  )
}