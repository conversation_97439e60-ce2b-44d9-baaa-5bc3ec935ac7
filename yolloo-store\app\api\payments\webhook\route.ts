import { headers } from "next/headers"
import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import Stripe from "stripe"
import { getOdooServiceForOrder } from "@/lib/odooServiceFactory"
import { OdooOrder } from "../../../types/odoo"

// 辅助函数：发送支付失败通知（预留接口）
async function notifyPaymentFailure(orderId: string, reason: string) {
  try {
    // 这里可以添加邮件通知、短信通知等逻辑
    console.log(`Payment failure notification for order ${orderId}: ${reason}`)

    // 示例：发送邮件通知
    // await sendEmail({
    //   to: order.user.email,
    //   subject: "Payment Failed - Please Try Again",
    //   template: "payment-failed",
    //   data: { orderId, reason }
    // })

    // 示例：发送系统通知
    // await createNotification({
    //   userId: order.userId,
    //   type: "payment_failed",
    //   message: `Payment failed for order ${orderId}. Please try again.`
    // })
  } catch (error) {
    console.error("Error sending payment failure notification:", error)
  }
}

// 辅助函数：记录支付分析数据（预留接口）
async function recordPaymentAnalytics(chargeData: any) {
  try {
    // 这里可以添加支付分析数据记录
    console.log(`Recording payment analytics for charge: ${chargeData.id}`)

    // 示例：记录到分析表
    // await prisma.paymentAnalytics.create({
    //   data: {
    //     chargeId: chargeData.id,
    //     amount: chargeData.amount / 100,
    //     currency: chargeData.currency,
    //     paymentMethod: chargeData.payment_method_details?.type,
    //     customerEmail: chargeData.billing_details?.email,
    //     createdAt: new Date(chargeData.created * 1000)
    //   }
    // })
  } catch (error) {
    console.error("Error recording payment analytics:", error)
  }
}

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


interface ShippingAddressSnapshot {
  name: string;
  phone: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  // @ts-ignore
  apiVersion: "2024-06-20",
})

export async function POST(req: Request) {
  const body = await req.text()
  const signature = headers().get("Stripe-Signature") as string

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (error: any) {
    console.error(`Webhook Error: ${error.message}`)
    return new NextResponse(`Webhook Error: ${error.message}`, { status: 400 })
  }

  console.log(`Processing webhook event: ${event.type}`)

  try {
    switch (event.type) {
      case "checkout.session.completed":
        // 处理支付完成事件
        const session = event.data.object as Stripe.Checkout.Session
        const orderId = session.metadata?.orderId

        if (!orderId) {
          // 尝试从测试事件中获取订单ID（用于Stripe CLI测试）
          if (process.env.NODE_ENV === "development") {
            console.log("Using test order ID for Stripe CLI webhook test")
            // 查找状态为PENDING的最新订单
            const latestPendingOrder = await prisma.order.findFirst({
              where: { status: "PENDING" },
              orderBy: { createdAt: "desc" },
              select: { id: true }
            })

            if (latestPendingOrder) {
              console.log(`Found latest pending order: ${latestPendingOrder.id}`)
              return handleCheckoutSessionCompleted(latestPendingOrder.id)
            }
          }

          console.error("Order ID not found in checkout.session.completed event")
          return new NextResponse("Order ID not found", { status: 400 })
        }

        console.log(`Processing checkout.session.completed for order: ${orderId}`)
        return await handleCheckoutSessionCompleted(orderId)

      case "checkout.session.expired":
        // 处理支付过期事件
        const expiredSession = event.data.object as Stripe.Checkout.Session
        const expiredOrderId = expiredSession.metadata?.orderId

        if (!expiredOrderId) {
          console.error("Order ID not found in checkout.session.expired event")
          return new NextResponse("Order ID not found", { status: 400 })
        }

        console.log(`Processing expired session for order: ${expiredOrderId}`)
        const existingOrder = await prisma.order.findUnique({
          where: { id: expiredOrderId },
        })

        if (!existingOrder) {
          console.error(`Order not found for expired session: ${expiredOrderId}`)
          throw new Error("Order not found")
        }

        console.log(`Found order for expired session: ${existingOrder.id}, current status: ${existingOrder.status}`)

        // 只有当订单状态为 PENDING 时才更新为 CANCELLED
        if (existingOrder.status === "PENDING") {
          console.log(`Updating expired order status to CANCELLED: ${expiredOrderId}`)
          await prisma.order.update({
            where: { id: expiredOrderId },
            data: {
              status: "CANCELLED",
            },
          })

          console.log(`Updating payment status for expired order: ${expiredOrderId}`)
          await prisma.payment.updateMany({
            where: {
              orders: {
                some: {
                  id: expiredOrderId,
                },
              },
            },
            data: {
              status: "FAILED",
            },
          })
        } else {
          console.log(`Skipping status update for non-pending order: ${expiredOrderId}, current status: ${existingOrder.status}`)
        }
        break

      case "payment_intent.payment_failed":
        // 处理支付失败事件
        const failedPaymentIntent = event.data.object as any
        console.log(`Payment failed for payment intent: ${failedPaymentIntent.id}`)
        console.log(`Failure reason: ${failedPaymentIntent.last_payment_error?.message || 'Unknown'}`)

        // 尝试通过payment intent查找相关的checkout session和订单
        try {
          // 查找相关的支付记录（通过金额和时间范围匹配）
          const amount = failedPaymentIntent.amount / 100; // 转换为美元
          const createdTime = new Date(failedPaymentIntent.created * 1000);
          const timeRange = new Date(createdTime.getTime() - 10 * 60 * 1000); // 10分钟前

          const relatedPayments = await prisma.payment.findMany({
            where: {
              amount: amount,
              status: "PENDING",
              createdAt: {
                gte: timeRange,
                lte: new Date(createdTime.getTime() + 10 * 60 * 1000) // 10分钟后
              }
            },
            include: {
              orders: true
            }
          });

          if (relatedPayments.length > 0) {
            for (const payment of relatedPayments) {
              console.log(`Found related payment ${payment.id} for failed payment intent`);

              // 更新支付状态为失败
              await prisma.payment.update({
                where: { id: payment.id },
                data: { status: "FAILED" }
              });

              // 更新相关订单状态（保持PENDING，让用户可以重试）
              for (const order of payment.orders) {
                console.log(`Keeping order ${order.id} as PENDING for retry after payment failure`);
                // 不更新订单状态，保持PENDING让用户可以重试支付

                // 发送支付失败通知
                await notifyPaymentFailure(order.id, failedPaymentIntent.last_payment_error?.message || 'Payment failed');
              }
            }
          } else {
            console.log(`No related payment found for failed payment intent: ${failedPaymentIntent.id}`);
          }
        } catch (error) {
          console.error("Error processing payment failure:", error);
        }
        break

      case "charge.succeeded":
        // 扣款成功 - 记录详细的支付信息用于分析和监控
        const successCharge = event.data.object as any
        console.log(`Charge succeeded: ${successCharge.id}`)
        console.log(`Amount: $${(successCharge.amount / 100).toFixed(2)} ${successCharge.currency.toUpperCase()}`)
        console.log(`Payment method: ${successCharge.payment_method_details?.type || 'unknown'}`)
        console.log(`Customer: ${successCharge.billing_details?.email || 'unknown'}`)

        // 记录支付成功的详细信息（可用于财务报表和分析）
        try {
          // 记录支付分析数据
          await recordPaymentAnalytics(successCharge);

          // 可以在这里添加其他支付成功的处理逻辑
          // 比如：
          // - 发送支付成功通知
          // - 更新财务统计
          // - 触发第三方集成

          console.log(`Charge processing completed for: ${successCharge.id}`);
        } catch (error) {
          console.error("Error processing charge success:", error);
        }
        break

      case "payment_intent.succeeded":
        // 支付意图成功 - 在checkout.session.completed之前触发
        const successPaymentIntent = event.data.object as any
        console.log(`Payment intent succeeded: ${successPaymentIntent.id}`)
        console.log(`Amount: $${(successPaymentIntent.amount / 100).toFixed(2)} ${successPaymentIntent.currency.toUpperCase()}`)
        console.log(`Status: ${successPaymentIntent.status}`)

        // 这个事件在checkout.session.completed之前触发
        // 可以用作支付流程的中间确认点
        try {
          // 可以在这里添加支付意图成功的处理逻辑
          // 比如：
          // - 预处理订单数据
          // - 发送支付处理中的通知
          // - 记录支付流程状态

          console.log(`Payment intent processing completed for: ${successPaymentIntent.id}`);
        } catch (error) {
          console.error("Error processing payment intent success:", error);
        }
        break

      case "payment_intent.created":
      case "charge.updated":
        // 这些事件我们不需要特殊处理，但需要返回200以避免Stripe重试
        console.log(`Received ${event.type} event - no action required`)
        break

      default:
        // 未知事件类型，记录日志但返回200
        console.log(`Received unknown webhook event type: ${event.type}`)
        break
    }

    return new NextResponse(null, { status: 200 })
  } catch (error) {
    console.error("Webhook processing error:", error)
    return new NextResponse("Webhook processing failed", { status: 500 })
  }
}

// 处理 checkout.session.completed 事件
async function handleCheckoutSessionCompleted(orderId: string) {
  try {
    // 获取订单详情
    console.log(`Fetching order details for: ${orderId}`)
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        items: true,
        shippingAddress: true,
        user: true
      }
    })

    if (!order) {
      console.error(`Order not found: ${orderId}`)
      throw new Error("Order not found")
    }

    console.log(`Found order: ${order.id}, current status: ${order.status}`)

    // 更新订单状态
    console.log(`Updating order status to PAID: ${orderId}`)
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: {
        status: "PAID",
      },
    })
    console.log(`Order status updated: ${updatedOrder.id}, new status: ${updatedOrder.status}`)

    // 更新支付状态
    console.log(`Updating payment status for order: ${orderId}`)
    const updatedPayment = await prisma.payment.updateMany({
      where: {
        orders: {
          some: {
            id: orderId,
          },
        },
      },
      data: {
        status: "COMPLETED",
      },
    })
    console.log(`Payment status updated: ${JSON.stringify(updatedPayment)}`)

    // 获取所有订单项目的产品信息
    const orderItemsWithProducts = await Promise.all(
      order.items.map(async (item) => {
        if (!item.productCode) return { ...item, product: null };

        const product = await prisma.product.findFirst({
          where: { sku: item.productCode },
          include: { category: true }
        });

        return { ...item, product };
      })
    );



    // 处理推广佣金 - 如果有推广码
    if (order.referralCode) {
      console.log(`Processing affiliate commission for order: ${orderId}, referral code: ${order.referralCode}`)
      try {
        // 直接调用prisma进行佣金处理，而不是通过API调用
        // 获取推广员资料
        const affiliateProfile = await prisma.affiliateProfile.findUnique({
          where: { code: order.referralCode },
          include: {
            organization: true,
          }
        });

        if (!affiliateProfile) {
          console.error(`Affiliate not found for code: ${order.referralCode}`);
          // 不中断流程，继续处理订单
        } else {
          // 确保不是自己推广自己
          if (order.userId === affiliateProfile.userId) {
            console.log(`User cannot refer themselves, skipping commission: ${order.id}`);
          } else {
            // 筛选出类型为"card"的商品项
            const cardItems = orderItemsWithProducts.filter(item =>
              item.product?.category?.name?.toLowerCase() === "card"
            );

            // 如果有"card"类型的商品，计算佣金
            if (cardItems.length > 0) {
              // 计算card商品总金额
              const cardItemsTotal = cardItems.reduce((total, item) =>
                total + (item.price * item.quantity), 0
              );

              let commissionAmount = 0;
              let organizationCommissionId = null;
              let organizationCommissionAmount = 0;

              // 如果推广员属于组织，则处理组织佣金
              if (affiliateProfile.organizationId) {
                // 获取组织资料
                const organization = await prisma.affiliateOrganization.findUnique({
                  where: { id: affiliateProfile.organizationId },
                });

                if (organization) {
                  // 计算组织佣金总额
                  organizationCommissionAmount = cardItemsTotal * organization.commissionRate;

                  // 计算会员佣金 - 基于会员的commissionRate计算从组织佣金中分配的比例
                  commissionAmount = cardItemsTotal * affiliateProfile.commissionRate;

                  // 组织实际获得的佣金
                  const organizationActualCommission = organizationCommissionAmount - commissionAmount;

                  // 创建组织佣金记录
                  const orgCommission = await prisma.organizationCommission.create({
                    data: {
                      organizationId: organization.id,
                      commissionAmount: organizationCommissionAmount,
                      status: "PENDING",
                    },
                  });

                  // 更新组织总收益
                  await prisma.affiliateOrganization.update({
                    where: { id: organization.id },
                    data: {
                      totalEarnings: {
                        increment: organizationActualCommission,
                      },
                    },
                  });

                  organizationCommissionId = orgCommission.id;
                }
              } else {
                // 非组织会员的佣金计算
                commissionAmount = cardItemsTotal * affiliateProfile.commissionRate;
              }

              // 创建推广记录
              const referral = await prisma.affiliateReferral.create({
                data: {
                  affiliateId: affiliateProfile.id,
                  orderId: order.id,
                  commissionAmount,
                  status: "PENDING",
                  ...(organizationCommissionId && { organizationCommissionId }),
                },
              });

              // 更新会员总收益
              await prisma.affiliateProfile.update({
                where: { id: affiliateProfile.id },
                data: {
                  totalEarnings: {
                    increment: commissionAmount,
                  },
                },
              });

              console.log(`Commission processing completed: affiliateId=${affiliateProfile.id}, orderId=${order.id}, amount=${commissionAmount}, referralId=${referral.id}`);
            } else {
              console.log(`No card products in order, skipping commission: ${order.id}`);
            }
          }
        }
      } catch (error) {
        console.error('Error processing commission:', error);
        // 佣金处理失败不应阻止订单处理
      }
    }

    // 创建Odoo订单
    console.log(`Creating Odoo orders for: ${orderId}`)

    // 首先按 variantCode 分组
    const itemsByVariantCode: Record<string, typeof order.items> = {};
    for (const item of order.items) {
      const variantCode = item.variantCode || 'default';
      if (!itemsByVariantCode[variantCode]) {
        itemsByVariantCode[variantCode] = [];
      }
      itemsByVariantCode[variantCode].push(item);
    }

    // 然后，对于每个 variantCode 组，再按 UID 分组
    // 这样可以保持 OdooOrderStatus 表的唯一约束 [orderId, variantCode]
    const itemsByGroup: Record<string, typeof order.items> = {};
    for (const [variantCode, items] of Object.entries(itemsByVariantCode)) {
      // 按 UID 分组
      const itemsByUid: Record<string, typeof order.items> = {};
      for (const item of items) {
        const uid = item.uid || 'no-uid';
        if (!itemsByUid[uid]) {
          itemsByUid[uid] = [];
        }
        itemsByUid[uid].push(item);
      }

      // 为每个 UID 创建一个组
      for (const [uid, uidItems] of Object.entries(itemsByUid)) {
        // 使用更可靠的分隔符（:::）来避免与UUID中的连字符冲突
        const key = `${variantCode}:::${uid}`;
        itemsByGroup[key] = uidItems;
      }
    }

    // 确定使用哪个 Odoo 服务（所有项目使用同一个服务）
    const isQrOrder = orderItemsWithProducts.some(item =>
      item.product?.category?.name?.toLowerCase() === 'qr_code'
    );
    const selectedOdooService = isQrOrder ?
      getOdooServiceForOrder(orderItemsWithProducts.filter(item => item.product?.category?.name?.toLowerCase() === 'qr_code')) :
      getOdooServiceForOrder(orderItemsWithProducts.filter(item => item.product?.category?.name?.toLowerCase() !== 'qr_code'));

    console.log(`Using ${isQrOrder ? 'QR' : 'Standard'} Odoo service for order ${order.id}`);

    // 准备通用的订单信息
    const commonOrderInfo = {
      shipping_address: {
        name: ((order.shippingAddressSnapshot as unknown) as ShippingAddressSnapshot)?.name || order.shippingAddress?.name || '',
        city: `${((order.shippingAddressSnapshot as unknown) as ShippingAddressSnapshot)?.city || order.shippingAddress?.city || ''} ${((order.shippingAddressSnapshot as unknown) as ShippingAddressSnapshot)?.state || order.shippingAddress?.state || ''}`,
        zip: ((order.shippingAddressSnapshot as unknown) as ShippingAddressSnapshot)?.postalCode || order.shippingAddress?.postalCode || '',
        address: `${((order.shippingAddressSnapshot as unknown) as ShippingAddressSnapshot)?.address2 || order.shippingAddress?.address2 || ''} ${((order.shippingAddressSnapshot as unknown) as ShippingAddressSnapshot)?.address1 || order.shippingAddress?.address1 || ''}, ${((order.shippingAddressSnapshot as unknown) as ShippingAddressSnapshot)?.city || order.shippingAddress?.city || ''} ${((order.shippingAddressSnapshot as unknown) as ShippingAddressSnapshot)?.state || order.shippingAddress?.state || ''}, ${((order.shippingAddressSnapshot as unknown) as ShippingAddressSnapshot)?.country || order.shippingAddress?.country || ''}`,
        phone: ((order.shippingAddressSnapshot as unknown) as ShippingAddressSnapshot)?.phone || order.shippingAddress?.phone || '',
        country: ((order.shippingAddressSnapshot as unknown) as ShippingAddressSnapshot)?.country || order.shippingAddress?.country || ''
      },
      payment_method: "prepaid",
      email: order.user.email!,
    };

    // 为每组创建一个 Odoo 订单
    const odooResponses = [];

    try {
      for (const [groupKey, items] of Object.entries(itemsByGroup)) {
        // 从分组键中提取 variantCode 和 uid，使用更可靠的分隔符
        const [variantCode, uid] = groupKey.includes(':::')
          ? groupKey.split(':::')
          : [groupKey.split('-')[0], groupKey.split('-').slice(1).join('-')]; // 向后兼容旧格式
        console.log(`Creating Odoo order for group ${groupKey} (variant: ${variantCode}, uid: ${uid}) with ${items.length} items`);

        const odooOrder: OdooOrder = {
          customer_order_ref: `${order.id}-${groupKey}`,
          ...commonOrderInfo,
          note: `Order from yolloo store (Variant: ${variantCode}, UID: ${uid})`,
          order_lines: items.map(item => ({
            customer_order_line_ref: `${order.id}-${item.id}`,
            product_code: item.variantCode || item.productCode || "",
            card_uid: item.uid || undefined,
            product_uom_qty: item.quantity,
            lpa_string: item.lpaString || undefined
          })),
        };

        try {
          // 创建 Odoo 订单
          const response = await selectedOdooService.createOrder(odooOrder);
          console.log(`Odoo order created for group ${groupKey} (variant: ${variantCode}, uid: ${uid}):`, response);

          // 获取 Odoo 响应中的状态和消息
          const responseStatus = response.result?.status || "unknown";
          const responseMessage = response.result?.message || "No response message";

          // 设置 OdooOrderStatus 的状态 - Odoo返回"ok"表示成功
          const isSuccess = responseStatus === "ok" || responseStatus === "success";
          const odooStatus = isSuccess ? "processing" : "error";

          // 设置 OdooOrderStatus 的描述
          const odooDescription = isSuccess
            ? `Successful: ${responseMessage}`
            : `Failed: ${responseMessage}`;

          odooResponses.push({
            groupKey,
            variantCode,
            uid,
            response,
            status: odooStatus,
            description: odooDescription,
            success: isSuccess,
            odooOrderRef: response.result?.data?.order_name || null
          });
        } catch (variantError) {
          console.error(`Error creating Odoo order for group ${groupKey} (variant: ${variantCode}, uid: ${uid}):`, variantError);

          odooResponses.push({
            groupKey,
            variantCode,
            uid,
            response: null,
            status: "error",
            description: `Error: ${variantError instanceof Error ? variantError.message : 'Unknown error'}`,
            success: false,
            odooOrderRef: null
          });
        }
      }

      // 在创建真实的 Odoo 订单状态记录之前，先删除默认的记录
      console.log(`删除订单 ${orderId} 的默认 odooOrderStatus 记录`);
      const deletedDefaultRecords = await prisma.odooOrderStatus.deleteMany({
        where: {
          orderId: orderId,
          variantCode: "default"
        }
      });
      console.log(`已删除 ${deletedDefaultRecords.count} 个默认 odooOrderStatus 记录`);

      // 为每个响应创建单独的 OdooOrderStatus 记录
      console.log(`准备将 ${odooResponses.length} 个Odoo响应记录到数据库`);
      for (const response of odooResponses) {
        try {
          const variantCode = response.variantCode;
          const uid = response.uid === 'no-uid' ? null : response.uid;
          const status = response.success ? "processing" : "error";

          console.log(`处理Odoo响应记录: orderId=${orderId}, variantCode=${variantCode}, uid=${uid || 'null'}`);

          // 查找是否已存在相同 orderId, variantCode, uid 的记录
          const existingStatus = await prisma.odooOrderStatus.findFirst({
            where: {
              orderId: orderId,
              variantCode: variantCode,
              uid: uid
            }
          });

          console.log(`数据库查询结果: ${existingStatus ? `找到现有记录 ID=${existingStatus.id}` : '未找到现有记录，将创建新记录'}`);

          if (existingStatus) {
            // 更新现有记录
            await prisma.odooOrderStatus.update({
              where: { id: existingStatus.id },
              data: {
                status: status,
                description: response.description,
                odooOrderRef: response.odooOrderRef,
                lastCheckedAt: new Date(),
              }
            });
          } else {
            // 创建新记录
            await prisma.odooOrderStatus.create({
              data: {
                orderId: orderId,
                variantCode: variantCode,
                uid: uid,
                status: status,
                description: response.description,
                odooOrderRef: response.odooOrderRef,
                isDigital: false,
                deliveredQty: 0,
                lastCheckedAt: new Date(),
              }
            });
          }

          console.log(`OdooOrderStatus 成功记录: orderId=${orderId}, variant=${variantCode}, uid=${uid || 'none'}, status=${status}`);
        } catch (statusError) {
          console.error(`记录 OdooOrderStatus 失败: orderId=${orderId}, variant=${response.variantCode}, uid=${response.uid || 'none'}`);
          console.error(`错误详情:`, statusError);

          // 记录更详细的错误信息
          if (statusError instanceof Error) {
            console.error(`错误名称: ${statusError.name}`);
            console.error(`错误消息: ${statusError.message}`);
            console.error(`堆栈跟踪: ${statusError.stack}`);
          }

          // 尝试以不同的方式记录，以防是格式问题
          try {
            console.log(`尝试使用替代方法记录 OdooOrderStatus...`);
            await prisma.odooOrderStatus.create({
              data: {
                orderId: orderId,
                variantCode: response.variantCode || "unknown",
                uid: response.uid === 'no-uid' ? null : response.uid,
                status: response.success ? "processing" : "error",
                description: `${response.description} (备用记录)`,
                odooOrderRef: response.odooOrderRef,
                isDigital: false,
                deliveredQty: 0,
                lastCheckedAt: new Date(),
              }
            });
            console.log(`使用替代方法成功记录 OdooOrderStatus`);
          } catch (fallbackError) {
            console.error(`替代记录方法也失败:`, fallbackError);
          }
        }
      }

      // 记录总体状态
      const allSuccess = odooResponses.every(r => r.success);
      console.log(`Created ${odooResponses.length} Odoo orders for order ${orderId}, all success: ${allSuccess}`);

    } catch (error) {
      console.error("Failed to create Odoo orders:", error);

      // 记录总体错误到 OdooOrderStatus 表
      try {
        await prisma.odooOrderStatus.create({
          data: {
            orderId: orderId,
            variantCode: "overall",
            status: "error",
            description: `Error creating Odoo orders: ${error instanceof Error ? error.message : 'Unknown error'}`,
            isDigital: false,
            deliveredQty: 0,
            lastCheckedAt: new Date(),
          }
        });
        console.log(`Recorded overall Odoo error in OdooOrderStatus for order ${orderId}`);
      } catch (dbError) {
        console.error("Failed to record Odoo error in database:", dbError);
      }
    }

    return new NextResponse(null, { status: 200 })
  } catch (error) {
    console.error("Error handling checkout session completed:", error)
    return new NextResponse("Error handling checkout session completed", { status: 500 })
  }
}