"use client"

import { useEffect, useState } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"

export default function BindCardPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { data: session, status } = useSession()
  const [isBinding, setIsBinding] = useState(false)
  const uid = searchParams.get("uid")

  useEffect(() => {
    if (!uid) {
      router.push("/cards")
      return
    }

    if (status === "unauthenticated") {
      // 使用 callbackUrl 替代 from
      router.push(`/auth/signin?callbackUrl=${encodeURIComponent(`/bind-card?uid=${uid}`)}`)
    }
  }, [uid, status, router])

  const handleBindCard = async () => {
    if (!uid || !session?.user?.id) return

    try {
      setIsBinding(true)
      const response = await fetch("/api/cards/bind", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ uid }),
      })

      if (!response.ok) {
        const error = await response.text()
        throw new Error(error)
      }

      toast.success("Card bound successfully")
      router.push("/cards")
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to bind card")
    } finally {
      setIsBinding(false)
    }
  }

  if (status === "loading" || status === "unauthenticated") {
    return (
      <div className="container py-10">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-10">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-6">Bind Yolloo Card</h1>
        <div className="bg-white rounded-lg shadow p-6">
          <p className="text-gray-600 mb-4">
            You are about to bind the Yolloo card with ID: <br />
            <span className="font-mono bg-gray-100 px-2 py-1 rounded">{uid}</span>
          </p>
          <div className="flex justify-end">
            <Button
              onClick={() => router.push("/cards")}
              variant="outline"
              className="mr-2"
            >
              Cancel
            </Button>
            <Button
              onClick={handleBindCard}
              disabled={isBinding}
            >
              {isBinding ? "Binding..." : "Bind Card"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
} 