import { Controller, Post, Param, UseGuards, BadRequestException } from '@nestjs/common';
import { CardsService } from './cards.service';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('esims')
export class EsimsController {
  constructor(private readonly cardsService: CardsService) {}

  @Post(':esimId/activate')
  activateEsim(
    @CurrentUser() user: any,
    @Param('esimId') esimId: string,
  ) {
    if (!user) {
      throw new BadRequestException('User not authenticated');
    }

    if (!esimId || esimId.trim() === '') {
      throw new BadRequestException('eSIM ID is required');
    }

    return this.cardsService.activateEsim(user.id, esimId);
  }
}
