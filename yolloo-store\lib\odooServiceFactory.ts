import { createOdooService } from "@/app/services/odooService";
import { ODOO_CONFIG } from "@/app/config/odoo";

// 确保环境变量存在
if (!process.env.ODOO_ADDRESS) {
  throw new Error("ODOO_ADDRESS is not defined");
}

if (!process.env.ODOO_CHANNEL_ID) {
  throw new Error("ODOO_CHANNEL_ID is not defined");
}

if (!process.env.ODOO_AUTH_SECRET) {
  throw new Error("ODOO_AUTH_SECRET is not defined");
}

if (!process.env.ODOO_QR_ADDRESS) {
  throw new Error("ODOO_QR_ADDRESS is not defined");
}

if (!process.env.ODOO_QR_CHANNEL_ID) {
  throw new Error("ODOO_QR_CHANNEL_ID is not defined");
}

if (!process.env.ODOO_QR_AUTH_SECRET) {
  throw new Error("ODOO_QR_AUTH_SECRET is not defined");
}

// 标准Odoo配置
const standardOdooConfig = {
  address: process.env.ODOO_ADDRESS || '',
  channelId: process.env.ODOO_CHANNEL_ID || '',
  channelLanguage: process.env.ODOO_CHANNEL_LANGUAGE || 'en_US',
  authSecret: process.env.ODOO_AUTH_SECRET || '',
  signMethod: process.env.ODOO_SIGN_METHOD || 'md5',
};

// QR专用Odoo配置
const qrOdooConfig = {
  address: process.env.ODOO_QR_ADDRESS || '',
  channelId: process.env.ODOO_QR_CHANNEL_ID || '',
  channelLanguage: process.env.ODOO_QR_CHANNEL_LANGUAGE || 'en_US',
  authSecret: process.env.ODOO_QR_AUTH_SECRET || '',
  signMethod: process.env.ODOO_QR_SIGN_METHOD || 'md5',
};

// 创建标准Odoo服务实例
export const standardOdooService = createOdooService(standardOdooConfig);

// 创建QR专用Odoo服务实例
export const qrOdooService = createOdooService(qrOdooConfig);

// 为了向后兼容，保留原始的odooService导出
export const odooService = standardOdooService;

/**
 * 根据产品类型获取合适的Odoo服务实例
 * @param productCategory 产品类别名称
 * @returns 对应的Odoo服务实例
 */
export function getOdooServiceByCategory(productCategory: string): typeof standardOdooService {
  if (productCategory.toLowerCase() === 'qr_code') {
    console.log(`Using QR Odoo service for category: ${productCategory}`);
    return qrOdooService;
  }
  
  console.log(`Using standard Odoo service for category: ${productCategory}`);
  return standardOdooService;
}

/**
 * 检查订单中是否包含QR code类型的产品
 * @param orderItems 订单项目数组
 * @returns 如果包含QR code产品则返回true，否则返回false
 */
export function hasQrCodeProducts(orderItems: Array<{product: {category: {name: string}}}>) {
  return orderItems.some(item => 
    item.product?.category?.name?.toLowerCase() === 'qr_code'
  );
}

/**
 * 根据订单项目获取合适的Odoo服务实例
 * 如果订单中包含QR code产品，则使用QR专用服务
 * 否则使用标准服务
 * @param orderItems 订单项目数组
 * @returns 对应的Odoo服务实例
 */
export function getOdooServiceForOrder(orderItems: Array<{product: {category: {name: string}}}>) {
  if (hasQrCodeProducts(orderItems)) {
    console.log('Order contains QR code products, using QR Odoo service');
    return qrOdooService;
  }
  
  console.log('Order does not contain QR code products, using standard Odoo service');
  return standardOdooService;
}
