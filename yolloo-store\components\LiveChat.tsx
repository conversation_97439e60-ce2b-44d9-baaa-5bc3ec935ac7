'use client'

import { But<PERSON> } from '@/components/ui/button'
import { MessageCircle } from 'lucide-react'

// Type declaration for the RocketChat global
declare global {
  interface Window {
    RocketChat?: ((callback: (this: any) => void) => void) & { _: any[]; url: string };
  }
}

export default function LiveChat() {
  const openRocketChat = () => {
    // Trigger Rocket.Chat widget to open
    if (typeof window !== 'undefined' && window.RocketChat) {
      try {
        window.RocketChat(function(this: any) {
          if (typeof this.maximizeWidget === 'function') {
            this.maximizeWidget();
          }
        });
      } catch (error) {
        console.error('Error opening Rocket.Chat widget:', error);
      }
    }
  }

  return (
    <Button
      className="fixed bottom-4 right-4 rounded-full w-12 h-12 flex items-center justify-center z-50"
      onClick={openRocketChat}
      aria-label="Customer Support"
    >
      <MessageCircle className="h-6 w-6" />
    </Button>
  )
}