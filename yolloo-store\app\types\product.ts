import { OdooProduct, OdooProductPrice } from './odoo';

export interface Product {
    id: string;
    name: string;
    description: string;
    websiteDescription: string;
    price: number;
    currency: string;
    images: string[];
    stock: number;
    sku: string;
    category: string;
    isActive: boolean;
    off_shelve: boolean;
    requiredUID: boolean;
    mcc?: string;
    dataSize?: number;
    planType?: string;
    country?: string;
    countryCode?: string;
    variants: ProductVariant[];
    parameters: ProductParameter[];
    metadata: {
        odooId?: string;
        odooProductCode?: string;
    };
}

export interface ProductVariant {
    id: string;
    name: string;
    price: number;
    currency: string;
    attributes: {
        [key: string]: string;
    };
    duration?: number;
    durationType?: string;
    variantCode: string;
}

export interface ProductParameter {
    code: string;
    name: string;
    value: string;
}

// 最低价格限制（美元）
const MINIMUM_PRICE = 0.5;

// Odoo商品数据转换器
export function convertOdooToProduct(odooProduct: OdooProduct, odooPrice?: OdooProductPrice): Product {
    // Get the default variant (first one) for base price
    const defaultVariant = odooProduct.variants[0];

    // Convert variants with enhanced attribute processing
    // 过滤掉variant_attributes中包含"code": "card"的变体
    const variants = odooProduct.variants
        .filter(variant => {
            // 检查变体属性中是否有code为"card"的属性
            const hasCardAttribute = variant.variant_attributes.some(attr => attr.code === 'card');
            // 只保留没有card属性的变体
            return !hasCardAttribute;
        })
        .map(variant => {
            // 处理变体属性，提取duration和durationType
            let duration: number | undefined;
            let durationType: string | undefined;

            if (variant.variant_attributes && Array.isArray(variant.variant_attributes)) {
                // 查找billing_period属性或renewal_time属性
                const billingPeriod = variant.variant_attributes.find(
                    attr => attr.code === 'billing_period' || attr.code === 'renewal_time'
                );
                if (billingPeriod) {
                    duration = parseInt(billingPeriod.value, 10) || 30;
                }

                // 查找billing_period_unit属性或renewal_unit属性
                const billingPeriodUnit = variant.variant_attributes.find(
                    attr => attr.code === 'billing_period_unit' || attr.code === 'renewal_unit'
                );
                if (billingPeriodUnit) {
                    // 转换Odoo的单位格式为我们的格式
                    const unitValue = billingPeriodUnit.value.toLowerCase();
                    if (unitValue === 'day' || unitValue === '日' || unitValue === '天' || unitValue.includes('day')) {
                        durationType = 'day';
                    } else if (unitValue === 'month' || unitValue === 'months' || unitValue === '月' || unitValue.includes('month')) {
                        durationType = 'month';
                    } else if (unitValue === 'year' || unitValue === 'years' || unitValue === '年' || unitValue.includes('year')) {
                        durationType = 'year';
                    }
                }
            }

            return {
                id: variant.variant_code,
                name: variant.variant_name,
                price: Math.max(variant.supply_price, MINIMUM_PRICE),
                currency: variant.currency,
                attributes: variant.variant_attributes.reduce((acc, attr) => ({
                    ...acc,
                    [attr.code]: attr.value
                }), {}),
                duration,
                durationType,
                variantCode: variant.variant_code
            };
        });

    // 提取特定参数
    let dataSize: number | undefined;
    let planType: string | undefined;
    let country: string | undefined;
    let countryCode: string | undefined;
    let mcc = odooProduct.mcc;

    // 处理参数
    if (odooProduct.product_parameters && Array.isArray(odooProduct.product_parameters)) {
        // 提取数据大小(dataSize)
        const dataSizeParam = odooProduct.product_parameters.find(
            param => param.code === 'data_size' || param.name === 'Data Size'
        );
        if (dataSizeParam && dataSizeParam.value) {
            dataSize = parseFloat(dataSizeParam.value);
        }

        // 提取计划类型(planType)
        const planTypeParam = odooProduct.product_parameters.find(
            param => param.code === 'plan_type' || param.name === 'Plan Type'
        );
        if (planTypeParam && planTypeParam.value) {
            // 标准化 plan_type 值，不区分大小写
            const planTypeValue = planTypeParam.value.toLowerCase();
            if (planTypeValue === 'daily' || planTypeValue.includes('day')) {
                planType = 'Daily';
            } else if (planTypeValue === 'total' || planTypeValue.includes('total')) {
                planType = 'Total';
            } else {
                planType = planTypeParam.value;
            }
        }

        // 提取国家(country)
        const countryParam = odooProduct.product_parameters.find(
            param => param.code === 'country' || param.name === 'Country'
        );
        if (countryParam && countryParam.value) {
            country = countryParam.value;
        }

        // 提取国家代码(countryCode)
        const countryCodeParam = odooProduct.product_parameters.find(
            param => param.code === 'country_code' || param.name === 'Country Code'
        );
        if (countryCodeParam && countryCodeParam.value) {
            countryCode = countryCodeParam.value;
        }

        // 如果mcc为空，尝试从参数中提取
        if (!mcc) {
            const mccParam = odooProduct.product_parameters.find(
                param => param.code === 'mcc' || param.name === 'MCC'
            );
            if (mccParam && mccParam.value) {
                mcc = mccParam.value;
            }
        }
    }

    // 标准化分隔符：将 ',' 和 '，' 转换为 ';'
    if (mcc) {
        mcc = mcc.replace(/[,，]/g, ';');
    }

    if (country) {
        country = country.replace(/[,，]/g, ';');
    }

    if (countryCode) {
        countryCode = countryCode.replace(/[,，]/g, ';');
    }

    // 忽略图片链接处理，使用空数组
    const images: string[] = [];

    // Convert parameters
    const parameters = odooProduct.product_parameters.map(param => ({
        code: param.code,
        name: param.name,
        value: param.value
    }));

    return {
        id: odooProduct.product_code,
        name: odooProduct.name,
        description: odooProduct.description_sale || '',
        websiteDescription: odooProduct.website_description || '',
        price: Math.max(defaultVariant?.supply_price || 0, MINIMUM_PRICE),
        currency: defaultVariant?.currency || 'USD',
        images,
        stock: 999, // Default stock since not provided in API
        sku: odooProduct.product_code,
        category: odooProduct.product_type || 'default',
        isActive: !odooProduct.off_shelve,
        off_shelve: odooProduct.off_shelve,
        requiredUID: odooProduct.requiredUID,
        mcc,
        dataSize,
        planType,
        country,
        countryCode,
        variants,
        parameters,
        metadata: {
            odooId: odooProduct.product_code,
            odooProductCode: odooProduct.product_code,
        }
    };
}