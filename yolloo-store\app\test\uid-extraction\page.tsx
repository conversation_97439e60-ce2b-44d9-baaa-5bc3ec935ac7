"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { formatUid } from "@/lib/utils";

export default function UidExtractionTestPage() {
  const [input, setInput] = useState<string>(`{
  "data": [
    {
      "uid": "29901000000000000042"
    },
    {
      "uid": "29901000000000000043"
    },
    {
      "uid": "29901000000000000044"
    }
  ]
}`);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // 测试用例
  const testCases = [
    {
      name: "单个 UID",
      data: {
        data: [
          {
            uid: "29901000000000000042"
          }
        ]
      }
    },
    {
      name: "多个 UID",
      data: {
        data: [
          {
            uid: "29901000000000000042"
          },
          {
            uid: "29901000000000000043"
          },
          {
            uid: "29901000000000000044"
          }
        ]
      }
    },
    {
      name: "带空格的 UID",
      data: {
        data: [
          {
            uid: " 29901000000000000042 "
          },
          {
            uid: "29901000000000000043 "
          }
        ]
      }
    },
    {
      name: "非数组格式的单个 UID",
      data: {
        uid: "29901000000000000042"
      }
    },
    {
      name: "空数组",
      data: {
        data: []
      }
    },
    {
      name: "无 UID 数据",
      data: {
        data: [
          {
            other_field: "value"
          }
        ]
      }
    }
  ];

  // 加载测试用例
  const loadTestCase = (testCase: any) => {
    setInput(JSON.stringify(testCase.data, null, 2));
  };

  // 测试 UID 提取
  const testExtraction = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = JSON.parse(input);
      
      const response = await fetch("/api/test/uid-extraction", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ testCase: { data } })
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || "API 请求失败");
      }
      
      setResult(result.extracted);
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container py-8">
      <h1 className="text-2xl font-bold mb-6">UID 提取测试</h1>
      
      <div className="grid gap-6 md:grid-cols-2">
        <div>
          <h2 className="text-lg font-semibold mb-4">测试用例</h2>
          <div className="space-y-2">
            {testCases.map((testCase, index) => (
              <Button 
                key={index}
                variant="outline"
                onClick={() => loadTestCase(testCase)}
                className="mr-2 mb-2"
              >
                {testCase.name}
              </Button>
            ))}
          </div>
          
          <div className="mt-6">
            <h2 className="text-lg font-semibold mb-4">输入 JSON</h2>
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              className="font-mono h-64"
              placeholder="输入要测试的 JSON 数据"
            />
            
            <Button 
              onClick={testExtraction}
              className="mt-4"
              disabled={loading}
            >
              {loading ? "处理中..." : "测试 UID 提取"}
            </Button>
          </div>
        </div>
        
        <div>
          <h2 className="text-lg font-semibold mb-4">提取结果</h2>
          
          {error && (
            <Card className="p-4 bg-red-50 text-red-800 mb-4">
              <p className="font-semibold">错误:</p>
              <p>{error}</p>
            </Card>
          )}
          
          {result !== null && (
            <Card className="p-6">
              <div className="mb-4">
                <p className="text-sm text-muted-foreground mb-1">提取的 UID 字符串:</p>
                <p className="font-mono bg-gray-100 p-2 rounded">{result || "(无)"}</p>
              </div>
              
              {result && (
                <div>
                  <p className="text-sm text-muted-foreground mb-1">显示效果:</p>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {result.split(',')
                      .map(uid => uid.trim())
                      .filter(uid => uid.length > 0)
                      .map((uid, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="h-6 px-2 text-xs bg-purple-50"
                        >
                          {formatUid(uid)}
                        </Badge>
                      ))
                    }
                  </div>
                </div>
              )}
            </Card>
          )}
          
          <div className="mt-6">
            <h2 className="text-lg font-semibold mb-4">说明</h2>
            <Card className="p-4">
              <p className="mb-2">此页面用于测试 UID 提取逻辑。您可以:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>选择预定义的测试用例</li>
                <li>编辑 JSON 输入</li>
                <li>点击"测试 UID 提取"按钮查看结果</li>
                <li>查看提取的 UID 字符串和显示效果</li>
              </ul>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
