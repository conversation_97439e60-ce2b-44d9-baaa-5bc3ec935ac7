-- 修复订单显示问题的脚本
-- 确保所有订单项目都有正确的显示信息

-- 1. 检查并修复缺少 variantText 的订单项目
UPDATE "OrderItem" 
SET "variantText" = CASE 
    WHEN "variantText" IS NULL OR "variantText" = '' THEN 
        COALESCE(
            (SELECT p.name FROM "Product" p WHERE p.sku = "OrderItem"."productCode"),
            'Unknown Product'
        )
    ELSE "variantText"
END
WHERE "variantText" IS NULL OR "variantText" = '';

-- 2. 检查并修复缺少 productCode 的订单项目
-- 这种情况应该很少见，但为了安全起见
UPDATE "OrderItem" 
SET "productCode" = 'unknown-product-' || "id"
WHERE "productCode" IS NULL OR "productCode" = '';

-- 3. 显示修复结果统计
SELECT 
    'OrderItem修复统计' as description,
    COUNT(*) as total_items,
    COUNT(CASE WHEN "variantText" IS NOT NULL AND "variantText" != '' THEN 1 END) as items_with_variant_text,
    COUNT(CASE WHEN "productCode" IS NOT NULL AND "productCode" != '' THEN 1 END) as items_with_product_code,
    COUNT(CASE WHEN "variantCode" IS NOT NULL AND "variantCode" != '' THEN 1 END) as items_with_variant_code
FROM "OrderItem";

-- 4. 显示最近的几个订单项目以验证修复
SELECT 
    oi.id,
    oi."orderId",
    oi."productCode",
    oi."variantCode",
    oi."variantText",
    o."createdAt" as order_created_at
FROM "OrderItem" oi
JOIN "Order" o ON oi."orderId" = o.id
ORDER BY o."createdAt" DESC
LIMIT 5;
