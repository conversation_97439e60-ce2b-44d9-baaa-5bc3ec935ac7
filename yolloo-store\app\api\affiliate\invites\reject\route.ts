import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for rejecting an invite
const rejectInviteSchema = z.object({
  inviteCode: z.string(),
});

// POST - Reject an invite
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validationResult = rejectInviteSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { inviteCode } = validationResult.data;
    
    // Find the invite
    const invite = await prisma.organizationInvite.findUnique({
      where: { inviteCode },
      include: {
        organization: {
          select: {
            name: true,
          },
        },
      },
    });
    
    if (!invite) {
      return NextResponse.json({ error: "Invite not found" }, { status: 404 });
    }
    
    // Check if invite is still valid
    if (invite.status !== "PENDING") {
      return NextResponse.json(
        { error: `Invite is already ${invite.status.toLowerCase()}` },
        { status: 400 }
      );
    }
    
    // Check if the invite is for the current user
    if (invite.email) {
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { email: true },
      });
      
      if (user?.email !== invite.email) {
        return NextResponse.json(
          { error: "This invite is not for you" },
          { status: 403 }
        );
      }
    }
    
    // If the invite has an affiliateId, check if it's for the current user
    if (invite.affiliateId) {
      const affiliate = await prisma.affiliateProfile.findUnique({
        where: { id: invite.affiliateId },
        select: { userId: true },
      });
      
      if (affiliate?.userId !== session.user.id) {
        return NextResponse.json(
          { error: "This invite is not for you" },
          { status: 403 }
        );
      }
    }
    
    // Update invite status to REJECTED
    await prisma.organizationInvite.update({
      where: { id: invite.id },
      data: { status: "REJECTED" },
    });
    
    return NextResponse.json({
      success: true,
      message: `You have rejected the invitation to join ${invite.organization.name}`,
    });
  } catch (error) {
    console.error("Error rejecting invite:", error);
    return NextResponse.json(
      { error: "Failed to reject invite" },
      { status: 500 }
    );
  }
} 