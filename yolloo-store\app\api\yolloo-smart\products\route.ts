import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { ProductStatus } from '@prisma/client';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url);

        // 分页参数
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '12');
        const skip = (page - 1) * limit;

        // 过滤参数
        const searchQuery = searchParams.get('search') || '';
        const regionType = searchParams.get('regionType') || 'all';
        const countryFilter = searchParams.get('country') || '';
        const sortBy = searchParams.get('sort') || 'default';

        // 构建基础查询条件 - 与products API保持一致
        const whereCondition: any = {
            AND: [
                { status: ProductStatus.ACTIVE },
                { off_shelve: false },
                {
                    category: {
                        name: {
                            in: ['data', 'other'] // 与products API保持一致
                        }
                    }
                }
            ]
        };

        // 搜索条件
        if (searchQuery) {
            whereCondition.AND.push({
                OR: [
                    { name: { contains: searchQuery, mode: 'insensitive' } },
                    { description: { contains: searchQuery, mode: 'insensitive' } },
                    { websiteDescription: { contains: searchQuery, mode: 'insensitive' } },
                    { country: { contains: searchQuery, mode: 'insensitive' } },
                    { sku: { contains: searchQuery, mode: 'insensitive' } },
                    {
                        specifications: {
                            path: ['odooProductCode'],
                            string_contains: searchQuery
                        }
                    }
                ]
            });
        }

        // 国家过滤
        if (countryFilter) {
            whereCondition.AND.push({
                country: {
                    contains: countryFilter,
                    mode: 'insensitive'
                }
            });
        }

        // 排序条件
        let orderBy: any = [{ createdAt: 'asc' }];
        switch (sortBy) {
            case 'price-asc':
                orderBy = [{ price: 'asc' }];
                break;
            case 'price-desc':
                orderBy = [{ price: 'desc' }];
                break;
            case 'name-asc':
                orderBy = [{ name: 'asc' }];
                break;
            case 'name-desc':
                orderBy = [{ name: 'desc' }];
                break;
        }

        // 始终获取所有数据，然后进行过滤和分页
        // 并行查询产品和总数
        const [allProducts, totalCount] = await Promise.all([
            prisma.product.findMany({
                where: whereCondition,
                select: {
                    id: true,
                    name: true,
                    description: true,
                    websiteDescription: true,
                    price: true,
                    off_shelve: true,
                    parameters: true,
                    category: {
                        select: {
                            name: true
                        }
                    },
                    country: true,
                    countryCode: true,
                    dataSize: true,
                    planType: true,
                },
                orderBy,
            }),
            prisma.product.count({
                where: whereCondition
            })
        ]);

        // 应用区域类型过滤
        let filteredProducts = allProducts;
        if (regionType !== 'all') {
            filteredProducts = allProducts.filter(product => {
                const isMultiRegion = isMultiRegionPackage(product);
                if (regionType === 'single') return !isMultiRegion;
                if (regionType === 'multi') return isMultiRegion;
                return true;
            });
        }

        // 计算过滤后的总数
        const filteredTotal = filteredProducts.length;

        // 应用分页到过滤后的结果
        const paginatedProducts = filteredProducts.slice(skip, skip + limit);

        const totalPages = Math.ceil(filteredTotal / limit);

        return NextResponse.json({
            products: paginatedProducts,
            pagination: {
                page,
                limit,
                total: filteredTotal,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1
            }
        });

    } catch (error) {
        console.error('Error fetching Yolloo Smart products:', error);
        return NextResponse.json(
            { error: 'Failed to fetch products' },
            { status: 500 }
        );
    }
}

// 辅助函数：判断是否为多区域产品
function isMultiRegionPackage(product: { country?: string | null; countryCode?: string | null }) {
    if (product.country) {
        const countries = product.country.split(/[,;]/).map(c => c.trim()).filter(Boolean);
        if (countries.length > 1) return true;
    }

    if (product.countryCode) {
        const countryCodes = product.countryCode.split(/[,;]/).map(c => c.trim()).filter(Boolean);
        if (countryCodes.length > 1) return true;
    }

    return false;
}
