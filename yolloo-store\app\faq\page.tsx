import React from 'react'

export default function FAQPage() {
  const faqs = [
    {
      question: "What is Yolloo eSIM?",
      answer: "Yolloo eSIM is a digital SIM card service that allows you to connect to mobile networks without a physical SIM card. It's perfect for travelers and anyone who needs flexible mobile data connectivity."
    },
    {
      question: "Which devices are compatible with eSIM?",
      answer: "Most modern smartphones support eSIM, including recent iPhone models (iPhone XS and newer), Google Pixel devices (Pixel 3 and newer), and many Samsung Galaxy devices. Check your device settings or manufacturer's website for eSIM compatibility."
    },
    {
      question: "How do I activate my eSIM?",
      answer: "After purchase, you'll receive a QR code. On your device, go to Settings > Cellular/Mobile Data > Add eSIM/Cellular Plan, then scan the QR code. Follow the on-screen instructions to complete activation."
    },
    {
      question: "Can I use multiple eSIMs?",
      answer: "Yes, most eSIM-compatible devices can store multiple eSIM profiles, though usually only one can be active at a time alongside a physical SIM card."
    },
    {
      question: "What happens if I change phones?",
      answer: "You'll need to deactivate your eSIM on your old device and reactivate it on your new device using the original QR code. Contact support if you need help with this process."
    },
    {
      question: "Do you offer refunds?",
      answer: "Due to the digital nature of our service, we generally don't offer refunds once the eSIM has been activated. Please ensure your device is compatible before purchasing."
    },
    {
      question: "How long are your eSIM plans valid?",
      answer: "Plan validity varies by package. We offer plans ranging from 7 days to 30 days. The validity period starts from the time of activation, not purchase."
    },
    {
      question: "What countries do you cover?",
      answer: "We offer coverage in multiple countries worldwide. You can check specific country coverage and pricing on our plans page before making a purchase."
    },
    {
      question: "Can I top up my data plan?",
      answer: "Yes, you can purchase additional data through your account dashboard if you need more data during your plan period."
    },
    {
      question: "What speeds can I expect?",
      answer: "Data speeds vary by location and network conditions. We provide 4G/LTE connectivity in most locations, with 5G available in select areas where supported."
    }
  ]

  return (
      <div className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <h1 className="text-4xl font-bold heading-gradient mb-8">Frequently Asked Questions</h1>
            
            <div className="space-y-8">
              {faqs.map((faq, index) => (
                <div key={index} className="border-b border-gray-200 pb-8 last:border-b-0">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">{faq.question}</h2>
                  <p className="text-gray-600">{faq.answer}</p>
                </div>
              ))}
            </div>

            <div className="mt-12 p-6 bg-blue-50 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Still Have Questions?</h2>
              <p className="text-gray-600 mb-4">
                If you couldn't find the answer you were looking for, please don't hesitate to contact our support team.
              </p>
              <div className="space-y-2">
                <p className="text-gray-600">
                  Email: <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
                </p>
                <p className="text-gray-600">
                  Visit our <a href="/help" className="text-blue-600 hover:underline">Help Center</a> for more detailed information.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
  )
} 