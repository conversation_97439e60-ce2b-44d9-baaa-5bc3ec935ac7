// Odoo 服务配置
export const ODOO_CONFIG = {
  address: process.env.ODOO_ADDRESS || '',
  channelId: process.env.ODOO_CHANNEL_ID || '',
  channelLanguage: process.env.ODOO_CHANNEL_LANGUAGE || 'en_US',
  authSecret: process.env.ODOO_AUTH_SECRET || '',
  signMethod: process.env.ODOO_SIGN_METHOD || 'md5',
} as const;

// 商品类型
export const PRODUCT_TYPES = ['esim', 'data', 'effective_date', 'external_data', 'other', 'esim-card'] as const;
export type ProductType = typeof PRODUCT_TYPES[number];

// 商品同步请求参数
export const SYNC_PARAMS = {
  defaultStart: 0,
  defaultLength: 500,
  maxLength: 5000,
} as const;

// 商品状态
export const PRODUCT_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
} as const;

// 商品分类默认值
export const CATEGORY_DEFAULTS = {
  defaultCategory: 'default',
  defaultDescription: (category: string) => `Category for ${category} products`,
} as const;

// 商品默认值
export const PRODUCT_DEFAULTS = {
  currency: 'USD',
  stock: 999,
} as const; 