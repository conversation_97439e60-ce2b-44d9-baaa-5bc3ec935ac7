import { format } from "date-fns"
import { DateFormatter } from "@/lib/utils"
import { prisma } from "@/lib/prisma"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

async function getSubscribers() {
  const subscriptions = await prisma.preSaleSubscription.findMany({
    orderBy: {
      subscribedAt: "desc",
    },
    include: {
      user: true,
    },
  })

  // Get all unique referral codes
  const referralCodes = subscriptions
    .map((sub) => sub.referralCode)
    .filter((code): code is string => !!code)

  // Get affiliates for these referral codes
  const affiliates = referralCodes.length > 0
    ? await prisma.affiliateProfile.findMany({
        where: {
          code: {
            in: referralCodes,
          },
        },
        include: {
          user: true,
        },
      })
    : []

  // Create a map of referral codes to affiliate names
  const referrerMap = new Map(
    affiliates.map((affiliate) => [
      affiliate.code,
      affiliate.user.name || affiliate.user.email || "Unknown",
    ])
  )

  return subscriptions.map((subscription) => ({
    ...subscription,
    referrerName: subscription.referralCode
      ? referrerMap.get(subscription.referralCode) || "Unknown"
      : null,
  }))
}

export default async function SubscribersPage() {
  const subscribers = await getSubscribers()

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Presale Subscribers</h2>
        <p className="text-muted-foreground">
          Manage and view all presale subscribers.
        </p>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Email</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Referral Code</TableHead>
              <TableHead>Referrer</TableHead>
              <TableHead>Subscribed At</TableHead>
              <TableHead>Converted to User</TableHead>
              <TableHead>IP Address</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {subscribers.map((subscriber) => (
              <TableRow key={subscriber.id}>
                <TableCell>{subscriber.email}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      subscriber.status === "CONFIRMED"
                        ? "success"
                        : subscriber.status === "UNSUBSCRIBED"
                        ? "destructive"
                        : "default"
                    }
                  >
                    {subscriber.status}
                  </Badge>
                </TableCell>
                <TableCell>{subscriber.referralCode || "-"}</TableCell>
                <TableCell>{subscriber.referrerName || "-"}</TableCell>
                <TableCell>
                  {DateFormatter.withTimezone(subscriber.subscribedAt)}
                </TableCell>
                <TableCell>
                  <Badge variant={subscriber.convertedToUser ? "success" : "secondary"}>
                    {subscriber.convertedToUser ? "Yes" : "No"}
                  </Badge>
                </TableCell>
                <TableCell>{subscriber.ipAddress || "-"}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}