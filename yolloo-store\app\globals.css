@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 347 71% 45%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 347 71% 45%;
    --radius: 0.5rem;
  }

  /* 禁用密码输入框的浏览器内置控件 */
  input[type="password"]::-ms-reveal,
  input[type="password"]::-ms-clear {
    display: none;
  }

  input[type="password"]::-webkit-contacts-auto-fill-button,
  input[type="password"]::-webkit-credentials-auto-fill-button {
    visibility: hidden;
    display: none !important;
    pointer-events: none;
    height: 0;
    width: 0;
    margin: 0;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 347 71% 45%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 347 71% 45%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 确保移动导航菜单始终显示在页面内容之上 */
.mobile-menu {
  position: relative;
  z-index: 1001;
}

/* 修复移动菜单导航层级问题 */
#__next, main, div, section {
  position: relative;
  z-index: auto;
}

/* 移动导航菜单链接样式 */
.mobile-nav-link {
  position: relative;
  overflow: hidden;
}

.mobile-nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, var(--primary), transparent);
  transition: width 0.3s ease;
  border-radius: 2px;
}

.mobile-nav-link:hover::after,
.mobile-nav-link.active::after {
  width: 100%;
}

.mobile-nav-link .icon-container {
  transition: all 0.2s ease;
}

.mobile-nav-link:hover .icon-container,
.mobile-nav-link.active .icon-container {
  transform: translateY(-2px);
}

/* 移动导航菜单动画 */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.mobile-nav-menu {
  animation: slideIn 0.3s ease forwards;
}

.mobile-nav-backdrop {
  animation: fadeIn 0.2s ease forwards;
}

@layer components {
  .container {
    @apply mx-auto max-w-[1400px] px-4 sm:px-6 lg:px-8;
  }

  .heading-gradient {
    @apply bg-gradient-to-r from-pink-500 to-rose-500 bg-clip-text text-transparent inline-block pb-1;
  }

  .card-hover {
    @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
  }

  .nav-link {
    @apply text-sm font-medium text-muted-foreground transition-colors hover:text-primary;
  }

  .button-gradient {
    @apply bg-gradient-to-r from-pink-500 to-rose-500 text-white hover:opacity-90;
  }

  .feature-card {
    @apply relative overflow-hidden rounded-2xl border bg-card p-6 shadow-sm transition-all duration-200 hover:shadow-md;
  }

  .feature-icon {
    @apply mb-5 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-primary/10 text-primary;
  }
}

/* 自定义动画 */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes float-delayed {
  0% {
    transform: translateY(-20px);
  }
  50% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-20px);
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 6s ease-in-out infinite;
}

.animate-cursor {
  animation: blink 1s step-end infinite;
}

/* 玻璃态效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 卡片悬浮效果 */
.hover-card {
  transition: all 0.3s ease;
}

.hover-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.dark .hover-card:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

/* 按钮样式 */
.custom-button {
  @apply rounded-full bg-primary px-8 py-3 text-sm font-medium text-white transition-all hover:bg-primary/90 hover:shadow-lg active:scale-95;
}

/* 导航链接样式 */
.nav-item {
  @apply relative text-sm font-medium text-muted-foreground transition-colors hover:text-primary;
}

.nav-item::after {
  content: '';
  @apply absolute bottom-0 left-0 h-0.5 w-0 bg-primary transition-all duration-300;
}

.nav-item:hover::after {
  @apply w-full;
}

/* 特性卡片样式 */
.feature-box {
  @apply relative overflow-hidden rounded-2xl border bg-card/50 backdrop-blur-sm;
}

.feature-box::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-br from-pink-500/5 to-transparent opacity-0 transition-opacity duration-300;
}

.feature-box:hover::before {
  @apply opacity-100;
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin !important; /* Firefox */
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent !important; /* Firefox */
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent !important;
  border-radius: 10px !important;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5) !important;
  border-radius: 10px !important;
  border: 2px solid transparent !important;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7) !important;
}

/* 暗色模式下的滚动条 */
.dark .custom-scrollbar {
  scrollbar-color: rgba(200, 200, 200, 0.3) transparent !important; /* Firefox */
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(200, 200, 200, 0.3) !important;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(200, 200, 200, 0.5) !important;
}

/* 更明显的滚动条样式 */
.visible-scrollbar,
.visible-scrollbar * {
  scrollbar-width: auto !important; /* Firefox */
  scrollbar-color: rgba(155, 155, 155, 0.7) rgba(155, 155, 155, 0.1) !important; /* Firefox */
}

.visible-scrollbar::-webkit-scrollbar,
.visible-scrollbar *::-webkit-scrollbar {
  width: 10px !important;
  height: 10px !important;
  display: block !important;
}

.visible-scrollbar::-webkit-scrollbar-track,
.visible-scrollbar *::-webkit-scrollbar-track {
  background: rgba(155, 155, 155, 0.1) !important;
  border-radius: 10px !important;
  margin: 3px !important;
}

.visible-scrollbar::-webkit-scrollbar-thumb,
.visible-scrollbar *::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.7) !important;
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  min-height: 40px !important;
}

.visible-scrollbar::-webkit-scrollbar-thumb:hover,
.visible-scrollbar *::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.9) !important;
}

/* 暗色模式下的明显滚动条 */
.dark .visible-scrollbar,
.dark .visible-scrollbar * {
  scrollbar-color: rgba(200, 200, 200, 0.5) rgba(50, 50, 50, 0.3) !important; /* Firefox */
}

.dark .visible-scrollbar::-webkit-scrollbar-track,
.dark .visible-scrollbar *::-webkit-scrollbar-track {
  background: rgba(50, 50, 50, 0.3) !important;
}

.dark .visible-scrollbar::-webkit-scrollbar-thumb,
.dark .visible-scrollbar *::-webkit-scrollbar-thumb {
  background-color: rgba(200, 200, 200, 0.5) !important;
}

.dark .visible-scrollbar::-webkit-scrollbar-thumb:hover,
.dark .visible-scrollbar *::-webkit-scrollbar-thumb:hover {
  background-color: rgba(200, 200, 200, 0.7) !important;
}

/* 针对Radix UI的Select组件滚动条样式 */
[data-radix-select-viewport] {
  scrollbar-width: auto !important;
  scrollbar-color: rgba(155, 155, 155, 0.7) rgba(155, 155, 155, 0.1) !important;
}

[data-radix-select-viewport]::-webkit-scrollbar {
  width: 10px !important;
  height: 10px !important;
  display: block !important;
}

[data-radix-select-viewport]::-webkit-scrollbar-track {
  background: rgba(155, 155, 155, 0.1) !important;
  border-radius: 10px !important;
  margin: 3px !important;
}

[data-radix-select-viewport]::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.7) !important;
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  min-height: 40px !important;
}

[data-radix-select-viewport]::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.9) !important;
}

.dark [data-radix-select-viewport] {
  scrollbar-color: rgba(200, 200, 200, 0.5) rgba(50, 50, 50, 0.3) !important;
}

.dark [data-radix-select-viewport]::-webkit-scrollbar-track {
  background: rgba(50, 50, 50, 0.3) !important;
}

.dark [data-radix-select-viewport]::-webkit-scrollbar-thumb {
  background-color: rgba(200, 200, 200, 0.5) !important;
}

.dark [data-radix-select-viewport]::-webkit-scrollbar-thumb:hover {
  background-color: rgba(200, 200, 200, 0.7) !important;
}

/* 滚动动画 */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-33.33%);
  }
}

@keyframes scroll-right {
  0% {
    transform: translateX(-33.33%);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

.animate-scroll-left {
  animation: scroll-left 60s linear infinite;
}

.animate-scroll-right {
  animation: scroll-right 60s linear infinite;
}

.pause-animation {
  animation-play-state: paused;
}

.hover\:pause-animation:hover {
  animation-play-state: paused;
}

/* 隐藏滚动条 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}
