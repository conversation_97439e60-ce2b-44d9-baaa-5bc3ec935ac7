"use client"

import { useState, useEffect, ChangeEvent, KeyboardEvent } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { useCart, CartItem } from "@/lib/hooks/use-cart"
import { formatPrice } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import type { CartItem as PrismaCartItem, Product, ProductVariant } from "@prisma/client"
import { Input } from "@/components/ui/input"

type CartItemWithRelations = PrismaCartItem & {
  product: Product
  variant: ProductVariant | null
}

interface CartButtonProps {
  initialItems?: CartItemWithRelations[] | null
}

export function CartButton({ initialItems }: CartButtonProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({})
  const [quantityInputs, setQuantityInputs] = useState<Record<string, string>>({})
  const { items, addItem, removeItem, updateQuantity, initializeCart } = useCart()
  const totalItems = items.reduce((acc, item) => acc + item.quantity, 0)
  const totalPrice = items.reduce((acc, item) => acc + item.price * item.quantity, 0)

  // 初始化购物车数据
  useEffect(() => {
    if (initialItems) {
      const formattedItems = initialItems.map(item => ({
        id: item.id,
        productId: item.productId,
        name: item.product.name,
        price: Number(item.variant?.price || item.product.price),
        quantity: item.quantity,
        image: item.product.images?.[0],
        stock: item.product.stock,
        variant: item.variant ? {
          id: item.variant.id,
          price: Number(item.variant.price),
          duration: item.variant.duration,
          durationType: item.variant.durationType
        } : undefined
      }))
      initializeCart(formattedItems)
    }
  }, [initialItems, initializeCart])

  // 避免水合不匹配
  useEffect(() => {
    setIsClient(true)

    // 检测是否为移动设备
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768) // 使用 md 断点 (768px)
    }

    // 初始检测
    checkIfMobile()

    // 监听窗口大小变化
    window.addEventListener('resize', checkIfMobile)

    // 清理事件监听器
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  // Initialize quantity inputs
  useEffect(() => {
    const initialInputs: Record<string, string> = {}
    items.forEach(item => {
      initialInputs[item.id] = item.quantity.toString()
    })
    setQuantityInputs(initialInputs)
  }, [items])

  const handleImageError = (itemId: string) => {
    setImageErrors(prev => ({ ...prev, [itemId]: true }))
  }

  const handleQuantityInputChange = (e: ChangeEvent<HTMLInputElement>, itemId: string) => {
    // Only allow numeric input
    const value = e.target.value.replace(/[^0-9]/g, '')
    setQuantityInputs(prev => ({ ...prev, [itemId]: value }))
  }

  const handleQuantityInputBlur = (item: CartItem) => {
    const value = quantityInputs[item.id] || '1'
    let newQuantity = parseInt(value, 10)

    // Handle invalid input
    if (isNaN(newQuantity) || value === '') {
      newQuantity = item.quantity
      setQuantityInputs(prev => ({ ...prev, [item.id]: item.quantity.toString() }))
    } else if (newQuantity > item.stock) {
      newQuantity = item.stock
      setQuantityInputs(prev => ({ ...prev, [item.id]: item.stock.toString() }))
    } else if (newQuantity < 1) {
      newQuantity = 1
      setQuantityInputs(prev => ({ ...prev, [item.id]: '1' }))
    }

    updateQuantity(item.productId, newQuantity, item.variant?.id, item.uid)
  }

  const handleQuantityInputKeyDown = (e: KeyboardEvent<HTMLInputElement>, item: CartItem) => {
    if (e.key === 'Enter') {
      handleQuantityInputBlur(item)
      e.currentTarget.blur()
    }
  }

  const handleIncreaseQuantity = (item: CartItem) => {
    if (item.quantity >= item.stock) return
    const newQuantity = item.quantity + 1
    updateQuantity(item.productId, newQuantity, item.variant?.id, item.uid)
    setQuantityInputs(prev => ({ ...prev, [item.id]: newQuantity.toString() }))
  }

  const handleDecreaseQuantity = (item: CartItem) => {
    if (item.quantity <= 1) return
    const newQuantity = item.quantity - 1
    updateQuantity(item.productId, newQuantity, item.variant?.id, item.uid)
    setQuantityInputs(prev => ({ ...prev, [item.id]: newQuantity.toString() }))
  }

  const handleRemoveItem = (item: CartItem) => {
    removeItem(item.productId, item.variant?.id, item.uid)
  }

  if (!isClient) {
    return null
  }

  return (
    <div
      className="relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Button variant="outline" asChild>
        <Link href="/cart" className="flex items-center gap-2">
          <div className="relative">
            <Icons.cart className="h-4 w-4" />
            {totalItems > 0 && (
              <span className="absolute -right-2 -top-2 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground">
                {totalItems}
              </span>
            )}
          </div>
          <span className="hidden sm:inline">Cart</span>
        </Link>
      </Button>

      <AnimatePresence>
        {isHovered && items.length > 0 && !isMobile && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 z-50 mt-2 w-80 rounded-lg border bg-background p-4 shadow-lg"
          >
            <div className="space-y-4">
              {items.slice(0, 3).map((item) => (
                <div key={item.id} className="flex items-center gap-4">
                  <div className="relative aspect-square h-16 w-16 min-w-fit overflow-hidden rounded-lg border">
                    {item.image && !imageErrors[item.id] ? (
                      <img
                        src={item.image}
                        alt={item.name}
                        className="h-full w-full object-cover"
                        onError={() => handleImageError(item.id)}
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center bg-secondary">
                        <Icons.image className="h-8 w-8 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="line-clamp-1 text-sm font-medium">{item.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatPrice(item.price)}
                    </p>
                    <div className="mt-1 flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => handleDecreaseQuantity(item)}
                      >
                        <Icons.minus className="h-3 w-3" />
                      </Button>
                      <div className="w-10 h-6 flex items-center justify-center rounded-md border bg-background overflow-hidden">
                        <Input
                          type="text"
                          value={quantityInputs[item.id] || item.quantity.toString()}
                          onChange={(e) => handleQuantityInputChange(e, item.id)}
                          onBlur={() => handleQuantityInputBlur(item)}
                          onKeyDown={(e) => handleQuantityInputKeyDown(e, item)}
                          className="h-full w-full border-0 text-center p-0 text-sm focus-visible:ring-0 focus-visible:ring-offset-0"
                          aria-label="Product quantity"
                        />
                      </div>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => handleIncreaseQuantity(item)}
                      >
                        <Icons.add className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 ml-2"
                        onClick={() => handleRemoveItem(item)}
                      >
                        <Icons.trash className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
              {items.length > 3 && (
                <p className="text-sm text-muted-foreground">
                  and {items.length - 3} more items
                </p>
              )}
              <div className="border-t pt-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Total</span>
                  <span className="text-sm font-bold">{formatPrice(totalPrice)}</span>
                </div>
                <div className="mt-4 flex flex-col gap-2">
                  <Button className="w-full" asChild>
                    <Link href="/cart">View Cart</Link>
                  </Button>
                  <Button className="w-full" variant="outline" asChild>
                    <Link href="/checkout">Checkout</Link>
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}