<svg width="3831" height="800" viewBox="0 0 3831 800" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="3831" height="800" fill="#1A1A1A"/>
<g clip-path="url(#clip0_167_5205)">
<rect width="3831" height="800" fill="url(#paint0_linear_167_5205)"/>
<g filter="url(#filter0_f_167_5205)">
<rect width="2298.6" height="480" transform="translate(-383.1 -240)" fill="url(#paint1_radial_167_5205)"/>
</g>
<g filter="url(#filter1_f_167_5205)">
<rect width="873" height="480.712" transform="translate(3341 479.525)" fill="url(#paint2_radial_167_5205)"/>
</g>
<g filter="url(#filter2_f_167_5205)">
<rect width="447" height="319.288" transform="translate(472 160.237)" fill="url(#paint3_radial_167_5205)"/>
</g>
<rect width="1153" height="800" transform="translate(1212 79.5251)" fill="url(#paint4_radial_167_5205)"/>
<rect width="499" height="602.967" transform="translate(2794 98.5164)" fill="url(#paint5_radial_167_5205)"/>
</g>
<defs>
<filter id="filter0_f_167_5205" x="-447.1" y="-304" width="2426.6" height="608" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="32" result="effect1_foregroundBlur_167_5205"/>
</filter>
<filter id="filter1_f_167_5205" x="3277" y="415.525" width="1001" height="608.712" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="32" result="effect1_foregroundBlur_167_5205"/>
</filter>
<filter id="filter2_f_167_5205" x="408" y="96.2373" width="575" height="447.288" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="32" result="effect1_foregroundBlur_167_5205"/>
</filter>
<linearGradient id="paint0_linear_167_5205" x1="1915.5" y1="0" x2="1915.5" y2="800" gradientUnits="userSpaceOnUse">
<stop stop-color="#F43F5E" stop-opacity="0.3"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint1_radial_167_5205" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1149.3 240) rotate(11.7952) scale(1174.09 469.865)">
<stop stop-color="#FF5050" stop-opacity="0.4"/>
<stop offset="0.7" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial_167_5205" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(436.5 240.356) rotate(28.8391) scale(498.3 421.093)">
<stop stop-color="#FF5050" stop-opacity="0.35"/>
<stop offset="0.7" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint3_radial_167_5205" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(223.5 159.644) rotate(35.5379) scale(274.661 259.815)">
<stop stop-color="#FF5050" stop-opacity="0.25"/>
<stop offset="0.7" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint4_radial_167_5205" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(576.5 400) rotate(34.7545) scale(701.678 657.281)">
<stop stop-color="#FF5050" stop-opacity="0.2"/>
<stop offset="0.5" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint5_radial_167_5205" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(249.5 301.484) rotate(50.3897) scale(391.334 384.429)">
<stop stop-color="#FF5050" stop-opacity="0.2"/>
<stop offset="0.5" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_167_5205">
<rect width="3831" height="800" fill="white"/>
</clipPath>
</defs>
</svg>
