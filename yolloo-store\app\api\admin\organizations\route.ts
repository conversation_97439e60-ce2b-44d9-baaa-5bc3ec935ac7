import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for creating an organization
const createOrgSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().optional(),
  logo: z.string().optional(),
  commissionRate: z.number().min(0).max(1).optional(),
  discountRate: z.number().min(0).max(1).optional(),
  adminEmail: z.string().email("Invalid email address"),
});

// GET - List all organizations (admin only)
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }
    
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    
    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Build search filter
    const searchFilter = search
      ? {
          OR: [
            { name: { contains: search, mode: "insensitive" as const } },
            { code: { contains: search, mode: "insensitive" as const } },
          ],
        }
      : {};
    
    // Get organizations with pagination and search
    const organizations = await prisma.affiliateOrganization.findMany({
      where: searchFilter,
      include: {
        _count: {
          select: { members: true },
        },
      },
      orderBy: { createdAt: "desc" },
      skip,
      take: limit,
    });
    
    // Get total count for pagination
    const totalCount = await prisma.affiliateOrganization.count({
      where: searchFilter,
    });
    
    return NextResponse.json({
      organizations,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching organizations:", error);
    return NextResponse.json(
      { error: "Failed to fetch organizations" },
      { status: 500 }
    );
  }
}

// POST - Create a new organization (admin only)
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validationResult = createOrgSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { name, description, logo, commissionRate, discountRate, adminEmail } = validationResult.data;
    
    // Check if admin user exists
    const adminUser = await prisma.user.findUnique({
      where: { email: adminEmail },
      include: { affiliate: true },
    });
    
    if (!adminUser) {
      return NextResponse.json(
        { error: "Admin user not found. The user must have an existing account." },
        { status: 404 }
      );
    }
    
    // Generate a unique code for the organization
    const code = `ORG-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
    
    // Create the organization
    const organization = await prisma.affiliateOrganization.create({
      data: {
        name,
        description,
        logo,
        code,
        commissionRate: commissionRate ?? 0.12,
        discountRate: discountRate ?? 0.05,
      },
    });
    
    // Connect the admin user to the organization
    if (adminUser.affiliate) {
      // Update existing affiliate profile
      await prisma.affiliateProfile.update({
        where: { id: adminUser.affiliate.id },
        data: {
          organizationId: organization.id,
          isAdmin: true,
        },
      });
    } else {
      // Create new affiliate profile
      const affiliateCode = `${adminUser.name?.substring(0, 3) || "ADM"}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
      
      await prisma.affiliateProfile.create({
        data: {
          userId: adminUser.id,
          code: affiliateCode,
          organizationId: organization.id,
          isAdmin: true,
        },
      });
    }
    
    // Return organization with updated _count
    const updatedOrganization = await prisma.affiliateOrganization.findUnique({
      where: { id: organization.id },
      include: {
        _count: {
          select: { members: true }
        }
      }
    });
    
    return NextResponse.json(updatedOrganization, { status: 201 });
  } catch (error) {
    console.error("Error creating organization:", error);
    return NextResponse.json(
      { error: "Failed to create organization" },
      { status: 500 }
    );
  }
} 