import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma.service';
import { ReverseGeocodeDto } from './dto/reverse-geocode.dto';
import { RequestContext } from '../common/interfaces/context.interface';

@Injectable()
export class LocationService {
  private readonly logger = new Logger(LocationService.name);
  private readonly googleMapsApiKey: string | undefined;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    this.googleMapsApiKey = this.configService.get<string>('GOOGLE_MAPS_API_KEY');
    if (!this.googleMapsApiKey) {
      this.logger.warn('GOOGLE_MAPS_API_KEY is not set. Geocoding service will not work properly.');
    }
  }

  async reverseGeocode(dto: ReverseGeocodeDto, ctx: RequestContext) {
    try {
      this.logger.log(`接收到反向地理编码请求，坐标: ${dto.lat},${dto.lng}`);

      // 如果有Google Maps API Key，尝试使用真实的地理编码服务
      if (this.googleMapsApiKey) {
        try {
          const realResult = await this.performRealReverseGeocode(dto, ctx);
          if (realResult) {
            this.logger.log('使用Google Maps API返回真实地理编码数据');
            return realResult;
          }
        } catch (error) {
          this.logger.warn('Google Maps API调用失败，使用fallback数据:', error.message);
        }
      }

      this.logger.log('尝试从数据库获取地理编码数据');

      // 尝试从数据库的Country表获取真实地理数据
      const location = await this.getLocationFromDatabase(dto.lat, dto.lng, ctx);
      if (location) {
        return { locations: [location] };
      }

      this.logger.log('无法获取地理编码数据');

      return {
        locations: [],
        error: 'Unable to geocode location'
      };
    } catch (error) {
      this.logger.error('反向地理编码处理过程中发生错误', error);
      return {
        locations: [{
          formattedAddress: '位置数据处理错误',
          placeId: '',
          country: '未知',
          countryCode: '',
          administrativeArea1: '',
          administrativeArea2: '',
          locality: '',
          coordinates: {
            lat: dto.lat,
            lng: dto.lng,
          },
        }]
      };
    }
  }

  private async performRealReverseGeocode(dto: ReverseGeocodeDto, ctx: RequestContext) {
    try {
      const language = ctx?.language?.startsWith('zh') ? 'zh-CN' : 'en';
      const url = 'https://maps.googleapis.com/maps/api/geocode/json';

      const params = {
        latlng: `${dto.lat},${dto.lng}`,
        key: this.googleMapsApiKey,
        language: language,
        result_type: 'street_address|route|neighborhood|locality|administrative_area_level_2|administrative_area_level_1|country',
      };

      this.logger.log(`调用Google Maps API: ${url}`);

      const response = await firstValueFrom(
        this.httpService.get(url, { params, timeout: 5000 })
      );

      if (response.data.status === 'OK' && response.data.results.length > 0) {
        return this.formatGeocodingResults(response.data.results);
      } else {
        this.logger.warn(`Google Maps API返回状态: ${response.data.status}`);
        return null;
      }
    } catch (error) {
      this.logger.error('Google Maps API调用失败:', error);
      throw error;
    }
  }

  private formatGeocodingResults(results: any[]) {
    return {
      locations: results.map((result: any) => {
        // 提取地址组件
        const addressComponents = result.address_components || [];

        // 初始化地址对象
        const location = {
          formattedAddress: result.formatted_address,
          placeId: result.place_id,
          country: '',
          countryCode: '',
          administrativeArea1: '', // 省/州
          administrativeArea2: '', // 县/区
          locality: '', // 城市
          coordinates: {
            lat: result.geometry?.location?.lat,
            lng: result.geometry?.location?.lng,
          },
        };

        // 从地址组件中提取信息
        for (const component of addressComponents) {
          const types = component.types || [];

          if (types.includes('country')) {
            location.country = component.long_name;
            location.countryCode = component.short_name;
          } else if (types.includes('administrative_area_level_1')) {
            location.administrativeArea1 = component.long_name;
          } else if (types.includes('administrative_area_level_2')) {
            location.administrativeArea2 = component.long_name;
          } else if (types.includes('locality')) {
            location.locality = component.long_name;
          }
        }

        return location;
      }),
    };
  }

  async getNearbyPlaces(lat: number, lng: number, radius: number = 1000, type?: string, ctx?: RequestContext) {
    try {
      this.logger.log(`获取附近地点请求，坐标: ${lat},${lng}, 半径: ${radius}m`);

      // 如果有Google Maps API Key，尝试使用真实的附近地点搜索
      if (this.googleMapsApiKey) {
        try {
          const realResult = await this.performRealNearbySearch(lat, lng, radius, type, ctx);
          if (realResult) {
            this.logger.log('使用Google Maps API返回真实附近地点数据');
            return realResult;
          }
        } catch (error) {
          this.logger.warn('Google Maps Nearby Search API调用失败，使用fallback数据:', error.message);
        }
      }

      this.logger.log('无法获取附近地点数据');
      return {
        places: [],
        error: 'Unable to fetch nearby places'
      };

    } catch (error) {
      this.logger.error('获取附近地点过程中发生错误', error);
      return {
        places: [],
        status: 'error',
        message: '获取附近地点失败',
      };
    }
  }

  private async performRealNearbySearch(lat: number, lng: number, radius: number, type?: string, ctx?: RequestContext) {
    try {
      const language = ctx?.language?.startsWith('zh') ? 'zh-CN' : 'en';
      const url = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';

      const params: any = {
        location: `${lat},${lng}`,
        radius: radius,
        key: this.googleMapsApiKey,
        language: language,
      };

      if (type) {
        params.type = type;
      }

      this.logger.log(`调用Google Places API: ${url}`);

      const response = await firstValueFrom(
        this.httpService.get(url, { params, timeout: 10000 })
      );

      if (response.data.status === 'OK') {
        return {
          places: response.data.results.map((place: any) => ({
            placeId: place.place_id,
            name: place.name,
            vicinity: place.vicinity,
            types: place.types,
            rating: place.rating,
            priceLevel: place.price_level,
            coordinates: {
              lat: place.geometry.location.lat,
              lng: place.geometry.location.lng,
            },
            openNow: place.opening_hours?.open_now,
            photos: place.photos?.map((photo: any) => ({
              reference: photo.photo_reference,
              width: photo.width,
              height: photo.height,
            })) || [],
          })),
          status: 'success',
        };
      } else {
        this.logger.warn(`Google Places API返回状态: ${response.data.status}`);
        return null;
      }
    } catch (error) {
      this.logger.error('Google Places API调用失败:', error);
      throw error;
    }
  }



  private async getLocationFromDatabase(lat: number, lng: number, ctx?: RequestContext) {
    try {
      const isZh = ctx?.language?.startsWith('zh');

      // 根据坐标范围查找对应的国家
      let countryCode: string | null = null;

      // 中国大陆范围的坐标
      if (lat >= 18 && lat <= 53 && lng >= 73 && lng <= 135) {
        countryCode = 'CN';
      }
      // 美国范围的坐标
      else if (lat >= 24 && lat <= 49 && lng >= -125 && lng <= -66) {
        countryCode = 'US';
      }
      // 日本范围的坐标
      else if (lat >= 24 && lat <= 46 && lng >= 123 && lng <= 146) {
        countryCode = 'JP';
      }

      if (countryCode) {
        const country = await this.prisma.country.findUnique({
          where: { code: countryCode },
          include: { continent: true },
        });

        if (country) {
          return {
            formattedAddress: isZh ? `${country.nameZh}` : `${country.nameEn}`,
            placeId: `db-place-${country.code}`,
            country: isZh ? country.nameZh : country.nameEn,
            countryCode: country.code,
            administrativeArea1: isZh ? country.nameZh : country.nameEn,
            administrativeArea2: '',
            locality: isZh ? country.nameZh : country.nameEn,
            geometry: {
              lat: lat,
              lng: lng,
            },
          };
        }
      }

      return null;
    } catch (error) {
      this.logger.error('Error fetching location from database:', error);
      return null;
    }
  }
}
