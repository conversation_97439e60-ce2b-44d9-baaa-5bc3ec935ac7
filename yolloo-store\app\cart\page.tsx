"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { CartItem } from "@/components/cart/cart-item"
import { useCart } from "@/lib/hooks/use-cart"
import { formatPrice } from "@/lib/utils"

export default function CartPage() {
  const { items } = useCart()
  const total = items.reduce((acc, item) => acc + item.price * item.quantity, 0)

  return (
    <div className="container py-8">
      <div className="grid gap-8 lg:grid-cols-12">
        <div className="lg:col-span-8">
          <div className="space-y-4">
            <h1 className="text-2xl font-bold">Shopping Cart</h1>
            {items.length === 0 ? (
              <Card className="p-6">
                <div className="text-center">
                  <p className="mb-4 text-muted-foreground">
                    Your cart is empty
                  </p>
                  <Button asChild>
                    <Link href="/products">Continue Shopping</Link>
                  </Button>
                </div>
              </Card>
            ) : (
              <div className="space-y-4">
                {items.map((item) => (
                  <Card key={item.id} className="p-4">
                    <CartItem item={item} />
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
        <div className="lg:col-span-4">
          <Card className="p-6">
            <h2 className="mb-4 text-lg font-semibold">Order Summary</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between border-b pb-4">
                <span className="text-muted-foreground">Subtotal</span>
                <span className="font-medium">{formatPrice(total)}</span>
              </div>
              <div className="flex items-center justify-between border-b pb-4">
                <span className="text-muted-foreground">Shipping</span>
                <span className="font-medium">Free</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Total</span>
                <span className="font-bold">{formatPrice(total)}</span>
              </div>
              {items.length === 0 ? (
                <Button className="w-full" size="lg" disabled>
                  Proceed to Checkout
                </Button>
              ) : (
                <Button className="w-full" size="lg" asChild>
                  <Link href="/checkout">Proceed to Checkout</Link>
                </Button>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}