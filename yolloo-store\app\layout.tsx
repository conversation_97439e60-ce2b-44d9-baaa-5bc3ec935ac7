import './globals.css'
import { Inter } from 'next/font/google'
import { Providers } from './providers'
import { cookies } from 'next/headers'
import { cn } from '@/lib/utils'
import { ClientLayout } from './components/client-layout'
import { StoreLayout } from './components/store-layout'

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: 'Yolloo Store',
  description: 'Your one-stop shop for all your needs',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const cookieStore = cookies();
  const hasReferralCode = cookieStore.has('referralCode');

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico?v=3" />
        <link rel="icon" href="/favicon.png?v=3" type="image/png" sizes="32x32" />
        <link rel="apple-touch-icon" href="/favicon.png?v=3" />
        {/* Rocket.Chat Livechat Script */}
        <script
          type="text/javascript"
          dangerouslySetInnerHTML={{
            __html: `
            (function(w, d, s, u) {
              w.RocketChat = function(c) { w.RocketChat._.push(c) }; w.RocketChat._ = []; w.RocketChat.url = u;
              var h = d.getElementsByTagName(s)[0], j = d.createElement(s);
              j.async = true; j.src = 'https://rocketchat.simmesh.com/livechat/rocketchat-livechat.min.js?_=201903270000';
              h.parentNode.insertBefore(j, h);
            })(window, document, 'script', 'https://rocketchat.simmesh.com/livechat');
            `
          }}
        />
      </head>
      <body className={cn(
        'min-h-screen bg-background font-sans antialiased',
        inter.className
      )}>
        <Providers>
          <ClientLayout hasReferralCode={hasReferralCode}>
            <StoreLayout>
              {children}
            </StoreLayout>
          </ClientLayout>
        </Providers>
      </body>
    </html>
  )
}

