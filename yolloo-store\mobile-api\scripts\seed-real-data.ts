import { PrismaClient, ProductStatus } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('开始初始化真实数据...');

  // 1. 初始化大洲数据
  console.log('初始化大洲数据...');
  const continents = [
    { code: 'asia', nameEn: 'Asia', nameZh: '亚洲', isActive: true },
    { code: 'europe', nameEn: 'Europe', nameZh: '欧洲', isActive: true },
    { code: 'america', nameEn: 'America', nameZh: '美洲', isActive: true },
    { code: 'africa', nameEn: 'Africa', nameZh: '非洲', isActive: true },
    { code: 'oceania', nameEn: 'Oceania', nameZh: '大洋洲', isActive: true },
  ];

  for (const continent of continents) {
    await prisma.continent.upsert({
      where: { code: continent.code },
      update: continent,
      create: continent,
    });
  }

  // 2. 初始化国家数据
  console.log('初始化国家数据...');
  const countries = [
    // 亚洲
    { code: 'JP', nameEn: 'Japan', nameZh: '日本', continentCode: 'asia', currency: 'JPY', timezone: 'Asia/Tokyo' },
    { code: 'KR', nameEn: 'South Korea', nameZh: '韩国', continentCode: 'asia', currency: 'KRW', timezone: 'Asia/Seoul' },
    { code: 'TH', nameEn: 'Thailand', nameZh: '泰国', continentCode: 'asia', currency: 'THB', timezone: 'Asia/Bangkok' },
    { code: 'SG', nameEn: 'Singapore', nameZh: '新加坡', continentCode: 'asia', currency: 'SGD', timezone: 'Asia/Singapore' },
    { code: 'MY', nameEn: 'Malaysia', nameZh: '马来西亚', continentCode: 'asia', currency: 'MYR', timezone: 'Asia/Kuala_Lumpur' },
    { code: 'VN', nameEn: 'Vietnam', nameZh: '越南', continentCode: 'asia', currency: 'VND', timezone: 'Asia/Ho_Chi_Minh' },
    { code: 'CN', nameEn: 'China', nameZh: '中国', continentCode: 'asia', currency: 'CNY', timezone: 'Asia/Shanghai' },
    { code: 'IN', nameEn: 'India', nameZh: '印度', continentCode: 'asia', currency: 'INR', timezone: 'Asia/Kolkata' },
    { code: 'ID', nameEn: 'Indonesia', nameZh: '印度尼西亚', continentCode: 'asia', currency: 'IDR', timezone: 'Asia/Jakarta' },
    { code: 'PH', nameEn: 'Philippines', nameZh: '菲律宾', continentCode: 'asia', currency: 'PHP', timezone: 'Asia/Manila' },

    // 欧洲
    { code: 'GB', nameEn: 'United Kingdom', nameZh: '英国', continentCode: 'europe', currency: 'GBP', timezone: 'Europe/London' },
    { code: 'FR', nameEn: 'France', nameZh: '法国', continentCode: 'europe', currency: 'EUR', timezone: 'Europe/Paris' },
    { code: 'DE', nameEn: 'Germany', nameZh: '德国', continentCode: 'europe', currency: 'EUR', timezone: 'Europe/Berlin' },
    { code: 'IT', nameEn: 'Italy', nameZh: '意大利', continentCode: 'europe', currency: 'EUR', timezone: 'Europe/Rome' },
    { code: 'ES', nameEn: 'Spain', nameZh: '西班牙', continentCode: 'europe', currency: 'EUR', timezone: 'Europe/Madrid' },
    { code: 'NL', nameEn: 'Netherlands', nameZh: '荷兰', continentCode: 'europe', currency: 'EUR', timezone: 'Europe/Amsterdam' },
    { code: 'CH', nameEn: 'Switzerland', nameZh: '瑞士', continentCode: 'europe', currency: 'CHF', timezone: 'Europe/Zurich' },
    { code: 'AT', nameEn: 'Austria', nameZh: '奥地利', continentCode: 'europe', currency: 'EUR', timezone: 'Europe/Vienna' },
    { code: 'BE', nameEn: 'Belgium', nameZh: '比利时', continentCode: 'europe', currency: 'EUR', timezone: 'Europe/Brussels' },
    { code: 'SE', nameEn: 'Sweden', nameZh: '瑞典', continentCode: 'europe', currency: 'SEK', timezone: 'Europe/Stockholm' },

    // 美洲
    { code: 'US', nameEn: 'United States', nameZh: '美国', continentCode: 'america', currency: 'USD', timezone: 'America/New_York' },
    { code: 'CA', nameEn: 'Canada', nameZh: '加拿大', continentCode: 'america', currency: 'CAD', timezone: 'America/Toronto' },
    { code: 'MX', nameEn: 'Mexico', nameZh: '墨西哥', continentCode: 'america', currency: 'MXN', timezone: 'America/Mexico_City' },
    { code: 'BR', nameEn: 'Brazil', nameZh: '巴西', continentCode: 'america', currency: 'BRL', timezone: 'America/Sao_Paulo' },
    { code: 'AR', nameEn: 'Argentina', nameZh: '阿根廷', continentCode: 'america', currency: 'ARS', timezone: 'America/Argentina/Buenos_Aires' },
    { code: 'CL', nameEn: 'Chile', nameZh: '智利', continentCode: 'america', currency: 'CLP', timezone: 'America/Santiago' },
    { code: 'CO', nameEn: 'Colombia', nameZh: '哥伦比亚', continentCode: 'america', currency: 'COP', timezone: 'America/Bogota' },
    { code: 'PE', nameEn: 'Peru', nameZh: '秘鲁', continentCode: 'america', currency: 'PEN', timezone: 'America/Lima' },

    // 大洋洲
    { code: 'AU', nameEn: 'Australia', nameZh: '澳大利亚', continentCode: 'oceania', currency: 'AUD', timezone: 'Australia/Sydney' },
    { code: 'NZ', nameEn: 'New Zealand', nameZh: '新西兰', continentCode: 'oceania', currency: 'NZD', timezone: 'Pacific/Auckland' },
    { code: 'FJ', nameEn: 'Fiji', nameZh: '斐济', continentCode: 'oceania', currency: 'FJD', timezone: 'Pacific/Fiji' },
    { code: 'PG', nameEn: 'Papua New Guinea', nameZh: '巴布亚新几内亚', continentCode: 'oceania', currency: 'PGK', timezone: 'Pacific/Port_Moresby' },

    // 非洲
    { code: 'ZA', nameEn: 'South Africa', nameZh: '南非', continentCode: 'africa', currency: 'ZAR', timezone: 'Africa/Johannesburg' },
    { code: 'EG', nameEn: 'Egypt', nameZh: '埃及', continentCode: 'africa', currency: 'EGP', timezone: 'Africa/Cairo' },
    { code: 'MA', nameEn: 'Morocco', nameZh: '摩洛哥', continentCode: 'africa', currency: 'MAD', timezone: 'Africa/Casablanca' },
    { code: 'KE', nameEn: 'Kenya', nameZh: '肯尼亚', continentCode: 'africa', currency: 'KES', timezone: 'Africa/Nairobi' },
    { code: 'NG', nameEn: 'Nigeria', nameZh: '尼日利亚', continentCode: 'africa', currency: 'NGN', timezone: 'Africa/Lagos' },
    { code: 'GH', nameEn: 'Ghana', nameZh: '加纳', continentCode: 'africa', currency: 'GHS', timezone: 'Africa/Accra' },
  ];

  for (const country of countries) {
    const continent = await prisma.continent.findUnique({ where: { code: country.continentCode } });
    if (continent) {
      await prisma.country.upsert({
        where: { code: country.code },
        update: {
          nameEn: country.nameEn,
          nameZh: country.nameZh,
          currency: country.currency,
          timezone: country.timezone,
        },
        create: {
          code: country.code,
          nameEn: country.nameEn,
          nameZh: country.nameZh,
          continentId: continent.id,
          currency: country.currency,
          timezone: country.timezone,
        },
      });
    }
  }

  // 3. 初始化运营商数据
  console.log('初始化运营商数据...');
  const operators = [
    // 中国运营商
    { code: 'china-mobile', nameEn: 'China Mobile', nameZh: '中国移动', countryCode: 'CN' },
    { code: 'china-unicom', nameEn: 'China Unicom', nameZh: '中国联通', countryCode: 'CN' },
    { code: 'china-telecom', nameEn: 'China Telecom', nameZh: '中国电信', countryCode: 'CN' },

    // 日本运营商
    { code: 'ntt-docomo', nameEn: 'NTT Docomo', nameZh: 'NTT Docomo', countryCode: 'JP' },
    { code: 'softbank', nameEn: 'SoftBank', nameZh: '软银', countryCode: 'JP' },
    { code: 'kddi-au', nameEn: 'KDDI au', nameZh: 'KDDI au', countryCode: 'JP' },

    // 韩国运营商
    { code: 'kt', nameEn: 'KT', nameZh: 'KT', countryCode: 'KR' },
    { code: 'skt', nameEn: 'SK Telecom', nameZh: 'SK电信', countryCode: 'KR' },
    { code: 'lgu-plus', nameEn: 'LG U+', nameZh: 'LG U+', countryCode: 'KR' },

    // 美国运营商
    { code: 'verizon', nameEn: 'Verizon', nameZh: 'Verizon', countryCode: 'US' },
    { code: 'att', nameEn: 'AT&T', nameZh: 'AT&T', countryCode: 'US' },
    { code: 't-mobile', nameEn: 'T-Mobile', nameZh: 'T-Mobile', countryCode: 'US' },

    // 英国运营商
    { code: 'ee', nameEn: 'EE', nameZh: 'EE', countryCode: 'GB' },
    { code: 'vodafone-uk', nameEn: 'Vodafone UK', nameZh: '沃达丰英国', countryCode: 'GB' },
    { code: 'o2-uk', nameEn: 'O2 UK', nameZh: 'O2英国', countryCode: 'GB' },

    // 德国运营商
    { code: 'deutsche-telekom', nameEn: 'Deutsche Telekom', nameZh: '德国电信', countryCode: 'DE' },
    { code: 'vodafone-de', nameEn: 'Vodafone Germany', nameZh: '沃达丰德国', countryCode: 'DE' },
    { code: 'o2-de', nameEn: 'O2 Germany', nameZh: 'O2德国', countryCode: 'DE' },

    // 澳大利亚运营商
    { code: 'telstra', nameEn: 'Telstra', nameZh: 'Telstra', countryCode: 'AU' },
    { code: 'optus', nameEn: 'Optus', nameZh: 'Optus', countryCode: 'AU' },
    { code: 'vodafone-au', nameEn: 'Vodafone Australia', nameZh: '沃达丰澳大利亚', countryCode: 'AU' },
  ];

  for (const operator of operators) {
    const country = await prisma.country.findUnique({ where: { code: operator.countryCode } });
    if (country) {
      await prisma.mobileOperator.upsert({
        where: { code: operator.code },
        update: {
          nameEn: operator.nameEn,
          nameZh: operator.nameZh,
        },
        create: {
          code: operator.code,
          nameEn: operator.nameEn,
          nameZh: operator.nameZh,
          countryId: country.id,
          logoUrl: `https://example.com/logos/${operator.code}.png`,
        },
      });
    }
  }

  // 4. 初始化首页Banner数据
  console.log('初始化首页Banner数据...');
  const banners = [
    {
      title: '5G全球套餐促销',
      subtitle: '限时8.5折优惠',
      imageUrl: 'https://example.com/banners/5g-global-promo.jpg',
      link: '/products/5g-global',
      priority: 10,
      language: 'zh',
    },
    {
      title: '5G Global Package Promotion',
      subtitle: 'Limited Time 15% Off',
      imageUrl: 'https://example.com/banners/5g-global-promo-en.jpg',
      link: '/products/5g-global',
      priority: 10,
      language: 'en',
    },
    {
      title: '亚洲旅游套餐',
      subtitle: '覆盖15个国家',
      imageUrl: 'https://example.com/banners/asia-travel.jpg',
      link: '/products/asia-travel',
      priority: 9,
      language: 'zh',
    },
    {
      title: 'Asia Travel Package',
      subtitle: 'Covers 15 Countries',
      imageUrl: 'https://example.com/banners/asia-travel-en.jpg',
      link: '/products/asia-travel',
      priority: 9,
      language: 'en',
    },
  ];

  for (const banner of banners) {
    await prisma.banner.create({
      data: banner,
    });
  }

  // 5. 初始化首页功能按钮数据
  console.log('初始化首页功能按钮数据...');
  const homeFeatures = [
    // 中文功能按钮
    { title: '保号套餐', icon: 'shield', type: 'native', action: '/number-retention', position: 1, color: '#007AFF', language: 'zh' },
    { title: '手机充值', icon: 'smartphone', type: 'native', action: '/mobile-recharge', position: 2, color: '#34C759', language: 'zh' },
    { title: '旅游套餐', icon: 'map', type: 'native', action: '/travel-packages', position: 3, color: '#FF9500', language: 'zh' },
    { title: '本地套餐', icon: 'map-pin', type: 'native', action: '/local-packages', position: 4, color: '#5856D6', language: 'zh' },
    { title: '加油流量包', icon: 'zap', type: 'native', action: '/data-boosters', position: 5, color: '#FF2D92', language: 'zh' },
    { title: '流量卡', icon: 'credit-card', type: 'html', action: '/pages/content?pageId=data-cards', position: 6, color: '#FF3B30', language: 'zh' },
    { title: '出行流量', icon: 'navigation', type: 'html', action: '/pages/content?pageId=travel-data', position: 7, color: '#30D158', language: 'zh' },
    { title: '5G套餐', icon: 'wifi', type: 'html', action: '/pages/content?pageId=5g-packages', position: 8, color: '#64D2FF', language: 'zh' },
    { title: '国际漫游', icon: 'globe', type: 'html', action: '/pages/content?pageId=international-roaming', position: 9, color: '#8E8E93', language: 'zh' },
    // 英文功能按钮
    { title: 'Number Retention', icon: 'shield', type: 'native', action: '/number-retention', position: 1, color: '#007AFF', language: 'en' },
    { title: 'Mobile Recharge', icon: 'smartphone', type: 'native', action: '/mobile-recharge', position: 2, color: '#34C759', language: 'en' },
    { title: 'Travel Packages', icon: 'map', type: 'native', action: '/travel-packages', position: 3, color: '#FF9500', language: 'en' },
    { title: 'Local Packages', icon: 'map-pin', type: 'native', action: '/local-packages', position: 4, color: '#5856D6', language: 'en' },
    { title: 'Data Boosters', icon: 'zap', type: 'native', action: '/data-boosters', position: 5, color: '#FF2D92', language: 'en' },
    { title: 'Data Cards', icon: 'credit-card', type: 'html', action: '/pages/content?pageId=data-cards', position: 6, color: '#FF3B30', language: 'en' },
    { title: 'Travel Data', icon: 'navigation', type: 'html', action: '/pages/content?pageId=travel-data', position: 7, color: '#30D158', language: 'en' },
    { title: '5G Packages', icon: 'wifi', type: 'html', action: '/pages/content?pageId=5g-packages', position: 8, color: '#64D2FF', language: 'en' },
    { title: 'International Roaming', icon: 'globe', type: 'html', action: '/pages/content?pageId=international-roaming', position: 9, color: '#8E8E93', language: 'en' },
  ];

  for (const feature of homeFeatures) {
    await prisma.homeFeature.create({
      data: feature,
    });
  }

  // 6. 初始化旅游贴士数据
  console.log('初始化旅游贴士数据...');
  const travelTips = [
    {
      title: '亚洲最佳数据套餐',
      content: '了解亚洲地区最受欢迎的数据套餐，包括日本、韩国、泰国等热门目的地。',
      imageUrl: 'https://example.com/tips/asia-best-plans.jpg',
      link: '/tips/asia-best-plans',
      category: 'hot-packages',
      priority: 10,
      language: 'zh',
    },
    {
      title: 'Best Data Plans for Asia',
      content: 'Discover the most popular data plans for Asia, including Japan, Korea, Thailand and other hot destinations.',
      imageUrl: 'https://example.com/tips/asia-best-plans-en.jpg',
      link: '/tips/asia-best-plans',
      category: 'hot-packages',
      priority: 10,
      language: 'en',
    },
    {
      title: '欧洲多国通用套餐指南',
      content: '一张卡畅游欧洲28国，了解如何选择最适合的欧洲多国套餐。',
      imageUrl: 'https://example.com/tips/europe-multi-country.jpg',
      link: '/tips/europe-multi-country',
      category: 'hot-packages',
      priority: 9,
      language: 'zh',
    },
    {
      title: 'Europe Multi-Country Package Guide',
      content: 'Travel across 28 European countries with one card. Learn how to choose the best Europe multi-country package.',
      imageUrl: 'https://example.com/tips/europe-multi-country-en.jpg',
      link: '/tips/europe-multi-country',
      category: 'hot-packages',
      priority: 9,
      language: 'en',
    },
    {
      title: '5G网络使用技巧',
      content: '了解如何充分利用5G网络的高速优势，提升您的移动网络体验。',
      imageUrl: 'https://example.com/tips/5g-usage-tips.jpg',
      link: '/tips/5g-usage-tips',
      category: '5g-promotion',
      priority: 8,
      language: 'zh',
    },
    {
      title: '5G Network Usage Tips',
      content: 'Learn how to make the most of 5G network high-speed advantages and enhance your mobile network experience.',
      imageUrl: 'https://example.com/tips/5g-usage-tips-en.jpg',
      link: '/tips/5g-usage-tips',
      category: '5g-promotion',
      priority: 8,
      language: 'en',
    },
  ];

  for (const tip of travelTips) {
    await prisma.travelTip.create({
      data: tip,
    });
  }

  // 7. 初始化产品分类数据
  console.log('初始化产品分类数据...');
  const categories = [
    {
      name: '旅游套餐',
      description: '适合出境旅游的数据套餐',
      image: 'https://example.com/categories/travel-packages.jpg',
      parentId: null,
    },
    {
      name: '本地套餐',
      description: '本地使用的数据套餐',
      image: 'https://example.com/categories/local-packages.jpg',
      parentId: null,
    },
    {
      name: '保号套餐',
      description: '保持号码有效的套餐',
      image: 'https://example.com/categories/number-retention.jpg',
      parentId: null,
    },
    {
      name: '流量加油包',
      description: '临时增加流量的套餐',
      image: 'https://example.com/categories/data-boosters.jpg',
      parentId: null,
    },
    {
      name: '5G套餐',
      description: '支持5G网络的高速套餐',
      image: 'https://example.com/categories/5g-packages.jpg',
      parentId: null,
    },
  ];

  for (const category of categories) {
    await prisma.category.create({
      data: category,
    });
  }

  // 8. 初始化示例产品数据
  console.log('初始化示例产品数据...');

  // 获取分类ID
  const travelCategory = await prisma.category.findFirst({ where: { name: '旅游套餐' } });
  const localCategory = await prisma.category.findFirst({ where: { name: '本地套餐' } });
  const retentionCategory = await prisma.category.findFirst({ where: { name: '保号套餐' } });
  const boosterCategory = await prisma.category.findFirst({ where: { name: '流量加油包' } });

  if (!travelCategory || !localCategory || !retentionCategory || !boosterCategory) {
    throw new Error('Required categories not found. Please ensure categories are created first.');
  }

  const products = [
    // 旅游套餐产品
    {
      name: '亚洲15国通用套餐',
      description: '覆盖亚洲15个热门旅游国家的数据套餐',
      websiteDescription: '专为亚洲旅游设计的多国通用数据套餐，覆盖日本、韩国、泰国、新加坡等15个热门目的地。',
      price: 89.00,
      dataSize: 5120, // 5GB
      planType: 'Total',
      categoryId: travelCategory.id,
      status: ProductStatus.ACTIVE,
      off_shelve: false,
      stock: 1000,
      sku: 'ASIA-15-5GB',
      images: ['https://example.com/products/asia-15-countries.jpg'],
      specifications: {
        countries: ['JP', 'KR', 'TH', 'SG', 'MY', 'VN', 'CN', 'IN', 'ID', 'PH'],
        network: '4G/5G',
        activation: '即时',
        provider: '全球网络',
        duration: 30
      },
    },
    {
      name: '欧洲28国通用套餐',
      description: '覆盖欧洲28个国家的数据套餐',
      websiteDescription: '一卡畅游欧洲28国，包含英国、法国、德国、意大利等主要欧洲国家。',
      price: 129.00,
      dataSize: 10240, // 10GB
      planType: 'Total',
      categoryId: travelCategory.id,
      status: ProductStatus.ACTIVE,
      off_shelve: false,
      stock: 800,
      sku: 'EUROPE-28-10GB',
      images: ['https://example.com/products/europe-28-countries.jpg'],
      specifications: {
        countries: ['GB', 'FR', 'DE', 'IT', 'ES', 'NL', 'CH', 'AT', 'BE', 'SE'],
        network: '4G/5G',
        activation: '即时',
        provider: '全球网络',
        duration: 30
      },
    },
    // 本地套餐产品
    {
      name: '中国大陆无限流量套餐',
      description: '中国大陆地区无限流量套餐',
      websiteDescription: '专为中国大陆用户设计的无限流量套餐，支持5G网络。',
      price: 199.00,
      dataSize: 0, // 无限
      planType: 'Unlimited',
      categoryId: localCategory.id,
      status: ProductStatus.ACTIVE,
      off_shelve: false,
      stock: 500,
      sku: 'CN-UNLIMITED',
      images: ['https://example.com/products/china-unlimited.jpg'],
      specifications: {
        countries: ['CN'],
        network: '4G/5G',
        activation: '即时',
        provider: '中国移动',
        duration: 30
      },
    },
    // 保号套餐产品
    {
      name: '基础保号套餐',
      description: '最低月租保号，适合长期不使用的号码',
      websiteDescription: '保持号码有效状态的基础套餐，包含少量通话和短信。',
      price: 15.00,
      dataSize: 500, // 500MB
      planType: 'Monthly',
      categoryId: retentionCategory.id,
      status: ProductStatus.ACTIVE,
      off_shelve: false,
      stock: 2000,
      sku: 'RETENTION-BASIC',
      images: ['https://example.com/products/basic-retention.jpg'],
      specifications: {
        countries: ['CN'],
        network: '4G',
        activation: '即时',
        provider: '中国移动',
        duration: 30,
        calls: 100,
        sms: 100
      },
    },
    // 流量加油包产品
    {
      name: '紧急流量包 100MB',
      description: '流量用完时的紧急救援，立即生效',
      websiteDescription: '当您的流量用完时，这个紧急流量包可以立即为您提供100MB的高速流量，让您在关键时刻保持连接。',
      price: 2.00,
      dataSize: 100, // 100MB
      planType: 'Emergency',
      categoryId: boosterCategory.id,
      status: ProductStatus.ACTIVE,
      off_shelve: false,
      stock: 5000,
      sku: 'EMERGENCY-100MB',
      images: ['https://example.com/products/emergency-100mb.jpg'],
      specifications: {
        countries: ['CN'],
        network: '4G/5G',
        activation: '即时',
        provider: '全球网络',
        duration: 1,
        boosterType: 'emergency',
        validity: '24小时',
        activationType: 'instant',
        features: ['立即激活', '24小时有效', '高速流量', '紧急使用']
      },
    },
    {
      name: '紧急流量包 1GB',
      description: '紧急情况下的1GB流量包',
      websiteDescription: '当流量不足时的紧急救援，1GB高速流量立即生效。',
      price: 19.90,
      dataSize: 1024, // 1GB
      planType: 'Emergency',
      categoryId: boosterCategory.id,
      status: ProductStatus.ACTIVE,
      off_shelve: false,
      stock: 3000,
      sku: 'EMERGENCY-1GB',
      images: ['https://example.com/products/emergency-1gb.jpg'],
      specifications: {
        countries: ['CN'],
        network: '4G/5G',
        activation: '即时',
        provider: '全球网络',
        duration: 7,
        boosterType: 'emergency',
        validity: '7天',
        activationType: 'instant',
        features: ['立即激活', '7天有效', '高速流量', '紧急使用']
      },
    },
    {
      name: '日流量包 1GB',
      description: '一天的额外流量，适合临时需求',
      websiteDescription: '为您提供一天的额外1GB流量，适合临时的高流量需求。',
      price: 5.00,
      dataSize: 1024, // 1GB
      planType: 'Daily',
      categoryId: boosterCategory.id,
      status: ProductStatus.ACTIVE,
      off_shelve: false,
      stock: 4000,
      sku: 'DAILY-1GB',
      images: ['https://example.com/products/daily-1gb.jpg'],
      specifications: {
        countries: ['CN'],
        network: '4G/5G',
        activation: '即时',
        provider: '全球网络',
        duration: 1,
        boosterType: 'daily',
        validity: '24小时',
        activationType: 'instant',
        features: ['1GB高速流量', '24小时有效', '即买即用', '性价比高']
      },
    },
    {
      name: '周流量包 5GB',
      description: '一周的额外流量，出差旅行必备',
      websiteDescription: '为您提供一周的额外5GB流量，是出差旅行的理想选择。',
      price: 18.00,
      dataSize: 5120, // 5GB
      planType: 'Weekly',
      categoryId: boosterCategory.id,
      status: ProductStatus.ACTIVE,
      off_shelve: false,
      stock: 2000,
      sku: 'WEEKLY-5GB',
      images: ['https://example.com/products/weekly-5gb.jpg'],
      specifications: {
        countries: ['CN'],
        network: '4G/5G',
        activation: '即时',
        provider: '全球网络',
        duration: 7,
        boosterType: 'weekly',
        validity: '7天',
        activationType: 'instant',
        features: ['5GB高速流量', '7天有效期', '支持热点分享', '出行必备']
      },
    },
    {
      name: '月流量包 10GB',
      description: '一个月的额外流量，长期补充首选',
      websiteDescription: '为您提供一个月的额外10GB流量，是长期流量补充的最佳选择。',
      price: 30.00,
      dataSize: 10240, // 10GB
      planType: 'Monthly',
      categoryId: boosterCategory.id,
      status: ProductStatus.ACTIVE,
      off_shelve: false,
      stock: 1500,
      sku: 'MONTHLY-10GB',
      images: ['https://example.com/products/monthly-10gb.jpg'],
      specifications: {
        countries: ['CN'],
        network: '4G/5G',
        activation: '即时',
        provider: '全球网络',
        duration: 30,
        boosterType: 'monthly',
        validity: '30天',
        activationType: 'instant',
        features: ['10GB高速流量', '30天有效期', '最优性价比', '长期使用']
      },
    },
    {
      name: '超值流量包 3GB',
      description: '3天有效的3GB流量包，短期出行优选',
      websiteDescription: '3GB流量包有效期3天，是短期出行的完美选择。',
      price: 12.00,
      dataSize: 3072, // 3GB
      planType: 'Daily',
      categoryId: boosterCategory.id,
      status: ProductStatus.ACTIVE,
      off_shelve: false,
      stock: 3500,
      sku: 'VALUE-3GB',
      images: ['https://example.com/products/value-3gb.jpg'],
      specifications: {
        countries: ['CN'],
        network: '4G/5G',
        activation: '即时',
        provider: '全球网络',
        duration: 3,
        boosterType: 'daily',
        validity: '3天',
        activationType: 'instant',
        features: ['3GB高速流量', '3天有效期', '短期出行', '超值优惠']
      },
    },
  ];

  for (const product of products) {
    await prisma.product.create({
      data: product,
    });
  }

  // 9. 为部分产品添加变体
  console.log('初始化产品变体数据...');

  // 获取紧急流量包产品
  const emergencyBooster = await prisma.product.findFirst({
    where: { name: '紧急流量包 1GB' }
  });

  if (emergencyBooster) {
    await prisma.productVariant.create({
      data: {
        productId: emergencyBooster.id,
        variantCode: 'EMERGENCY-1GB-STD',
        price: 19.90,
        currency: 'USD',
        duration: 7,
        durationType: 'day',
        attributes: { type: '标准版', description: '标准紧急流量包' },
      },
    });

    await prisma.productVariant.create({
      data: {
        productId: emergencyBooster.id,
        variantCode: 'EMERGENCY-1GB-DISC',
        price: 15.90,
        currency: 'USD',
        duration: 7,
        durationType: 'day',
        attributes: { type: '优惠版', description: '优惠紧急流量包' },
      },
    });
  }

  // 获取月流量包产品
  const monthlyBooster = await prisma.product.findFirst({
    where: { name: '月流量包 10GB' }
  });

  if (monthlyBooster) {
    await prisma.productVariant.create({
      data: {
        productId: monthlyBooster.id,
        variantCode: 'MONTHLY-10GB-STD',
        price: 30.00,
        currency: 'USD',
        duration: 30,
        durationType: 'day',
        attributes: { type: '标准版', description: '标准月流量包' },
      },
    });

    await prisma.productVariant.create({
      data: {
        productId: monthlyBooster.id,
        variantCode: 'MONTHLY-10GB-VIP',
        price: 25.00,
        currency: 'USD',
        duration: 30,
        durationType: 'day',
        attributes: { type: '会员版', description: '会员月流量包' },
      },
    });
  }

  console.log('真实数据初始化完成！');
}

main()
  .catch((e) => {
    console.error('初始化数据时发生错误:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
