import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { AccountForm } from "./account-form"

async function getUserData() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.id) {
    return null
  }

  const user = await prisma.user.findUnique({
    where: {
      id: session.user.id,
    },
    select: {
      id: true,
      name: true,
      email: true,
      image: true
    },
  })

  return user
}

export default async function AccountPage() {
  const user = await getUserData()

  if (!user) {
    return null
  }

  return <AccountForm user={user} />
} 