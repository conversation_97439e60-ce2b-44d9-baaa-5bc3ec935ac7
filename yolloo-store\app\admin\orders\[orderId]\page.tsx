import { notFound } from "next/navigation"
import { prisma } from "@/lib/prisma"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Icons } from "@/components/icons"
import { ShipOrderButton } from "@/components/admin/ship-order-button"
import { OrderStatusSelect } from "@/components/admin/order-status-select"
import { CreateOdooOrderButton } from "@/components/admin/create-odoo-order-button"
import { UpdateOdooStatusButton } from "@/components/admin/update-odoo-status-button"
import { AutoUpdateOdooStatus } from "@/components/admin/auto-update-odoo-status"
import { OdooStatusList } from "@/components/admin/odoo-status-list"
import { ProductLink } from "@/components/product-link"
import { QRCodeDisplay } from "@/components/orders/qr-code-display"
import Link from "next/link"
import { DateFormatter } from "@/lib/utils"

type OrderWithRelations = {
  id: string;
  userId: string;
  total: number;
  status: string;
  addressId: string | null;
  shippingAddressSnapshot: any | null;
  paymentId: string | null;
  createdAt: Date;
  updatedAt: Date;
  referralCode: string | null;
  user: any | null;
  items: {
    id: string;
    quantity: number;
    price: number;
    uid: string | null;
    variantText?: string | null;
    productCode?: string | null;
    variantCode: string | null;
    productId?: string | null;
    product?: {
      id: string;
      name: string;
      category: {
        id: string;
        name: string;
      };
    } | null;
  }[];
  shippingAddress: any | null;
  payment: any | null;
  odooStatuses: any[]; // 添加 odooStatuses 数组
  odooStatus: any | null; // 为了兼容性保留
}

interface OrderDetailsPageProps {
  params: {
    orderId: string
  }
}

async function getOrder(orderId: string) {
  const order = await prisma.order.findUnique({
    where: {
      id: orderId,
    },
    select: {
      id: true,
      userId: true,
      total: true,
      status: true,
      addressId: true,
      shippingAddressSnapshot: true,
      paymentId: true,
      createdAt: true,
      updatedAt: true,
      referralCode: true,
      user: true,
      items: {
        select: {
          id: true,
          quantity: true,
          price: true,
          uid: true,
          variantText: true,
          productCode: true,
          variantCode: true
        }
      },
      shippingAddress: true,
      payment: true,
      odooStatuses: {
        select: {
          id: true,
          orderId: true,
          variantCode: true,
          odooOrderRef: true,
          status: true,
          description: true,
          productName: true,
          isDigital: true,
          deliveredQty: true,
          trackingNumber: true,
          planState: true,
          uid: true,
          lastCheckedAt: true,
          createdAt: true,
          updatedAt: true
        }
      },
    }
  })

  if (!order) {
    notFound()
  }

  // 查询每个商品项目对应的商品信息，包括类别
  const itemsWithProductInfo = await Promise.all(
    order.items.map(async (item) => {
      if (item.productCode) {
        // 通过 productCode 查询商品信息，包括类别
        const product = await prisma.product.findFirst({
          where: { sku: item.productCode },
          include: { category: true }
        });

        return {
          ...item,
          productId: product?.id || null,
          product: product
        };
      }
      return {
        ...item,
        productId: null,
        product: null
      };
    })
  );

  // 获取默认的 Odoo 状态记录（如果存在）
  const defaultVariantCode = "default";
  const defaultOdooStatus = order.odooStatuses.find(status => status.variantCode === defaultVariantCode) || null;

  // 转换Prisma返回的数据为期望的类型
  const formattedOrder = {
    ...order,
    items: itemsWithProductInfo,
    odooStatus: defaultOdooStatus // 为了兼容性添加 odooStatus 字段
  }

  return formattedOrder as OrderWithRelations
}

// 解析 variantText 字段，提取 duration 和 durationType 信息
function extractDurationInfo(variantText: string | null | undefined) {
  if (!variantText) return null;

  // 尝试匹配格式：产品名称 + 数字 + 单位(day/month)
  const regex = /(.+?)\s+(\d+)\s+(day|month)s?$/i;
  const match = variantText.match(regex);

  if (match) {
    return {
      productName: match[1].trim(),
      duration: parseInt(match[2], 10),
      durationType: match[3].toLowerCase()
    };
  }

  return null;
}

export default async function OrderDetailsPage({ params }: OrderDetailsPageProps) {
  const order = await getOrder(params.orderId)

  return (
    <div className="space-y-8">
      {/* Add AutoUpdateOdooStatus component to automatically update Odoo status when page loads */}
      <AutoUpdateOdooStatus orderId={order.id} />

      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2">
            <Link href="/admin/orders">
              <Button variant="ghost" size="sm">
                <Icons.chevronLeft className="h-4 w-4 mr-2" />
                Back to Orders
              </Button>
            </Link>
            <h2 className="text-2xl font-bold tracking-tight">
              Order #{order.id.slice(0, 8)}
            </h2>
          </div>
          <p className="text-muted-foreground">
            Placed on {DateFormatter.withTimezone(order.createdAt)}
          </p>
        </div>
        <div className="flex items-center gap-4">
          <OrderStatusSelect orderId={order.id} currentStatus={order.status} />
          <ShipOrderButton orderId={order.id} currentStatus={order.status} />
        </div>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        <Card className="p-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">Customer Information</h3>
              <div className="space-y-4">
                <div>
                  <p className="font-medium">Name</p>
                  <p className="text-sm text-muted-foreground">
                    {order.user?.name || "Guest"}
                  </p>
                </div>
                <div>
                  <p className="font-medium">Email</p>
                  <p className="text-sm text-muted-foreground">
                    {order.user?.email || "N/A"}
                  </p>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Shipping Address</h3>
              <div className="space-y-1 text-sm text-muted-foreground">
                <p>{order.shippingAddressSnapshot?.name || order.shippingAddress?.name}</p>
                <p>{order.shippingAddressSnapshot?.address1 || order.shippingAddress?.address1}</p>
                {(order.shippingAddressSnapshot?.address2 || order.shippingAddress?.address2) && (
                  <p>{order.shippingAddressSnapshot?.address2 || order.shippingAddress?.address2}</p>
                )}
                <p>
                  {order.shippingAddressSnapshot?.city || order.shippingAddress?.city}, {order.shippingAddressSnapshot?.state || order.shippingAddress?.state}{" "}
                  {order.shippingAddressSnapshot?.postalCode || order.shippingAddress?.postalCode}
                </p>
                <p>{order.shippingAddressSnapshot?.country || order.shippingAddress?.country}</p>
                <p>Phone: {order.shippingAddressSnapshot?.phone || order.shippingAddress?.phone}</p>
              </div>
            </div>
            {/* 移除旧的Shipping Information部分，这些信息将在OdooStatusList组件中显示 */}
            {/* 使用新的OdooStatusList组件替换旧的Odoo信息显示 */}
            <OdooStatusList
              orderId={order.id}
              orderItems={order.items}
              odooStatuses={order.odooStatuses}
            />

          </div>
        </Card>

        <Card className="p-6">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Order Summary</h3>
            <div className="divide-y">
              {order.items.map((item) => {
                // 由于我们移除了外键关系，不再有 product 字段
                // 使用 variantText 作为主要显示内容
                console.log(`[ADMIN_ORDER_DETAILS] Item ${item.id}: variantText=${item.variantText}`);

                return (
                  <div key={item.id} className="flex gap-4 py-4">
                    <div className="h-16 w-16 rounded-md bg-gray-200 flex items-center justify-center">
                      <Icons.package className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <div>
                          <p className="font-medium">
                            <ProductLink productCode={item.productCode}>
                              {item.variantText || "Product Deleted"}
                            </ProductLink>
                          </p>
                          <div className="flex flex-col gap-1 mt-1">
                            {item.variantCode && (
                              <p className="text-xs text-muted-foreground">
                                <span className="font-medium">SKU:</span> <span className="font-mono">{item.variantCode}</span>
                              </p>
                            )}
                          </div>
                          <div className="mt-2 flex flex-wrap gap-2">
                            {(() => {
                              const durationInfo = extractDurationInfo(item.variantText);
                              if (durationInfo?.duration && durationInfo?.durationType) {
                                return (
                                  <div className="inline-flex items-center rounded-md bg-blue-50 border border-blue-200 px-3 py-1.5 text-sm">
                                    <span className="font-mono text-blue-600">{durationInfo.duration} {durationInfo.durationType}</span>
                                  </div>
                                );
                              }
                              return null;
                            })()}

                            {item.uid && (
                              <div className="inline-flex items-center rounded-md bg-blue-50 border border-blue-200 px-3 py-1.5 text-sm">
                                <Icons.creditCard className="mr-2 h-4 w-4 text-blue-500" />
                                <span className="font-medium text-blue-700 mr-2">UID:</span>
                                <span className="font-mono text-blue-600">{item.uid}</span>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            ${(item.quantity * item.price).toFixed(2)}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {item.quantity} × ${item.price.toFixed(2)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
              <div className="space-y-2 pt-4">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>${order.total.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Shipping</span>
                  <span>Free</span>
                </div>
                <div className="flex justify-between font-medium text-lg">
                  <span>Total</span>
                  <span>${order.total.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {order.payment && (
              <div className="mt-6 pt-6 border-t">
                <h3 className="text-lg font-semibold mb-2">Payment Information</h3>
                <div className="space-y-2">
                  <div>
                    <p className="font-medium">Status</p>
                    <p className="text-sm text-muted-foreground">
                      {order.payment.status}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Method</p>
                    <p className="text-sm text-muted-foreground">
                      {order.payment.paymentMethod}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Amount</p>
                    <p className="text-sm text-muted-foreground">
                      ${order.payment.amount.toFixed(2)} {order.payment.currency}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Payment Date</p>
                    <p className="text-sm text-muted-foreground">
                      {DateFormatter.withTimezone(order.payment.createdAt)}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* QR Code Display for QR Code Products */}
      {(() => {
        // 检查是否有QR code商品
        const hasQrCodeProducts = order.items.some((item: any) => {
          return item.product?.category?.name?.toLowerCase() === 'qr_code';
        });

        if (!hasQrCodeProducts) return null;

        // 获取第一个QR code产品的Odoo状态记录
        const qrCodeStatus = order.odooStatuses.find(status => {
          // 查找对应的订单项目
          const matchingItem = order.items.find(item =>
            item.variantCode === status.variantCode &&
            (status.uid === null || status.uid === item.uid)
          );
          // 检查是否是QR code产品
          return matchingItem?.product?.category?.name?.toLowerCase() === 'qr_code';
        });

        if (!qrCodeStatus) return null;

        // 构建正确的customer_order_ref
        const variantCode = qrCodeStatus.variantCode || 'default';
        const uid = qrCodeStatus.uid || 'no-uid';
        const customerOrderRef = `${order.id}-${variantCode}:::${uid}`;

        return (
          <QRCodeDisplay
            orderId={order.id}
            orderRef={customerOrderRef}
            status={qrCodeStatus.status}
            isQrCodeProduct={hasQrCodeProducts}
          />
        );
      })()}
    </div>
  )
}