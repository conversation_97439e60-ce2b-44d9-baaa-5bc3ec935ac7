import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { z } from "zod"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// 用户更新验证Schema
const updateUserSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").optional(),
  email: z.string().email("Invalid email address").optional(),
  role: z.enum(["ADMIN", "CUSTOMER", "STAFF"]).optional(),
})

// GET - 获取单个用户详情
export async function GET(
  req: Request,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: {
        id: params.userId,
      },
      include: {
        orders: {
          orderBy: {
            createdAt: "desc",
          },
          include: {
            items: true,
          },
        },
        addresses: true,
        loginHistory: {
          orderBy: {
            loginTimestamp: 'desc'
          },
          take: 20,
        },
        accounts: {
          select: {
            provider: true,
            providerAccountId: true
          }
        },
        _count: {
          select: {
            orders: true,
            reviews: true,
            wishlist: true,
          },
        },
      },
    })

    if (!user) {
      return new NextResponse("User not found", { status: 404 })
    }

    const lastLogin = user.loginHistory.length > 0 ? user.loginHistory[0] : null;

    return NextResponse.json({
      ...user,
      lastLoginTime: lastLogin?.loginTimestamp || null,
      lastLoginIp: lastLogin?.ipAddress || null,
      lastLoginMethod: lastLogin?.loginMethod || null,
    })
  } catch (error) {
    console.error("[USER_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

// PATCH - 更新用户信息
export async function PATCH(
  req: Request,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await req.json()

    // 验证请求数据
    const validationResult = updateUserSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          message: "Validation failed",
          errors: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: params.userId },
    })

    if (!existingUser) {
      return new NextResponse("User not found", { status: 404 })
    }

    // 如果要更新邮箱，检查邮箱是否已被使用
    if (body.email && body.email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email: body.email },
      })

      if (emailExists) {
        return NextResponse.json(
          { message: "Email already in use" },
          { status: 400 }
        )
      }
    }

    // 更新用户信息
    const updatedUser = await prisma.user.update({
      where: {
        id: params.userId,
      },
      data: validationResult.data,
    })

    return NextResponse.json(updatedUser)
  } catch (error) {
    console.error("[USER_PATCH]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

// DELETE - 删除用户
export async function DELETE(
  req: Request,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: params.userId },
    })

    if (!existingUser) {
      return new NextResponse("User not found", { status: 404 })
    }

    // 不能删除当前登录的管理员账户
    if (existingUser.id === session.user.id) {
      return NextResponse.json(
        { message: "Cannot delete your own admin account" },
        { status: 400 }
      )
    }

    // 删除用户关联的数据
    await prisma.$transaction(async (tx) => {
      // 删除营销佣金
      await tx.marketingCommission.deleteMany({
        where: { userId: params.userId },
      })

      // 删除预售订阅
      await tx.preSaleSubscription.updateMany({
        where: { userId: params.userId },
        data: { userId: null }
      })

      // 处理评论
      await tx.review.deleteMany({
        where: { userId: params.userId },
      })

      // 删除密码重置令牌
      await tx.passwordResetToken.deleteMany({
        where: { userId: params.userId },
      })

      // 处理 Yolloo 卡片
      await tx.yollooCard.updateMany({
        where: { userId: params.userId },
        data: { userId: null }
      })

      // 处理会员推广资料，可能需要特殊处理
      const affiliateProfile = await tx.affiliateProfile.findUnique({
        where: { userId: params.userId },
      })

      if (affiliateProfile) {
        // 处理相关的会员推广引用
        await tx.affiliateReferral.deleteMany({
          where: { affiliateId: affiliateProfile.id },
        })

        // 处理相关的会员推广访问
        await tx.affiliateVisit.deleteMany({
          where: { affiliateId: affiliateProfile.id },
        })

        // 处理相关的会员推广提款
        await tx.affiliateWithdrawal.deleteMany({
          where: { affiliateId: affiliateProfile.id },
        })

        // 处理相关的组织邀请
        await tx.organizationInvite.deleteMany({
          where: { affiliateId: affiliateProfile.id },
        })

        // 删除会员推广资料
        await tx.affiliateProfile.delete({
          where: { id: affiliateProfile.id },
        })
      }

      // 删除订单项目和订单
      const orders = await tx.order.findMany({
        where: { userId: params.userId },
        select: { id: true },
      })

      for (const order of orders) {
        // 删除订单项目
        await tx.orderItem.deleteMany({
          where: { orderId: order.id },
        })

        // 删除 Odoo 订单状态
        await tx.odooOrderStatus.deleteMany({
          where: { orderId: order.id },
        })

        // 删除相关的会员推广引用
        await tx.affiliateReferral.deleteMany({
          where: { orderId: order.id },
        })
      }

      // 删除所有订单
      await tx.order.deleteMany({
        where: { userId: params.userId },
      })

      // 删除用户地址
      await tx.address.deleteMany({
        where: { userId: params.userId },
      })

      // 删除购物车
      await tx.cartItem.deleteMany({
        where: { userId: params.userId },
      })

      // 删除愿望清单
      await tx.wishlistItem.deleteMany({
        where: { userId: params.userId },
      })

      // 删除用户会话
      await tx.session.deleteMany({
        where: { userId: params.userId },
      })

      // 删除用户账户连接
      await tx.account.deleteMany({
        where: { userId: params.userId },
      })

      // 最后删除用户
      await tx.user.delete({
        where: {
          id: params.userId,
        },
      })
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[USER_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}