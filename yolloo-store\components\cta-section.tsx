import Link from "next/link"
import { SectionBackground } from "@/components/ui/section-background"

export function CTASection() {
  return (
    <SectionBackground withTopGradient isWhite={false}>
      <div className="container relative z-10">
        <div className="mx-auto max-w-[700px] text-center">
          <div className="inline-block px-6 py-2 bg-gradient-to-br from-[#F799A6]/20 to-[#B82E4E]/20 backdrop-blur-sm 
          rounded-full border border-[#F799A6]/30 text-[#B82E4E] font-semibold tracking-wide mb-4 shadow-lg shadow-pink-500/10">
            Get Connected Today
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] 
          text-transparent bg-clip-text">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium mb-12">
            Join thousands of satisfied travelers who trust our eSIM solutions for their
            global connectivity needs. Experience the freedom of instant connectivity.
          </p>
          <div className="flex flex-wrap justify-center gap-6">
            <Link 
              href="/products" 
              className="px-8 py-4 bg-gradient-to-r from-[#B82E4E] to-[#F799A6] hover:from-[#A02745] hover:to-[#E88A97] 
              text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 
              text-lg font-semibold shadow-pink-500/20 hover:shadow-pink-500/30"
            >
              Browse Plans
            </Link>
            <Link
              href="/contact"
              className="px-8 py-4 bg-gradient-to-br from-[#F799A6]/10 to-[#B82E4E]/10 backdrop-blur-sm hover:from-[#F799A6]/15 
              hover:to-[#B82E4E]/15 text-[#B82E4E] hover:text-[#B82E4E] rounded-full border border-[#F799A6]/30 
              shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 text-lg font-semibold 
              hover:border-[#F799A6]/50 hover:shadow-pink-500/20"
            >
              Contact Sales
            </Link>
          </div>
        </div>
      </div>
    </SectionBackground>
  )
} 