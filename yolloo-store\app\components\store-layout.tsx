'use client';

import { usePathname } from 'next/navigation';
import Header from './header';
import Footer from './footer';
import { Suspense } from 'react';

interface StoreLayoutProps {
  children: React.ReactNode;
}

export function StoreLayout({ children }: StoreLayoutProps): JSX.Element {
  const pathname = usePathname();
  const isAdminPath = pathname?.startsWith('/admin');

  if (isAdminPath) {
    return <>{children}</>;
  }

  return (
    <div className="relative flex min-h-screen flex-col">
      <Suspense fallback={<div className="h-16 border-b" />}>
        <Header />
      </Suspense>
      <main className="flex-1 relative z-10">{children}</main>
      <Footer />
    </div>
  );
} 
