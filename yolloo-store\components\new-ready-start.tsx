'use client'

import Image from 'next/image'
import { motion } from 'framer-motion'

export default function NewReadyStart() {
  return (
    <section id="ready-start" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6" style={{
            background: 'linear-gradient(90deg, #B82E4E 0%, #F799A6 50%, #B82E4E 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            Ready to Get Started?
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-12">
            Join thousands of satisfied travelers who trust our eSIM solutions for their global connectivity needs. Experience the freedom of instant connectivity.
          </p>
        </motion.div>

        {/* World Map and Buttons */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="relative max-w-6xl mx-auto"
        >
          {/* World Map Background */}
          <div className="relative w-full h-[500px] md:h-[600px] flex items-center justify-center">
            <Image
              src="/worldLow 1.svg"
              alt="World map"
              fill
              className="object-contain scale-110"
            />

            {/* Overlay Buttons */}
            <div className="absolute inset-0 flex flex-col items-center justify-end pb-8 gap-4 z-10">
              {/* Browse Yolloo Plans Button */}
              <motion.button
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="bg-pink-500 hover:bg-pink-600 text-white font-medium rounded-full shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 w-64 h-12 justify-center"
              >
                <span className="w-5 h-5 flex items-center justify-center">
                  <Image
                    src="/gt1.svg"
                    alt="Browse plans"
                    width={20}
                    height={20}
                    className="object-contain"
                  />
                </span>
                <span>Browse Yolloo Plans</span>
              </motion.button>

              {/* Contact Sales Button */}
              <motion.button
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
                className="bg-white hover:bg-gray-50 text-pink-500 font-medium rounded-full border border-pink-200 shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 w-64 h-12 justify-center"
              >
                <span className="w-5 h-5 flex items-center justify-center">
                  <Image
                    src="/gt2.svg"
                    alt="Contact sales"
                    width={20}
                    height={20}
                    className="object-contain"
                  />
                </span>
                <span>Contact Sales</span>
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
