import { RequestContext } from '../interfaces/context.interface';
import { DateFormatter } from './date.utils';

export class LoggerUtil {
  static logContext(methodName: string, ctx: RequestContext): void {
    console.log(`[${methodName}] Context:`, {
      language: ctx.language,
      theme: ctx.theme,
      currency: ctx.currency,
      timestamp: DateFormatter.iso(new Date()),
    });
  }

  static logRequest(methodName: string, params?: any): void {
    console.log(`[${methodName}] Request:`, {
      params,
      timestamp: DateFormatter.iso(new Date()),
    });
  }

  static logError(methodName: string, error: any): void {
    console.error(`[${methodName}] Error:`, {
      error: error.message || error,
      stack: error.stack,
      timestamp: DateFormatter.iso(new Date()),
    });
  }
}
