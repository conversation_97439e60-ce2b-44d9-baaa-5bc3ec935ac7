import { NextResponse } from 'next/server';
import { z } from 'zod';
import { getVerificationCode, deleteVerificationCode, setRateLimit } from '@/lib/redis';
import { prisma } from '@/lib/prisma';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// 获取客户端IP地址
function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

// 输入验证schema
const verifyCodeSchema = z.object({
  email: z.string().email('Invalid email address'),
  code: z.string().length(6, 'Verification code must be 6 digits'),
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { email, code } = verifyCodeSchema.parse(body);

    // 获取客户端IP进行速率限制
    const clientIP = getClientIP(request);
    const rateLimitKey = `verify_code:${clientIP}:${email}`;
    
    // 速率限制：每个IP+邮箱组合每分钟最多尝试5次
    const canVerify = await setRateLimit(rateLimitKey, 5, 60);
    
    if (!canVerify) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Too many verification attempts. Please wait before trying again' 
        },
        { status: 429 }
      );
    }

    // 从Redis获取存储的验证码
    const storedCode = await getVerificationCode(email);
    
    if (!storedCode) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Verification code has expired or does not exist. Please request a new code' 
        },
        { status: 400 }
      );
    }

    // 验证码比较
    if (storedCode !== code) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid verification code. Please check and try again' 
        },
        { status: 400 }
      );
    }

    // 验证成功，删除验证码
    await deleteVerificationCode(email);

    return NextResponse.json({
      success: true,
      message: 'Verification successful'
    });

  } catch (error) {
    console.error('Error in verify-code API:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input data' 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
