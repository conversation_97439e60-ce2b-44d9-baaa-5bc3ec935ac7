import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import jsQR from "jsqr"

// 使用 require 导入 Jimp
const Jimp = require('jimp')

interface JimpImage {
  bitmap: {
    data: Buffer
    width: number
    height: number
  }
  scan: (
    x: number,
    y: number,
    w: number,
    h: number,
    f: (this: JimpImage, x: number, y: number, idx: number) => void
  ) => JimpImage
}

// 添加动态路由配置
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get("qrCode") as File
    
    if (!file) {
      return new NextResponse("No file uploaded", { status: 400 })
    }

    // 读取文件内容
    const buffer = Buffer.from(await file.arrayBuffer())

    // 使用Jimp读取图片
    const image = await Jimp.read(buffer) as JimpImage
    const { width, height } = image.bitmap
    
    // 获取图片数据
    const imageData = new Uint8ClampedArray(width * height * 4)
    let i = 0
    image.scan(0, 0, width, height, function(this: JimpImage, x: number, y: number, idx: number) {
      imageData[i++] = this.bitmap.data[idx + 0]! // r
      imageData[i++] = this.bitmap.data[idx + 1]! // g
      imageData[i++] = this.bitmap.data[idx + 2]! // b
      imageData[i++] = this.bitmap.data[idx + 3]! // a
    })

    // 使用jsQR解析QR码
    const code = jsQR(imageData, width, height)
    
    if (!code) {
      return new NextResponse("No QR code found in image", { status: 400 })
    }

    // 验证是否为LPA字符串
    if (!code.data.startsWith("LPA:")) {
      return new NextResponse("Invalid eSIM QR code", { status: 400 })
    }

    return NextResponse.json({ lpaString: code.data })
  } catch (error) {
    console.error("[PARSE_QR]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 