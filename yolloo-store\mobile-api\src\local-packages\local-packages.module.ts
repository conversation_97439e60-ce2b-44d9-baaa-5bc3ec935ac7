import { Module } from '@nestjs/common';
import { LocalPackagesService } from './local-packages.service';
import { LocalPackagesController } from './local-packages.controller';
import { PrismaService } from '../prisma.service';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [AuthModule],
  controllers: [LocalPackagesController],
  providers: [LocalPackagesService, PrismaService],
})
export class LocalPackagesModule {}
