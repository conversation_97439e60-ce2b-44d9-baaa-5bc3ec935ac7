"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function OrganizationRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the new integrated affiliate page with organizations tab
    router.push("/affiliate?tab=organizations");
  }, [router]);

  return (
    <div className="flex justify-center items-center h-64">
      <p className="text-muted-foreground">Redirecting to Affiliate Center...</p>
    </div>
  );
} 