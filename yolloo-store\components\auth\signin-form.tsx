"use client"

import * as React from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { signIn } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Icons } from "@/components/icons"
import { toast } from "sonner"
import Link from "next/link"

interface SignInFormProps extends React.HTMLAttributes<HTMLDivElement> {}

export function SignInForm({ className, ...props }: SignInFormProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = React.useState<boolean>(false)
  const [isGoogleLoading, setIsGoogleLoading] = React.useState<boolean>(false)
  const [isCodeLoading, setIsCodeLoading] = React.useState<boolean>(false)
  const [showCodeForm, setShowCodeForm] = React.useState<boolean>(false)
  const [email, setEmail] = React.useState<string>("")
  const [code, setCode] = React.useState<string>("")
  const [codeSent, setCodeSent] = React.useState<boolean>(false)
  const [countdown, setCountdown] = React.useState<number>(0)
  const callbackUrl = searchParams?.get("callbackUrl") || "/"

  // 倒计时效果
  React.useEffect(() => {
    let interval: NodeJS.Timeout
    if (countdown > 0) {
      interval = setInterval(() => {
        setCountdown(countdown - 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [countdown])

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setIsLoading(true)

    const formData = new FormData(event.currentTarget)
    const email = formData.get("email") as string
    const password = formData.get("password") as string

    try {
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
        callbackUrl
      })

      if (result?.error) {
        toast.error("Invalid email or password")
        return
      }

      if (result?.url) {
        window.location.href = result.url
      } else {
        if (callbackUrl.startsWith('/')) {
          window.location.href = callbackUrl
        } else {
          window.location.href = `/${callbackUrl}`
        }
      }
      
      toast.success("Signed in successfully")
    } catch (error) {
      toast.error("Something went wrong")
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    try {
      setIsGoogleLoading(true)
      await signIn("google", {
        callbackUrl,
        redirect: true
      })
    } catch (error) {
      toast.error("Something went wrong with Google sign in")
      setIsGoogleLoading(false)
    }
  }

  const handleSendCode = async () => {
    if (!email) {
      toast.error("Please enter your email address")
      return
    }

    setIsCodeLoading(true)
    try {
      const response = await fetch("/api/auth/send-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (data.success) {
        setCodeSent(true)
        setCountdown(60) // 60秒倒计时
        toast.success("Verification code sent to your email")
      } else {
        toast.error(data.error || "Failed to send verification code")
      }
    } catch (error) {
      toast.error("Something went wrong")
    } finally {
      setIsCodeLoading(false)
    }
  }

  const handleCodeLogin = async () => {
    if (!email || !code) {
      toast.error("Please enter both email and verification code")
      return
    }

    setIsLoading(true)
    try {
      const result = await signIn("email-code", {
        email,
        code,
        redirect: false,
        callbackUrl
      })

      if (result?.error) {
        toast.error("Invalid verification code")
        return
      }

      if (result?.url) {
        window.location.href = result.url
      } else {
        if (callbackUrl && callbackUrl.startsWith('/')) {
          window.location.href = callbackUrl
        } else {
          window.location.href = '/account'
        }
      }

      toast.success("Signed in successfully")
    } catch (error) {
      toast.error("Something went wrong")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="grid gap-6" {...props}>
      {!showCodeForm ? (
        <>
          <form onSubmit={onSubmit}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  placeholder="<EMAIL>"
                  type="email"
                  autoCapitalize="none"
                  autoComplete="email"
                  autoCorrect="off"
                  disabled={isLoading}
                  required
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    href="/auth/forgot-password"
                    className="text-xs text-muted-foreground hover:text-primary"
                  >
                    Forgot password?
                  </Link>
                </div>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoCapitalize="none"
                  autoComplete="current-password"
                  autoCorrect="off"
                  disabled={isLoading}
                  required
                />
              </div>
              <Button disabled={isLoading}>
                {isLoading && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Sign In with Password
              </Button>
            </div>
          </form>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or
              </span>
            </div>
          </div>
          <Button
            variant="outline"
            type="button"
            disabled={isCodeLoading}
            onClick={() => setShowCodeForm(true)}
          >
            {isCodeLoading ? (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Icons.mail className="mr-2 h-4 w-4" />
            )}
            Sign in with Email Code
          </Button>
        </>
      ) : (
        <div className="grid gap-4">
          <div className="grid gap-2">
            <Label htmlFor="code-email">Email</Label>
            <Input
              id="code-email"
              placeholder="<EMAIL>"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isLoading || codeSent}
              required
            />
          </div>
          {!codeSent ? (
            <Button
              type="button"
              disabled={isCodeLoading || !email}
              onClick={handleSendCode}
            >
              {isCodeLoading && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Send Verification Code
            </Button>
          ) : (
            <>
              <div className="grid gap-2">
                <Label htmlFor="verification-code">Verification Code</Label>
                <Input
                  id="verification-code"
                  placeholder="Enter 6-digit code"
                  type="text"
                  value={code}
                  onChange={(e) => setCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  disabled={isLoading}
                  maxLength={6}
                  required
                />
              </div>
              <Button
                type="button"
                disabled={isLoading || code.length !== 6}
                onClick={handleCodeLogin}
              >
                {isLoading && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Verify and Sign In
              </Button>
              <Button
                variant="outline"
                type="button"
                disabled={isCodeLoading || countdown > 0}
                onClick={handleSendCode}
              >
                {isCodeLoading && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                {countdown > 0 ? `Resend in ${countdown}s` : 'Resend Code'}
              </Button>
            </>
          )}
          <Button
            variant="ghost"
            type="button"
            onClick={() => {
              setShowCodeForm(false)
              setCodeSent(false)
              setCode("")
              setEmail("")
              setCountdown(0)
            }}
          >
            Back to Password Login
          </Button>
        </div>
      )}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div>
      <Button
        variant="outline"
        type="button"
        disabled={isGoogleLoading}
        onClick={handleGoogleSignIn}
      >
        {isGoogleLoading ? (
          <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <Icons.google className="mr-2 h-4 w-4" />
        )}{" "}
        Sign in with Google
      </Button>
    </div>
  )
} 