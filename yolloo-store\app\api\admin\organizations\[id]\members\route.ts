import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { sendOrganizationInviteEmail } from "@/app/services/emailService";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for adding a member
const addMemberSchema = z.object({
  userId: z.string().optional(),
  email: z.string().email().optional(),
  commissionRate: z.number().min(0).max(1).optional(),
  isAdmin: z.boolean().optional(),
});

// GET - List all members for an organization (admin only)
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }
    
    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: params.id },
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Get members for the organization
    const members = await prisma.affiliateProfile.findMany({
      where: { organizationId: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });
    
    return NextResponse.json(members);
  } catch (error) {
    console.error("Error fetching organization members:", error);
    return NextResponse.json(
      { error: "Failed to fetch organization members" },
      { status: 500 }
    );
  }
}

// POST - Add a member to the organization (admin only)
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }
    
    const organizationId = params.id;
    
    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validationResult = addMemberSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { userId, email, commissionRate, isAdmin } = validationResult.data;
    
    // If email is provided, create an invitation
    if (email) {
      // Check if this email already has a user account
      const existingUser = await prisma.user.findUnique({
        where: { email },
        include: { affiliate: true },
      });

      // Check if user already has an affiliate profile in this organization
      if (existingUser?.affiliate?.organizationId === organizationId) {
        return NextResponse.json(
          { error: "User already belongs to this organization" },
          { status: 400 }
        );
      }

      // Check if there's an existing pending invite for this email
      const existingInvite = await prisma.organizationInvite.findFirst({
        where: {
          email,
          organizationId,
          status: "PENDING",
          expiresAt: { gt: new Date() },
        },
      });

      if (existingInvite) {
        return NextResponse.json(
          { error: "An invitation has already been sent to this email" },
          { status: 400 }
        );
      }

      // Generate a unique invite code
      const inviteCode = `INV-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;
      
      // Set expiration date (7 days from now)
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);
      
      // Create the invitation
      const invite = await prisma.organizationInvite.create({
        data: {
          email,
          inviteCode,
          expiresAt,
          organization: {
            connect: { id: organizationId },
          },
          // Set commission rate and admin status if provided
          commissionRate: commissionRate,
          isAdmin: isAdmin,
        },
      });
      
      // Send invitation email
      if (organization) {
        const inviteUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invite/${inviteCode}`;
        await sendOrganizationInviteEmail(
          email,
          organization.name,
          inviteUrl,
          expiresAt,
          session.user.name || "Admin",
          isAdmin
        );
      }
      
      return NextResponse.json(
        { 
          success: true, 
          message: "Invitation sent successfully",
          invite 
        }, 
        { status: 201 }
      );
    }
    
    // If userId is provided, add existing user directly
    if (userId) {
      // Check if user exists
      const targetUser = await prisma.user.findUnique({
        where: { id: userId },
        include: { affiliate: true },
      });
      
      if (!targetUser) {
        return NextResponse.json({ error: "User not found" }, { status: 404 });
      }
      
      // If user already has an affiliate profile
      if (targetUser.affiliate) {
        // Check if already in this organization
        if (targetUser.affiliate.organizationId === organizationId) {
          return NextResponse.json(
            { error: "User is already a member of this organization" },
            { status: 400 }
          );
        }
        
        // If in another organization, move to this one
        const updatedAffiliate = await prisma.affiliateProfile.update({
          where: { id: targetUser.affiliate.id },
          data: {
            organizationId,
            ...(commissionRate !== undefined && { commissionRate }),
            ...(isAdmin !== undefined && { isAdmin }),
          },
        });
        
        return NextResponse.json(updatedAffiliate);
      }
      
      // If user doesn't have an affiliate profile, create one
      const code = `${targetUser.name?.substring(0, 3) || "AFF"}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
      
      const newAffiliate = await prisma.affiliateProfile.create({
        data: {
          userId: targetUser.id,
          code,
          organizationId,
          ...(commissionRate !== undefined && { commissionRate }),
          ...(isAdmin !== undefined && { isAdmin }),
        },
      });
      
      return NextResponse.json(newAffiliate);
    }
    
    return NextResponse.json(
      { error: "Either userId or email must be provided" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error adding organization member:", error);
    return NextResponse.json(
      { error: "Failed to add organization member" },
      { status: 500 }
    );
  }
} 