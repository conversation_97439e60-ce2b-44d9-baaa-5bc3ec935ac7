import { Metadata } from "next"
import { getServerSession } from "next-auth"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { CardForm } from "../card-form"

export const metadata: Metadata = {
  title: "Edit Card",
  description: "Edit Yolloo Card details",
}

interface EditCardPageProps {
  params: {
    cardId: string
  }
}

export default async function EditCardPage({ params }: EditCardPageProps) {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== "ADMIN") {
    redirect("/auth/signin")
  }

  const card = await prisma.yollooCard.findUnique({
    where: {
      id: params.cardId,
    },
  })

  if (!card) {
    redirect("/admin/cards")
  }

  return (
    <div className="flex flex-col gap-4 p-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Edit Card</h2>
        <p className="text-muted-foreground">
          Edit Yolloo Card details and settings
        </p>
      </div>
      <CardForm initialData={card} />
    </div>
  )
} 