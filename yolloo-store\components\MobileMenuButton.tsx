"use client"

import { Menu, X, Tag, Package, CreditCard, ShoppingCart, HelpCircle, Mail, HomeIcon, QrCode, Zap } from "lucide-react"
import { useState, useEffect, useRef } from "react"
import { cn } from "@/lib/utils"
import Link from "next/link"
import { usePathname } from "next/navigation"

const navItems = [
  { name: "Home", href: "/", icon: HomeIcon },
  { name: "Pricing", href: "/pricing", icon: Tag },
  { name: "My Cards", href: "/cards", icon: CreditCard },
  { name: "Packages", href: "/products", icon: Package },
  { name: "Orders", href: "/orders", icon: ShoppingCart },
  { name: "Affiliate", href: "/affiliate", icon: Zap },
  { name: "eSIMs", href: "/esims", icon: QrCode },
  { name: "FAQ", href: "/faq", icon: HelpCircle },
  { name: "Contact", href: "/contact", icon: Mail },
]

export function MobileMenuButton() {
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const pathname = usePathname()

  // Close menu when clicking outside
  useEffect(() => {
    const handleClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (menuRef.current && !menuRef.current.contains(target) && isOpen) {
        setIsOpen(false)
      }
    }

    document.addEventListener('click', handleClick)
    return () => document.removeEventListener('click', handleClick)
  }, [isOpen])

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  // Check if a nav item is active
  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname?.startsWith(href)
  }

  return (
    <div className="mobile-menu md:hidden">
      {/* Menu Button */}
      <button
        onClick={(e) => {
          e.stopPropagation()
          setIsOpen(!isOpen)
        }}
        className={cn(
          "flex items-center justify-center w-10 h-10 rounded-xl relative z-[1001]",
          "transition-all duration-200 active:scale-95 shadow-sm",
          isOpen
            ? "bg-primary/20 text-primary border border-primary/30"
            : "bg-accent/30 hover:bg-accent/50 text-primary border border-border/30"
        )}
        aria-label="Open navigation menu"
      >
        {isOpen ? (
          <X className="w-5 h-5" />
        ) : (
          <Menu className="w-5 h-5" />
        )}
      </button>

      {/* Backdrop - higher z-index and covering entire viewport */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-background/80 backdrop-blur-sm z-[998] mobile-nav-backdrop"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Navigation Menu - reference for outside click detection */}
      <div
        ref={menuRef}
        className={cn(
          "fixed top-0 left-0 h-[100dvh] w-72 bg-background border-r z-[999] shadow-xl",
          isOpen ? "mobile-nav-menu" : "transform -translate-x-full"
        )}
        style={{ height: '100dvh' }}
      >
        <div className="flex flex-col h-full pt-16 pb-8 bg-gradient-to-b from-background to-background/95">
          {/* Header */}
          <div className="px-6 mb-6">
            <h3 className="text-lg font-semibold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Navigation
            </h3>
          </div>

          {/* Navigation Links */}
          <nav className="flex-1 overflow-y-auto custom-scrollbar">
            <ul className="px-4 space-y-2">
              {navItems.map((item) => {
                const active = isActive(item.href);
                const Icon = item.icon;
                return (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      onClick={() => setIsOpen(false)}
                      className={cn(
                        "mobile-nav-link flex items-center w-full px-4 py-3.5 rounded-xl text-sm font-medium gap-3",
                        "transition-all duration-200 active:scale-98",
                        active
                          ? "active text-primary bg-primary/10 shadow-sm"
                          : "text-muted-foreground hover:text-primary hover:bg-accent/50"
                      )}
                    >
                      <span className={cn(
                        "icon-container flex items-center justify-center w-9 h-9 rounded-xl",
                        active
                          ? "bg-primary/20 text-primary shadow-sm"
                          : "bg-accent/30 text-muted-foreground"
                      )}>
                        <Icon className="w-5 h-5" />
                      </span>
                      <span className="font-medium">{item.name}</span>
                      {active && (
                        <span className="absolute left-0 top-0 bottom-0 w-1.5 bg-primary rounded-r-full" />
                      )}
                    </Link>
                  </li>
                );
              })}
            </ul>
          </nav>

          {/* Footer */}
          <div className="px-6 pt-4 mt-auto border-t border-border/30">
            <Link href="/products" onClick={() => setIsOpen(false)}>
              <div className="w-full py-3.5 px-4 rounded-xl bg-gradient-to-r from-primary to-primary/80
              text-white text-center font-medium hover:opacity-90 transition-all duration-200
              active:scale-95 shadow-md cursor-pointer flex items-center justify-center gap-2">
                <ShoppingCart className="w-4 h-4" />
                <span>Browse Packages</span>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}