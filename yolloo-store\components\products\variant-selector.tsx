'use client'

import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { formatPrice } from '@/lib/utils'

interface ProductVariant {
  id: string
  price: number
  duration?: number | null
  durationType?: string | null
  attributes?: Record<string, string>
}

interface VariantSelectorProps {
  variants: ProductVariant[]
  onVariantSelect: (variant: ProductVariant) => void
  selectedVariant?: ProductVariant | null
}

export function VariantSelector({
  variants,
  onVariantSelect,
  selectedVariant,
}: VariantSelectorProps) {
  
  // 获取所有可能的单位选项
  const periodUnits = Array.from(
    new Set(
      variants.map((variant) => variant.durationType || '')
    )
  ).filter(Boolean)
  

  // 用户选择的值，使用selectedVariant的值作为默认值
  const [selectedUnit, setSelectedUnit] = useState<string>(
    selectedVariant?.durationType || periodUnits[0] || ''
  )
  
  // 根据选择的单位过滤可用的时长选项
  const availablePeriods = Array.from(
    new Set(
      variants
        .filter(variant => variant.durationType === selectedUnit)
        .map(variant => variant.duration?.toString() || '')
    )
  ).filter(Boolean).sort((a, b) => Number(a) - Number(b))
  
  
  const [selectedPeriod, setSelectedPeriod] = useState<string>(
    selectedVariant?.duration?.toString() || availablePeriods[0] || ''
  )

  // 当selectedVariant改变时，更新选中的值
  useEffect(() => {
    if (selectedVariant) {
      setSelectedUnit(selectedVariant.durationType || '')
      setSelectedPeriod(selectedVariant.duration?.toString() || '')
    }
  }, [selectedVariant])

  // 根据选择找到匹配的变体
  useEffect(() => {
    
    if (selectedPeriod && selectedUnit) {
      const matchingVariant = variants.find((variant) => {
        const matches = variant.duration?.toString() === selectedPeriod &&
          variant.durationType === selectedUnit
        return matches
      })

      if (matchingVariant) {
        onVariantSelect(matchingVariant)
      }
    }
  }, [selectedPeriod, selectedUnit, variants, onVariantSelect])

  // 当单位改变时，重新计算可用的时长选项并重置选择
  useEffect(() => {
    // 如果当前选择的时长在新单位下不可用，则重置为第一个可用选项
    if (!availablePeriods.includes(selectedPeriod) && availablePeriods.length > 0) {
      setSelectedPeriod(availablePeriods[0])
    }
  }, [selectedUnit, availablePeriods, selectedPeriod])

  const handlePeriodChange = (value: string) => {
    setSelectedPeriod(value)
  }

  const handleUnitChange = (value: string) => {
    setSelectedUnit(value)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label className="text-base">Unit</Label>
        <RadioGroup
          value={selectedUnit}
          onValueChange={handleUnitChange}
        >
          <div className="flex flex-wrap gap-2">
            {periodUnits.map((unit) => (
              <div key={unit}>
                <RadioGroupItem
                  value={unit}
                  id={`unit-${unit}`}
                  className="peer sr-only"
                />
                <Label
                  htmlFor={`unit-${unit}`}
                  className="flex cursor-pointer items-center justify-center rounded-md border-2 border-muted bg-popover px-3 py-2 text-sm font-medium ring-offset-background transition-all hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary peer-data-[state=checked]:text-primary"
                >
                  {unit}
                </Label>
              </div>
            ))}
          </div>
        </RadioGroup>
      </div>

      <div className="space-y-2">
        <Label className="text-base">Duration</Label>
        <RadioGroup
          value={selectedPeriod}
          onValueChange={handlePeriodChange}
        >
          <div className="flex flex-wrap gap-2">
            {availablePeriods.map((period) => (
              <div key={period}>
                <RadioGroupItem
                  value={period}
                  id={`period-${period}`}
                  className="peer sr-only"
                />
                <Label
                  htmlFor={`period-${period}`}
                  className="flex cursor-pointer items-center justify-center rounded-md border-2 border-muted bg-popover px-3 py-2 text-sm font-medium ring-offset-background transition-all hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary peer-data-[state=checked]:text-primary"
                >
                  {period}
                </Label>
              </div>
            ))}
          </div>
        </RadioGroup>
      </div>

      {selectedVariant && (
        <div className="mt-4">
          <p className="text-lg font-semibold">
            Selected: {selectedPeriod} {selectedUnit}
          </p>
          <p className="text-2xl font-bold">
            {formatPrice(selectedVariant.price)}
          </p>
        </div>
      )}
    </div>
  )
} 