// Package status mapping
export const packageStatusMap: Record<number, { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning' }> = {
  300: { label: 'Waiting', variant: 'outline' },           // 白色 - 待开通
  400: { label: 'Opening', variant: 'outline' },        // 白色 - 开通中
  500: { label: 'Ready', variant: 'success' },             // 绿色
  600: { label: 'Activating', variant: 'success' }, // 绿色
  700: { label: 'Active', variant: 'success' },            // 绿色
  800: { label: 'End', variant: 'default' }     // 红色
};

// Order source mapping
export const orderSourceMap: Record<string, string> = {
  'withuid_localload_order': 'Package',
  'withuid_rpm_order': 'QR download',
  'withoutuid_package_order': 'Pure eSIM',
  'withuid_renew_order': 'package renewal',
  // Legacy mappings for backward compatibility
  'rpm_order': 'QR download',
  'product_order': 'Package'
};

import { DateFormatter } from './utils'

// Format date time - 管理端使用 (中国时区)
export const formatDateTime = (dateTimeStr: string | null) => {
  if (!dateTimeStr) return '-';
  return DateFormatter.withTimezone(dateTimeStr, undefined, '-');
};

// Format date time - 用户前端使用 (用户浏览器时区，SSR安全)
export const formatDateTimeForUser = (dateTimeStr: string | null) => {
  if (!dateTimeStr) return '-';
  return DateFormatter.forUserSafe(dateTimeStr, '-');
};

// Format data usage
export const formatDataUsage = (bytes: string | number | null) => {
  if (bytes === null || bytes === undefined) return '-';

  const numBytes = typeof bytes === 'string' ? parseInt(bytes) : bytes;
  if (isNaN(numBytes)) return bytes;

  if (numBytes < 1024) return `${numBytes} B`;
  if (numBytes < 1024 * 1024) return `${(numBytes / 1024).toFixed(2)} KB`;
  if (numBytes < 1024 * 1024 * 1024) return `${(numBytes / (1024 * 1024)).toFixed(2)} MB`;
  return `${(numBytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
};
