import { IsOptional, Is<PERSON><PERSON>, <PERSON>In, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class NumberRetentionQueryDto {
  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsIn(['monthly', 'yearly', 'unlimited'])
  planType?: 'monthly' | 'yearly' | 'unlimited';

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  pageSize?: number = 10;

  @IsOptional()
  @IsString()
  sortBy?: string = 'price';

  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'asc';
}
