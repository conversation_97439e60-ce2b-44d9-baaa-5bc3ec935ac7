import * as React from 'react';
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

export interface OrganizationInviteEmailProps {
  organizationName: string;
  inviteUrl: string;
  expireDate: string;
  inviterName?: string;
  isAdmin?: boolean;
  previewText?: string;
}

export const OrganizationInviteEmail = ({
  organizationName,
  inviteUrl,
  expireDate,
  inviterName = 'The Team',
  isAdmin = false,
  previewText = 'You have been invited to join an organization on Yolloo Store.',
}: OrganizationInviteEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src="https://i.postimg.cc/F15k4vk9/logo.png"
            width="128"
            height="42"
            alt="Yolloo Store"
            style={logo}
          />
          <Heading style={heading}>Organization Invitation</Heading>
          <Text style={paragraph}>
            You have been invited to join <strong>{organizationName}</strong> on Yolloo Store.
            {isAdmin ? ' You will have administrator privileges in this organization.' : ''}
          </Text>
          <Text style={paragraph}>
            This invitation was sent by {inviterName} and will expire on {expireDate}.
          </Text>
          <Section style={btnContainer}>
            <Link style={button} href={inviteUrl}>
              Accept Invitation
            </Link>
          </Section>
          <Text style={paragraph}>
            Or copy and paste this URL into your browser:
          </Text>
          <Text style={linkText}>
            {inviteUrl}
          </Text>
          <Text style={footer}>
            If you were not expecting this invitation, you can ignore this email. If you have any
            questions, please{' '}
            <Link href="mailto:<EMAIL>" style={link}>
              contact our support team
            </Link>
            .
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default OrganizationInviteEmail;

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px',
  maxWidth: '600px',
  borderRadius: '5px',
  boxShadow: '0 2px 5px rgba(0, 0, 0, 0.05)',
};

const logo = {
  margin: '0 auto',
  marginBottom: '20px',
  display: 'block',
};

const heading = {
  fontSize: '24px',
  letterSpacing: '-0.5px',
  lineHeight: '1.3',
  fontWeight: '400',
  textAlign: 'center' as const,
  color: '#111827',
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#4b5563',
  marginTop: '16px',
};

const btnContainer = {
  textAlign: 'center' as const,
  marginTop: '24px',
};

const button = {
  backgroundColor: '#2563eb',
  borderRadius: '5px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
};

const linkText = {
  fontSize: '14px',
  lineHeight: '1.4',
  color: '#4b5563',
  margin: '12px 0',
  wordBreak: 'break-all' as const,
};

const link = {
  color: '#2563eb',
  textDecoration: 'underline',
};

const footer = {
  fontSize: '14px',
  color: '#6b7280',
  marginTop: '32px',
}; 