"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "sonner"

const ORDER_STATUS_MAP = {
  PENDING: { label: "Pending", variant: "secondary" },
  PROCESSING: { label: "Processing", variant: "warning" },
  SHIPPED: { label: "Shipped", variant: "info" },
  DELIVERED: { label: "Delivered", variant: "success" },
  CANCELLED: { label: "Cancelled", variant: "destructive" },
  PAID: { label: "Paid", variant: "default" },
} as const

interface OrderStatusProps {
  order: {
    id: string
    status: string
  }
}

export function OrderStatus({ order }: OrderStatusProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  async function updateStatus(status: string) {
    setIsLoading(true)

    try {
      const response = await fetch(`/api/admin/orders/${order.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      })

      if (!response.ok) {
        throw new Error("Failed to update order status")
      }

      toast.success("Order status updated successfully")
      router.refresh()
    } catch (error) {
      console.error(error)
      toast.error("Something went wrong")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex items-center gap-2">
      <Badge variant={ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP]?.variant || "secondary"}>
        {ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP]?.label || order.status}
      </Badge>
      <Select
        defaultValue={order.status}
        onValueChange={updateStatus}
        disabled={isLoading}
      >
        <SelectTrigger className="w-[140px]">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {Object.entries(ORDER_STATUS_MAP).map(([value, { label }]) => (
            <SelectItem key={value} value={value}>
              {label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {isLoading && <Icons.spinner className="h-4 w-4 animate-spin" />}
    </div>
  )
} 