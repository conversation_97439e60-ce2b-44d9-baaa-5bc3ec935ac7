'use client';

import { usePathname } from 'next/navigation';
import { AffiliateTracker } from '@/components/affiliate/tracker';
import LiveChat from '@/components/LiveChat';
import { Toaster } from "@/components/ui/toaster";

interface ClientLayoutProps {
  hasReferralCode: boolean;
  children: React.ReactNode;
}

export function ClientLayout({ hasReferralCode, children }: ClientLayoutProps) {
  const pathname = usePathname();
  const isAdminPath = pathname?.startsWith('/admin');

  return (
    <>
      {hasReferralCode && !isAdminPath && <AffiliateTracker />}
      {children}
      {!isAdminPath && <LiveChat />}
      <Toaster />
    </>
  );
}
