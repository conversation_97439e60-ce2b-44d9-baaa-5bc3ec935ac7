"use client"

import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useEffect, useState } from "react"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import { AddCardDialog } from "@/components/cards/add-card-dialog"
import { CheckCircle, CreditCard, Plus, Signal, ShieldCheck, AlertTriangle, ChevronRight, Info  } from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { DateFormatter } from "@/lib/utils"

interface YollooCard {
  id: string
  number: string
  customName?: string
  status: "Active" | "Inactive" | "Expired"
  type: string
  activationDate?: string
  expiryDate?: string
}

export default function CardsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [cards, setCards] = useState<YollooCard[]>([])
  const [loading, setLoading] = useState(true)
  const [viewType, setViewType] = useState<"all" | "active" | "inactive">("all")

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
    } else if (status === "authenticated") {
      fetchCards()
    }
  }, [status, router])

  const fetchCards = async () => {
    try {
      const response = await fetch('/api/cards')
      const data = await response.json()
      if (response.ok) {
        setCards(data)
      } else {
        toast.error("Failed to load cards")
      }
    } catch (error) {
      toast.error("Error loading cards")
    } finally {
      setLoading(false)
    }
  }

  const activateCard = async (cardId: string) => {
    try {
      const response = await fetch(`/api/cards/${cardId}/activate`, {
        method: 'POST'
      })
      if (response.ok) {
        toast.success("Card activated successfully")
        fetchCards()
      } else {
        toast.error("Failed to activate card")
      }
    } catch (error) {
      toast.error("Error activating card")
    }
  }

  const filteredCards = cards.filter(card => {
    if (viewType === "all") return true;
    if (viewType === "active") return card.status === "Active";
    if (viewType === "inactive") return card.status === "Inactive";
    return true;
  });

  const getCardStatusStyles = (status: string) => {
    switch (status) {
      case 'Active':
        return {
          icon: <CheckCircle className="h-5 w-5" />,
          color: 'text-green-600',
          bg: 'bg-green-50 border-green-100',
          badge: 'bg-green-100 text-green-800'
        };
      case 'Inactive':
        return {
          icon: <AlertTriangle className="h-5 w-5" />,
          color: 'text-amber-600',
          bg: 'bg-amber-50 border-amber-100',
          badge: 'bg-amber-100 text-amber-800'
        };
      default:
        return {
          icon: <Info className="h-5 w-5" />,
          color: 'text-red-600',
          bg: 'bg-red-50 border-red-100',
          badge: 'bg-red-100 text-red-800'
        };
    }
  };

  if (status === "loading" || (status === "unauthenticated")) {
    return (
      <div className="container py-10">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container max-w-6xl py-12">
      <div className="flex flex-col md:flex-row gap-4 md:justify-between md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Yolloo Cards</h1>
          <p className="text-muted-foreground mt-2">Manage your eSIM cards and connected devices</p>
        </div>
        <div className="flex">
          <AddCardDialog />
        </div>
      </div>

      <Tabs defaultValue="all" className="mb-8" onValueChange={(value) => setViewType(value as "all" | "active" | "inactive")}>
        <div className="flex justify-between items-center">
          <TabsList className="mb-4">
            <TabsTrigger value="all" className="px-4">
              All Cards ({cards.length})
            </TabsTrigger>
            <TabsTrigger value="active" className="px-4">
              Active ({cards.filter(card => card.status === "Active").length})
            </TabsTrigger>
            <TabsTrigger value="inactive" className="px-4">
              Inactive ({cards.filter(card => card.status === "Inactive").length})
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="all" className="mt-0">
          {loading ? (
            <CardSkeleton count={3} />
          ) : filteredCards.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCards.map((card) => (
                <CardItem
                  key={card.id}
                  card={card}
                  activateCard={activateCard}
                  router={router}
                  getCardStatusStyles={getCardStatusStyles}
                />
              ))}
            </div>
          ) : (
            <EmptyState />
          )}
        </TabsContent>

        <TabsContent value="active" className="mt-0">
          {loading ? (
            <CardSkeleton count={2} />
          ) : filteredCards.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCards.map((card) => (
                <CardItem
                  key={card.id}
                  card={card}
                  activateCard={activateCard}
                  router={router}
                  getCardStatusStyles={getCardStatusStyles}
                />
              ))}
            </div>
          ) : (
            <EmptyStateFiltered type="active" />
          )}
        </TabsContent>

        <TabsContent value="inactive" className="mt-0">
          {loading ? (
            <CardSkeleton count={2} />
          ) : filteredCards.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCards.map((card) => (
                <CardItem
                  key={card.id}
                  card={card}
                  activateCard={activateCard}
                  router={router}
                  getCardStatusStyles={getCardStatusStyles}
                />
              ))}
            </div>
          ) : (
            <EmptyStateFiltered type="inactive" />
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Card component
function CardItem({ card, activateCard, router, getCardStatusStyles }: {
  card: YollooCard,
  activateCard: (id: string) => Promise<void>,
  router: any,
  getCardStatusStyles: (status: string) => any
}) {
  const styles = getCardStatusStyles(card.status);

  return (
    <Card className="shadow-md border-0 overflow-hidden transition-all hover:shadow-lg">
      <div className={`bg-gradient-to-r ${
        card.status === 'Active' ? 'from-primary/80 to-primary/60' :
        card.status === 'Inactive' ? 'from-amber-500/70 to-amber-400/50' :
        'from-red-500/70 to-red-400/50'
      } p-6`}>
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-semibold text-white mb-1">
              {card.customName || `Yolloo Card`}
            </h3>
            <p className="text-sm text-white/80">#{card.number}</p>
          </div>
          <div className="text-white/90">
            {styles.icon}
          </div>
        </div>
      </div>

      <CardContent className="p-6 space-y-4">
        <div className="flex flex-col space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Status</span>
            <Badge className={styles.badge}>{card.status}</Badge>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Type</span>
            <span className="text-sm font-medium">{card.type}</span>
          </div>
          {card.activationDate && (
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Activated</span>
              <span className="text-sm font-medium">{DateFormatter.forUserSafe(card.activationDate)}</span>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="px-6 py-4 border-t bg-slate-50/80 flex justify-between">
        <Button
          variant="ghost"
          className="text-slate-700 hover:text-primary hover:bg-primary/5"
          onClick={() => router.push(`/cards/${card.id}`)}
        >
          View Details
          <ChevronRight className="ml-1 h-4 w-4" />
        </Button>

        {card.status === 'Inactive' && (
          <Button
            onClick={() => activateCard(card.id)}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            Activate
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}

// Skeleton loader for cards
function CardSkeleton({ count }: { count: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array(count).fill(0).map((_, i) => (
        <Card key={i} className="shadow-md border-0 overflow-hidden">
          <div className="bg-gradient-to-r from-slate-200 to-slate-100 p-6">
            <Skeleton className="h-6 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </div>

          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>
              <div className="flex justify-between items-center">
                <Skeleton className="h-4 w-14" />
                <Skeleton className="h-4 w-24" />
              </div>
              <div className="flex justify-between items-center">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-20" />
              </div>
            </div>
          </CardContent>

          <CardFooter className="px-6 py-4 border-t bg-slate-50/80 flex justify-between">
            <Skeleton className="h-9 w-28" />
            <Skeleton className="h-9 w-24" />
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}

// Empty state component
function EmptyState() {
  return (
    <div className="border-2 border-dashed border-slate-200 rounded-lg p-10 text-center">
      <div className="mx-auto w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mb-6">
        <CreditCard className="h-8 w-8 text-slate-400" />
      </div>
      <h3 className="text-xl font-semibold mb-2">No Yolloo Cards Yet</h3>
      <p className="text-muted-foreground max-w-md mx-auto mb-6">
        You haven't added any Yolloo cards to your account. Add a card to get started with managing your eSIMs.
      </p>
    </div>
  )
}

// Empty state for filtered views
function EmptyStateFiltered({ type }: { type: "active" | "inactive" }) {
  return (
    <div className="border-2 border-dashed border-slate-200 rounded-lg p-10 text-center">
      <div className="mx-auto w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mb-6">
        {type === "active" ? (
          <CheckCircle className="h-8 w-8 text-slate-400" />
        ) : (
          <AlertTriangle className="h-8 w-8 text-slate-400" />
        )}
      </div>
      <h3 className="text-xl font-semibold mb-2">No {type === "active" ? "Active" : "Inactive"} Cards</h3>
      <p className="text-muted-foreground max-w-md mx-auto mb-6">
        {type === "active"
          ? "You don't have any active cards yet. Activate a card to see it here."
          : "You don't have any inactive cards. All your cards are activated or you need to add more cards."}
      </p>
    </div>
  )
}