{"version": 1, "rules": [{"id": "no-unused-imports", "pattern": "import\\s+([^*{}\\n]+?)\\s+from\\s+['\"]([^'\"]+)['\"]", "description": "检测未使用的导入", "filterPattern": "\\b\\1\\b", "matchesFilter": false, "severity": "warning", "message": "导入没有被使用: $1"}, {"id": "next-image-missing-alt", "pattern": "\\b(Image)\\s+([^>]*?)(?:\\/>|>)", "filterPattern": "\\balt=(['\"])[^'\"]*\\1", "matchesFilter": false, "description": "检查Image组件是否缺少alt属性", "severity": "warning", "message": "Image组件缺少alt属性，这对于可访问性很重要"}, {"id": "no-direct-document-access", "pattern": "\\bdocument\\.", "description": "避免在Next.js组件中直接访问document对象", "severity": "warning", "message": "在Next.js中直接访问document对象可能导致SSR问题，考虑使用useEffect或其他客户端钩子"}, {"id": "use-client-directive", "pattern": "\\b(useState|useEffect|useCallback|useMemo|useRef)\\b", "filterPattern": "^('use client'|\"use client\"|`use client`)", "matchesFilter": false, "filePattern": "\\.(tsx|jsx)$", "description": "检查使用React钩子的组件文件是否有'use client'指令", "severity": "warning", "message": "使用React钩子的组件文件需要添加'use client'指令"}, {"id": "inconsistent-naming", "pattern": "function\\s+([a-z][A-Za-z0-9]*)", "filePattern": "\\.(tsx|jsx)$", "description": "组件应该使用PascalCase命名", "severity": "warning", "message": "React组件应该使用PascalCase命名: $1 应该改为 ${1/^([a-z])(.*)/\\u$1$2/}"}, {"id": "no-any-type", "pattern": ":\\s*any\\b", "description": "避免使用any类型", "severity": "warning", "message": "尽量避免使用any类型，考虑使用更具体的类型或unknown"}, {"id": "no-console-logs", "pattern": "console\\.(log|debug|info)", "filePattern": "^(?!.*(\\.test|\\.spec)).*\\.(ts|tsx|js|jsx)$", "description": "避免在生产代码中使用console.log", "severity": "warning", "message": "避免在生产代码中使用console.log，考虑使用适当的日志库"}, {"id": "async-await-missing-try-catch", "pattern": "\\basync\\s+([^{]*?)\\{(?![^{}]*?try\\s*\\{)", "description": "异步函数应该使用try-catch处理错误", "severity": "warning", "message": "异步函数应该使用try-catch块来处理潜在错误"}, {"id": "explicit-function-return-type", "pattern": "function\\s+\\w+\\([^)]*\\)(?!\\s*:\\s*\\w)", "filePattern": "\\.tsx?$", "description": "函数应该有明确的返回类型", "severity": "information", "message": "考虑为函数添加明确的返回类型"}, {"id": "wrong-next-api-import", "pattern": "import\\s+[^*{}\\n]*?\\bapi\\b[^*{}\\n]*?\\s+from\\s+['\"]next/api", "description": "Next.js API路由不应该被导入", "severity": "error", "message": "Next.js API路由不应该被导入，它们应该作为端点使用"}, {"id": "tailwind-missing-classname", "pattern": "<[a-zA-Z0-9]+(\\s+[^>]*?)?\\s*>", "filterPattern": "className=|<(html|body|head|meta|script|link|style|title)\\b", "matchesFilter": true, "filePattern": "\\.(jsx|tsx)$", "description": "React组件应该有className属性用于Tailwind样式", "severity": "information", "message": "考虑添加className属性来应用Tailwind样式"}, {"id": "missing-prisma-error-handling", "pattern": "\\bprisma\\.[a-zA-Z]+\\.[a-zA-Z]+\\(", "filterPattern": "try\\s*\\{|catch\\s*\\(", "matchesFilter": true, "description": "Prisma查询应该有错误处理", "severity": "warning", "message": "Prisma查询应该用try-catch块处理潜在错误"}, {"id": "inline-styles", "pattern": "style=\\{\\{[^}]+\\}\\}", "description": "避免使用内联样式，优先使用Tailwind类", "severity": "information", "message": "考虑使用Tailwind类而不是内联样式"}, {"id": "form-missing-validation", "pattern": "<form[^>]*>", "filterPattern": "useForm|register|handleSubmit|onSubmit|validation", "matchesFilter": true, "description": "表单应该使用验证", "severity": "information", "message": "考虑添加表单验证，例如使用react-hook-form和zod"}]}