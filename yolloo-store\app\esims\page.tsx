import { Suspense } from "react";
import EsimsClient from "./EsimsClient";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";

// 商品类型定义
interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  stock: number;
  status: string;
  country: string | null;
  countryCode?: string | null;
  planType?: string | null;
  dataSize?: number | null;
  duration?: number | null;
  durationType?: string | null;
  sku: string;
  variants: {
    id: string;
    price: number;
    duration: number;
    durationType: string;
  }[];
}

// 获取所有 qr_code 商品
async function getQrCodeProducts(): Promise<Product[]> {
  try {
    const products = await prisma.product.findMany({
      where: {
        status: { in: ["ACTIVE", "INACTIVE"] },
        category: { name: "qr_code" },
      },
      select: {
        id: true,
        name: true,
        description: true,
        price: true,
        images: true,
        stock: true,
        status: true,
        country: true,
        countryCode: true,
        planType: true,
        dataSize: true,
        sku: true,
        variants: {
          select: {
            id: true,
            price: true,
            duration: true,
            durationType: true,
          }
        },
      },
      orderBy: { createdAt: "desc" },
    });
    return products.map((p) => ({
      ...p,
      variants: Array.isArray(p.variants)
        ? p.variants.map(v => ({
            ...v,
            price: typeof v.price === 'number' ? v.price : Number(v.price),
          }))
        : [],
      price: typeof p.price === 'number' ? p.price : Number(p.price),
    }));
  } catch (e) {
    return [];
  }
}

export default async function EsimsPage() {
  const products = await getQrCodeProducts();
  if (!products) return notFound();

  // 分类 Local/Regional
  const localProducts = products.filter(
    (p) => p.country && p.country.split(/[,;]/).length === 1
  );
  const regionalProducts = products.filter(
    (p) => p.country && p.country.split(/[,;]/).length > 1
  );

  // 聚合 destination 列表
  const localDestinations = Array.from(
    new Set(localProducts.map((p) => p.country?.trim()).filter(Boolean))
  ) as string[];
  const regionalDestinations = Array.from(
    new Set(
      regionalProducts
        .flatMap((p) =>
          p.country
            ? p.country.split(/[,;]/).map((c) => c.trim())
            : []
        )
        .filter(Boolean)
    )
  ) as string[];

  return (
    <main className="max-w-5xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-2">eSIM QR Code Plans</h1>
      <p className="text-muted-foreground mb-8">
        Buy a general eSIM QR-Code for your destination which can be used anywhere. Choose Local or Regional, then select your country/region to see available esims.
      </p>
      <Suspense fallback={<div className="text-center py-12">Loading...</div>}>
        <EsimsClient
          allProducts={products}
          localDestinations={localDestinations}
          regionalDestinations={regionalDestinations}
        />
      </Suspense>
    </main>
  );
} 