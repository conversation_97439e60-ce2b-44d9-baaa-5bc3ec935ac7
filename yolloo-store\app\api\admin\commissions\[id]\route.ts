import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


type MarketingCommissionStatus = "PENDING" | "APPROVED" | "REJECTED" | "PAID"

interface RouteParams {
  params: {
    id: string
  }
}

export async function PATCH(
  request: Request,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await request.json()
    const { status } = body as { status: MarketingCommissionStatus }

    if (!status || !Object.values(["PENDING", "APPROVED", "REJECTED", "PAID"]).includes(status)) {
      return new NextResponse("Invalid status", { status: 400 })
    }

    // Update commission status
    const commission = await prisma.marketingCommission.update({
      where: {
        id: params.id,
      },
      data: {
        status,
        updatedAt: new Date(),
      },
    })

    return NextResponse.json(commission)
  } catch (error) {
    console.error("[COMMISSION_PATCH]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 