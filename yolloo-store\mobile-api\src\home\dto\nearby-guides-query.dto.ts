import { IsInt, <PERSON>N<PERSON>ber, <PERSON>Optional, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class NearbyGuidesQueryDto {
  @IsNumber()
  @Type(() => Number)
  lat: number;

  @IsNumber()
  @Type(() => Number)
  lng: number;

  @IsOptional()
  @IsNumber()
  @Min(0.1)
  @Type(() => Number)
  radius?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number;
}
