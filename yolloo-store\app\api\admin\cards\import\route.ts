import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { cleanCardNumber } from "@/lib/utils"
import * as XLSX from 'xlsx'

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// 清理 UID 字符串的辅助函数
function cleanUID(uid: string): string {
  if (typeof uid !== 'string') {
    // 处理 Excel 数字格式
    uid = String(uid)
  }
  // 使用统一的卡号清理函数，移除所有非数字字符
  return cleanCardNumber(uid)
}

// 通用的 UID 处理函数
async function processUIDs(uids: string[], source: string) {
  console.log(`[CARDS_IMPORT] Received ${uids.length} UIDs from ${source} for processing`)

  // 数据清洗和验证
  const cleanedUids = uids
    .map((uid: string) => cleanUID(uid))
    .filter((uid: string) => uid.length > 0) // 过滤掉空字符串

  console.log(`[CARDS_IMPORT] After cleaning: ${cleanedUids.length} valid UIDs`)
  
  // 检查重复的 UID
  const uniqueUids = Array.from(new Set(cleanedUids)) as string[]
  const duplicatesCount = cleanedUids.length - uniqueUids.length
  
  console.log(`[CARDS_IMPORT] Found ${duplicatesCount} duplicate UIDs in input`)
  if (duplicatesCount > 0) {
    // 找出重复的 UID
    const duplicates = cleanedUids.filter((uid: string, index: number) => cleanedUids.indexOf(uid) !== index)
    console.log(`[CARDS_IMPORT] Duplicate UIDs: ${duplicates.join(', ')}`)
  }

  // 检查这些 UID 是否已经存在
  const existingCards = await prisma.yollooCard.findMany({
    where: {
      number: {
        in: uniqueUids
      }
    },
    select: {
      number: true
    }
  })

  const existingUids = existingCards.map(card => card.number)
  const newUids = uniqueUids.filter(uid => !existingUids.includes(uid))

  console.log(`[CARDS_IMPORT] Found ${existingUids.length} existing UIDs, ${newUids.length} new UIDs to create`)
  if (existingUids.length > 0) {
    console.log(`[CARDS_IMPORT] Existing UIDs: ${existingUids.join(', ')}`)
  }

  if (newUids.length === 0) {
    return {
      success: false,
      message: "All UIDs already exist in the database",
      stats: {
        total: uids.length,
        processed: cleanedUids.length,
        duplicatesInInput: duplicatesCount,
        existingInDB: existingUids.length,
        created: 0,
        skipped: existingUids,
      }
    }
  }

  console.log(`[CARDS_IMPORT] Creating ${newUids.length} new cards...`)
  const cards = await prisma.$transaction(
    newUids.map((uid: string) => {
      return prisma.yollooCard.create({
        data: {
          number: uid,
          type: "physical",
          status: "Inactive"
        },
      })
    })
  )

  return {
    success: true,
    stats: {
      total: uids.length,
      processed: cleanedUids.length,
      duplicatesInInput: duplicatesCount,
      existingInDB: existingUids.length,
      created: cards.length,
      skipped: existingUids,
    }
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      console.log("[CARDS_IMPORT] Unauthorized access attempt")
      return new NextResponse("Unauthorized", { status: 403 })
    }

    const formData = await req.formData()
    const file = formData.get("file") as File
    const uidsJson = formData.get("uids") as string

    let uids: string[] = []
    let source = ''

    if (file) {
      // 处理文件上传（Excel 或 CSV）
      const buffer = await file.arrayBuffer()
      const fileName = file.name.toLowerCase()

      if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        // Excel 处理
        source = 'Excel'
        const workbook = XLSX.read(buffer)
        
        // Check if SheetNames array is not empty
        if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
          return NextResponse.json({ error: 'Excel file has no sheets' }, { status: 400 })
        }
        
        // Use type assertions to tell TypeScript these values are defined
        const sheetName = workbook.SheetNames[0] as string
        const firstSheet = workbook.Sheets[sheetName as keyof typeof workbook.Sheets]
        
        // Check if the sheet exists
        if (!firstSheet) {
          return NextResponse.json({ error: 'Sheet not found in Excel file' }, { status: 400 })
        }
        
        const data = XLSX.utils.sheet_to_json(firstSheet, { header: 1 }) as any[][]
        
        // 假设第一列是 UID，跳过标题行
        uids = data.slice(1).map(row => String(row[0] || ''))
      } else if (fileName.endsWith('.csv')) {
        // CSV 处理
        source = 'CSV'
        const text = new TextDecoder().decode(buffer)
        uids = text.split(/\r?\n/).slice(1) // 跳过标题行
      } else {
        return new NextResponse("Unsupported file format. Please upload .xlsx, .xls, or .csv file", { status: 400 })
      }
    } else if (uidsJson) {
      // JSON 格式处理
      source = 'JSON'
      uids = JSON.parse(uidsJson) as string[]
    } else {
      console.log("[CARDS_IMPORT] No input provided")
      return new NextResponse("No file or UIDs provided", { status: 400 })
    }

    const result = await processUIDs(uids, source)
    console.log("[CARDS_IMPORT] Import completed successfully", result)
    
    if (!result.success) {
      return new NextResponse(result.message, { status: 400 })
    }

    return NextResponse.json(result.stats)
  } catch (error) {
    console.error("[CARDS_IMPORT] Error during import:", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 