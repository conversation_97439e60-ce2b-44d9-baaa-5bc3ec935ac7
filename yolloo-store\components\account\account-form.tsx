"use client"

import { useState } from "react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Icons } from "@/components/icons"
import { toast } from "sonner"
import type { User } from "@prisma/client"

interface AccountFormProps {
  user: Pick<User, "id" | "name" | "email" | "image">
}

export function AccountForm({ user }: AccountFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [nameError, setNameError] = useState<string | null>(null)

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setIsLoading(true)
    // Reset error states
    setNameError(null)

    try {
      const formData = new FormData(event.currentTarget)
      const name = formData.get("name") as string
      
      // Validate required fields
      if (!name || name.trim() === "") {
        setNameError("Name is required")
        setIsLoading(false)
        return
      }

      const response = await fetch("/api/users", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          // Email is no longer sent
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        if (data.details && Array.isArray(data.details)) {
          // Handle field validation errors
          data.details.forEach((issue: any) => {
            if (issue.path.includes("name")) {
              setNameError(issue.message)
            }
          })
        }
        
        throw new Error(data.error || "Failed to update profile")
      }

      toast.success(data.message || "Your profile has been updated.")
      router.refresh()
    } catch (error: any) {
      toast.error(error.message || "Something went wrong. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={onSubmit} className="space-y-8">
      <div className="space-y-2">
        <h4 className="text-sm font-medium">Profile</h4>
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            {user.image && (
              <Image
                src={user.image}
                alt={user.name || "Avatar"}
                width={40}
                height={40}
                className="rounded-full"
              />
            )}
            <Button variant="outline" type="button" disabled>
              Change Avatar
            </Button>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              name="name"
              className={`max-w-[400px] ${nameError ? "border-red-500" : ""}`}
              size={32}
              defaultValue={user.name || ""}
            />
            {nameError && (
              <p className="text-sm text-red-500">{nameError}</p>
            )}
          </div>
          <div className="grid gap-2">
            <Label htmlFor="email">Email (Cannot be modified)</Label>
            <Input
              id="email"
              name="email"
              type="email"
              className="max-w-[400px] bg-muted"
              size={32}
              defaultValue={user.email || ""}
              disabled
            />
            <p className="text-xs text-muted-foreground">Email address cannot be changed at this time</p>
          </div>
        </div>
      </div>
      <Button type="submit" disabled={isLoading}>
        {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
        Update Profile
      </Button>
    </form>
  )
} 