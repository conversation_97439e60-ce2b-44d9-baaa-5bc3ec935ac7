"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Address } from "@prisma/client"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card } from "@/components/ui/card"
import { Icons } from "@/components/icons"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { toast } from "sonner"

interface AddressFormProps {
  userId: string
  defaultAddresses: Address[]
}

export function AddressForm({ userId, defaultAddresses }: AddressFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [addresses, setAddresses] = useState(defaultAddresses)
  const [isAdding, setIsAdding] = useState(false)
  const [addressToDelete, setAddressToDelete] = useState<string | null>(null)

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setIsLoading(true)

    const formData = new FormData(event.currentTarget)
    const response = await fetch("/api/addresses", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        name: formData.get("name"),
        phone: formData.get("phone"),
        address1: formData.get("address1"),
        address2: formData.get("address2"),
        city: formData.get("city"),
        state: formData.get("state"),
        postalCode: formData.get("postalCode"),
        country: formData.get("country"),
      }),
    })

    setIsLoading(false)

    if (!response?.ok) {
      return toast.error("Something went wrong.")
    }

    const address = await response.json()
    setAddresses([...addresses, address])
    setIsAdding(false)
    toast.success("Address added successfully.")
    router.refresh()
  }

  async function onDelete(addressId: string) {
    setIsLoading(true)

    try {
      const response = await fetch(`/api/addresses/${addressId}`, {
        method: "DELETE",
      })

      if (!response?.ok) {
        throw new Error("Failed to delete address")
      }

      setAddresses(addresses.filter((address) => address.id !== addressId))
      toast.success("Address deleted successfully.")
      router.refresh()
    } catch (error) {
      toast.error("Something went wrong.")
    } finally {
      setIsLoading(false)
      setAddressToDelete(null)
    }
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-sm font-medium">Addresses</h4>
          <p className="text-sm text-muted-foreground">
            Manage your shipping addresses
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => setIsAdding(!isAdding)}
          disabled={isLoading}
        >
          {isAdding ? (
            <>Cancel</>
          ) : (
            <>
              <Icons.add className="mr-2 h-4 w-4" />
              Add Address
            </>
          )}
        </Button>
      </div>

      <div className="grid gap-4">
        {addresses.map((address) => (
          <Card key={address.id} className="p-4">
            <div className="flex items-start justify-between">
              <div className="grid gap-1">
                <p className="font-medium">{address.name}</p>
                <p className="text-sm text-muted-foreground">
                  {address.address1}
                  {address.address2 && `, ${address.address2}`}
                  <br />
                  {address.city}, {address.state} {address.postalCode}
                  <br />
                  {address.country}
                  <br />
                  Phone: {address.phone}
                </p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="text-red-500 hover:text-red-600 hover:bg-red-50"
                onClick={() => setAddressToDelete(address.id)}
                disabled={isLoading}
              >
                <Icons.trash className="h-4 w-4" />
              </Button>
            </div>
          </Card>
        ))}

        {isAdding && (
          <form onSubmit={onSubmit} className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                name="name"
                required
                placeholder="Enter your full name"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                required
                placeholder="Enter your phone number"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="address1">Address Line 1</Label>
              <Input
                id="address1"
                name="address1"
                required
                placeholder="Street address or P.O. Box"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="address2">Address Line 2</Label>
              <Input
                id="address2"
                name="address2"
                placeholder="Apartment, suite, unit, etc."
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="city">City</Label>
                <Input id="city" name="city" required />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="state">State / Province</Label>
                <Input id="state" name="state" required />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input id="postalCode" name="postalCode" required />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="country">Country</Label>
                <Input id="country" name="country" required />
              </div>
            </div>
            <Button type="submit" disabled={isLoading}>
              {isLoading && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Add Address
            </Button>
          </form>
        )}
      </div>

      <AlertDialog open={!!addressToDelete} onOpenChange={() => setAddressToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Address</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this address? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              onClick={() => addressToDelete && onDelete(addressToDelete)}
            >
              {isLoading ? (
                <>
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 