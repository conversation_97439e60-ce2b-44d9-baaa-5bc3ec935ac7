"use client"

import { useRouter, useSearchParams } from "next/navigation"
import { Card } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

interface ProductFilterProps {
  mccs: { mcc: string | null }[]
}

export function ProductFilter({ mccs }: ProductFilterProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const currentMcc = searchParams.get("mcc")

  const handleMccChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString())
    if (value === "all") {
      params.delete("mcc")
    } else {
      params.set("mcc", value)
    }
    router.push(`/products?${params.toString()}`)
  }

  return (
    <Card className="p-6">
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Operator</h3>
          <RadioGroup
            value={currentMcc || "all"}
            onValueChange={handleMccChange}
            className="space-y-3"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="all" />
              <Label htmlFor="all">All Operators</Label>
            </div>
            {mccs.map((item) => 
              item.mcc && (
                <div key={item.mcc} className="flex items-center space-x-2">
                  <RadioGroupItem value={item.mcc} id={item.mcc} />
                  <Label htmlFor={item.mcc}>
                    MCC: {item.mcc}
                  </Label>
                </div>
              )
            )}
          </RadioGroup>
        </div>
      </div>
    </Card>
  )
} 