import React from 'react'
import { DateFormatter } from '@/lib/utils'

export default function TermsPage() {
  return (
      <div className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <h1 className="text-4xl font-bold heading-gradient mb-8">Terms of Usage</h1>

            <div className="prose prose-lg max-w-none">
              <p className="text-gray-600">
                Last updated: {DateFormatter.forUserSafe(new Date())}
              </p>

              <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">1. Acceptance of Terms</h2>
              <p>
                By accessing and using Yolloo's services, you agree to be bound by these Terms of Usage. If you do not agree to these terms, please do not use our services.
              </p>

              <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">2. Service Description</h2>
              <p>
                Yolloo provides eSIM services that allow users to access mobile data connectivity. Our services are subject to availability and may vary by region and device compatibility.
              </p>

              <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">3. User Responsibilities</h2>
              <ul className="list-disc pl-6 space-y-2">
                <li>Provide accurate and complete information when creating an account</li>
                <li>Maintain the security of your account credentials</li>
                <li>Use the services in compliance with applicable laws and regulations</li>
                <li>Not engage in any unauthorized or fraudulent use of the service</li>
              </ul>

              <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">4. Payment and Billing</h2>
              <p>
                All purchases are final and non-refundable unless otherwise required by law. Prices are subject to change without notice. You agree to pay all charges at the prices then in effect for your purchases.
              </p>

              <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">5. Service Availability</h2>
              <p>
                While we strive to provide uninterrupted service, we do not guarantee continuous, uninterrupted access to our services. Network availability may vary and is subject to various factors beyond our control.
              </p>

              <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">6. Privacy Policy</h2>
              <p>
                Your use of our services is also governed by our Privacy Policy. Please review our Privacy Policy to understand how we collect, use, and protect your information.
              </p>

              <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">7. Limitation of Liability</h2>
              <p>
                To the maximum extent permitted by law, Yolloo shall not be liable for any indirect, incidental, special, consequential, or punitive damages, or any loss of profits or revenues.
              </p>

              <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">8. Changes to Terms</h2>
              <p>
                We reserve the right to modify these terms at any time. We will notify users of any material changes via email or through our website. Continued use of our services after such modifications constitutes acceptance of the updated terms.
              </p>

              <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">9. Contact Information</h2>
              <p>
                If you have any questions about these Terms, please contact us at{' '}
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
  )
}