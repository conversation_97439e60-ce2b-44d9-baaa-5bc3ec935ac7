import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import crypto from "crypto";
import { sendPasswordResetEmail } from "@/app/services/emailService";
import { DateUtils } from "@/lib/utils";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Validate email schema
const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
});

// POST - Request password reset
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // Validate input
    const validationResult = forgotPasswordSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid email address" },
        { status: 400 }
      );
    }

    const { email } = validationResult.data;

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
    });

    // For security reasons, always return success even if the email doesn't exist
    // This prevents email enumeration attacks
    if (!user) {
      return NextResponse.json({
        success: true,
        message: "If your email is in our system, you will receive a password reset link shortly."
      });
    }

    // Delete any existing reset tokens for this user
    await prisma.passwordResetToken.deleteMany({
      where: { userId: user.id },
    });

    // Generate a new password reset token
    const token = crypto.randomBytes(32).toString("hex");

    // Set expiration date (7 days from now)
    const expiresAt = DateUtils.addDays(new Date(), 7);

    // Create password reset token
    await prisma.passwordResetToken.create({
      data: {
        userId: user.id,
        token,
        expiresAt,
      },
    });

    // Generate reset link
    const resetLink = `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password?token=${token}`;

    // Send password reset email
    try {
      await sendPasswordResetEmail(
        user.email!,
        user.name || "User",
        resetLink
      );
    } catch (emailError) {
      console.error("Error sending password reset email:", emailError);
      // Continue anyway to avoid revealing whether the email exists
    }

    return NextResponse.json({
      success: true,
      message: "If your email is in our system, you will receive a password reset link shortly."
    });
  } catch (error) {
    console.error("Error processing forgot password request:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}