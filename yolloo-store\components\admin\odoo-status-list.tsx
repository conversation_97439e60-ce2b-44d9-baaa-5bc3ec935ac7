"use client"

import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DateFormatter } from "@/lib/utils"
import { UpdateOdooStatusButton } from "@/components/admin/update-odoo-status-button"
import { CreateOdooOrderButton } from "@/components/admin/create-odoo-order-button"

// 定义状态样式映射
const STATUS_STYLES = {
  processing: { variant: "default" as const },
  delivered: { variant: "default" as const },
  shipped: { variant: "secondary" as const },
  error: { variant: "destructive" as const },
  pending: { variant: "secondary" as const },
  default: { variant: "secondary" as const },
}

// 获取状态样式
function getStatusStyle(status: string) {
  const normalizedStatus = status.toLowerCase()
  return STATUS_STYLES[normalizedStatus as keyof typeof STATUS_STYLES] || STATUS_STYLES.default
}

interface OdooStatusListProps {
  orderId: string
  orderItems: any[]
  odooStatuses: any[]
}

export function OdooStatusList({ orderId, orderItems, odooStatuses }: OdooStatusListProps) {
  // 过滤掉默认状态，只显示真实的Odoo订单状态
  const realStatuses = odooStatuses.filter(status => status.variantCode !== "default")

  // 获取商品项目信息
  const getItemInfo = (variantCode: string, uid: string | null) => {
    return orderItems.find(item =>
      item.variantCode === variantCode &&
      (uid === null || item.uid === uid)
    )
  }

  // 简化产品名称显示
  const getProductDisplayName = (status: any) => {
    const item = getItemInfo(status.variantCode, status.uid)
    if (item?.variantText) {
      return item.variantText
    }
    return status.productName || status.variantCode
  }

  // 按商品顺序排序状态记录，保持与商品列表的一致性
  const orderedStatuses = orderItems.map((item, itemIndex) => {
    const matchingStatus = realStatuses.find(status =>
      status.variantCode === item.variantCode &&
      (status.uid === null || status.uid === item.uid)
    );
    return matchingStatus ? { ...matchingStatus, itemIndex } : null;
  }).filter(Boolean); // 过滤掉没有状态记录的商品

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Odoo Order Status</h3>
        <div className="flex items-center gap-2">
          <UpdateOdooStatusButton orderId={orderId} />
          <CreateOdooOrderButton orderId={orderId} />
        </div>
      </div>

      {/* Content */}
      {orderedStatuses.length === 0 ? (
        <div className="text-center py-6 text-muted-foreground">
          <p>No Odoo order status available. Click "Create Odoo Order" to create one.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {orderedStatuses.map((statusWithIndex) => {
            const statusStyle = getStatusStyle(statusWithIndex.status)
            const productName = getProductDisplayName(statusWithIndex)

            return (
              <Card key={statusWithIndex.id} className="p-4">
                {/* Header with product name and status */}
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium">{productName}</h4>
                    {orderedStatuses.length > 1 && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Item {statusWithIndex.itemIndex + 1} of {orderItems.length}
                      </p>
                    )}
                  </div>
                  <Badge variant={statusStyle.variant}>{statusWithIndex.status}</Badge>
                </div>

                {/* Details in simple key-value format */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="font-medium text-muted-foreground">Description: </span>
                    <span>{statusWithIndex.description || '-'}</span>
                  </div>

                  <div>
                    <span className="font-medium text-muted-foreground">Odoo Order Ref: </span>
                    <span className="font-mono">{statusWithIndex.odooOrderRef || '-'}</span>
                  </div>

                  <div>
                    <span className="font-medium text-muted-foreground">Tracking Number: </span>
                    <span className="font-mono">{statusWithIndex.trackingNumber || '-'}</span>
                  </div>

                  <div>
                    <span className="font-medium text-muted-foreground">Plan State: </span>
                    <span>{statusWithIndex.planState || '-'}</span>
                  </div>

                  <div>
                    <span className="font-medium text-muted-foreground">UID: </span>
                    <span className="font-mono">{statusWithIndex.uid || '-'}</span>
                  </div>

                  <div>
                    <span className="font-medium text-muted-foreground">Last Updated: </span>
                    <span>{DateFormatter.withTimezone(statusWithIndex.lastCheckedAt)}</span>
                  </div>

                  <div>
                    <span className="font-medium text-muted-foreground">Variant Code: </span>
                    <span className="font-mono text-xs">{statusWithIndex.variantCode || '-'}</span>
                  </div>

                  <div>
                    <span className="font-medium text-muted-foreground">Digital: </span>
                    <span>{statusWithIndex.isDigital ? 'Yes' : 'No'}</span>
                  </div>

                  <div>
                    <span className="font-medium text-muted-foreground">Delivered Qty: </span>
                    <span>{statusWithIndex.deliveredQty || 0}</span>
                  </div>
                </div>
              </Card>
            )
          })}
        </div>
      )}
    </div>
  )
}
