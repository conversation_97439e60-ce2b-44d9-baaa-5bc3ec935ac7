import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export const dynamic = 'force-dynamic';

// POST - Manually refresh organization statistics (for organization admins)
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const organizationId = params.id;
    
    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Check if the user is a member of this organization and has admin privileges
    const userProfile = await prisma.affiliateProfile.findFirst({
      where: {
        userId: session.user.id,
        organizationId: organizationId,
      },
    });
    
    if (!userProfile || !userProfile.isAdmin) {
      return NextResponse.json({ error: "You don't have permission to refresh this organization's statistics" }, { status: 403 });
    }
    
    // Get all members of the organization
    const members = await prisma.affiliateProfile.findMany({
      where: { organizationId },
      select: { id: true },
    });
    
    const memberIds = members.map(member => member.id);
    
    // Get all orders promoted by members
    const referrals = await prisma.affiliateReferral.findMany({
      where: {
        affiliateId: { in: memberIds },
      },
      include: {
        order: true,
      },
    });
    
    // Process all referrals
    for (const referral of referrals) {
      // 1. Check and create visit records if needed
      const existingVisit = await prisma.affiliateVisit.findFirst({
        where: {
          affiliateId: referral.affiliateId,
          orderId: referral.orderId,
        },
      });
      
      if (!existingVisit) {
        // Create visit record
        await prisma.affiliateVisit.create({
          data: {
            affiliateId: referral.affiliateId,
            organizationId,
            source: "direct",
            path: "/",
            userAgent: "Manual refresh",
            referrer: "Manual refresh",
            convertedToOrder: true,
            orderId: referral.orderId,
          },
        });
      }
      
      // 2. Check and create organization commission records if needed
      if (!referral.organizationCommissionId) {
        // Check if organization commission record exists
        const existingOrgCommission = await prisma.organizationCommission.findFirst({
          where: {
            organizationId,
            referrals: {
              some: {
                orderId: referral.orderId
              }
            }
          }
        });
        
        if (!existingOrgCommission) {
          // Get order information - already loaded in include
          const order = referral.order;
          
          if (order) {
            // Calculate organization commission
            const commissionAmount = order.total * organization.commissionRate;
            const newOrgCommission = await prisma.organizationCommission.create({
              data: {
                organizationId,
                commissionAmount,
                status: order.status === "DELIVERED" ? "APPROVED" : "PENDING",
              }
            });
            
            // Update referral relationship
            await prisma.affiliateReferral.update({
              where: { id: referral.id },
              data: {
                organizationCommissionId: newOrgCommission.id
              }
            });
          }
        } else {
          // If commission record exists but not linked, update the link
          await prisma.affiliateReferral.update({
            where: { id: referral.id },
            data: {
              organizationCommissionId: existingOrgCommission.id
            }
          });
        }
      }
    }
    
    // Update organization commission status
    const updatedCommissions = await prisma.organizationCommission.updateMany({
      where: {
        organizationId,
        status: "PENDING",
        referrals: {
          some: {
            order: {
              status: "DELIVERED",
            },
          },
        },
      },
      data: {
        status: "APPROVED",
      },
    });
    
    // Update member referral status
    const updatedReferrals = await prisma.affiliateReferral.updateMany({
      where: {
        affiliate: {
          organizationId,
        },
        status: "PENDING",
        order: {
          status: "DELIVERED",
        },
      },
      data: {
        status: "APPROVED",
      },
    });
    
    // Recalculate organization's total earnings
    const totalCommissions = await prisma.organizationCommission.aggregate({
      where: {
        organizationId,
        status: "APPROVED",
      },
      _sum: {
        commissionAmount: true,
      },
    });
    
    // Get total commissions earned by members
    const memberCommissions = await prisma.affiliateReferral.aggregate({
      where: {
        affiliate: {
          organizationId,
        },
        status: "APPROVED",
      },
      _sum: {
        commissionAmount: true,
      },
    });
    
    const orgCommissions = totalCommissions._sum.commissionAmount || 0;
    const memCommissions = memberCommissions._sum.commissionAmount || 0;
    
    // Organization actual earnings = Total organization commissions - Member commissions
    const orgActualEarnings = orgCommissions - memCommissions;
    
    // Update organization's totalEarnings
    await prisma.affiliateOrganization.update({
      where: { id: organizationId },
      data: {
        totalEarnings: orgActualEarnings > 0 ? orgActualEarnings : 0,
      },
    });
    
    // Calculate visit and conversion data
    const totalVisits = await prisma.affiliateVisit.count({ where: { organizationId } });
    const totalConversions = await prisma.affiliateVisit.count({ 
      where: { organizationId, convertedToOrder: true } 
    });
    const conversionRate = totalVisits > 0 ? (totalConversions / totalVisits) * 100 : 0;
    
    return NextResponse.json({
      success: true,
      message: "Organization statistics refreshed successfully",
      updatedVisits: referrals.length,
      updatedCommissions: updatedCommissions.count,
      updatedReferrals: updatedReferrals.count,
      stats: {
        totalVisits,
        totalConversions,
        conversionRate,
        totalCommissions: orgCommissions,
        memberCommissions: memCommissions,
        organizationActualEarnings: orgActualEarnings
      }
    });
  } catch (error) {
    console.error("Error refreshing organization statistics:", error);
    return NextResponse.json(
      { error: "Failed to refresh organization statistics" },
      { status: 500 }
    );
  }
} 