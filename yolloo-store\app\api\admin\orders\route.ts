import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// GET /api/admin/orders - 管理员获取所有订单
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const search = searchParams.get("search");
    const sort = searchParams.get("sort");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    const skip = (page - 1) * limit;

    let orderBy: any = { createdAt: "desc" };
    if (sort === "total_asc") orderBy = { total: "asc" };
    if (sort === "total_desc") orderBy = { total: "desc" };

    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { id: { contains: search } },
        {
          user: {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { email: { contains: search, mode: "insensitive" } },
            ],
          },
        },
        {
          items: {
            some: {
              OR: [
                // 移除 product 关系查询，因为我们已经移除了外键关系
                // 使用 variantText 进行搜索
                { variantText: { contains: search, mode: "insensitive" } }
              ]
            }
          }
        }
      ];
    }

    if (startDate && endDate) {
      where.createdAt = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    }

    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        select: {
          id: true,
          total: true,
          status: true,
          createdAt: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          items: {
            select: {
              id: true,
              quantity: true,
              price: true,
              // 移除 product 关系查询，因为我们已经移除了外键关系
            },
          },
          shippingAddress: true,
          shippingAddressSnapshot: true,
          payment: true,
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.order.count({ where }),
    ]);

    // 计算统计信息
    const stats = await prisma.order.aggregate({
      where,
      _sum: {
        total: true,
      },
      _count: true,
    });

    // 按状态分组统计
    const statusStats = await prisma.order.groupBy({
      by: ["status"],
      where,
      _count: true,
    });

    return NextResponse.json({
      orders,
      total,
      pages: Math.ceil(total / limit),
      stats: {
        totalOrders: stats._count,
        totalRevenue: stats._sum.total || 0,
        statusBreakdown: statusStats.reduce((acc, curr) => {
          acc[curr.status] = curr._count;
          return acc;
        }, {} as Record<string, number>),
      },
    });
  } catch (error) {
    console.error("[ADMIN_ORDERS_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// PATCH /api/admin/orders - 批量更新订单状态
export async function PATCH(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { orderIds, status } = body;

    if (!orderIds || !Array.isArray(orderIds) || !status) {
      return new NextResponse("Invalid request", { status: 400 });
    }

    // 更新订单状态
    await prisma.order.updateMany({
      where: {
        id: {
          in: orderIds,
        },
      },
      data: {
        status,
      },
    });

    return new NextResponse(null, { status: 200 });
  } catch (error) {
    console.error("[ADMIN_ORDERS_PATCH]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}