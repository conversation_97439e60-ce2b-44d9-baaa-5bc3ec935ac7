'use client'

import Image from 'next/image'
import { motion } from 'framer-motion'
import { CheckIcon } from '@heroicons/react/24/solid'

const InfinityIcon = () => (
  <div className="w-5 h-5 relative">
    <Image src="/Frame.svg" alt="Infinity" fill className="object-contain" />
  </div>
)

export default function NewComparePlans() {
  return (
    <section id="compare-plans" className="py-20 bg-gradient-to-b from-white to-[#FFF0F5]">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl font-bold mb-6 text-[#D63A5A] leading-tight">
            Compare Plans
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Find the perfect Yolloo card for your needs
          </p>
        </motion.div>

        {/* Comparison Table */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="max-w-6xl mx-auto"
        >
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto relative">
            <table className="w-full border-collapse bg-white rounded-2xl shadow-lg overflow-hidden">
              <thead>
                <tr>
                  <th className="p-6 text-left bg-gray-50 border-b border-gray-200 font-semibold text-gray-700 relative">
                    {/* Decorative Image positioned in the first header cell */}
                    <div className="absolute top-2 left-2 z-10">
                      <div className="w-32 h-20 relative">
                        <Image
                          src="/image1.svg"
                          alt="Comparison illustration"
                          fill
                          className="object-contain"
                        />
                      </div>
                    </div>
                  </th>
                  <th className="p-6 text-center bg-gray-50 border-b border-gray-200">
                    <div className="text-xl font-bold text-gray-700">Lite</div>
                    <div className="text-sm text-gray-500 mt-1">Basic</div>
                  </th>
                  <th className="p-6 text-center bg-gray-50 border-b border-gray-200">
                    <div className="text-xl font-bold text-gray-700">Plus</div>
                    <div className="text-sm text-gray-500 mt-1">Popular</div>
                  </th>
                  <th className="p-6 text-center bg-gradient-to-br from-[#D63A5A] to-[#B82E4E] border-b border-[#D63A5A]">
                    <div className="text-xl font-bold text-white">Max</div>
                    <div className="text-sm text-white/80 mt-1">Best Value</div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {/* Price Row */}
                <tr className="border-b border-gray-100">
                  <td className="p-6 font-semibold text-gray-700">Price</td>
                  <td className="p-6 text-center">
                    <span className="text-2xl font-bold text-[#D63A5A]">$12</span>
                    <span className="text-sm text-gray-500 ml-1">USD</span>
                  </td>
                  <td className="p-6 text-center">
                    <span className="text-2xl font-bold text-[#D63A5A]">$21</span>
                    <span className="text-sm text-gray-500 ml-1">USD</span>
                  </td>
                  <td className="p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10">
                    <span className="text-2xl font-bold text-[#D63A5A]">$23</span>
                    <span className="text-sm text-gray-500 ml-1">USD</span>
                  </td>
                </tr>

                {/* eSIM Downloads Row */}
                <tr className="border-b border-gray-100">
                  <td className="p-6 font-semibold text-gray-700">eSIM Downloads</td>
                  <td className="p-6 text-center">
                    <div className="flex items-center justify-center gap-2">
                      <InfinityIcon />
                      <span className="text-sm font-medium">Unlimited</span>
                    </div>
                  </td>
                  <td className="p-6 text-center">
                    <div className="flex items-center justify-center gap-2">
                      <InfinityIcon />
                      <span className="text-sm font-medium">Unlimited</span>
                    </div>
                  </td>
                  <td className="p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10">
                    <div className="flex items-center justify-center gap-2">
                      <InfinityIcon />
                      <span className="text-sm font-medium text-white">Unlimited</span>
                    </div>
                  </td>
                </tr>

                {/* Device Support Row */}
                <tr className="border-b border-gray-100">
                  <td className="p-6 font-semibold text-gray-700">Device Support</td>
                  <td className="p-6 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-6 h-6 relative">
                        <Image src="/android .svg" alt="Android" fill className="object-contain" />
                      </div>
                    </div>
                  </td>
                  <td className="p-6 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-6 h-6 relative">
                        <Image src="/android .svg" alt="Android" fill className="object-contain" />
                      </div>
                    </div>
                  </td>
                  <td className="p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10">
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-6 h-6 relative">
                        <Image src="/android .svg" alt="Android" fill className="object-contain" />
                      </div>
                      <div className="w-6 h-6 relative">
                        <Image src="/apple.svg" alt="Apple" fill className="object-contain" />
                      </div>
                    </div>
                  </td>
                </tr>

                {/* eSIM Storage Row */}
                <tr className="border-b border-gray-100">
                  <td className="p-6 font-semibold text-gray-700">eSIM Storage</td>
                  <td className="p-6 text-center text-sm font-medium">5 profiles</td>
                  <td className="p-6 text-center text-sm font-medium">15 profiles</td>
                  <td className="p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10 text-sm font-medium">30 profiles</td>
                </tr>

                {/* Global Coverage Row */}
                <tr className="border-b border-gray-100">
                  <td className="p-6 font-semibold text-gray-700">Global Coverage</td>
                  <td className="p-6 text-center">
                    <CheckIcon className="w-6 h-6 text-green-500 mx-auto" />
                  </td>
                  <td className="p-6 text-center">
                    <CheckIcon className="w-6 h-6 text-green-500 mx-auto" />
                  </td>
                  <td className="p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10">
                    <CheckIcon className="w-6 h-6 text-white bg-[#D63A5A] rounded-full p-1 mx-auto" />
                  </td>
                </tr>

                {/* Support Row */}
                <tr className="border-b border-gray-100">
                  <td className="p-6 font-semibold text-gray-700">Support</td>
                  <td className="p-6 text-center text-sm font-medium">Basic</td>
                  <td className="p-6 text-center text-sm font-medium">Priority</td>
                  <td className="p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10 text-sm font-medium">24/7 Priority</td>
                </tr>

                {/* Free Updates Row */}
                <tr className="border-b border-gray-100">
                  <td className="p-6 font-semibold text-gray-700">Free Updates</td>
                  <td className="p-6 text-center">
                    <span className="text-gray-400">-</span>
                  </td>
                  <td className="p-6 text-center">
                    <CheckIcon className="w-6 h-6 text-green-500 mx-auto" />
                  </td>
                  <td className="p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10">
                    <CheckIcon className="w-6 h-6 text-white bg-[#D63A5A] rounded-full p-1 mx-auto" />
                  </td>
                </tr>

                {/* Advanced Features Row */}
                <tr>
                  <td className="p-6 font-semibold text-gray-700">Advanced Features</td>
                  <td className="p-6 text-center">
                    <span className="text-gray-400">-</span>
                  </td>
                  <td className="p-6 text-center">
                    <span className="text-gray-400">-</span>
                  </td>
                  <td className="p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10">
                    <CheckIcon className="w-6 h-6 text-white bg-[#D63A5A] rounded-full p-1 mx-auto" />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden space-y-6">
            {[
              { name: "Lite", price: "$12", color: "bg-gray-50" },
              { name: "Plus", price: "$21", color: "bg-gray-50" },
              { name: "Max", price: "$23", color: "bg-gradient-to-br from-[#D63A5A] to-[#B82E4E]" }
            ].map((plan, index) => (
              <div key={index} className={`p-6 rounded-2xl ${plan.color} ${plan.name === 'Max' ? 'text-white' : 'text-gray-700'}`}>
                <div className="text-center mb-4">
                  <h3 className="text-xl font-bold">{plan.name}</h3>
                  <p className="text-2xl font-bold mt-2">{plan.price} <span className="text-sm font-normal">USD</span></p>
                </div>
                {/* Add mobile-specific feature list here if needed */}
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
