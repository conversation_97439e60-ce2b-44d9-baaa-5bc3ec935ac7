import axios, { AxiosInstance } from 'axios';
import { createHmac } from 'crypto';
import { DateFormatter } from './utils';

interface BossConfig {
  baseUrl: string;
  deviceApp: string;
  appSecret: string;
}

interface ApiResponse<T> {
  data: T;
  resultCode: string;
  resultMsg: string;
}

// 分页查询参数接口
interface PageQueryParams {
  pageNum: number;
  pageSize: number;
  uid: string;
  queryParam?: {
    orderSn?: string;
    uid?: string;
    packageStatus?: number;
    queryBeginTime?: string;
    queryEndTime?: string;
    esimProfileStatus?: string;
    whetherDelete?: boolean;
    [key: string]: any; // 其他可能的过滤参数
  };
}

// 创建套餐订单参数接口
interface CreateProductOrderParams {
  customerOrderSn: string;
  productSkuId: string;
  uid: string;
}

// 创建RPM订单参数接口
interface CreateRpmOrderParams {
  alias?: string;
  customerOrderSn: string;
  lpaString: string;
  uid: string;
}

// 套餐订单响应接口
interface OrderResponse {
  customerOrderSn: string;
  orderSn: string;
}

// 套餐用量查询响应接口
interface UsageResponse {
  usageBytes: string;
  orderSn: string;
}

// 套餐详情查询响应接口
interface PlanDetailResponse {
  planState: string;
  planStartTime: string;
  planExpireTime: string;
  usageBytes: string;
  [key: string]: any; // 其他可能的字段
}

export class BossService {
  private client: AxiosInstance;
  private config: BossConfig;

  constructor(config: BossConfig) {
    this.config = config;
    this.client = axios.create({
      baseURL: config.baseUrl,
      headers: {
        'Content-Type': 'application/json',
      }
    });
  }

  private generateNonce(length: number = 16): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  private generateSignature(deviceApp: string, nonce: string, timestamp: string, appSecret: string): string {
    // 1. 拼接参数
    const message = deviceApp + nonce + timestamp;

    console.log('[BOSS_SERVICE] Generating signature', {
      timestamp: DateFormatter.iso(new Date()),
      components: {
        deviceApp,
        nonce,
        timestamp
      },
      concatenatedMessage: message,
      secretKey: appSecret
    });

    // 2. 使用HMAC-SHA256计算签名，直接输出十六进制格式
    const hmac = createHmac('sha256', appSecret);
    const signature = hmac.update(message).digest('hex');

    console.log('[BOSS_SERVICE] Generated signature:', {
      signature,
      algorithm: 'HmacSHA256',
      encoding: 'hex',
      debug: {
        inputMessage: message,
        hexSignature: signature
      }
    });

    return signature;
  }

  private getAuthHeaders(): Record<string, string> {
    const timestamp = Date.now().toString();
    const nonce = this.generateNonce();

    const signature = this.generateSignature(
      this.config.deviceApp,
      nonce,
      timestamp,
      this.config.appSecret
    );

    const headers = {
      'APP': this.config.deviceApp,
      'TIMESTAMP': timestamp,
      'NONCE': nonce,
      'SIGN': signature
    };

    return headers;
  }

  private async request<T>(method: string, url: string, data?: any): Promise<ApiResponse<T>> {
    const requestData = data || {};
    const headers = this.getAuthHeaders();
    const startTime = Date.now();

    console.log(`[BOSS_SERVICE] Starting ${method} request`, {
      url: this.config.baseUrl + url,
      method,
      timestamp: DateFormatter.iso(new Date()),
      requestData,
      headers
    });

    try {
      const response = await this.client.request({
        method,
        url,
        data: requestData,
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        }
      });

      const endTime = Date.now();
      console.log('[BOSS_SERVICE] Request completed successfully', {
        url: this.config.baseUrl + url,
        method,
        statusCode: response.status,
        duration: `${endTime - startTime}ms`,
        timestamp: DateFormatter.iso(new Date()),
        responseData: response.data
      });

      return response.data;
    } catch (error) {
      const endTime = Date.now();
      console.error('[BOSS_SERVICE] Request failed', {
        url: this.config.baseUrl + url,
        method,
        duration: `${endTime - startTime}ms`,
        timestamp: DateFormatter.iso(new Date()),
        error: axios.isAxiosError(error) ? {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          headers: error.response?.headers
        } : error
      });
      throw error;
    }
  }

  /**
   * 1. UID套餐分页查询
   * 查询UID套餐的分页数据
   */
  async getDeviceOrderPage(params: PageQueryParams): Promise<ApiResponse<any>> {
    console.log('[BOSS_SERVICE] Getting device order page', {
      timestamp: DateFormatter.iso(new Date()),
      params
    });
    return this.request('POST', '/open/device/order/page', params);
  }

  /**
   * 2.1 创建套餐订单
   * 创建新的套餐订单
   */
  async createProductOrder(params: CreateProductOrderParams): Promise<ApiResponse<OrderResponse>> {
    console.log('[BOSS_SERVICE] Creating product order', {
      timestamp: DateFormatter.iso(new Date()),
      params
    });
    return this.request('POST', '/open/order/plan/createProductOrder', params);
  }

  /**
   * 2.2 创建RPM订单
   * 创建新的RPM订单
   */
  async createRpmOrder(params: CreateRpmOrderParams): Promise<ApiResponse<OrderResponse>> {
    console.log('[BOSS_SERVICE] Creating RPM order', {
      timestamp: DateFormatter.iso(new Date()),
      params
    });
    return this.request('POST', '/open/order/plan/createRpmOrder', params);
  }

  /**
   * 3.1 取消套餐（退款）
   * 取消指定订单号的套餐
   */
  async cancelOrderPlan(orderSn: string): Promise<ApiResponse<any>> {
    console.log('[BOSS_SERVICE] Cancelling order plan', {
      timestamp: DateFormatter.iso(new Date()),
      orderSn
    });
    return this.request('POST', '/open/order/plan/cancelOrderPlan', { orderSn });
  }

  /**
   * 3.2 提前停用套餐
   * 提前停用指定订单号的套餐
   */
  async closeOrderPlan(orderSn: string): Promise<ApiResponse<any>> {
    console.log('[BOSS_SERVICE] Closing order plan', {
      timestamp: DateFormatter.iso(new Date()),
      orderSn
    });
    return this.request('POST', '/open/order/plan/closeOrderPlan', { orderSn });
  }

  /**
   * 4.1 套餐用量查询
   * 查询指定订单号的套餐用量
   */
  async queryUsageOrderPlan(orderSn: string): Promise<ApiResponse<UsageResponse>> {
    console.log('[BOSS_SERVICE] Querying order plan usage', {
      timestamp: DateFormatter.iso(new Date()),
      orderSn
    });
    return this.request('POST', '/open/order/plan/qryusageOrderPlan', { orderSn });
  }

  /**
   * 4.2 套餐详情查询
   * 查询指定订单号的套餐详情
   */
  async queryOrderPlan(orderSn: string): Promise<ApiResponse<PlanDetailResponse>> {
    console.log('[BOSS_SERVICE] Querying order plan details', {
      timestamp: DateFormatter.iso(new Date()),
      orderSn
    });
    return this.request('POST', '/open/order/plan/queryOrderPlan', { orderSn });
  }

  /**
   * 5. 套餐置顶切换
   * 将指定用户的指定订单号套餐置顶
   */
  async toppingOrderPlan(orderSn: string, uid: string): Promise<ApiResponse<any>> {
    console.log('[BOSS_SERVICE] Setting order plan as top', {
      timestamp: DateFormatter.iso(new Date()),
      orderSn,
      uid
    });
    return this.request('POST', '/open/order/plan/toppingOrderPlan', { orderSn, uid });
  }
}
