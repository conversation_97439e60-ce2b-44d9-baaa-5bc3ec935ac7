"use client"

import { useEffect, useState, useCallback } from "react"
import { ChevronDown } from "lucide-react"

export function ScrollDownButton() {
  const [show, setShow] = useState(true)

  useEffect(() => {
    const hero = document.getElementById('hero')
    if (!hero) return
    const observer = new window.IntersectionObserver(
      ([entry]) => setShow(entry.isIntersecting),
      { threshold: 0.5 }
    )
    observer.observe(hero)
    return () => observer.disconnect()
  }, [])

  const handleScrollToNextSection = useCallback(() => {
    const nextSection = document.getElementById('why-choose-us')
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' })
    }
  }, [])

  if (!show) return null

  return (
    <div className="fixed md:absolute bottom-6 md:bottom-12 left-1/2 -translate-x-1/2 z-50">
      <button 
        onClick={handleScrollToNextSection}
        className="group relative flex flex-col items-center gap-2 transition-opacity duration-300 hover:opacity-80"
        aria-label="Scroll down to next section"
      >
        <div className="relative">
          <div className="absolute -inset-3 md:-inset-4 bg-[#F799A6]/20 rounded-full blur-xl opacity-0 
          group-hover:opacity-100 transition-opacity" />
          <div className="relative w-12 h-12 md:w-16 md:h-16 rounded-full border border-white/20 
          bg-white/5 backdrop-blur-sm flex items-center justify-center transform-gpu transition-transform 
          duration-500 hover:scale-110">
            <ChevronDown className="w-6 h-6 md:w-8 md:h-8 text-white/80 animate-bounce" />
          </div>
        </div>
      </button>
    </div>
  )
} 