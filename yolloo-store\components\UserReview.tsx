import { Star } from 'lucide-react'

interface UserReviewProps {
  name: string
  rating: number
  comment: string
}

export default function UserReview({ name, rating, comment }: UserReviewProps) {
  return (
    <div className="bg-white shadow-md rounded-lg p-4">
      <div className="flex items-center mb-2">
        <p className="font-semibold mr-2">{name}</p>
        <div className="flex">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
            />
          ))}
        </div>
      </div>
      <p className="text-gray-600">{comment}</p>
    </div>
  )
}

