import { PrismaClient } from "@prisma/client"

declare global {
  var prisma: PrismaClient | undefined
}

const prisma = global.prisma || new PrismaClient({
  log: process.env.NODE_ENV === "development" ? ["error", "warn"] : ["error"],
})

if (process.env.NODE_ENV !== "production") {
  global.prisma = prisma
}

if (typeof window !== "undefined") {
  throw new Error(
    "PrismaClient is unable to be run in the browser. You need to ensure all prisma usage is on the server side."
  )
}

export { prisma }

