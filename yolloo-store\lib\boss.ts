import { BossService } from '@/lib/bossService';
import { BOSS_CONFIG } from '@/config/boss';

if (!process.env.BOSS_API_URL) {
  throw new Error("BOSS_API_URL is not defined");
}

if (!process.env.BOSS_APP) {
  throw new Error("BOSS_APP is not defined");
}

if (!process.env.BOSS_APP_SECRET) {
  throw new Error("BOSS_APP_SECRET is not defined");
}

export const bossService = new BossService(BOSS_CONFIG);
