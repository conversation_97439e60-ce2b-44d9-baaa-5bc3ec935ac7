import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { sendOrganizationInviteEmail } from "@/app/services/emailService";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for adding a member
const addMemberSchema = z.object({
  affiliateId: z.string().optional(),
  userId: z.string().optional(),
  email: z.string().email().optional(),
  commissionRate: z.number().min(0).max(1).optional(),
  isAdmin: z.boolean().optional(),
});

// Schema for updating a member
const updateMemberSchema = z.object({
  commissionRate: z.number().min(0).max(1).optional(),
  isAdmin: z.boolean().optional(),
});

// Helper function to check if user has admin access to the organization
async function hasAdminAccess(userId: string, organizationId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { 
      role: true,
      affiliate: {
        select: {
          id: true,
          organizationId: true,
          isAdmin: true,
        },
      },
    },
  });

  if (!user) return false;
  
  // System admin has access to all organizations
  if (user.role === "ADMIN") return true;
  
  // Organization admin has access to their organization
  if (user.affiliate?.organizationId === organizationId && user.affiliate.isAdmin) {
    return true;
  }
  
  return false;
}

// GET - List organization members
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const organizationId = params.id;
    
    // Check if user has access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);
    
    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    // Get organization members
    const members = await prisma.affiliateProfile.findMany({
      where: { organizationId },
      select: {
        id: true,
        code: true,
        commissionRate: true,
        totalEarnings: true,
        isAdmin: true,
        createdAt: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
    
    // 改为返回一个包含members属性的对象
    return NextResponse.json({ members });
  } catch (error) {
    console.error("Error fetching organization members:", error);
    return NextResponse.json(
      { error: "Failed to fetch organization members" },
      { status: 500 }
    );
  }
}

// POST - Add a member to the organization
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const organizationId = params.id;
    
    // Check if user has admin access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);
    
    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validationResult = addMemberSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { affiliateId, userId, email, commissionRate, isAdmin } = validationResult.data;
    
    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // If affiliateId is provided, add existing affiliate to organization
    if (affiliateId) {
      const affiliate = await prisma.affiliateProfile.findUnique({
        where: { id: affiliateId },
      });
      
      if (!affiliate) {
        return NextResponse.json({ error: "Affiliate not found" }, { status: 404 });
      }
      
      // Check if affiliate is already in an organization
      if (affiliate.organizationId) {
        return NextResponse.json(
          { error: "Affiliate is already a member of an organization" },
          { status: 400 }
        );
      }
      
      // Add affiliate to organization
      const updatedAffiliate = await prisma.affiliateProfile.update({
        where: { id: affiliateId },
        data: {
          organizationId,
          ...(commissionRate !== undefined && { commissionRate }),
          ...(isAdmin !== undefined && { isAdmin }),
        },
      });
      
      return NextResponse.json(updatedAffiliate);
    }
    
    // If userId is provided, check if user exists and has an affiliate profile
    if (userId) {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { affiliate: true },
      });
      
      if (!user) {
        return NextResponse.json({ error: "User not found" }, { status: 404 });
      }
      
      // If user has an affiliate profile, add to organization
      if (user.affiliate) {
        // Check if affiliate is already in an organization
        if (user.affiliate.organizationId) {
          return NextResponse.json(
            { error: "User is already a member of an organization" },
            { status: 400 }
          );
        }
        
        // Add affiliate to organization
        const updatedAffiliate = await prisma.affiliateProfile.update({
          where: { id: user.affiliate.id },
          data: {
            organizationId,
            ...(commissionRate !== undefined && { commissionRate }),
            ...(isAdmin !== undefined && { isAdmin }),
          },
        });
        
        return NextResponse.json(updatedAffiliate);
      }
      
      // If user doesn't have an affiliate profile, create one and add to organization
      // Generate a unique affiliate code
      const code = `${user.name?.substring(0, 3) || "AFF"}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
      
      const newAffiliate = await prisma.affiliateProfile.create({
        data: {
          userId: user.id,
          code,
          organizationId,
          ...(commissionRate !== undefined && { commissionRate }),
          ...(isAdmin !== undefined && { isAdmin }),
        },
      });
      
      return NextResponse.json(newAffiliate);
    }
    
    // If email is provided, create an invite
    if (email) {
      // Check if user with this email exists
      const user = await prisma.user.findUnique({
        where: { email },
        include: { affiliate: true },
      });
      
      // Generate a unique invite code
      const inviteCode = `INV-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
      
      // Create an invite
      const invite = await prisma.organizationInvite.create({
        data: {
          organizationId,
          email,
          inviteCode,
          // If user exists and has an affiliate profile, link the invite
          ...(user?.affiliate && { affiliateId: user.affiliate.id }),
          // Set expiration date to 7 days from now
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        },
      });
      
      // Send email invitation
      if (organization) {
        const inviteUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invite/${inviteCode}`;
        await sendOrganizationInviteEmail(
          email,
          organization.name,
          inviteUrl,
          invite.expiresAt,
          session.user.name || undefined
        );
      }
      
      return NextResponse.json(invite);
    }
    
    return NextResponse.json(
      { error: "Either affiliateId, userId, or email must be provided" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error adding organization member:", error);
    return NextResponse.json(
      { error: "Failed to add organization member" },
      { status: 500 }
    );
  }
} 