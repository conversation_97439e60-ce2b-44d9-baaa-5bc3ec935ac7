import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 403 })
    }

    const affiliates = await prisma.affiliateProfile.findMany({
      orderBy: {
        createdAt: "asc",
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
        referrals: {
          include: {
            order: {
              include: {
                items: {
                  select: {
                    id: true,
                    quantity: true,
                    price: true,
                    productCode: true,
                    variantCode: true,
                    variantText: true,
                  },
                },
                user: {
                  select: {
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    // 获取每个推广码的预售订阅统计
    const affiliateStats = await Promise.all(
      affiliates.map(async (affiliate) => {
        const [presaleSubscriptions, convertedSubscriptions] = await Promise.all([
          // 总预售订阅数
          prisma.preSaleSubscription.count({
            where: {
              referralCode: affiliate.code,
            },
          }),
          // 已转化为用户的订阅数
          prisma.preSaleSubscription.count({
            where: {
              referralCode: affiliate.code,
              convertedToUser: true,
            },
          }),
        ])

        // 处理订单和购买数据
        const purchaseData = affiliate.referrals
          .filter((ref) => ref.order.status === "PAID")
          .map((ref) => ({
            orderId: ref.order.id,
            orderDate: ref.order.createdAt,
            customer: {
              name: ref.order.user.name,
              email: ref.order.user.email,
            },
            total: ref.order.total,
            items: ref.order.items.map((item) => ({
              productName: item.variantText || item.productCode || "Unknown Product",
              quantity: item.quantity,
              price: item.price,
            })),
          }))

        return {
          ...affiliate,
          statistics: {
            presaleSubscriptions,
            convertedSubscriptions,
            conversionRate: presaleSubscriptions > 0
              ? (convertedSubscriptions / presaleSubscriptions * 100).toFixed(1)
              : "0",
            purchases: purchaseData,
            totalPurchases: purchaseData.length,
            totalPurchaseAmount: purchaseData.reduce((sum, order) => sum + order.total, 0),
          },
        }
      })
    )

    return NextResponse.json(affiliateStats)
  } catch (error) {
    console.error("[AFFILIATES_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}