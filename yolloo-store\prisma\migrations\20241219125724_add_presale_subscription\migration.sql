-- CreateEnum
CREATE TYPE "PreSaleSubscriptionStatus" AS ENUM ('PENDING', 'CONFIRMED', 'UNSUBSCRIBED');

-- CreateTable
CREATE TABLE "PreSaleSubscription" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "referralCode" TEXT,
    "status" "PreSaleSubscriptionStatus" NOT NULL DEFAULT 'PENDING',
    "discountCode" TEXT,
    "subscribedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "convertedToUser" BOOLEAN NOT NULL DEFAULT false,
    "userId" TEXT,

    CONSTRAINT "PreSaleSubscription_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PreSaleSubscription_email_key" ON "PreSaleSubscription"("email");

-- AddForeignKey
ALTER TABLE "PreSaleSubscription" ADD CONSTRAINT "PreSaleSubscription_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
