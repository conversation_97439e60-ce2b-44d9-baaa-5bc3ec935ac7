/**
 * 图片资源管理工具类
 * 用于生成标准化的图片URL路径
 */

export class ImageUtil {
  private static readonly BASE_PATH = '/api/mobile/static/images';

  /**
   * 生成Banner图片URL
   * @param type Banner类型 (home, category, promotion, articles, tips)
   * @param id Banner ID
   * @param language 语言代码 (zh, en)
   * @param format 图片格式 (webp, jpg, png)
   * @returns 完整的图片URL
   */
  static getBannerUrl(
    type: 'home' | 'category' | 'promotion' | 'articles' | 'tips',
    id: string,
    language: string = 'zh',
    format: string = 'webp'
  ): string {
    return `${this.BASE_PATH}/banners/${type}/banner_${type}_${id}_${language}.${format}`;
  }

  /**
   * 生成图标URL
   * @param name 图标名称
   * @param format 图片格式 (svg, png)
   * @param size 图片尺寸 (仅PNG格式需要)
   * @returns 完整的图标URL
   */
  static getIconUrl(
    name: string,
    format: string = 'svg',
    size?: number
  ): string {
    const sizeStr = size && format === 'png' ? `_${size}` : '';
    return `${this.BASE_PATH}/icons/icon_${name}${sizeStr}.${format}`;
  }

  /**
   * 生成产品图片URL
   * @param productId 产品ID
   * @param imageType 图片类型 (main, thumb, gallery)
   * @param index 图片索引 (用于gallery类型)
   * @param format 图片格式
   * @returns 完整的产品图片URL
   */
  static getProductImageUrl(
    productId: string,
    imageType: 'main' | 'thumb' | 'gallery' = 'main',
    index?: number,
    format: string = 'webp'
  ): string {
    const indexStr = imageType === 'gallery' && index !== undefined ? `_${index}` : '';
    return `${this.BASE_PATH}/products/${productId}_${imageType}${indexStr}.${format}`;
  }

  /**
   * 生成用户头像URL
   * @param userId 用户ID
   * @param size 头像尺寸
   * @param format 图片格式
   * @returns 完整的头像URL
   */
  static getAvatarUrl(
    userId: string,
    size: number = 128,
    format: string = 'webp'
  ): string {
    return `${this.BASE_PATH}/avatars/avatar_${userId}_${size}.${format}`;
  }

  /**
   * 生成国家/地区标志图片URL
   * @param countryCode 国家代码 (ISO 3166-1 alpha-2)
   * @param format 图片格式
   * @returns 完整的国旗URL
   */
  static getCountryFlagUrl(
    countryCode: string,
    format: string = 'svg'
  ): string {
    return `${this.BASE_PATH}/flags/flag_${countryCode.toLowerCase()}.${format}`;
  }

  /**
   * 生成占位符图片URL
   * @param width 宽度
   * @param height 高度
   * @param text 占位符文字
   * @returns 占位符图片URL
   */
  static getPlaceholderUrl(
    width: number,
    height: number,
    text?: string
  ): string {
    const textParam = text ? `&text=${encodeURIComponent(text)}` : '';
    return `https://via.placeholder.com/${width}x${height}?${textParam}`;
  }

  /**
   * 检查图片URL是否为本地静态资源
   * @param url 图片URL
   * @returns 是否为本地静态资源
   */
  static isLocalStaticImage(url: string): boolean {
    return url.startsWith(this.BASE_PATH);
  }

  /**
   * 获取图片的完整URL（包含域名）
   * @param path 图片路径
   * @param baseUrl 基础URL（可选，默认为空）
   * @returns 完整的图片URL
   */
  static getFullImageUrl(path: string, baseUrl?: string): string {
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }

    if (baseUrl) {
      return `${baseUrl.replace(/\/$/, '')}${path}`;
    }

    return path;
  }

  /**
   * 根据语言和设备类型生成响应式图片URL
   * @param baseName 基础文件名
   * @param type 图片类型
   * @param language 语言代码
   * @param deviceType 设备类型 (mobile, tablet, desktop)
   * @param format 图片格式
   * @returns 响应式图片URL
   */
  static getResponsiveImageUrl(
    baseName: string,
    type: 'banner' | 'icon' | 'product',
    language: string = 'zh',
    deviceType: 'mobile' | 'tablet' | 'desktop' = 'mobile',
    format: string = 'webp'
  ): string {
    const deviceSuffix = deviceType !== 'mobile' ? `_${deviceType}` : '';
    return `${this.BASE_PATH}/${type}s/${baseName}_${language}${deviceSuffix}.${format}`;
  }
}

/**
 * 图片尺寸常量
 */
export const IMAGE_SIZES = {
  BANNER: {
    HOME: { width: 750, height: 300 },
    CATEGORY: { width: 750, height: 200 },
    PROMOTION: { width: 750, height: 300 },
  },
  ICON: {
    SMALL: 24,
    MEDIUM: 48,
    LARGE: 64,
    XLARGE: 128,
  },
  AVATAR: {
    SMALL: 32,
    MEDIUM: 64,
    LARGE: 128,
    XLARGE: 256,
  },
  PRODUCT: {
    THUMB: { width: 150, height: 150 },
    MAIN: { width: 400, height: 400 },
    GALLERY: { width: 800, height: 600 },
  },
} as const;

/**
 * 支持的图片格式
 */
export const SUPPORTED_FORMATS = ['webp', 'jpg', 'jpeg', 'png', 'svg'] as const;

export type SupportedFormat = typeof SUPPORTED_FORMATS[number];
