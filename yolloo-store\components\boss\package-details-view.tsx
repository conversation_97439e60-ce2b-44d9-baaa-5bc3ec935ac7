'use client';

import { Badge } from "@/components/ui/badge";
import { packageStatusMap, orderSourceMap, formatDateTime, formatDataUsage } from "@/lib/boss-utils";

interface PackageDetailsViewProps {
  detailsData: any;
}

export function PackageDetailsView({ detailsData }: PackageDetailsViewProps) {
  if (!detailsData) {
    return <div className="py-4 text-center text-gray-500">No details available</div>;
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h3 className="text-sm font-medium">Order Number</h3>
          <p className="text-sm">{detailsData.orderSn || '-'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Package Status</h3>
          <div>
            {detailsData.packageStatus !== null && detailsData.packageStatus !== undefined ? (
              <Badge variant={packageStatusMap[detailsData.packageStatus]?.variant || 'default'}>
                {packageStatusMap[detailsData.packageStatus]?.label || `Status: ${detailsData.packageStatus}`}
              </Badge>
            ) : '-'}
          </div>
        </div>
        <div>
          <h3 className="text-sm font-medium">Package Name</h3>
          <p className="text-sm">{detailsData.packageName || '-'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">User UID</h3>
          <p className="text-sm">{detailsData.uid || '-'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Currently Active</h3>
          <p className="text-sm">{detailsData.whetherCurrent ? 'Yes' : 'No'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">eSIM Profile Status</h3>
          <p className="text-sm">{detailsData.esimProfileStatus || '-'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Order Source</h3>
          <p className="text-sm">
            {detailsData.orderSource ? orderSourceMap[detailsData.orderSource] || detailsData.orderSource : '-'}
          </p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Data Usage</h3>
          <p className="text-sm">{formatDataUsage(detailsData.usageBytes)}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Priority</h3>
          <p className="text-sm">{detailsData.uidSort || '-'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Start Time</h3>
          <p className="text-sm">{formatDateTime(detailsData.planStartTime)}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">End Time</h3>
          <p className="text-sm">{formatDateTime(detailsData.planEndTime)}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Expiry Time</h3>
          <p className="text-sm">{formatDateTime(detailsData.planExpireTime)}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">ID</h3>
          <p className="text-sm text-xs">{detailsData.id || '-'}</p>
        </div>
      </div>
    </div>
  );
}
