import { Icons } from "@/components/icons"
import { HeroTitle } from "@/components/hero-title"
import FeatureSection from "@/components/feature-section"
import WhyChooseUs from "@/components/why-choose-us"
import HowItWorks from "@/components/how-it-works"
import PricingSection from "@/components/pricing-section"
import { CTASection } from "@/components/cta-section"
import { ScrollDownButton } from "@/components/scroll-down-button"
import { RefHandler } from "./ref-handler"

export default async function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Affiliate Referral Handler */}
      <RefHandler />

      {/* Hero Section */}
      <section id="hero" className="relative h-screen bg-[#1a1a1a]">
        <div className="container relative z-20 h-full flex items-start pt-[4vh] md:pt-[10vh]">
          <div className="mx-auto max-w-[980px] text-center">
            <div className="glass-effect mb-5 md:mb-10 inline-flex items-center rounded-full px-4 py-1.5 text-white/80">
              <Icons.globe className="mr-2 h-4 w-4" />
              <span className="text-sm font-medium">One Card, One World</span>
            </div>
            <HeroTitle />
          </div>
        </div>
        {/* 背景装饰 */}
        <div className="absolute inset-0 overflow-hidden">
          {/* 主要的渐变背景 */}
          <div className="absolute inset-0 bg-gradient-to-b from-rose-500/30 to-transparent" />

          {/* 左上角光效 */}
          <div className="absolute -top-[30%] -left-[10%] h-[60%] w-[60%]
          bg-[radial-gradient(circle,rgba(255,80,80,0.4),transparent_70%)] blur-3xl" />

          {/* 右下角光效 */}
          <div className="absolute -bottom-[20%] -right-[10%] h-[60%] w-[60%]
          bg-[radial-gradient(circle,rgba(255,80,80,0.35),transparent_70%)] blur-3xl" />

          {/* 中心光效 */}
          <div className="absolute top-[40%] left-[50%] -translate-x-1/2 -translate-y-1/2 h-[40%] w-[80%]
          bg-[radial-gradient(ellipse,rgba(255,80,80,0.25),transparent_70%)] blur-3xl" />

          {/* 额外的装饰效果 */}
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(255,80,80,0.2),transparent_50%)]" />
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(255,80,80,0.2),transparent_50%)]" />
          <div className="absolute inset-0 bg-grid-white/[0.03]" />
        </div>

        {/* Scroll Down Button */}
        <ScrollDownButton />
      </section>

      {/* Why Choose Us Section */}
      <section id="why-choose-us">
        <WhyChooseUs />
      </section>

      {/* Features Section */}
      <section id="features">
        <FeatureSection />
      </section>

      {/* How It Works Section */}
      <section id="how-it-works">
        <HowItWorks />
      </section>

      {/* Pricing Section */}
      <section id="pricing">
        <PricingSection />
      </section>

      {/* CTA Section */}
      <CTASection />


    </div>
  )
}

