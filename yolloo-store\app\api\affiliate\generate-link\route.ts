import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";
import { nanoid } from 'nanoid';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    console.log("Session:", JSON.stringify(session, null, 2));

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // 直接使用会话中的用户ID查询
    const user = await prisma.user.findUnique({
      where: { 
        id: session.user.id
      },
      include: {
        affiliate: true
      }
    });

    console.log("Found user:", JSON.stringify(user, null, 2));

    if (!user) {
      return new NextResponse("User not found", { status: 404 });
    }

    // 如果用户已经是会员，直接返回现有资料
    if (user.affiliate) {
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
      const affiliateLink = `${baseUrl}?ref=${user.affiliate.code}`;

      return NextResponse.json({
        profile: {
          code: user.affiliate.code,
          link: affiliateLink,
          commissionRate: user.affiliate.commissionRate,
          totalEarnings: user.affiliate.totalEarnings,
          status: user.affiliate.status,
        }
      });
    }

    // 生成新的会员资料
    const code = nanoid(8);
    
    console.log("Creating affiliate profile for user:", user.id);

    // 使用 connect 创建关联
    const affiliateProfile = await prisma.user.update({
      where: { id: user.id },
      data: {
        affiliate: {
          create: {
            code,
            commissionRate: 0.1,
            status: "ACTIVE",
          }
        }
      },
      include: {
        affiliate: true
      }
    });

    console.log("Created affiliate profile:", JSON.stringify(affiliateProfile.affiliate, null, 2));

    // 生成推广链接
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    
    if (!affiliateProfile.affiliate) {
      return new NextResponse("Affiliate profile not found", { status: 404 });
    }
    
    const affiliateLink = `${baseUrl}?ref=${affiliateProfile.affiliate.code}`;

    return NextResponse.json({
      profile: {
        code: affiliateProfile.affiliate.code,
        link: affiliateLink,
        commissionRate: affiliateProfile.affiliate.commissionRate,
        totalEarnings: affiliateProfile.affiliate.totalEarnings,
        status: affiliateProfile.affiliate.status,
      }
    });
  } catch (error) {
    console.error("[AFFILIATE_GENERATE_LINK]", error);
    // 返回更详细的错误信息
    const errorMessage = error instanceof Error 
      ? `Error: ${error.message}\nStack: ${error.stack}` 
      : "Internal error";
    return new NextResponse(errorMessage, { status: 500 });
  }
} 