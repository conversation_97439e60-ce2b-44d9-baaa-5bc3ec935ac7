import { getServerSession } from "next-auth"
import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // 获取用户的默认地址
    const defaultAddress = await prisma.address.findFirst({
      where: {
        userId: session.user.id,
        isDefault: true,
      },
    })

    // 如果用户有默认地址，直接返回
    if (defaultAddress) {
      return NextResponse.json(defaultAddress)
    }

    // 尝试获取系统默认地址
    const systemDefaultAddress = await prisma.address.findFirst({
      where: {
        userId: null,
        name: 'System Default Address',
      },
    });

    let newAddress = systemDefaultAddress;

    if (!newAddress) {
      // 如果没有默认地址和系统默认地址，创建一个数字商品专用的默认地址
      newAddress = await prisma.address.create({
        data: {
          userId: null, // 不关联任何用户
          type: "SHIPPING",
          name: "System Default Address",
          phone: "0000000000",
          address1: "Virtual Product Delivery",
          address2: "No Physical Shipping Required",
          city: "Virtual City",
          state: "Virtual State",
          postalCode: "00000",
          country: "VIRTUAL",
          isDefault: true,
        },
      })
    }

    return NextResponse.json(newAddress)
  } catch (error) {
    console.error("[ADDRESS_DEFAULT_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}