import { Shield, Wallet, Smartphone, Globe, Download } from "lucide-react"
import { SectionBackground } from "@/components/ui/section-background"
import { Icons } from "@/components/icons"

const FeatureCard = ({ icon: Icon, title, description }: {
  icon: any,
  title: string,
  description: string
}) => (
  <div className="flex items-start gap-6 p-8 rounded-3xl bg-gradient-to-br from-[#F799A6]/10 to-[#B82E4E]/10 backdrop-blur-sm 
  hover:from-[#F799A6]/15 hover:to-[#B82E4E]/15 transition-all duration-500 hover:shadow-[0_8px_32px_rgba(247,153,166,0.2)] 
  hover:-translate-y-1 border border-[#F799A6]/30 hover:border-[#F799A6]/50 group">
    <div className="shrink-0 w-16 h-16 flex items-center justify-center rounded-2xl bg-gradient-to-br from-[#B82E4E]/20 
    to-[#F799A6]/20 shadow-[0_4px_16px_rgba(247,153,166,0.2)]">
      <Icon className="w-8 h-8 text-[#B82E4E] transition-transform duration-500 group-hover:scale-110" />
    </div>
    <div className="flex-1 pt-1">
      <h3 className="font-semibold text-xl mb-2 text-gray-900 group-hover:text-[#B82E4E] transition-colors duration-300">
        {title}
      </h3>
      <p className="text-gray-600 leading-relaxed">
        {description}
      </p>
    </div>
  </div>
)

export default function WhyChooseUs() {
  return (
    <SectionBackground isWhite={false}>
      <div className="container">
        <div className="text-center mb-20">
          <div className="inline-block px-6 py-2 bg-gradient-to-br from-[#F799A6]/20 to-[#B82E4E]/20 backdrop-blur-sm 
          rounded-full border border-[#F799A6]/30 text-[#B82E4E] font-semibold tracking-wide mb-4 shadow-lg shadow-pink-500/10">
            The Yolloo Card Advantage
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] 
          text-transparent bg-clip-text">
            Global Connectivity Without Limitations
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
            With Yolloo Card, enjoy seamless global mobile data, easy eSIM switching, and freedom from physical SIM constraints – all at better prices and with superior experience.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-6xl mx-auto">
          <FeatureCard
            icon={Globe}
            title="Global Coverage Made Simple"
            description="Access mobile data worldwide with one Yolloo Card – enjoy seamless connectivity across 190+ countries without changing physical SIM cards."
          />
          <FeatureCard
            icon={Download}
            title="eSIM-Like Convenience"
            description="Switch between countries and plans as easily as downloading an eSIM – no physical limitations, just digital flexibility for all your travels."
          />
          <FeatureCard
            icon={Smartphone}
            title="Device Freedom"
            description="Works with all compatible phones regardless of region – no device restrictions or compatibility issues to worry about."
          />
          <FeatureCard
            icon={Wallet}
            title="Better Value, Lower Cost"
            description="Enjoy premium connectivity at competitive prices – our optimized network partnerships mean significant savings on your global data."
          />
          <FeatureCard
            icon={Icons.mapPin}
            title="Borderless Experience"
            description="Travel freely between countries without hunting for local SIMs – your Yolloo Card provides continuous service as you cross borders."
          />
          <FeatureCard
            icon={Shield}
            title="Secure & Reliable Connection"
            description="Our advanced encryption and network technology ensures your connection remains private, stable and fast wherever you travel."
          />
        </div>
      </div>
    </SectionBackground>
  )
} 