"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { useCart } from "@/lib/hooks/use-cart"
import { useToast } from "@/components/ui/use-toast"
import { useState, useEffect, ChangeEvent, KeyboardEvent } from "react"
import { Badge } from "@/components/ui/badge"
import { formatUid } from "@/lib/utils"
import { Input } from "@/components/ui/input"

interface CartItemProps {
  item: {
    id: string
    productId: string
    name: string
    price: number
    quantity: number
    image?: string
    stock: number
    variant?: {
      id: string
      price: number
      duration?: number | null
      durationType?: string | null
      attributes?: Record<string, string>
    }
    uid?: string
  }
}

export function CartItem({ item }: CartItemProps) {
  const { updateQuantity, removeItem } = useCart()
  const { toast } = useToast()
  const [imageError, setImageError] = useState(false)
  const [quantityInput, setQuantityInput] = useState(item.quantity.toString())
  const [isEditing, setIsEditing] = useState(false)

  // Update input value when item quantity changes
  useEffect(() => {
    if (!isEditing) {
      setQuantityInput(item.quantity.toString())
    }
  }, [item.quantity, isEditing])

  console.log('CartItem:', {
    item,
    hasVariant: !!item.variant,
    variantAttributes: item.variant?.attributes,
    uid: item.uid
  })

  const handleUpdateQuantity = (newQuantity: number) => {
    if (newQuantity > item.stock) {
      toast({
        title: "Cannot add more",
        description: `Only ${item.stock} items available in stock`,
        variant: "destructive",
      })
      setQuantityInput(item.quantity.toString())
      return
    }
    if (newQuantity < 1) {
      setQuantityInput("1")
      return
    }
    console.log('Updating quantity:', {
      productId: item.productId,
      quantity: newQuantity,
      variantId: item.variant?.id,
      uid: item.uid
    })
    updateQuantity(item.productId, newQuantity, item.variant?.id, item.uid)
    setQuantityInput(newQuantity.toString())
  }

  const handleQuantityInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    // Only allow numeric input
    const value = e.target.value.replace(/[^0-9]/g, '')
    setQuantityInput(value)
  }

  const handleQuantityInputBlur = () => {
    let newQuantity = parseInt(quantityInput, 10)

    // Handle invalid input
    if (isNaN(newQuantity) || quantityInput === '') {
      newQuantity = item.quantity
      setQuantityInput(item.quantity.toString())
    } else {
      // Validate and update quantity
      handleUpdateQuantity(newQuantity)
    }

    setIsEditing(false)
  }

  const handleQuantityInputKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.currentTarget.blur()
    }
  }

  const handleRemoveItem = () => {
    removeItem(item.productId, item.variant?.id, item.uid)
    toast({
      title: "Item removed",
      description: "The item has been removed from your cart",
      duration: 2000,
    })
  }

  return (
    <div className="flex gap-4 py-4">
      <div className="relative aspect-square h-24 w-24 min-w-fit overflow-hidden rounded-lg border">
        {item.image && !imageError ? (
          <Image
            src={item.image}
            alt={item.name}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100px, 200px"
            priority
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-secondary">
            <Icons.image className="h-8 w-8 text-muted-foreground" />
          </div>
        )}
      </div>
      <div className="flex flex-1 flex-col gap-2">
        <div className="flex items-start justify-between gap-2">
          <div>
            <h3 className="font-medium leading-none">{item.name}</h3>
            <p className="text-sm text-muted-foreground">
              ${item.price.toFixed(2)}
            </p>
            <div className="mt-2 flex flex-wrap gap-1.5">
              {item.variant?.duration && item.variant?.durationType && (
                <Badge variant="secondary" className="capitalize">
                  Duration: {item.variant.duration} {item.variant.durationType}
                </Badge>
              )}
              {item.variant?.attributes && Object.entries(item.variant.attributes).map(([key, value]) => (
                <Badge key={key} variant="secondary" className="capitalize">
                  {key.replace('_', ' ')}: {value}
                </Badge>
              ))}
              {item.uid && (
                <Badge
                  variant="outline"
                  className="border-primary/20 bg-primary/10 flex flex-col sm:flex-row sm:items-center px-2 py-1.5 h-auto"
                >
                  <div className="flex items-center">
                    <Icons.billing className="mr-1 h-3 w-3 flex-shrink-0" />
                    <span className="font-medium">UID:</span>
                  </div>
                  <div className="mt-0.5 sm:mt-0 sm:ml-1 font-mono text-xs break-all">
                    {formatUid(item.uid)}
                  </div>
                </Badge>
              )}
            </div>
            {item.stock <= 5 && (
              <p className="mt-1 text-xs text-red-500">
                Only {item.stock} left in stock
              </p>
            )}
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleRemoveItem}
            className="hover:text-red-500"
            title="Remove item"
          >
            <Icons.close className="h-4 w-4" />
            <span className="sr-only">Remove</span>
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              disabled={item.quantity <= 1}
              onClick={() => {
                const newQuantity = item.quantity - 1
                handleUpdateQuantity(newQuantity)
              }}
              title="Decrease quantity"
            >
              <Icons.minus className="h-3 w-3" />
              <span className="sr-only">Decrease quantity</span>
            </Button>
            <div className="flex h-8 w-16 items-center justify-center rounded-md border bg-background overflow-hidden">
              <Input
                type="text"
                value={quantityInput}
                onChange={handleQuantityInputChange}
                onBlur={handleQuantityInputBlur}
                onKeyDown={handleQuantityInputKeyDown}
                onClick={() => setIsEditing(true)}
                className="h-full w-full border-0 text-center p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                aria-label="Product quantity"
              />
            </div>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              disabled={item.quantity >= item.stock}
              onClick={() => {
                const newQuantity = item.quantity + 1
                handleUpdateQuantity(newQuantity)
              }}
              title="Increase quantity"
            >
              <Icons.add className="h-3 w-3" />
              <span className="sr-only">Increase quantity</span>
            </Button>
          </div>
          <p className="ml-auto font-medium">
            ${(item.price * item.quantity).toFixed(2)}
          </p>
        </div>
      </div>
    </div>
  )
}
