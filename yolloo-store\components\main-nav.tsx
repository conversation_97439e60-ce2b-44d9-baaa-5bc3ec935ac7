"use client"

import Link from "next/link"
import { useState } from "react"
import { usePathname } from "next/navigation"
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/YollooLogo"
import { MobileMenuButton } from "@/components/MobileMenuButton"
import { ChevronDown, SearchIcon } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

// 模拟的热门地区数据 (3x4 = 12个)
const popularRegions = [
  { name: "Antigua and Barbuda", flagCode: "ag" },
  { name: "Åland Islands", flagCode: "ax" },
  { name: "Albania", flagCode: "al" },
  { name: "Antigua and Barbuda", flagCode: "ag" },
  { name: "Åland Islands", flagCode: "ax" },
  { name: "Albania", flagCode: "al" },
  { name: "Antigua and Barbuda", flagCode: "ag" },
  { name: "Antigua and Barbuda", flagCode: "ag" },
  { name: "Antigua and Barbuda", flagCode: "ag" },
  { name: "Åland Islands", flagCode: "ax" },
  { name: "Åland Islands", flagCode: "ax" },
  { name: "Åland Islands", flagCode: "ax" },
]

export function MainNav() {
  const [isYollooSmartHovered, setIsYollooSmartHovered] = useState(false)
  const pathname = usePathname()

  return (
    <div className="flex items-center gap-2">
      <MobileMenuButton />
      <Link href="/" className="flex items-center transition-colors hover:text-brand">
        <YollooLogo className="h-7 sm:h-8 w-auto" />
      </Link>
      <nav className="hidden md:flex gap-4 ml-6 items-center">
        {/* Yolloo Smart with dropdown */}
        <div
          className="relative"
          onMouseEnter={() => setIsYollooSmartHovered(true)}
          onMouseLeave={() => setIsYollooSmartHovered(false)}
        >
          <Link
            href="/yolloo-smart"
            className={`flex items-center text-sm font-medium relative px-2 py-2 h-12 rounded-md transition-all duration-300 hover:text-[#B82E4E] hover:bg-[#F799A6]/10 after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-gradient-to-r after:from-[#B82E4E] after:to-[#F799A6] after:transition-all after:duration-300 hover:after:w-full ${
              pathname === '/yolloo-smart'
                ? 'text-[#B82E4E] bg-[#F799A6]/10 after:w-full'
                : 'text-muted-foreground after:w-0'
            }`}
          >
            <span className="whitespace-nowrap">Yolloo Smart</span>
            <ChevronDown className="ml-1 h-3 w-3 transition-transform duration-200" style={{
              transform: isYollooSmartHovered ? 'rotate(180deg)' : 'rotate(0deg)'
            }} />
          </Link>

          {/* Dropdown Menu */}
          {isYollooSmartHovered && (
            <>
              {/* Invisible bridge to prevent dropdown from closing */}
              <div className="absolute top-full left-0 w-[600px] h-2 z-40"></div>
              <div className="absolute top-full left-0 mt-1 w-[600px] bg-white rounded-lg shadow-xl border border-gray-200 p-6 z-50">
                {/* Search Bar */}
                <div className="mb-6">
                  <div className="relative">
                    <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Where do you want to go"
                      className="pl-10 pr-12 py-3 text-sm rounded-lg border border-gray-300 bg-white text-gray-900 placeholder-gray-500"
                    />
                    <Button
                      size="sm"
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-[#B82E4E] to-[#F799A6] hover:from-[#A02745] hover:to-[#E88A97] text-white rounded-md h-8 w-8 p-0 shadow-lg transition-all duration-300"
                    >
                      <SearchIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-4">Popular Regions</h3>
                </div>

                <div className="grid grid-cols-3 gap-3 mb-6">
                  {popularRegions.map((region, index) => (
                    <Link
                      key={index}
                      href={`/region/${region.name.toLowerCase().replace(/\s+/g, '-')}`}
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm bg-gray-50 border border-gray-200"
                    >
                      <img
                        src={`https://flagcdn.com/w20/${region.flagCode}.png`}
                        alt={`${region.name} flag`}
                        className="w-5 h-4 object-cover rounded-sm flex-shrink-0"
                      />
                      <span className="text-gray-900 font-medium truncate">{region.name}</span>
                    </Link>
                  ))}
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <Link
                    href="/yolloo-smart"
                    className="inline-flex items-center justify-center w-full px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-[#B82E4E] to-[#F799A6] hover:from-[#A02745] hover:to-[#E88A97] rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    View all
                  </Link>
                </div>
              </div>
            </>
          )}
        </div>

        <Link
          href="/esims"
          className={`flex items-center text-sm font-medium relative px-2 py-2 h-12 rounded-md transition-all duration-300 hover:text-[#B82E4E] hover:bg-[#F799A6]/10 after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-gradient-to-r after:from-[#B82E4E] after:to-[#F799A6] after:transition-all after:duration-300 hover:after:w-full ${
            pathname === '/esims'
              ? 'text-[#B82E4E] bg-[#F799A6]/10 after:w-full'
              : 'text-muted-foreground after:w-0'
          }`}
        >
          <span className="relative whitespace-nowrap">
            eSIM
            <span className="absolute -top-1 left-[34px] bg-[#DF4362] text-white text-[8px] font-bold px-1 py-0.5 rounded-full leading-none">
              AI
            </span>
          </span>
        </Link>

        <Link
          href="/affiliate"
          className={`flex items-center text-sm font-medium relative px-2 py-2 h-12 rounded-md transition-all duration-300 hover:text-[#B82E4E] hover:bg-[#F799A6]/10 after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-gradient-to-r after:from-[#B82E4E] after:to-[#F799A6] after:transition-all after:duration-300 hover:after:w-full ${
            pathname === '/affiliate'
              ? 'text-[#B82E4E] bg-[#F799A6]/10 after:w-full'
              : 'text-muted-foreground after:w-0'
          }`}
        >
          <span className="whitespace-nowrap">Earn with Yolloo</span>
        </Link>

        <Link
          href="/faq"
          className={`flex items-center text-sm font-medium relative px-2 py-2 h-12 rounded-md transition-all duration-300 hover:text-[#B82E4E] hover:bg-[#F799A6]/10 after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-gradient-to-r after:from-[#B82E4E] after:to-[#F799A6] after:transition-all after:duration-300 hover:after:w-full ${
            pathname === '/faq'
              ? 'text-[#B82E4E] bg-[#F799A6]/10 after:w-full'
              : 'text-muted-foreground after:w-0'
          }`}
        >
          <span className="whitespace-nowrap">FAQ</span>
        </Link>

        <Link
          href="/contact"
          className={`flex items-center text-sm font-medium relative px-2 py-2 h-12 rounded-md transition-all duration-300 hover:text-[#B82E4E] hover:bg-[#F799A6]/10 after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-gradient-to-r after:from-[#B82E4E] after:to-[#F799A6] after:transition-all after:duration-300 hover:after:w-full ${
            pathname === '/contact'
              ? 'text-[#B82E4E] bg-[#F799A6]/10 after:w-full'
              : 'text-muted-foreground after:w-0'
          }`}
        >
          <span className="whitespace-nowrap">Contact</span>
        </Link>
      </nav>
    </div>
  )
}
