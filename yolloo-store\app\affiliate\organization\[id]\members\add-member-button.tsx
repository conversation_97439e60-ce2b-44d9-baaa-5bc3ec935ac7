"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import { AlertCircle } from "lucide-react";

// Form schema
const formSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  commissionRate: z.coerce.number().min(0, "Min is 0%").max(1, "Max is 100%").default(0.5),
  isAdmin: z.boolean().default(false),
});

interface AddMemberButtonProps {
  organizationId: string;
  showAsPage?: boolean;
}

export function AddMemberButton({ organizationId, showAsPage = false }: AddMemberButtonProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [inviteLink, setInviteLink] = useState<string | null>(null);
  const router = useRouter();

  // Define form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      commissionRate: 0.5,
      isAdmin: false,
    },
  });

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/affiliate/organizations/${organizationId}/members`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || "Failed to add member");
      }
      
      // Get the base URL for the invite link
      const baseUrl = window.location.origin;
      const inviteUrl = `${baseUrl}/invite/${data.invite.inviteCode}`;
      setInviteLink(inviteUrl);
      
      toast.success("Invitation created successfully");
      
      // Don't close the dialog so user can copy the link
      // Just refresh the members list
      router.refresh();
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Failed to add member");
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Copy invite link to clipboard
  const copyInviteLink = () => {
    if (inviteLink) {
      navigator.clipboard.writeText(inviteLink);
      toast.success("Invite link copied to clipboard");
    }
  };
  
  // Reset form and state when dialog closes
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      form.reset();
      setInviteLink(null);
    }
    setOpen(open);
  };

  // For page mode, render just the form without a dialog wrapper
  if (showAsPage) {
    return (
      <div className="w-full">
        {inviteLink ? (
          <div className="space-y-4">
            <Alert className="bg-green-50 border-green-200">
              <AlertDescription className="text-green-800">
                Invitation created successfully! Share the link below with the invited member.
              </AlertDescription>
            </Alert>
            
            <div className="flex items-center space-x-2">
              <Input value={inviteLink} readOnly className="flex-1" />
              <Button size="sm" onClick={copyInviteLink}>Copy</Button>
            </div>
            
            <div className="text-sm text-muted-foreground space-y-2">
              <p className="flex items-start">
                <AlertCircle className="h-4 w-4 mr-2 mt-0.5 text-blue-500" />
                The invited user will be able to set their own password when they follow this link.
              </p>
              <p className="flex items-start">
                <AlertCircle className="h-4 w-4 mr-2 mt-0.5 text-blue-500" />
                This invitation link will expire in 7 days.
              </p>
            </div>
            
            <div className="flex justify-end space-x-2 mt-4">
              <Button 
                variant="secondary" 
                onClick={() => {
                  form.reset();
                  setInviteLink(null);
                }}
              >
                Create Another Invitation
              </Button>
              <Button onClick={() => router.push(`/affiliate/organization/${organizationId}/members`)}>Done</Button>
            </div>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="<EMAIL>"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormDescription>
                      An invitation link will be generated for this email address.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="commissionRate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Commission Rate (%)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        max="1"
                        placeholder="0.5"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormDescription>
                      This is the percentage of the product price the member will earn. This comes from the organization's commission (e.g., if organization earns 30% and member rate is 20%, member gets 20% and organization keeps 10%).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="isAdmin"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>Admin Access</FormLabel>
                      <FormDescription>
                        Grant administrator access to this member
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={loading}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => router.push(`/affiliate/organization/${organizationId}/members`)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? "Creating..." : "Create Invitation"}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </div>
    );
  }

  // Regular dialog mode
  return (
    <>
      <Button onClick={() => setOpen(true)}>Add Member</Button>
      
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add Organization Member</DialogTitle>
            <DialogDescription>
              Send an invitation to a new member to join your organization.
            </DialogDescription>
          </DialogHeader>
          
          {inviteLink ? (
            <div className="space-y-4">
              <Alert className="bg-green-50 border-green-200">
                <AlertDescription className="text-green-800">
                  Invitation created successfully! Share the link below with the invited member.
                </AlertDescription>
              </Alert>
              
              <div className="flex items-center space-x-2">
                <Input value={inviteLink} readOnly className="flex-1" />
                <Button size="sm" onClick={copyInviteLink}>Copy</Button>
              </div>
              
              <div className="text-sm text-muted-foreground space-y-2">
                <p className="flex items-start">
                  <AlertCircle className="h-4 w-4 mr-2 mt-0.5 text-blue-500" />
                  The invited user will be able to set their own password when they follow this link.
                </p>
                <p className="flex items-start">
                  <AlertCircle className="h-4 w-4 mr-2 mt-0.5 text-blue-500" />
                  This invitation link will expire in 7 days.
                </p>
              </div>
              
              <DialogFooter>
                <Button 
                  variant="secondary" 
                  onClick={() => {
                    form.reset();
                    setInviteLink(null);
                  }}
                >
                  Create Another Invitation
                </Button>
                <Button onClick={() => setOpen(false)}>Done</Button>
              </DialogFooter>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormDescription>
                        An invitation link will be generated for this email address.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="commissionRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Commission Rate (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max="1"
                          placeholder="0.5"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormDescription>
                        This is the percentage of the product price the member will earn. This comes from the organization's commission (e.g., if organization earns 30% and member rate is 20%, member gets 20% and organization keeps 10%).
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="isAdmin"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Admin Access</FormLabel>
                        <FormDescription>
                          Grant administrator access to this member
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={loading}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button type="submit" disabled={loading}>
                    {loading ? "Creating..." : "Create Invitation"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
} 