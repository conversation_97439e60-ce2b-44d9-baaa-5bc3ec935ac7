import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { sendOrganizationInviteEmail } from "@/app/services/emailService";
import { add } from "date-fns";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// 定义辅助函数hasAdminAccess，因为lib/permissions可能不存在
async function hasAdminAccess(userId: string, organizationId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { 
      role: true,
      affiliate: {
        select: {
          id: true,
          organizationId: true,
          isAdmin: true,
        },
      },
    },
  });

  if (!user) return false;
  
  // System admin has access to all organizations
  if (user.role === "ADMIN") return true;
  
  // Organization admin has access to their organization
  if (user.affiliate?.organizationId === organizationId && user.affiliate.isAdmin) {
    return true;
  }
  
  return false;
}

// Schema for batch inviting users
const batchInviteSchema = z.object({
  emails: z.array(z.string().email("Invalid email address")),
  commissionRate: z.number().min(0).max(1).default(0.5),
  isAdmin: z.boolean().default(false),
});

export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to perform this action" },
        { status: 401 }
      );
    }

    // Check if user has admin access to this organization
    const hasAccess = await hasAdminAccess(session.user.id, params.id);
    if (!hasAccess) {
      return NextResponse.json(
        { error: "You do not have admin access to this organization" },
        { status: 403 }
      );
    }

    // Get request body and validate
    const body = await req.json();
    const validationResult = batchInviteSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { emails, commissionRate, isAdmin } = validationResult.data;

    // Get the organization
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: params.id },
      include: {
        members: {
          where: { userId: session.user.id },
          select: {
            id: true,
            user: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    const inviterName = organization.members[0]?.user?.name || session.user.name || "A team member";
    const results = [];

    // Process each email
    for (const email of emails) {
      try {
        // Check if there's already a pending invite for this email
        const existingInvite = await prisma.organizationInvite.findFirst({
          where: {
            organizationId: params.id,
            email: email,
            status: "PENDING",
            expiresAt: {
              gt: new Date()
            }
          }
        });

        if (existingInvite) {
          // Re-send invite email with existing code
          await sendOrganizationInviteEmail(
            email,
            organization.name,
            `${process.env.NEXT_PUBLIC_APP_URL}/invite/${existingInvite.inviteCode}`,
            existingInvite.expiresAt || new Date(),
            inviterName,
            existingInvite.isAdmin
          );

          results.push({
            email,
            success: true,
            message: "Invitation re-sent"
          });
          continue;
        }

        // Create a new invite
        const expiresAt = add(new Date(), { days: 7 });
        const inviteCode = `${Math.random().toString(36).substring(2, 10)}-${Math.random().toString(36).substring(2, 10)}`;

        const invite = await prisma.organizationInvite.create({
          data: {
            organizationId: params.id,
            affiliateId: organization.members[0]?.id,
            email,
            status: "PENDING",
            inviteCode,
            expiresAt,
            commissionRate,
            isAdmin,
          }
        });

        // Send invite email
        await sendOrganizationInviteEmail(
          email,
          organization.name,
          `${process.env.NEXT_PUBLIC_APP_URL}/invite/${inviteCode}`,
          expiresAt,
          inviterName,
          isAdmin
        );

        results.push({
          email,
          success: true,
          message: "Invitation sent"
        });
      } catch (error) {
        console.error(`Error processing invite for ${email}:`, error);
        results.push({
          email,
          success: false,
          error: "Failed to send invitation"
        });
      }
    }

    // Return the results
    return NextResponse.json({
      results,
      successCount: results.filter(r => r.success).length,
      totalCount: emails.length
    });

  } catch (error) {
    console.error("Error processing batch invites:", error);
    return NextResponse.json(
      { error: "Failed to process batch invites" },
      { status: 500 }
    );
  }
} 