import React from 'react'

export default function AboutPage() {
  return (
      <div className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h1 className="text-4xl font-bold heading-gradient mb-4">About Yolloo</h1>
              <p className="text-xl text-muted-foreground">
                Connecting the world through innovative eSIM technology
              </p>
            </div>

            {/* Company Story Section */}
            <div className="mb-16">
              <h2 className="text-3xl font-semibold text-gray-900 mb-6">Our Story</h2>
              <div className="prose prose-lg max-w-none">
                <p className="mb-4">
                  Founded in 2023, <PERSON><PERSON><PERSON> emerged from a simple yet powerful vision: to make global connectivity seamless and accessible to everyone. Our founders, experienced in telecommunications and technology, recognized the challenges travelers and digital nomads face with traditional SIM cards.
                </p>
                <p className="mb-4">
                  What started as a solution for international travelers has grown into a comprehensive eSIM platform serving customers worldwide. Today, Yolloo is at the forefront of the digital transformation in mobile connectivity.
                </p>
              </div>
            </div>

            {/* Mission & Values Section */}
            <div className="grid md:grid-cols-2 gap-12 mb-16">
              <div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-4">Our Mission</h2>
                <p className="text-gray-600">
                  To revolutionize global connectivity by providing seamless, affordable, and instant access to mobile networks worldwide through cutting-edge eSIM technology.
                </p>
              </div>
              <div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-4">Our Vision</h2>
                <p className="text-gray-600">
                  A world where staying connected across borders is effortless and accessible to everyone, enabling people to explore, work, and connect without boundaries.
                </p>
              </div>
            </div>

            {/* Core Values Section */}
            <div className="mb-16">
              <h2 className="text-3xl font-semibold text-gray-900 mb-8">Our Core Values</h2>
              <div className="grid md:grid-cols-3 gap-8">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Innovation</h3>
                  <p className="text-gray-600">
                    We continuously push the boundaries of technology to provide the best connectivity solutions.
                  </p>
                </div>
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Reliability</h3>
                  <p className="text-gray-600">
                    We ensure our services are dependable and our support is always available when needed.
                  </p>
                </div>
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Customer First</h3>
                  <p className="text-gray-600">
                    We put our customers at the heart of everything we do, ensuring their needs drive our decisions.
                  </p>
                </div>
              </div>
            </div>

            {/* Global Coverage Section */}
            <div className="mb-16">
              <h2 className="text-3xl font-semibold text-gray-900 mb-6">Global Coverage</h2>
              <div className="prose prose-lg max-w-none">
                <p className="mb-4">
                  Our eSIM service provides coverage in over 100 countries, partnering with leading mobile network operators to ensure reliable connectivity wherever you go. From bustling cities to remote locations, we've got you covered.
                </p>
                <ul className="grid md:grid-cols-2 gap-4 list-none pl-0 mt-6">
                  <li className="flex items-center text-gray-600">
                    <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    100+ Countries Covered
                  </li>
                  <li className="flex items-center text-gray-600">
                    <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    24/7 Global Support
                  </li>
                  <li className="flex items-center text-gray-600">
                    <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    4G/5G Networks
                  </li>
                  <li className="flex items-center text-gray-600">
                    <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Instant Activation
                  </li>
                </ul>
              </div>
            </div>

            {/* Join Us Section */}
            <div className="text-center bg-card p-8 rounded-lg">
              <h2 className="text-3xl font-semibold heading-gradient mb-4">Join the Future of Connectivity</h2>
              <p className="text-muted-foreground mb-6">
                Experience the freedom of borderless communication with Yolloo eSIM. Whether you're a frequent traveler, digital nomad, or business professional, we're here to keep you connected.
              </p>
              <a
                href="/products"
                className="button-gradient inline-block px-8 py-3 rounded-md transition-all duration-200"
              >
                Explore Our Plans
              </a>
            </div>
          </div>
        </div>
      </div>
  )
} 