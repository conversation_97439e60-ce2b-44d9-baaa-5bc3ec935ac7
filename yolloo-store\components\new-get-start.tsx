'use client'

import Image from 'next/image'
import { motion } from 'framer-motion'

const steps = [
  {
    id: 1,
    title: "Choose Plan",
    description: "Select your ideal data plan from our range of global coverage options",
    image: "/Frame 207.svg",
    position: "left"
  },
  {
    id: 2,
    title: "Instant Delivery",
    description: "Receive your eSIM QR code immediately in your email after purchase",
    image: "/Frame 212.svg",
    position: "right"
  },
  {
    id: 3,
    title: "Quick Install",
    description: "Scan the QR code with your phone camera to install the eSIM profile",
    image: "/Frame 214.svg",
    position: "left"
  },
  {
    id: 4,
    title: "Ready to Go",
    description: "Activate your eSIM with one tap and enjoy global connectivity",
    image: "/Frame 213.svg",
    position: "right"
  }
]

export default function NewGetStart() {
  return (
    <section id="get-start" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-block px-4 py-2 bg-pink-100 text-pink-600 rounded-full text-sm font-medium mb-6">
            Simple Setup Process
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6" style={{
            background: 'linear-gradient(90deg, #B82E4E 0%, #F799A6 50%, #B82E4E 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            Get Started in Minutes
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Our streamlined activation process ensures you can start using your eSIM right away, with no technical expertise required.
          </p>
        </motion.div>

        {/* Steps Timeline */}
        <div className="relative max-w-6xl mx-auto">
          {/* Vertical Line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-pink-300 to-pink-500 h-full hidden md:block"></div>

          {/* Steps */}
          <div className="space-y-16 md:space-y-24">
            {steps.map((step, index) => (
              <motion.div
                key={step.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className={`flex flex-col md:flex-row items-center gap-8 ${
                  step.position === 'right' ? 'md:flex-row-reverse' : ''
                }`}
              >
                {/* Content Side */}
                <div className={`flex-1 text-center ${step.position === 'right' ? 'md:text-right' : 'md:text-left'}`}>
                  <div className={`inline-block px-3 py-1 bg-pink-100 text-pink-600 rounded-full text-sm font-medium mb-4 ${
                    step.position === 'right' ? 'md:ml-auto' : ''
                  }`}>
                    Step {step.id}
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                    {step.title}
                  </h3>
                  <p className={`text-gray-600 text-lg leading-relaxed max-w-md ${
                    step.position === 'right' ? 'mx-auto md:ml-auto md:mr-0' : 'mx-auto md:mx-0'
                  }`}>
                    {step.description}
                  </p>
                </div>

                {/* Timeline Dot */}
                <div className="relative z-10 hidden md:block">
                  <div className="w-6 h-6 bg-pink-500 rounded-full border-4 border-white shadow-lg"></div>
                </div>

                {/* Image Side */}
                <div className="flex-1 flex justify-center">
                  <div className="w-64 h-64 md:w-80 md:h-80 relative">
                    <Image
                      src={step.image}
                      alt={step.title}
                      fill
                      className="object-contain"
                    />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
