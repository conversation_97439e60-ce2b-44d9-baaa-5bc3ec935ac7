"use client"

import { useState, useRef, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Address } from "@prisma/client"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { toast } from "sonner"
import { useCart } from "@/lib/hooks/use-cart"
import { Icons } from "@/components/icons"

interface CheckoutFormProps {
  addresses: Address[]
  items?: any[]
}

export function CheckoutForm({ addresses, items: propItems }: CheckoutFormProps) {
  console.log("CheckoutForm rendered with addresses:", addresses)

  const router = useRouter()
  const formRef = useRef<HTMLFormElement>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { items: cartItems, clearCart } = useCart()
  const items = propItems ?? cartItems
  const [productsInfo, setProductsInfo] = useState<Record<string, { category: { name: string } }>>({})
  const [isLoadingProducts, setIsLoadingProducts] = useState(true)

  // 获取商品详细信息
  useEffect(() => {
    const fetchProductsInfo = async () => {
      setIsLoadingProducts(true)
      try {
        const productIds = items.map(item => item.productId)
        const productsRes = await fetch(`/api/products/batch?ids=${productIds.join(',')}`)
        if (productsRes.ok) {
          const products = await productsRes.json()
          const productsMap = products.reduce((acc: Record<string, { category: { name: string } }>, product: any) => {
            acc[product.id] = { category: product.category }
            return acc
          }, {})
          setProductsInfo(productsMap)
        }
      } catch (error) {
        console.error('Failed to fetch products info:', error)
      } finally {
        setIsLoadingProducts(false)
      }
    }

    if (items.length > 0) {
      fetchProductsInfo()
    } else {
      setIsLoadingProducts(false)
    }
  }, [items])

  // 检查是否所有商品都是虚拟商品（esim、data、effective_date 或 external_data）
  const isAllVirtualProducts = items.every(item => {
    const productInfo = productsInfo[item.productId]
    return productInfo && ['esim', 'data', 'effective_date', 'external_data', 'qr_code'].includes(productInfo.category?.name?.toLowerCase() || '')
  })

  // 如果全是虚拟商品，使用默认的数字发货地址
  const [selectedAddressId, setSelectedAddressId] = useState<string | undefined>(
    isAllVirtualProducts
      ? 'digital-delivery' // 这个ID将在创建订单时被特殊处理，会获取系统默认地址
      : (addresses && addresses.length > 0 && addresses[0]?.id) || undefined
  )
  const [isNewAddress, setIsNewAddress] = useState((!addresses || !addresses.length) && !isAllVirtualProducts)

  // 检查是否有有效的地址
  const hasValidAddress = isAllVirtualProducts || (!isNewAddress ? !!selectedAddressId : false)

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    console.log("Form submitted")
    setIsLoading(true)

    try {
      console.log("Checking stock...")
      // 检查库存
      const stockCheckRes = await fetch("/api/stock/check", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          items: items.map(item => ({
            productId: item.productId,
            quantity: item.quantity
          }))
        })
      })

      if (!stockCheckRes.ok) {
        const error = await stockCheckRes.json()
        throw new Error(error.message || "Stock check failed")
      }

      let addressId = selectedAddressId

      // 如果是虚拟商品，使用系统默认地址
      if (isAllVirtualProducts) {
        const digitalAddressRes = await fetch("/api/addresses/default", {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        })

        if (!digitalAddressRes.ok) {
          throw new Error("Failed to get digital delivery address")
        }

        const digitalAddress = await digitalAddressRes.json()
        addressId = digitalAddress.id
      } else if (isNewAddress && formRef.current) {
        console.log("Creating new address...")
        const formData = new FormData(formRef.current)
        const addressData = {
          name: formData.get("name") as string,
          phone: formData.get("phone") as string,
          address1: formData.get("address1") as string,
          address2: formData.get("address2") as string || undefined,
          city: formData.get("city") as string,
          state: formData.get("state") as string,
          postalCode: formData.get("postalCode") as string,
          country: formData.get("country") as string,
        }
        console.log("Address data:", addressData)

        const addressRes = await fetch("/api/addresses", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(addressData),
        })

        if (!addressRes.ok) {
          throw new Error("Failed to create address")
        }

        const address = await addressRes.json()
        addressId = address.id
        console.log("New address created:", address)
      }

      if (!addressId) {
        throw new Error("Please select an address")
      }

      console.log("Creating order...")
      // 创建订单
      const orderRes = await fetch("/api/orders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          addressId,
          items: items.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            variant: item.variant,
            variantId: item.variant?.id,
            uid: item.uid
          })),
          clearCart: true
        }),
      })

      if (!orderRes.ok) {
        throw new Error("Failed to create order")
      }

      const order = await orderRes.json()
      console.log("Order created:", order)

      console.log("Creating payment...")
      // 创建支付
      const paymentRes = await fetch("/api/payments", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          orderId: order.id,
        }),
      })

      if (!paymentRes.ok) {
        throw new Error("Failed to create payment")
      }

      const { url } = await paymentRes.json()
      console.log("Payment session created, URL:", url)

      // 清空购物车
      clearCart()
      console.log("Cart cleared")

      // 直接跳转到支付页面
      console.log("Redirecting to payment page...")
      window.location.href = url
    } catch (error) {
      console.error("Error during checkout:", error)
      toast.error(error instanceof Error ? error.message : "Something went wrong")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form ref={formRef} onSubmit={onSubmit} className="space-y-8">
      {isLoadingProducts ? (
        <Card className="p-6">
          <div className="flex items-center justify-center">
            <Icons.spinner className="h-6 w-6 animate-spin" />
          </div>
        </Card>
      ) : isAllVirtualProducts ? (
        <Card className="p-6">
          <div className="space-y-1">
            <h2 className="text-xl font-semibold tracking-tight">Digital Delivery</h2>
            <p className="text-sm text-muted-foreground">
              Your order contains only digital items that will be delivered electronically
            </p>
          </div>
        </Card>
      ) : (
        <Card className="divide-y divide-border">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="space-y-1">
                <h2 className="text-xl font-semibold tracking-tight">Shipping Address</h2>
                <p className="text-sm text-muted-foreground">
                  Choose where you want your order delivered
                </p>
              </div>
              {addresses.length > 0 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setIsNewAddress(!isNewAddress)}
                >
                  {isNewAddress ? "Use Saved Address" : "Add New Address"}
                </Button>
              )}
            </div>
            {addresses.length > 0 ? (
              <div className="space-y-6">
                <RadioGroup
                  value={isNewAddress ? "new" : "saved"}
                  onValueChange={(value) => {
                    console.log("Address type changed:", value)
                    setIsNewAddress(value === "new")
                    // 清除选中的地址
                    if (value === "new") {
                      setSelectedAddressId(undefined)
                    }
                  }}
                  className="grid grid-cols-1 sm:grid-cols-2 gap-4"
                >
                  <div className="cursor-pointer h-full">
                    <Label
                      htmlFor="saved"
                      className="block h-full"
                    >
                      <div className={`relative flex h-full rounded-lg border p-4 transition-all duration-200 ${!isNewAddress ? 'border-primary bg-primary/5' : 'hover:border-primary/50 hover:shadow-sm'}`}>
                        <RadioGroupItem value="saved" id="saved" className="absolute right-4 top-4" />
                        <div className="flex flex-col justify-center min-h-[80px]">
                          <span className="text-base font-medium mb-2">Saved Addresses</span>
                          <p className="text-sm text-muted-foreground">Choose from your saved shipping addresses</p>
                        </div>
                      </div>
                    </Label>
                  </div>
                  <div className="cursor-pointer h-full">
                    <Label
                      htmlFor="new"
                      className="block h-full"
                    >
                      <div className={`relative flex h-full rounded-lg border p-4 transition-all duration-200 ${isNewAddress ? 'border-primary bg-primary/5' : 'hover:border-primary/50 hover:shadow-sm'}`}>
                        <RadioGroupItem value="new" id="new" className="absolute right-4 top-4" />
                        <div className="flex flex-col justify-center min-h-[80px]">
                          <span className="text-base font-medium mb-2">New Address</span>
                          <p className="text-sm text-muted-foreground">Add a new shipping address</p>
                        </div>
                      </div>
                    </Label>
                  </div>
                </RadioGroup>

                {!isNewAddress && (
                  <div className="rounded-lg border border-border/50 p-6 bg-background/50">
                    <h3 className="text-base font-medium mb-4">Select a Delivery Address</h3>
                    <RadioGroup
                      value={selectedAddressId}
                      onValueChange={(value) => {
                        console.log("Selected address:", value)
                        setSelectedAddressId(value)
                      }}
                      className="grid gap-4"
                    >
                      {addresses.map((address) => (
                        <Label
                          key={address.id}
                          htmlFor={address.id}
                          className="cursor-pointer"
                        >
                          <div className={`relative flex items-start space-x-4 rounded-lg border p-4 transition-all duration-200 hover:border-primary/50 hover:shadow-sm ${selectedAddressId === address.id ? 'border-primary bg-primary/5' : 'border-border'}`}>
                            <RadioGroupItem value={address.id} id={address.id} className="mt-1" />
                            <div className="grid gap-1.5 w-full">
                              <div className="flex items-center justify-between">
                                <span className="text-base font-medium">{address.name}</span>
                                <span className="text-sm text-muted-foreground">Phone: {address.phone}</span>
                              </div>
                              <div className="text-sm text-muted-foreground space-y-1">
                                <p className="font-medium text-foreground/80">
                                  {address.address1}
                                  {address.address2 && <span className="text-muted-foreground">, {address.address2}</span>}
                                </p>
                                <p>{address.city}, {address.state} {address.postalCode}</p>
                                <p>{address.country}</p>
                              </div>
                            </div>
                          </div>
                        </Label>
                      ))}
                    </RadioGroup>
                  </div>
                )}
              </div>
            ) : null}

            {(isNewAddress || addresses.length === 0) && (
              <div className="rounded-lg border border-border/50 p-6 bg-background/50">
                <h3 className="text-base font-medium mb-4">Enter New Address</h3>
                <div className="grid gap-6">
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="grid gap-2">
                      <Label htmlFor="name" className="text-sm font-medium">Full Name</Label>
                      <Input
                        id="name"
                        name="name"
                        required
                        placeholder="Enter your full name"
                        className="h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="phone" className="text-sm font-medium">Phone Number</Label>
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        required
                        placeholder="Enter your phone number"
                        className="h-10"
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="grid gap-2">
                      <Label htmlFor="address1" className="text-sm font-medium">Address Line 1</Label>
                      <Input
                        id="address1"
                        name="address1"
                        required
                        placeholder="Street address or P.O. Box"
                        className="h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="address2" className="text-sm font-medium">Address Line 2 (Optional)</Label>
                      <Input
                        id="address2"
                        name="address2"
                        placeholder="Apartment, suite, unit, etc."
                        className="h-10"
                      />
                    </div>
                  </div>
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="grid gap-2">
                      <Label htmlFor="city" className="text-sm font-medium">City</Label>
                      <Input id="city" name="city" required className="h-10" />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="state" className="text-sm font-medium">State / Province</Label>
                      <Input id="state" name="state" required className="h-10" />
                    </div>
                  </div>
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="grid gap-2">
                      <Label htmlFor="postalCode" className="text-sm font-medium">Postal Code</Label>
                      <Input id="postalCode" name="postalCode" required className="h-10" />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="country" className="text-sm font-medium">Country</Label>
                      <Input id="country" name="country" required className="h-10" />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      <Button
        type="submit"
        className="w-full"
        size="lg"
        disabled={isLoading || (!isNewAddress && !selectedAddressId)}
      >
        <div className="flex items-center space-x-2">
          {isLoading ? (
            <>
              <Icons.spinner className="h-4 w-4 animate-spin" />
              <span>Processing...</span>
            </>
          ) : (
            <>
              <Icons.creditCard className="h-4 w-4" />
              <span>Continue to Payment</span>
            </>
          )}
        </div>
      </Button>
    </form>
  )
}