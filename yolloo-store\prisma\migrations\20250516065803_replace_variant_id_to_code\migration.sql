/*
  Warnings:

  - You are about to drop the column `productId` on the `OrderItem` table. All the data in the column will be lost.
  - You are about to drop the column `variantId` on the `OrderItem` table. All the data in the column will be lost.

*/
-- 1. 添加新字段
ALTER TABLE "OrderItem" ADD COLUMN "productCode" TEXT;
ALTER TABLE "OrderItem" ADD COLUMN "variantCode" TEXT;
ALTER TABLE "OrderItem" ADD COLUMN "variantText" TEXT;

-- 2. 数据转换（使用临时表方法，更高效）
CREATE TEMP TABLE temp_order_item_relations AS
SELECT
  oi.id AS order_item_id,
  p.sku AS product_sku,
  pv."variantCode" AS variant_code,
  CASE
    WHEN pv.duration IS NOT NULL AND pv."durationType" IS NOT NULL
    THEN CONCAT(p.name, ' ', pv.duration, ' ', pv."durationType")
    ELSE p.name
  END AS variant_text
FROM
  "OrderItem" oi
LEFT JOIN
  "Product" p ON oi."productId" = p.id
LEFT JOIN
  "ProductVariant" pv ON oi."variantId" = pv.id;

-- 一次性更新所有字段
UPDATE "OrderItem" oi
SET
  "productCode" = tr.product_sku,
  "variantCode" = tr.variant_code,
  "variantText" = tr.variant_text
FROM
  temp_order_item_relations tr
WHERE
  oi.id = tr.order_item_id;

-- 删除临时表
DROP TABLE temp_order_item_relations;

-- 添加日志记录
DO $$
DECLARE
  updated_count INTEGER;
  total_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO updated_count FROM "OrderItem" WHERE "productCode" IS NOT NULL;
  SELECT COUNT(*) INTO total_count FROM "OrderItem";

  RAISE NOTICE 'OrderItem migration completed: % of % records updated with product codes', updated_count, total_count;
END $$;

-- 3. 删除旧字段和约束
ALTER TABLE "OrderItem" DROP CONSTRAINT IF EXISTS "OrderItem_productId_fkey";
ALTER TABLE "OrderItem" DROP CONSTRAINT IF EXISTS "OrderItem_variantId_fkey";
ALTER TABLE "OrderItem" DROP COLUMN "productId";
ALTER TABLE "OrderItem" DROP COLUMN "variantId";

-- 4. 不添加外键约束，这样当Product或ProductVariant被删除时，OrderItem中的productCode和variantCode字段将保持不变
-- 注意：这意味着我们失去了外键约束提供的数据完整性保证，但满足了保留原始值的需求
