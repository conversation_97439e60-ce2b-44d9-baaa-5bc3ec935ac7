import { NextResponse } from "next/server"
import { verify } from "jsonwebtoken"
import { headers } from "next/headers"

interface AuthUser {
  userId: string
  email: string
  role: string
}

interface AuthSuccess {
  success: true
  user: AuthUser
}

interface AuthError {
  success: false
  status: number
  message: string
}

type AuthResult = AuthSuccess | AuthError

export async function verifyAuth(): Promise<AuthResult> {
  try {
    const headersList = headers()
    const authorization = headersList.get("authorization")

    if (!authorization || !authorization.startsWith("Bearer ")) {
      return {
        success: false,
        status: 401,
        message: "Unauthorized: No token provided"
      }
    }

    const token = authorization.split(" ")[1]
    const decoded = verify(token, process.env.NEXTAUTH_SECRET || "your-secret-key") as AuthUser

    return {
      success: true,
      user: decoded
    }
  } catch (error) {
    return {
      success: false,
      status: 401,
      message: "Unauthorized: Invalid token"
    }
  }
} 