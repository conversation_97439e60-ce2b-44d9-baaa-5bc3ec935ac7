'use client'

import Image from 'next/image'
import { useState } from 'react'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'

export default function NewChooseUs() {
  const [currentSlide, setCurrentSlide] = useState(0)

  const carouselItems = [
    {
      icon: '/cs7.svg',
      title: 'Global Coverage Made Simple',
      description: 'Access mobile data worldwide with one Yolloo Card – enjoy seamless connectivity across 190+ countries without changing physical SIM cards.'
    },
    {
      icon: '/cs8.svg',
      title: 'eSIM-Like Convenience',
      description: 'Switch between countries and plans as easily as downloading an eSIM – no physical limitations, just digital flexibility for all your travels.'
    },
    {
      icon: '/cs9.svg',
      title: 'Device Freedom',
      description: 'Works with all compatible phones regardless of region – no device restrictions or compatibility issues to worry about.'
    },
    {
      icon: '/cs10.svg',
      title: 'Better Value, Lower Cost',
      description: 'Enjoy premium connectivity at competitive prices – our optimized network partnerships mean significant savings on your global data.'
    },
    {
      icon: '/cs11.svg',
      title: 'Borderless Experience',
      description: 'Travel freely between countries without hunting for local SIMs – your Yolloo Card provides seamless connectivity wherever business takes you.'
    },
    {
      icon: '/cs12.svg',
      title: 'Secure & Reliable Connection',
      description: 'Advanced encryption and proven technology ensures your connection remains private, secure, and fast wherever you travel.'
    }
  ]

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % carouselItems.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + carouselItems.length) % carouselItems.length)
  }

  return (
    <section id="choose-us" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold mb-6 text-[#D63A5A] leading-tight">
            Why choose Yolloo?
          </h2>
        </div>

        {/* Top Section with Character and Features */}
        <div className="max-w-6xl mx-auto mb-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left: Character Image */}
            <div className="flex justify-center">
              <div className="w-32 h-32 relative">
                <Image
                  src="/cs1.svg"
                  alt="Yolloo character"
                  fill
                  className="object-contain"
                />
              </div>
            </div>

            {/* Right: Main Feature */}
            <div className="text-right">
              <h3 className="text-3xl font-bold text-[#D63A5A] mb-4">
                Digital Connectivity Made Simple
              </h3>
              <p className="text-lg text-gray-600 leading-relaxed">
                Experience the future of mobile connectivity with our cutting-edge eSIM technology,
                designed for modern travelers and digital nomads.
              </p>
            </div>
          </div>
        </div>

        {/* Interactive Features Section with Yolloo Card in Center */}
        <div className="max-w-7xl mx-auto mb-20 relative">
          {/* Container with proper spacing for feature cards */}
          <div className="relative min-h-[700px] flex items-center justify-center">

            {/* Top Left - Global Network - Floating Cloud */}
            <div className="absolute top-0 left-0 w-96 z-10 animate-float-1" style={{ transform: 'translate(150px, 100px)' }}>
              <div className="bg-[#FFF0F0] p-6 rounded-2xl border border-pink-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 relative flex-shrink-0">
                    <Image src="/cs3.svg" alt="Global Network" fill className="object-contain" />
                  </div>
                  <div>
                    <h3 className="font-bold text-xl mb-3 text-[#D63A5A]">Global Network</h3>
                    <p className="text-gray-600 leading-relaxed">
                      Access premium carriers in 190+ countries with seamless network switching
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Top Right - Instant Activation - Floating Cloud */}
            <div className="absolute top-0 right-0 w-96 z-10 animate-float-2" style={{ transform: 'translate(-150px, 50px)' }}>
              <div className="bg-[#FFF0F0] p-6 rounded-2xl border border-pink-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 relative flex-shrink-0">
                    <Image src="/cs2.svg" alt="Instant Activation" fill className="object-contain" />
                  </div>
                  <div>
                    <h3 className="font-bold text-xl mb-3 text-[#D63A5A]">Instant Activation</h3>
                    <p className="text-gray-600 leading-relaxed">
                      Get connected within minutes using our streamlined QR code activation process
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Central Yolloo Card - Pulsing Animation */}
            <div className="w-[500px] h-[500px] relative z-20 transform rotate-12 animate-pulse-slow">
              <Image
                src="/yolloo.svg"
                alt="Yolloo Card"
                fill
                className="object-contain drop-shadow-2xl"
              />
            </div>

            {/* Bottom Left - Travel Freedom - Floating Cloud */}
            <div className="absolute bottom-0 left-0 w-96 z-10 animate-float-3" style={{ transform: 'translate(150px, -50px)' }}>
              <div className="bg-[#FFF0F0] p-6 rounded-2xl border border-pink-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 relative flex-shrink-0">
                    <Image src="/cs5.svg" alt="Travel Freedom" fill className="object-contain" />
                  </div>
                  <div>
                    <h3 className="font-bold text-xl mb-3 text-[#D63A5A]">Travel Freedom</h3>
                    <p className="text-gray-600 leading-relaxed">
                      Say goodbye to physical SIMs and enjoy hassle-free international roaming
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Bottom Right - Smart Compatible - Floating Cloud */}
            <div className="absolute bottom-0 right-0 w-96 z-10 animate-float-4" style={{ transform: 'translate(-150px, -100px)' }}>
              <div className="bg-[#FFF0F0] p-6 rounded-2xl border border-pink-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 relative flex-shrink-0">
                    <Image src="/cs4.svg" alt="Smart Compatible" fill className="object-contain" />
                  </div>
                  <div>
                    <h3 className="font-bold text-xl mb-3 text-[#D63A5A]">Smart Compatible</h3>
                    <p className="text-gray-600 leading-relaxed">
                      Works flawlessly with all modern eSIM-enabled devices and tablets
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Custom CSS for floating animations */}
        <style jsx>{`
          @keyframes float-1 {
            0%, 100% { transform: translate(150px, 100px) translateY(0px); }
            50% { transform: translate(150px, 100px) translateY(-10px); }
          }
          @keyframes float-2 {
            0%, 100% { transform: translate(-150px, 50px) translateY(0px); }
            50% { transform: translate(-150px, 50px) translateY(-15px); }
          }
          @keyframes float-3 {
            0%, 100% { transform: translate(150px, -50px) translateY(0px); }
            50% { transform: translate(150px, -50px) translateY(-12px); }
          }
          @keyframes float-4 {
            0%, 100% { transform: translate(-150px, -100px) translateY(0px); }
            50% { transform: translate(-150px, -100px) translateY(-8px); }
          }
          @keyframes pulse-slow {
            0%, 100% { transform: rotate(12deg) scale(1); }
            50% { transform: rotate(12deg) scale(1.05); }
          }
          .animate-float-1 { animation: float-1 6s ease-in-out infinite; }
          .animate-float-2 { animation: float-2 8s ease-in-out infinite; }
          .animate-float-3 { animation: float-3 7s ease-in-out infinite; }
          .animate-float-4 { animation: float-4 9s ease-in-out infinite; }
          .animate-pulse-slow { animation: pulse-slow 4s ease-in-out infinite; }
        `}</style>

        {/* Section Header */}
        <div className="max-w-6xl mx-auto mb-16">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 className="text-3xl font-bold text-[#D63A5A] mb-6">
                Global Connectivity Without Limitations
              </h3>
              <p className="text-lg text-gray-600 leading-relaxed max-w-2xl">
                With Yolloo Card, enjoy seamless global mobile data, easy eSIM switching, and
                freedom from physical SIM constraints – all at better prices and with superior
                experience.
              </p>
            </div>

            {/* Character Image */}
            <div className="flex-shrink-0 ml-8">
              <div className="w-32 h-32 relative">
                <Image
                  src="/cs6.svg"
                  alt="Yolloo character"
                  fill
                  className="object-contain"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section - Horizontal Carousel */}
        <div className="max-w-6xl mx-auto">
          {/* Central Circle with Device Freedom */}
          <div className="relative flex justify-center items-center min-h-[500px]">

            {/* Horizontal Carousel */}
            <div className="absolute left-0 right-0 top-1/2 transform -translate-y-1/2">
              <div className="flex items-center justify-center gap-8">
                {/* Left Arrow */}
                <button
                  onClick={prevSlide}
                  className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors z-20"
                >
                  <ChevronLeftIcon className="w-6 h-6 text-[#D63A5A]" />
                </button>

                {/* Carousel Items */}
                <div className="flex items-center gap-4">
                  {[-1, 0, 1].map((offset) => {
                    const index = (currentSlide + offset + carouselItems.length) % carouselItems.length
                    const item = carouselItems[index]
                    const isCenter = offset === 0

                    return (
                      <div
                        key={index}
                        className={`transition-all duration-500 ${
                          isCenter
                            ? 'opacity-100 scale-110 z-20'
                            : 'opacity-70 scale-85 z-10'
                        }`}
                      >
                        <div className={`${
                          isCenter ? 'w-96 h-96' : 'w-80 h-80'
                        } bg-white rounded-full p-8 shadow-xl border border-gray-100 flex flex-col items-center justify-center text-center transform ${
                          isCenter ? 'shadow-2xl' : 'shadow-lg'
                        }`}>
                          <div className="w-16 h-16 relative mb-4">
                            <Image src={item.icon} alt={item.title} fill className="object-contain" />
                          </div>
                          <h5 className={`font-bold mb-3 text-[#D63A5A] ${
                            isCenter ? 'text-xl' : 'text-lg'
                          }`}>{item.title}</h5>
                          <p className={`text-gray-600 leading-relaxed ${
                            isCenter ? 'text-sm px-2' : 'text-xs px-1'
                          }`}>
                            {item.description}
                          </p>
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* Right Arrow */}
                <button
                  onClick={nextSlide}
                  className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors z-20"
                >
                  <ChevronRightIcon className="w-6 h-6 text-[#D63A5A]" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
