'use client';

import { usePathname } from 'next/navigation';
import { ReactElement, Suspense } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { ScrollToTop } from '@/components/scroll-to-top';

interface LayoutWrapperProps {
  children: React.ReactNode;
  header: React.ReactNode;
  footer: React.ReactNode;
}

function LayoutContent({ children, header, footer }: LayoutWrapperProps): ReactElement {
  const pathname = usePathname();
  const isAdminPath = pathname?.startsWith('/admin');

  if (isAdminPath) {
    return <>{children}</>;
  }

  return (
    <div className="relative flex min-h-screen flex-col">
      {header}
      <main className="flex-1 mb-16 border-b border-gray-100 relative z-10">{children}</main>
      {footer}
      <ScrollToTop />
    </div>
  );
}

export default function LayoutWrapper(props: LayoutWrapperProps): ReactElement {
  return (
    <ErrorBoundary fallback={<div>Something went wrong</div>}>
      <Suspense fallback={<div>Loading...</div>}>
        <LayoutContent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
} 