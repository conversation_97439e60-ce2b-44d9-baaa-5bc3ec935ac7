import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { ProductStatus } from '@prisma/client';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

export async function GET() {
  try {
    // 获取所有活跃且未下架的产品的国家信息
    const products = await prisma.product.findMany({
      where: {
        status: ProductStatus.ACTIVE,
        off_shelve: false,
        category: {
          name: {
            in: ['data', 'other']
          }
        }
      },
      select: {
        country: true,
      }
    });

    // 提取所有唯一的国家
    const countriesSet = new Set<string>();
    const countryCounts: Record<string, number> = {};

    products.forEach(product => {
      if (product.country) {
        const countries = product.country.split(/[,;]/).map(c => c.trim()).filter(Boolean);
        countries.forEach(country => {
          countriesSet.add(country);
          countryCounts[country] = (countryCounts[country] || 0) + 1;
        });
      }
    });

    // 转换为数组并排序
    const countries = Array.from(countriesSet)
      .map(country => ({
        name: country,
        count: countryCounts[country]
      }))
      .sort((a, b) => a.name.localeCompare(b.name));

    return NextResponse.json({ countries });

  } catch (error) {
    console.error('Error fetching countries:', error);
    return NextResponse.json(
      { error: 'Failed to fetch countries' },
      { status: 500 }
    );
  }
}
