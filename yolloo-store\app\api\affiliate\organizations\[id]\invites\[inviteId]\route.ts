import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Helper function to check if user has admin access to the organization
async function hasAdminAccess(userId: string, organizationId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { 
      role: true,
      affiliate: {
        select: {
          id: true,
          organizationId: true,
          isAdmin: true,
        },
      },
    },
  });

  if (!user) return false;
  
  // System admin has access to all organizations
  if (user.role === "ADMIN") return true;
  
  // Organization admin has access to their organization
  if (user.affiliate?.organizationId === organizationId && user.affiliate.isAdmin) {
    return true;
  }
  
  return false;
}

// GET - Get invite details
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string; inviteId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id: organizationId, inviteId } = params;
    
    // Check if user has admin access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);
    
    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    // Get invite details
    const invite = await prisma.organizationInvite.findFirst({
      where: {
        id: inviteId,
        organizationId,
      },
      include: {
        affiliate: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });
    
    if (!invite) {
      return NextResponse.json({ error: "Invite not found" }, { status: 404 });
    }
    
    return NextResponse.json(invite);
  } catch (error) {
    console.error("Error fetching invite:", error);
    return NextResponse.json(
      { error: "Failed to fetch invite" },
      { status: 500 }
    );
  }
}

// PATCH - Update invite status (cancel)
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string; inviteId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id: organizationId, inviteId } = params;
    
    // Check if user has admin access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);
    
    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    // Check if invite exists
    const invite = await prisma.organizationInvite.findFirst({
      where: {
        id: inviteId,
        organizationId,
      },
    });
    
    if (!invite) {
      return NextResponse.json({ error: "Invite not found" }, { status: 404 });
    }
    
    // Cancel the invite
    const updatedInvite = await prisma.organizationInvite.update({
      where: { id: inviteId },
      data: {
        status: "REJECTED",
      },
    });
    
    return NextResponse.json(updatedInvite);
  } catch (error) {
    console.error("Error updating invite:", error);
    return NextResponse.json(
      { error: "Failed to update invite" },
      { status: 500 }
    );
  }
}

// DELETE - Delete invite
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string; inviteId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id: organizationId, inviteId } = params;
    
    // Check if user has admin access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);
    
    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    // Check if invite exists
    const invite = await prisma.organizationInvite.findFirst({
      where: {
        id: inviteId,
        organizationId,
      },
    });
    
    if (!invite) {
      return NextResponse.json({ error: "Invite not found" }, { status: 404 });
    }
    
    // Delete the invite
    await prisma.organizationInvite.delete({
      where: { id: inviteId },
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting invite:", error);
    return NextResponse.json(
      { error: "Failed to delete invite" },
      { status: 500 }
    );
  }
} 