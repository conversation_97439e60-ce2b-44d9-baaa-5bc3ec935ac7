"use client"

import { createContext, useContext } from "react"

interface Order {
  id: string
  status: string
  total: number
  items: Array<{
    id: string
    quantity: number
    product?: {
      id: string
      name: string
      price: number
      images: string[]
    } | null
  }>
  shippingAddress: {
    fullName: string
    address: string
    city: string
    country: string
    postalCode: string
  }
  user?: {
    id: string
    name: string | null
    email: string | null
    image: string | null
  }
  createdAt: Date
}

export const OrdersContext = createContext<Order[] | null>(null)

export function useOrders() {
  const context = useContext(OrdersContext)
  if (context === null) {
    throw new Error("useOrders must be used within an OrdersProvider")
  }
  return context
}