"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ExternalLink, Check, Loader2, FileText, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import axios from "axios";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { DateFormatter } from "@/lib/utils";

interface InviteLinkButtonProps {
  organizationId: string;
}

export function InviteLinkButton({ organizationId }: InviteLinkButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [inviteLink, setInviteLink] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  // Check for existing general invites when dialog opens
  const fetchOrCreateInviteLink = async () => {
    try {
      setIsLoading(true);

      // First try to get existing general invites that haven't expired
      const existingInvitesResponse = await axios.get(`/api/affiliate/organizations/${organizationId}/invites`);

      // Find a general invite (one without an email) that isn't expired
      const generalInvite = existingInvitesResponse.data.find(invite =>
        !invite.email &&
        invite.status === "PENDING" &&
        new Date(invite.expiresAt) > new Date()
      );

      if (generalInvite) {
        // Use the existing invite
        const inviteCode = generalInvite.inviteCode;
        const baseUrl = window.location.origin;
        const link = `${baseUrl}/invite/${inviteCode}`;

        // Format expiry date
        const formattedDate = DateFormatter.forUserSafe(generalInvite.expiresAt);

        setInviteLink(link);
        setExpiryDate(formattedDate);
      } else {
        // No valid general invite found, create a new one
        const response = await axios.post(`/api/affiliate/organizations/${organizationId}/invites/general`);

        const inviteCode = response.data.inviteCode;
        const baseUrl = window.location.origin;
        const link = `${baseUrl}/invite/${inviteCode}`;

        // Format expiry date
        const formattedDate = DateFormatter.forUserSafe(response.data.expiresAt);

        setInviteLink(link);
        setExpiryDate(formattedDate);
      }

      setIsCopied(false);
    } catch (error) {
      console.error("Error with invite link:", error);
      toast.error("Failed to generate invite link");
    } finally {
      setIsLoading(false);
    }
  };

  const generateNewInviteLink = async () => {
    try {
      setIsGenerating(true);

      // Create a new general invite
      const response = await axios.post(`/api/affiliate/organizations/${organizationId}/invites/general`);

      const inviteCode = response.data.inviteCode;
      const baseUrl = window.location.origin;
      const link = `${baseUrl}/invite/${inviteCode}`;

      // Format expiry date
      const formattedDate = DateFormatter.forUserSafe(response.data.expiresAt);

      setInviteLink(link);
      setExpiryDate(formattedDate);
      setIsCopied(false);

      toast.success("New invite link generated");
    } catch (error) {
      console.error("Error generating invite link:", error);
      toast.error("Failed to generate new invite link");
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(inviteLink).then(() => {
      setIsCopied(true);
      toast.success("Invite link copied to clipboard");

      // Reset copy icon after 2 seconds
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    });
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      fetchOrCreateInviteLink();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button size="sm" variant="outline" className="gap-1">
          <ExternalLink className="h-4 w-4" />
          Invite Link
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="mt-2 text-sm text-muted-foreground">Loading invite link...</p>
          </div>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>Organization Invite Link</DialogTitle>
              <DialogDescription>
                Share this link with anyone to join your organization. The link will expire in 7 days.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Input
                    readOnly
                    value={inviteLink}
                    className="font-mono text-sm"
                  />
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={copyToClipboard}
                    disabled={!inviteLink}
                    className="shrink-0"
                    title="Copy to clipboard"
                  >
                    {isCopied ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <FileText className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {expiryDate && (
                  <p className="text-xs text-muted-foreground mt-2">
                    This link will expire on {expiryDate}
                  </p>
                )}
              </div>

              <div className="flex justify-between items-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={generateNewInviteLink}
                  disabled={isGenerating}
                  className="gap-1"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4" />
                      Generate New Link
                    </>
                  )}
                </Button>
              </div>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}