import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { format, formatDistanceToNow, isValid, parseISO } from "date-fns"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(price: number) {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(price)
}

// 应用配置
export const APP_CONFIG = {
  TIMEZONE: process.env.NEXT_PUBLIC_TIMEZONE || 'Asia/Shanghai',
  LOCALE: process.env.NEXT_PUBLIC_LOCALE || 'zh-CN',
  DATE_FORMAT: process.env.NEXT_PUBLIC_DATE_FORMAT || 'yyyy-MM-dd',
  DATETIME_FORMAT: process.env.NEXT_PUBLIC_DATETIME_FORMAT || 'yyyy-MM-dd HH:mm:ss',
} as const

// 获取用户浏览器时区 (仅在客户端使用)
export function getUserTimezone(): string {
  // 检查是否在客户端环境
  if (typeof window === 'undefined') {
    // 服务端渲染时返回默认时区，避免水合不一致
    return APP_CONFIG.TIMEZONE
  }

  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone
  } catch (error) {
    console.warn('Failed to get user timezone, falling back to default:', error)
    return APP_CONFIG.TIMEZONE
  }
}

// 安全的日期解析函数
export function safeParseDate(input: unknown): Date | null {
  if (!input) return null

  try {
    let date: Date

    if (input instanceof Date) {
      date = input
    } else if (typeof input === 'string') {
      // 尝试解析ISO字符串
      if (input.includes('T') || input.includes('Z')) {
        date = parseISO(input)
      } else {
        date = new Date(input)
      }
    } else if (typeof input === 'number') {
      date = new Date(input)
    } else {
      return null
    }

    return isValid(date) ? date : null
  } catch (error) {
    console.warn('Date parsing error:', error, 'Input:', input)
    return null
  }
}

// 统一的日期格式化工具
export const DateFormatter = {
  // 短日期格式 (2024-01-15)
  short: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback
    return format(date, APP_CONFIG.DATE_FORMAT)
  },

  // 完整日期时间格式 (2024-01-15 14:30:25)
  full: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback
    return format(date, APP_CONFIG.DATETIME_FORMAT)
  },

  // 本地化长日期格式 (January 15, 2024)
  long: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback
    return date.toLocaleDateString(APP_CONFIG.LOCALE, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  },

  // 相对时间格式 (2 hours ago)
  relative: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback
    return formatDistanceToNow(date, { addSuffix: true })
  },

  // 时间格式 (14:30:25)
  time: (input: unknown, fallback = 'Invalid Time'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback
    return format(date, 'HH:mm:ss')
  },

  // 简短时间格式 (14:30)
  timeShort: (input: unknown, fallback = 'Invalid Time'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback
    return format(date, 'HH:mm')
  },

  // 带时区的完整格式 (管理端使用，默认中国时区)
  withTimezone: (input: unknown, timezone?: string, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback

    return new Intl.DateTimeFormat(APP_CONFIG.LOCALE, {
      timeZone: timezone || APP_CONFIG.TIMEZONE,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    }).format(date)
  },

  // 用户前端专用格式化 (使用用户浏览器时区)
  forUser: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback

    const timezone = getUserTimezone()

    return new Intl.DateTimeFormat(APP_CONFIG.LOCALE, {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    }).format(date)
  },

  // 用户前端短日期格式 (使用用户浏览器时区)
  forUserShort: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback

    const timezone = getUserTimezone()

    return new Intl.DateTimeFormat(APP_CONFIG.LOCALE, {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    }).format(date)
  },

  // 安全的用户前端格式化 (避免SSR水合问题)
  forUserSafe: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback

    // 在服务端渲染时使用默认时区，客户端使用用户时区
    const timezone = typeof window === 'undefined' ? APP_CONFIG.TIMEZONE : getUserTimezone()

    return new Intl.DateTimeFormat(APP_CONFIG.LOCALE, {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    }).format(date)
  },

  // 用户前端相对时间 (date-fns已经基于本地时间)
  forUserRelative: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback
    return formatDistanceToNow(date, { addSuffix: true })
  },

  // ISO字符串格式 (用于API传输)
  iso: (input: unknown, fallback = ''): string => {
    const date = safeParseDate(input)
    if (!date) return fallback
    return date.toISOString()
  },

  // 自定义格式
  custom: (input: unknown, formatString: string, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input)
    if (!date) return fallback
    return format(date, formatString)
  }
}

// 日期计算工具函数
export const DateUtils = {
  // 安全的日期加法 (避免跨月份问题)
  addDays: (date: Date | string | number, days: number): Date => {
    const baseDate = safeParseDate(date) || new Date()
    return new Date(baseDate.getTime() + days * 24 * 60 * 60 * 1000)
  },

  addHours: (date: Date | string | number, hours: number): Date => {
    const baseDate = safeParseDate(date) || new Date()
    return new Date(baseDate.getTime() + hours * 60 * 60 * 1000)
  },

  addMinutes: (date: Date | string | number, minutes: number): Date => {
    const baseDate = safeParseDate(date) || new Date()
    return new Date(baseDate.getTime() + minutes * 60 * 1000)
  },

  // 计算两个日期之间的天数差
  daysBetween: (date1: Date | string | number, date2: Date | string | number): number => {
    const d1 = safeParseDate(date1)
    const d2 = safeParseDate(date2)
    if (!d1 || !d2) return 0
    return Math.floor((d2.getTime() - d1.getTime()) / (1000 * 60 * 60 * 24))
  },

  // 检查日期是否过期
  isExpired: (date: Date | string | number): boolean => {
    const targetDate = safeParseDate(date)
    if (!targetDate) return false
    return targetDate.getTime() < Date.now()
  }
}

// 向后兼容的函数 (保持现有API不变)
export function formatDate(input: string | number | Date): string {
  return DateFormatter.long(input)
}

export function formatDatetime(input: string | number | Date): string {
  return DateFormatter.full(input)
}

export function formatDateYMDHM(input: string | number | Date): string {
  return DateFormatter.custom(input, 'yyyy/MM/dd HH:mm')
}

/**
 * Cleans a card number by removing all non-digit characters
 * Example: "29901-00000-00000-00025" -> "29901000000000000025"
 * Example: "299 01 - 000 00 - 000 00 - 000 25" -> "29901000000000000025"
 */
export function cleanCardNumber(cardNumber: string | undefined | null): string {
  if (!cardNumber) return "";
  // Remove all non-digit characters (hyphens, spaces, punctuation, etc.)
  return cardNumber.replace(/\D/g, '');
}

/**
 * Formats a UID by adding a hyphen after every 5 digits
 * Example: "1234567890123456789" -> "12345-67890-12345-67890"
 */
export function formatUid(uid: string | undefined | null): string {
  if (!uid) return "";
  // If UID is more than 20 chars and not all digits, do not display
  if (uid.length > 20 && !/^\d+$/.test(uid)) {
    return "";
  }
  // Remove any existing non-digit characters
  const digitsOnly = uid.replace(/\D/g, '');
  // Add hyphens after every 5 digits
  return digitsOnly.replace(/(\d{5})(?=\d)/g, '$1-');
}

/**
 * Formats a product name by ensuring spaces around '-' and '/'.
 * Example: "Asia 14-1GB/Natural day" -> "Asia 14 - 1GB / Natural day"
 */
export function formatProductName(name: string): string {
  if (!name) return "";
  // Add spaces around '-' and '/'
  return name
    .replace(/\s*([-\/])\s*/g, ' $1 ')
    .replace(/\s{2,}/g, ' ') // Replace multiple spaces with single space
    .trim();
}

/**
 * 解析商品名称前的 [5G] 或 [4G] 徽章
 * 返回 { badge: "5G" | "4G" | undefined, name: string }
 */
export function parseProductBadge(name: string): { badge?: string; name: string } {
  const match = name.match(/^\s*\[(5G|4G)\]\s*(.*)$/i);
  if (match) {
    return { badge: match[1].toUpperCase(), name: match[2] };
  }
  return { name };
}
