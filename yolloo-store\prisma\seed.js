const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('Starting seed...');

  // Check if default system address already exists
  const existingDefaultAddress = await prisma.address.findFirst({
    where: {
      userId: null,
      name: 'System Default Address',
    },
  });

  if (!existingDefaultAddress) {
    // Create a default system address that doesn't belong to any user
    const defaultAddress = await prisma.address.create({
      data: {
        userId: null, // No user associated
        type: 'SHIPPING',
        name: 'System Default Address',
        phone: '0000000000',
        address1: 'Virtual Product Delivery',
        address2: 'No Physical Shipping Required',
        city: 'Virtual City',
        state: 'Virtual State',
        postalCode: '00000',
        country: 'VIRTUAL',
        isDefault: true,
      },
    });

    console.log('Created default system address:', defaultAddress.id);
  } else {
    console.log('Default system address already exists:', existingDefaultAddress.id);
  }

  console.log('Seed completed successfully.');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
