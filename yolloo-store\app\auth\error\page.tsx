"use client"

import { useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function AuthError() {
  const searchParams = useSearchParams()
  const error = searchParams.get("error")

  let errorMessage = "An error occurred during authentication"
  
  if (error === "AccessDenied") {
    errorMessage = "Access denied. You do not have permission to access this resource."
  } else if (error === "Configuration") {
    errorMessage = "There is a problem with the server configuration."
  } else if (error === "Verification") {
    errorMessage = "The verification token has expired or has already been used."
  } else if (error === "OAuthSignin") {
    errorMessage = "Error occurred during sign in. Please try again."
  } else if (error === "OAuthCallback") {
    errorMessage = "Error occurred during OAuth callback. Please try again."
  } else if (error === "OAuthCreateAccount") {
    errorMessage = "Could not create OAuth provider account."
  } else if (error === "EmailCreateAccount") {
    errorMessage = "Could not create email provider account."
  } else if (error === "Callback") {
    errorMessage = "Error occurred during callback processing."
  } else if (error === "EmailSignin") {
    errorMessage = "The e-mail could not be sent."
  } else if (error === "CredentialsSignin") {
    errorMessage = "Invalid credentials. Please check your email and password."
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen py-2">
      <div className="p-8 bg-white rounded-lg shadow-md max-w-md w-full space-y-4">
        <h1 className="text-2xl font-bold text-center text-red-600">Authentication Error</h1>
        <p className="text-center text-gray-600">{errorMessage}</p>
        <div className="flex justify-center pt-4">
          <Button asChild>
            <Link href="/auth/signin">
              Try Again
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
} 