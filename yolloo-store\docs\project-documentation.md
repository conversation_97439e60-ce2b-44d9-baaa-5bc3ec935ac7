# Yolloo Store 项目完整技术文档

## 项目概述

Yolloo Store 是一个基于 Next.js 14 的现代化 eSIM 电商平台，集成了完整的电商功能、eSIM 管理、联盟营销系统和企业级 ERP 集成。

### 技术栈
- **前端**: Next.js 14 + React 18 + TypeScript + Tailwind CSS
- **后端**: Next.js API Routes + NestJS (移动端API)
- **数据库**: PostgreSQL + Prisma ORM
- **缓存**: Redis + ioredis
- **认证**: NextAuth.js (支持邮箱验证码、密码、Google OAuth)
- **支付**: Stripe
- **部署**: Docker + 多环境支持

## 核心业务模块

### 1. 用户认证与授权系统

#### 业务逻辑
- 支持多种登录方式：邮箱密码、邮箱验证码、Google OAuth
- 基于角色的权限控制：ADMIN、CUSTOMER、STAFF
- 会话管理和安全控制

#### 代码模块
```
auth.ts                    # NextAuth 配置
auth.config.ts             # 认证配置
lib/auth.ts               # 认证工具函数
app/auth/                 # 认证页面
middleware.ts             # 路由中间件
```

#### 数据流
1. 用户提交登录信息
2. NextAuth.js 验证凭据
3. 创建 JWT token 和 session
4. 中间件验证路由权限
5. 返回用户信息和权限

### 2. 产品管理系统

#### 业务逻辑
- 支持多类型产品：eSIM、设备、套餐、QR码产品
- 产品变体管理（价格、时长、属性）
- 分类层级管理
- 库存管理
- 与 Odoo ERP 双向同步

#### 代码模块
```
app/api/products/         # 产品API
app/api/admin/products/   # 管理员产品API
app/services/productSyncService.ts  # 产品同步服务
app/services/odooService.ts         # Odoo集成服务
components/products/      # 产品组件
```

#### 数据流
1. Odoo ERP → 产品同步服务 → 本地数据库
2. 管理员创建/编辑产品 → 数据库 → 前端展示
3. 用户浏览产品 → API查询 → 返回产品列表
4. 产品筛选 → 数据库查询 → 分页返回

### 3. 购物车与订单系统

#### 业务逻辑
- 购物车管理（添加、删除、修改数量）
- 订单创建和状态管理
- 支持立即购买和购物车结算
- UID信息收集（针对特定产品）
- 订单状态与 Odoo 同步

#### 代码模块
```
app/api/cart/             # 购物车API
app/api/orders/           # 订单API
app/checkout/             # 结算页面
components/cart/          # 购物车组件
components/checkout-form.tsx  # 结算表单
lib/hooks/use-cart.ts     # 购物车状态管理
```

#### 数据流
1. 添加商品到购物车 → CartItem 表
2. 结算 → 创建 Order 和 OrderItem
3. 支付成功 → 更新订单状态
4. 订单信息 → 同步到 Odoo ERP
5. Odoo 状态更新 → 回调更新本地状态

### 4. 支付处理系统

#### 业务逻辑
- Stripe 支付集成
- 支付会话创建和管理
- 支付状态跟踪
- 支付成功后订单处理

#### 代码模块
```
app/api/payments/         # 支付API
app/api/webhooks/stripe/  # Stripe webhook
components/complete-payment-button.tsx  # 支付按钮
```

#### 数据流
1. 创建支付会话 → Stripe API
2. 用户完成支付 → Stripe 处理
3. Stripe webhook → 更新支付状态
4. 支付成功 → 触发订单处理流程

### 5. eSIM 管理系统

#### 业务逻辑
- Yolloo 卡管理
- eSIM 激活和生命周期管理
- 与 Boss 服务集成
- QR 码生成和管理

#### 代码模块
```
app/api/cards/            # 卡片管理API
lib/boss.ts               # Boss服务集成
lib/bossService.ts        # Boss服务封装
mobile-api/src/cards/     # 移动端卡片API
```

#### 数据流
1. 订单包含 eSIM 产品 → 调用 Boss API
2. Boss 服务创建套餐订单 → 返回激活信息
3. 更新 eSIM 状态 → 生成 QR 码
4. 用户获取 eSIM 信息和 QR 码

### 6. 联盟营销系统

#### 业务逻辑
- 个人联盟会员管理
- 组织联盟管理
- 推荐链接跟踪
- 佣金计算和提现
- 访问统计和转化跟踪

#### 代码模块
```
app/affiliate/            # 联盟页面
app/api/affiliate/        # 联盟API
app/admin/affiliates/     # 管理员联盟管理
components/affiliate/     # 联盟组件
```

#### 数据流
1. 用户申请联盟 → 创建 AffiliateProfile
2. 生成推荐链接 → 跟踪访问 (AffiliateVisit)
3. 访客通过推荐链接下单 → 创建 AffiliateReferral
4. 计算佣金 → 更新收益统计
5. 申请提现 → 创建 AffiliateWithdrawal

### 7. 管理后台系统

#### 业务逻辑
- 产品管理（CRUD、同步、上下架）
- 订单管理（状态更新、退款）
- 用户管理（权限、信息）
- 联盟管理（审核、佣金）
- 数据统计和报表

#### 代码模块
```
app/admin/                # 管理后台页面
app/api/admin/            # 管理员API
components/admin/         # 管理后台组件
middleware.ts             # 权限中间件
```

#### 数据流
1. 管理员登录 → 验证 ADMIN 角色
2. 操作数据 → API 验证权限
3. 数据变更 → 数据库更新
4. 同步操作 → 调用外部服务
5. 生成报表 → 聚合查询数据

## 外部系统集成

### 1. Odoo ERP 集成

#### 集成目的
- 产品信息同步
- 订单状态同步
- 库存管理
- 业务数据统一

#### 技术实现
```typescript
// app/services/odooService.ts
class OdooService {
  // 产品同步
  async pullProducts(productType: string): Promise<OdooResponse<OdooProduct[]>>
  
  // 订单创建
  async createOrder(order: OdooOrder): Promise<OdooOrderResponse>
  
  // 订单状态查询
  async queryOrderStatusMultiple(customerOrderRefs: string[]): Promise<OdooResponse<any>>
  
  // QR码获取
  async getOrderQRCode(customerOrderRef: string | string[]): Promise<OdooResponse<OdooQRCodeResponse[]>>
}
```

#### 数据流
1. 定时任务 → 从 Odoo 拉取产品信息
2. 本地订单创建 → 推送到 Odoo
3. Odoo 状态变更 → webhook 回调本地
4. 本地状态更新 → 通知用户

### 2. Boss 服务集成

#### 集成目的
- eSIM 套餐管理
- 设备订单处理
- 套餐激活和控制

#### 技术实现
```typescript
// lib/bossService.ts
class BossService {
  // 创建套餐订单
  async createProductOrder(params: CreateProductOrderParams): Promise<BossResponse>
  
  // 查询套餐分页
  async getDeviceOrderPage(params: GetDeviceOrderPageParams): Promise<BossResponse>
  
  // 取消套餐
  async cancelPackage(params: CancelPackageParams): Promise<BossResponse>
}
```

### 3. 第三方服务集成

#### Stripe 支付
- 支付会话创建
- Webhook 事件处理
- 退款处理

#### Resend 邮件服务
- 验证码发送
- 订单通知
- 营销邮件

#### Redis 缓存
- 会话存储
- 验证码缓存
- 频率限制

## 移动端 API (NestJS)

### 架构设计
```
mobile-api/src/
├── auth/                    # 认证模块
│   ├── auth.controller.ts   # 认证控制器
│   ├── auth.service.ts      # 认证服务
│   ├── guards/              # 认证守卫
│   └── dto/                 # 数据传输对象
├── users/                   # 用户管理
├── products/                # 产品服务
├── cart/                    # 购物车
├── orders/                  # 订单管理
├── cards/                   # 卡片管理
│   ├── cards.controller.ts  # 卡片控制器
│   ├── cards.service.ts     # 卡片服务
│   └── esims.controller.ts  # eSIM控制器
├── home/                    # 首页数据
├── geography/               # 地理位置服务
├── travel-packages/         # 旅行套餐
├── local-packages/          # 本地套餐
├── data-boosters/           # 数据加速包
├── user-packages/           # 用户套餐
├── mobile-recharge/         # 手机充值
├── number-retention/        # 号码保留
├── articles/                # 文章管理
├── pages/                   # 页面内容
├── location/                # 位置服务
├── health/                  # 健康检查
└── common/                  # 公共模块
    ├── decorators/          # 装饰器
    ├── filters/             # 异常过滤器
    ├── guards/              # 守卫
    ├── interfaces/          # 接口定义
    └── utils/               # 工具函数
```

### 核心功能模块

#### 1. 认证模块 (auth/)
```typescript
// JWT认证策略
@Injectable()
export class JwtAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    // JWT token验证逻辑
  }
}

// 公开接口装饰器
@Public()
@Get('home')
getHomeData() {
  // 无需认证的接口
}
```

#### 2. 产品服务 (products/)
```typescript
// 产品分类查询
@Get('categories')
getCategories(@RequestCtx() ctx: RequestContext) {
  return this.productsService.getCategories(ctx);
}

// 分类产品查询
@Get('category/:categoryId')
getProducts(
  @Param('categoryId') categoryId: string,
  @Query() query: ProductQueryDto,
  @RequestCtx() ctx: RequestContext,
) {
  return this.productsService.getProductsByCategory(categoryId, query, ctx);
}
```

#### 3. 卡片管理 (cards/)
```typescript
// 获取用户卡片列表
@Get()
getUserCards(
  @CurrentUser() user: any,
  @Query() query: GetCardsQueryDto,
) {
  return this.cardsService.getUserCards(user.id, query);
}

// eSIM管理
@Get(':cardId/esims')
getCardEsims(
  @CurrentUser() user: any,
  @Param('cardId') cardId: string,
) {
  return this.cardsService.getCardEsims(user.id, cardId);
}
```

#### 4. 首页数据 (home/)
```typescript
// 首页数据聚合
@Public()
@Get('home')
getHomeData(
  @CurrentUser() user: any,
  @Query() query: HomeQueryDto,
  @RequestCtx() ctx: RequestContext,
) {
  return this.homeService.getHomeData(user?.id, query, ctx);
}

// Banner数据
@Public()
@Get('home/banners')
getBanners(
  @Query() query: BannerQueryDto,
  @RequestCtx() ctx: RequestContext,
) {
  return this.homeService.getBanners(query, ctx);
}
```

### 移动端特有功能

#### 1. 地理位置服务 (geography/)
- 国家/地区查询
- 位置相关产品推荐
- 地理位置缓存

#### 2. 旅行套餐 (travel-packages/)
- 旅行目的地套餐
- 多国套餐管理
- 行程相关推荐

#### 3. 本地套餐 (local-packages/)
- 本地运营商套餐
- 本地优惠活动
- 地区特色服务

#### 4. 用户套餐管理 (user-packages/)
- 用户已购套餐
- 套餐使用情况
- 套餐续费提醒

### 移动端API特点

#### 1. 响应式设计
- 适配不同屏幕尺寸
- 移动端优化的数据结构
- 减少数据传输量

#### 2. 离线支持
- 关键数据缓存
- 离线状态处理
- 数据同步机制

#### 3. 性能优化
- 分页加载
- 图片懒加载
- 接口响应优化

## 数据库设计

### 核心表结构

#### 用户相关
- `User`: 用户基本信息
- `Account`: OAuth 账户关联
- `Session`: 用户会话
- `UserLoginHistory`: 登录历史

#### 产品相关
- `Product`: 产品主表
- `Category`: 产品分类
- `ProductVariant`: 产品变体
- `ProductParameter`: 产品参数

#### 订单相关
- `Order`: 订单主表
- `OrderItem`: 订单项
- `OdooOrderStatus`: Odoo 状态跟踪
- `Payment`: 支付记录

#### eSIM 相关
- `YollooCard`: Yolloo 卡
- `Esim`: eSIM 实例
- `EsimProfile`: eSIM 配置
- `EsimPlan`: eSIM 套餐

#### 联盟营销
- `AffiliateProfile`: 联盟会员
- `AffiliateOrganization`: 联盟组织
- `AffiliateReferral`: 推荐记录
- `AffiliateVisit`: 访问跟踪

## 部署架构

### 环境配置
- **开发环境**: `docker-compose.yml`
- **测试环境**: `docker-compose.testing.yml`
- **生产环境**: `docker-compose.production.yml`

### 容器化
```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 8000
CMD ["npm", "start"]
```

### 启动脚本
- `start-production.sh`: 生产环境启动
- `start-testing.sh`: 测试环境启动
- `deploy.sh`: 部署脚本

## 安全措施

### 认证安全
- JWT token 管理
- 会话过期控制
- 密码哈希存储
- OAuth 安全集成

### API 安全
- 路由权限验证
- 频率限制 (Redis)
- 输入验证和清理
- CORS 配置

### 数据安全
- 数据库连接加密
- 敏感信息环境变量
- API 密钥管理
- 审计日志记录

## 性能优化

### 数据库优化
- 索引优化
- 查询优化
- 连接池管理
- 分页查询

### 缓存策略
- Redis 会话缓存
- API 响应缓存
- 静态资源缓存
- 数据库查询缓存

### 前端优化
- 代码分割
- 懒加载
- 图片优化
- CDN 集成

## 监控与日志

### 日志系统
- 应用日志
- 错误日志
- 访问日志
- 同步日志 (`SyncLog` 表)

### 监控指标
- 应用性能
- 数据库性能
- API 响应时间
- 错误率统计

## 详细业务流程

### 1. 用户注册登录流程

#### 邮箱验证码登录
```
用户输入邮箱 → 发送验证码 → Redis缓存验证码 → 用户输入验证码 → 验证成功 → 创建/更新用户 → 生成JWT → 登录成功
```

#### 密码登录
```
用户输入邮箱密码 → bcrypt验证密码 → 验证成功 → 生成JWT → 记录登录历史 → 登录成功
```

#### Google OAuth登录
```
用户点击Google登录 → 跳转Google授权 → 获取授权码 → 交换access_token → 获取用户信息 → 创建/关联账户 → 登录成功
```

### 2. 产品购买完整流程

#### 浏览和选择
```
访问产品页面 → 加载产品列表(分页) → 应用筛选条件 → 查看产品详情 → 选择变体 → 添加到购物车/立即购买
```

#### 结算流程
```
进入结算页面 → 验证登录状态 → 加载收货地址 → 检查UID要求 → 输入UID信息 → 创建订单 → 跳转支付
```

#### 支付流程
```
创建Stripe会话 → 跳转Stripe支付页面 → 用户完成支付 → Stripe webhook回调 → 更新支付状态 → 触发订单处理
```

#### 订单处理
```
支付成功 → 创建Odoo订单 → 判断产品类型 → 数字产品:调用Boss API → 物理产品:安排发货 → 更新订单状态 → 通知用户
```

### 3. eSIM激活流程

#### Boss服务集成流程
```
订单包含eSIM → 构建Boss API参数 → 调用createProductOrder → Boss返回套餐信息 → 更新eSIM状态 → 生成QR码 → 用户下载
```

#### 状态同步流程
```
Boss状态变更 → 定时查询Boss API → 更新本地eSIM状态 → 同步到Odoo → 通知用户状态变更
```

### 4. 联盟营销流程

#### 会员申请流程
```
用户申请联盟 → 填写申请信息 → 系统审核 → 生成联盟码 → 发送通知 → 会员激活
```

#### 推广转化流程
```
会员分享链接 → 访客点击(记录AffiliateVisit) → 访客浏览产品 → 访客下单 → 创建AffiliateReferral → 计算佣金 → 更新收益
```

#### 组织管理流程
```
创建组织 → 邀请成员 → 成员接受邀请 → 设置佣金比例 → 组织推广 → 收益分配 → 统一提现
```

## 关键数据流分析

### 1. 产品数据流

#### Odoo → 本地同步
```typescript
// 同步流程
1. 定时任务触发 → app/api/admin/products/sync/route.ts
2. 调用 OdooService.pullProducts() → 获取Odoo产品数据
3. convertOdooToProduct() → 转换数据格式
4. 批量更新/创建产品 → Prisma操作
5. 更新同步日志 → SyncLog表
```

#### 前端产品查询
```typescript
// 查询流程
1. 用户访问产品页面 → app/products/page.tsx
2. 调用 /api/products → app/api/products/route.ts
3. Prisma查询 → 应用筛选条件和分页
4. 返回产品列表 → 前端渲染
```

### 2. 订单数据流

#### 订单创建流程
```typescript
// 创建流程
1. 结算提交 → components/checkout-form.tsx
2. 调用 /api/orders → app/api/orders/route.ts
3. 创建Order和OrderItem → Prisma事务
4. 同步到Odoo → OdooService.createOrder()
5. 返回订单ID → 跳转支付
```

#### 状态同步流程
```typescript
// 同步流程
1. Odoo状态变更 → webhook回调
2. 解析订单状态 → 更新OdooOrderStatus
3. 判断状态变化 → 触发相应业务逻辑
4. 通知用户 → 邮件/站内消息
```

### 3. 支付数据流

#### Stripe集成流程
```typescript
// 支付流程
1. 创建支付会话 → app/api/payments/route.ts
2. Stripe.checkout.sessions.create() → 生成支付链接
3. 用户完成支付 → Stripe处理
4. Webhook回调 → app/api/webhooks/stripe/route.ts
5. 更新Payment状态 → 触发订单处理
```

### 4. eSIM数据流

#### Boss服务集成
```typescript
// eSIM激活流程
1. 订单包含eSIM产品 → 检测产品类型
2. 构建Boss API参数 → 包含customerOrderSn, productSkuId, uid
3. 调用BossService.createProductOrder() → Boss API
4. 解析返回结果 → 更新eSIM状态
5. 生成QR码 → 用户可下载
```

### 5. 联盟数据流

#### 访问跟踪
```typescript
// 跟踪流程
1. 用户点击推荐链接 → 包含referral参数
2. 中间件解析推荐码 → 查找AffiliateProfile
3. 记录访问 → 创建AffiliateVisit
4. 设置Cookie → 标记推荐来源
5. 用户下单时 → 关联推荐记录
```

#### 佣金计算
```typescript
// 计算流程
1. 订单支付成功 → 检查推荐来源
2. 查找联盟会员 → AffiliateProfile
3. 计算佣金 → 根据commissionRate
4. 创建推荐记录 → AffiliateReferral
5. 更新总收益 → AffiliateProfile.totalEarnings
```

## API接口设计

### 1. 产品相关API

```typescript
// 公开API
GET /api/products                    # 获取产品列表
GET /api/products/[productId]        # 获取产品详情
POST /api/products/price             # 获取产品价格

// 管理员API
GET /api/admin/products              # 管理员产品列表
POST /api/admin/products             # 创建产品
PUT /api/admin/products/[productId]  # 更新产品
DELETE /api/admin/products/[productId] # 删除产品
POST /api/admin/products/sync        # 同步产品
```

### 2. 订单相关API

```typescript
// 用户API
GET /api/orders                      # 获取用户订单
POST /api/orders                     # 创建订单
GET /api/orders/[orderId]           # 获取订单详情

// 管理员API
GET /api/admin/orders               # 管理员订单列表
PUT /api/admin/orders/[orderId]     # 更新订单状态
```

### 3. 购物车API

```typescript
GET /api/cart                       # 获取购物车
POST /api/cart                      # 添加到购物车
PUT /api/cart/[itemId]             # 更新购物车项
DELETE /api/cart/[itemId]          # 删除购物车项
```

### 4. 联盟营销API

```typescript
GET /api/affiliate                  # 获取联盟信息
POST /api/affiliate                 # 申请联盟
GET /api/affiliate/stats           # 获取统计数据
POST /api/affiliate/withdraw       # 申请提现
```

## 错误处理和异常管理

### 1. API错误处理
```typescript
// 统一错误响应格式
{
  "error": "错误信息",
  "code": "ERROR_CODE",
  "details": "详细错误信息"
}
```

### 2. 业务异常处理
- 支付失败处理
- Odoo同步失败重试
- Boss服务调用失败
- 邮件发送失败

### 3. 数据一致性保证
- 数据库事务
- 分布式锁
- 幂等性设计
- 补偿机制

## 开发规范

### 代码规范
- TypeScript 严格模式
- ESLint 代码检查
- Prettier 代码格式化
- Git commit 规范

### 测试策略
- 单元测试
- 集成测试
- API 测试
- E2E 测试

### 版本控制
- Git 分支策略
- 代码审查流程
- 发布流程
- 回滚策略

## 配置管理

### 环境变量配置

#### 数据库配置
```bash
# PostgreSQL数据库连接
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# Redis缓存连接
REDIS_URL="redis://localhost:6379"
UPSTASH_REDIS_REST_URL="https://your-redis-instance.upstash.io"
UPSTASH_REDIS_REST_TOKEN="your-redis-token"
```

#### 认证配置
```bash
# NextAuth.js配置
NEXTAUTH_URL="http://localhost:8000"
NEXTAUTH_SECRET="your-nextauth-secret"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# 移动端API JWT
MOBILE_API_JWT_SECRET="your-mobile-jwt-secret"
```

#### Odoo ERP配置
```bash
# 标准Odoo配置
ODOO_ADDRESS="https://your-odoo-instance.com"
ODOO_CHANNEL_ID="your-channel-id"
ODOO_CHANNEL_LANGUAGE="en_US"
ODOO_AUTH_SECRET="your-odoo-auth-secret"
ODOO_SIGN_METHOD="MD5"

# QR专用Odoo配置
ODOO_QR_ADDRESS="https://your-qr-odoo-instance.com"
ODOO_QR_CHANNEL_ID="your-qr-channel-id"
ODOO_QR_CHANNEL_LANGUAGE="en_US"
ODOO_QR_AUTH_SECRET="your-qr-auth-secret"
ODOO_QR_SIGN_METHOD="MD5"
```

#### Boss服务配置
```bash
# Boss API配置
BOSS_ACCOUNT="your-boss-account"
BOSS_SECRET="your-boss-secret"
BOSS_BASE_URL="https://your-boss-api.com"
```

#### 支付配置
```bash
# Stripe配置
STRIPE_PUBLISHABLE_KEY="pk_test_your-publishable-key"
STRIPE_SECRET_KEY="sk_test_your-secret-key"
STRIPE_WEBHOOK_SECRET="whsec_your-webhook-secret"
```

#### 邮件服务配置
```bash
# Resend邮件服务
RESEND_API_KEY="re_your-resend-api-key"
```

#### 应用配置
```bash
# 应用基础配置
NEXT_PUBLIC_APP_URL="http://localhost:8000"
NODE_ENV="development"
```

### 配置文件结构

#### Next.js配置 (next.config.js)
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['your-image-domains.com'],
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
}

module.exports = nextConfig
```

#### Tailwind配置 (tailwind.config.ts)
```typescript
import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // 自定义主题配置
    },
  },
  plugins: [require('tailwindcss-animate')],
}

export default config
```

#### Prisma配置 (prisma/schema.prisma)
```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
```

### Docker配置

#### 主Dockerfile
```dockerfile
FROM node:18-alpine AS base

# 安装依赖
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci

# 构建应用
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

# 生产镜像
FROM base AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs
EXPOSE 8000
ENV PORT 8000
CMD ["node", "server.js"]
```

#### Docker Compose配置
```yaml
# docker-compose.yml (开发环境)
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=${DATABASE_URL}
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: yolloo_store
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

### 部署脚本

#### 生产环境启动脚本 (start-production.sh)
```bash
#!/bin/bash
echo "Starting Yolloo Store in production mode..."

# 检查环境变量
if [ -z "$DATABASE_URL" ]; then
    echo "Error: DATABASE_URL is not set"
    exit 1
fi

# 运行数据库迁移
npm run prisma:deploy

# 启动应用
docker-compose -f docker-compose.production.yml up -d

echo "Yolloo Store started successfully!"
```

#### 部署脚本 (deploy.sh)
```bash
#!/bin/bash
set -e

echo "Deploying Yolloo Store..."

# 拉取最新代码
git pull origin main

# 构建Docker镜像
docker build -t yolloo-store:latest .

# 停止旧容器
docker-compose down

# 启动新容器
docker-compose up -d

# 运行数据库迁移
docker-compose exec app npm run prisma:deploy

echo "Deployment completed successfully!"
```

## 故障排除指南

### 常见问题

#### 1. 数据库连接问题
```bash
# 检查数据库连接
npx prisma db pull

# 重置数据库
npx prisma migrate reset

# 生成Prisma客户端
npx prisma generate
```

#### 2. Redis连接问题
```bash
# 检查Redis连接
redis-cli ping

# 清空Redis缓存
redis-cli flushall
```

#### 3. Odoo集成问题
```bash
# 检查Odoo连接
curl -X POST "https://your-odoo-instance.com/openapi/v3/get_proudct_list" \
  -H "Content-Type: application/json" \
  -H "X-Channel-Id: your-channel-id" \
  -d '{"product_type": "esim", "start": 0, "length": 10}'
```

#### 4. 构建问题
```bash
# 清理构建缓存
npm run clean
rm -rf .next node_modules
npm install
npm run build
```

### 日志查看

#### 应用日志
```bash
# Docker容器日志
docker-compose logs -f app

# 特定服务日志
docker-compose logs -f postgres
docker-compose logs -f redis
```

#### 数据库日志
```sql
-- 查看同步日志
SELECT * FROM "SyncLog" ORDER BY timestamp DESC LIMIT 100;

-- 查看同步任务
SELECT * FROM "SyncTask" ORDER BY "startTime" DESC LIMIT 10;
```

## 性能监控

### 关键指标

#### 应用性能
- 响应时间
- 吞吐量
- 错误率
- 内存使用

#### 数据库性能
- 查询执行时间
- 连接数
- 锁等待
- 缓存命中率

#### 外部服务
- Odoo API响应时间
- Boss服务可用性
- Stripe支付成功率
- 邮件发送成功率

### 监控工具
- 应用性能监控 (APM)
- 数据库监控
- 日志聚合
- 告警系统

## 核心代码实现详解

### 1. 购物车API实现

#### 获取购物车 (GET /api/cart)
```typescript
// app/api/cart/route.ts
export async function GET(request: Request) {
  try {
    // 1. 验证用户会话
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // 2. 查询用户购物车项目，包含产品和变体信息
    const cartItems = await prisma.cartItem.findMany({
      where: { userId: session.user.id },
      include: {
        product: true,    // 关联产品信息
        variant: true,    // 关联变体信息
      },
    });

    return NextResponse.json(cartItems);
  } catch (error) {
    console.error("[CART_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
```

#### 添加到购物车 (POST /api/cart)
```typescript
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { productId, quantity = 1, variantId } = await request.json();

    // 1. 验证产品存在性
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: { variants: true },
    });

    if (!product) {
      return new NextResponse("Product not found", { status: 404 });
    }

    // 2. 验证变体选择
    if (product.variants.length > 0 && !variantId) {
      return new NextResponse("Variant selection is required", { status: 400 });
    }

    // 3. 检查是否已存在相同项目
    const existingCartItem = await prisma.cartItem.findFirst({
      where: {
        userId: session.user.id,
        productId: product.id,
        variantId: variantId || null,
      },
    });

    let cartItem;
    if (existingCartItem) {
      // 更新数量
      cartItem = await prisma.cartItem.update({
        where: { id: existingCartItem.id },
        data: { quantity: existingCartItem.quantity + quantity },
        include: { product: true, variant: true },
      });
    } else {
      // 创建新项目
      cartItem = await prisma.cartItem.create({
        data: {
          userId: session.user.id,
          productId: product.id,
          variantId: variantId || null,
          quantity,
        },
        include: { product: true, variant: true },
      });
    }

    return NextResponse.json(cartItem);
  } catch (error) {
    console.error("[CART_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
```

### 2. 移动端购物车服务

#### NestJS购物车服务实现
```typescript
// mobile-api/src/cart/cart.service.ts
@Injectable()
export class CartService {
  constructor(private prisma: PrismaService) {}

  async getCart(userId: string) {
    const cartItems = await this.prisma.cartItem.findMany({
      where: { userId },
      include: {
        product: true,
        variant: true,
      },
    });

    // 计算购物车摘要
    if (cartItems.length === 0) {
      return {
        items: [],
        summary: {
          subtotal: 0,
          shipping: 0,
          tax: 0,
          total: 0,
          currency: 'USD',
        },
      };
    }

    // 计算总价
    const subtotal = cartItems.reduce((sum, item) => {
      const price = item.variant ? Number(item.variant.price) : item.product.price;
      return sum + (price * item.quantity);
    }, 0);

    return {
      items: cartItems,
      summary: {
        subtotal,
        shipping: 0, // 免费配送
        tax: 0,      // 暂不计税
        total: subtotal,
        currency: 'USD',
      },
    };
  }

  async addCartItem(userId: string, addCartItemDto: AddCartItemDto) {
    const { productId, variantId, quantity } = addCartItemDto;

    // 验证产品存在
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
      include: { variants: true },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // 检查库存
    if (quantity > product.stock) {
      throw new BadRequestException('Insufficient stock');
    }

    // 查找现有购物车项目
    const existingItem = await this.prisma.cartItem.findFirst({
      where: { userId, productId, variantId },
    });

    if (existingItem) {
      // 更新数量
      return this.prisma.cartItem.update({
        where: { id: existingItem.id },
        data: { quantity: existingItem.quantity + quantity },
        include: { product: true, variant: true },
      });
    } else {
      // 创建新项目
      return this.prisma.cartItem.create({
        data: { userId, productId, variantId, quantity },
        include: { product: true, variant: true },
      });
    }
  }
}
```

### 3. 支付处理实现

#### Stripe支付会话创建
```typescript
// app/api/payments/route.ts
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { orderId } = await req.json();

    // 1. 验证订单
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: { items: true },
    });

    if (!order || order.userId !== session.user.id) {
      return new NextResponse("Order not found", { status: 404 });
    }

    if (order.status !== "PENDING") {
      return new NextResponse("Order is not pending", { status: 400 });
    }

    // 2. 创建Stripe支付会话
    const lineItems = order.items.map((item) => ({
      price_data: {
        currency: "usd",
        product_data: {
          name: item.variantText || "Product",
          images: [],
        },
        unit_amount: Math.round(item.price * 100), // 转换为分
      },
      quantity: item.quantity,
    }));

    const stripeSession = await stripe.checkout.sessions.create({
      customer_email: session.user.email!,
      line_items: lineItems,
      mode: "payment",
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/orders/${order.id}?success=true`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/orders/${order.id}?canceled=true`,
      metadata: { orderId: order.id },
    });

    // 3. 创建支付记录
    const payment = await prisma.payment.create({
      data: {
        amount: order.total,
        status: "PENDING",
        provider: "stripe",
        paymentMethod: "card",
        orders: { connect: { id: order.id } },
      },
    });

    // 4. 更新订单支付ID
    await prisma.order.update({
      where: { id: order.id },
      data: { paymentId: payment.id },
    });

    return NextResponse.json({ url: stripeSession.url });
  } catch (error) {
    console.error("[PAYMENTS_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
```

### 4. Odoo ERP集成实现

#### Odoo服务核心请求方法
```typescript
// app/services/odooService.ts
class OdooService {
  private async request<T>(method: string, url: string, data?: any): Promise<OdooResponse<T>> {
    const requestData = data || {};
    const body = JSON.stringify(requestData, null, 0);

    // 1. 生成签名
    const signature = this.generateSignature(body);
    const fullUrl = `${this.config.address}${url}`;

    // 2. 构建请求头
    const headers = {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'X-Channel-Id': this.config.channelId,
      'X-Channel-Language': this.config.channelLanguage,
      'X-sign-Method': this.config.signMethod.toLowerCase(),
      'X-Sign-Value': signature
    };

    try {
      // 3. 发送请求
      const response = await this.client.request({
        method,
        url: fullUrl,
        data: body,
        headers
      });

      return response.data;
    } catch (error) {
      // 4. 错误处理
      if (axios.isAxiosError(error)) {
        console.error('Odoo API Error:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          config: {
            url: error.config?.url,
            method: error.config?.method,
            headers: error.config?.headers,
            data: error.config?.data
          }
        });
        throw new Error(`Odoo API Error: ${error.response?.data?.error || error.message}`);
      }
      throw error;
    }
  }

  // 签名生成方法
  private generateSignature(body: string): string {
    const signString = `${this.config.channelId}${body}${this.config.authSecret}`;
    return createHash('md5').update(signString).digest('hex').toUpperCase();
  }

  // 创建订单
  async createOrder(order: OdooOrder): Promise<OdooOrderResponse> {
    return this.request('POST', '/openapi/v3/create_purchase_order', order);
  }

  // 查询订单状态
  async queryOrderStatusMultiple(customerOrderRefs: string[]): Promise<OdooResponse<any>> {
    const data = { customer_order_ref: customerOrderRefs };
    return this.request('POST', '/openapi/v3/get_sale_order_status', data);
  }

  // 获取QR码
  async getOrderQRCode(customerOrderRef: string | string[]): Promise<OdooResponse<OdooQRCodeResponse[]>> {
    const data = {
      customer_order_ref: Array.isArray(customerOrderRef)
        ? customerOrderRef.join(',')
        : customerOrderRef
    };
    return this.request('POST', '/openapi/v3/get_sale_order_esim_qrcode', data);
  }
}
```

### 5. Boss服务集成实现

#### Boss API调用封装
```typescript
// lib/bossService.ts
class BossService {
  private async request<T>(method: string, endpoint: string, data?: any): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;

    // 1. 构建请求参数
    const requestData = {
      account: this.account,
      ...data,
      timestamp: Date.now(),
    };

    // 2. 生成签名
    const signature = this.generateSignature(requestData);
    requestData.sign = signature;

    try {
      // 3. 发送请求
      const response = await axios({
        method,
        url,
        data: requestData,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      });

      return response.data;
    } catch (error) {
      console.error('[BOSS_SERVICE] Request failed:', {
        endpoint,
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  // 创建套餐订单
  async createProductOrder(params: CreateProductOrderParams): Promise<ApiResponse<OrderResponse>> {
    console.log('[BOSS_SERVICE] Creating product order', {
      timestamp: new Date().toISOString(),
      params
    });
    return this.request('POST', '/open/order/plan/createProductOrder', params);
  }

  // 查询设备订单分页
  async getDeviceOrderPage(params: GetDeviceOrderPageParams): Promise<ApiResponse<DeviceOrderPageResponse>> {
    return this.request('POST', '/open/order/plan/getDeviceOrderPage', params);
  }

  // 取消套餐
  async cancelPackage(params: CancelPackageParams): Promise<ApiResponse<any>> {
    return this.request('POST', '/open/order/plan/cancelPackage', params);
  }
}
```

### 6. 错误处理机制

#### 统一错误处理中间件
```typescript
// mobile-api/src/common/filters/http-exception.filter.ts
@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let error = 'Internal Server Error';

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      if (typeof exceptionResponse === 'object') {
        message = (exceptionResponse as any).message || message;
        error = (exceptionResponse as any).error || error;
      } else {
        message = exceptionResponse;
      }
    }

    // 记录错误日志
    console.error('Exception caught:', {
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      status,
      message,
      stack: exception instanceof Error ? exception.stack : undefined,
    });

    // 返回统一错误格式
    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      error,
      message,
    });
  }
}
```

#### API错误响应格式
```typescript
// 统一错误响应接口
interface ApiErrorResponse {
  statusCode: number;
  timestamp: string;
  path: string;
  error: string;
  message: string | string[];
  details?: any;
}

// 业务错误处理示例
export async function handleBusinessError(error: any, context: string) {
  console.error(`[${context}] Business error:`, {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
  });

  // 根据错误类型返回不同的HTTP状态码
  if (error.message.includes('not found')) {
    return new NextResponse("Resource not found", { status: 404 });
  }

  if (error.message.includes('unauthorized')) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  if (error.message.includes('validation')) {
    return new NextResponse("Validation error", { status: 400 });
  }

  return new NextResponse("Internal server error", { status: 500 });
}
```

## 业务逻辑最佳实践

### 1. 数据一致性保证

#### 数据库事务使用
```typescript
// 订单创建事务示例
async function createOrderWithTransaction(orderData: CreateOrderData) {
  return await prisma.$transaction(async (tx) => {
    // 1. 创建订单
    const order = await tx.order.create({
      data: {
        userId: orderData.userId,
        total: orderData.total,
        status: 'PENDING',
      },
    });

    // 2. 创建订单项目
    const orderItems = await Promise.all(
      orderData.items.map(item =>
        tx.orderItem.create({
          data: {
            orderId: order.id,
            productCode: item.productCode,
            variantCode: item.variantCode,
            quantity: item.quantity,
            price: item.price,
            uid: item.uid,
          },
        })
      )
    );

    // 3. 更新库存
    await Promise.all(
      orderData.items.map(item =>
        tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity,
            },
          },
        })
      )
    );

    // 4. 清空购物车
    await tx.cartItem.deleteMany({
      where: { userId: orderData.userId },
    });

    return { order, orderItems };
  });
}
```

#### 幂等性设计
```typescript
// 幂等性订单创建
async function createIdempotentOrder(idempotencyKey: string, orderData: CreateOrderData) {
  // 1. 检查是否已存在相同的幂等性键
  const existingOrder = await prisma.order.findFirst({
    where: {
      userId: orderData.userId,
      // 使用自定义字段存储幂等性键
      metadata: {
        path: ['idempotencyKey'],
        equals: idempotencyKey,
      },
    },
  });

  if (existingOrder) {
    return existingOrder; // 返回已存在的订单
  }

  // 2. 创建新订单
  return await createOrderWithTransaction({
    ...orderData,
    metadata: { idempotencyKey },
  });
}
```

### 2. 缓存策略实现

#### Redis缓存封装
```typescript
// lib/cache.ts
class CacheService {
  private redis: Redis;

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL!);
  }

  // 通用缓存方法
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    try {
      await this.redis.setex(key, ttl, JSON.stringify(value));
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  // 产品缓存
  async getProduct(productId: string) {
    return this.get(`product:${productId}`);
  }

  async setProduct(productId: string, product: any) {
    await this.set(`product:${productId}`, product, 1800); // 30分钟
  }

  // 用户会话缓存
  async getUserSession(userId: string) {
    return this.get(`session:${userId}`);
  }

  async setUserSession(userId: string, session: any) {
    await this.set(`session:${userId}`, session, 86400); // 24小时
  }

  // 验证码缓存
  async setVerificationCode(email: string, code: string) {
    await this.set(`verification:${email}`, code, 300); // 5分钟
  }

  async getVerificationCode(email: string): Promise<string | null> {
    return this.get(`verification:${email}`);
  }

  async deleteVerificationCode(email: string) {
    await this.redis.del(`verification:${email}`);
  }
}

export const cacheService = new CacheService();
```

### 3. 异步任务处理

#### 订单状态同步任务
```typescript
// lib/tasks/orderSync.ts
class OrderSyncTask {
  async syncOrderStatuses() {
    console.log('Starting order status sync...');

    try {
      // 1. 获取需要同步的订单
      const pendingOrders = await prisma.order.findMany({
        where: {
          status: {
            in: ['PENDING', 'PAID', 'PROCESSING'],
          },
          updatedAt: {
            lt: new Date(Date.now() - 5 * 60 * 1000), // 5分钟前的订单
          },
        },
        include: {
          items: true,
          odooStatuses: true,
        },
      });

      // 2. 批量查询Odoo状态
      const customerOrderRefs = pendingOrders.map(order =>
        order.items.map(item => `${order.id}-${item.variantCode}:::${item.uid}`)
      ).flat();

      if (customerOrderRefs.length === 0) {
        console.log('No orders to sync');
        return;
      }

      const odooResponse = await odooService.queryOrderStatusMultiple(customerOrderRefs);

      // 3. 更新本地状态
      if (odooResponse.result?.data) {
        await this.updateLocalOrderStatuses(odooResponse.result.data);
      }

      console.log(`Synced ${customerOrderRefs.length} order references`);
    } catch (error) {
      console.error('Order sync failed:', error);
    }
  }

  private async updateLocalOrderStatuses(odooStatuses: any[]) {
    for (const status of odooStatuses) {
      try {
        // 解析customer_order_ref
        const [orderId, variantCode, uid] = status.customer_order_ref.split(/[-:]+/);

        // 更新或创建OdooOrderStatus
        await prisma.odooOrderStatus.upsert({
          where: {
            orderId_variantCode_uid: {
              orderId,
              variantCode: variantCode || 'default',
              uid: uid || '',
            },
          },
          update: {
            status: status.state,
            description: status.description,
            deliveredQty: status.delivered_qty || 0,
            trackingNumber: status.tracking_number,
            planState: status.plan_state,
            lastCheckedAt: new Date(),
          },
          create: {
            orderId,
            variantCode: variantCode || 'default',
            uid: uid || '',
            status: status.state,
            description: status.description,
            deliveredQty: status.delivered_qty || 0,
            trackingNumber: status.tracking_number,
            planState: status.plan_state,
            lastCheckedAt: new Date(),
          },
        });

        // 根据状态更新主订单状态
        await this.updateMainOrderStatus(orderId, status.state);
      } catch (error) {
        console.error('Failed to update order status:', error);
      }
    }
  }

  private async updateMainOrderStatus(orderId: string, odooState: string) {
    let newStatus: string;

    switch (odooState) {
      case 'sale':
        newStatus = 'PROCESSING';
        break;
      case 'done':
        newStatus = 'DELIVERED';
        break;
      case 'cancel':
        newStatus = 'CANCELLED';
        break;
      default:
        return; // 不更新未知状态
    }

    await prisma.order.update({
      where: { id: orderId },
      data: { status: newStatus as any },
    });
  }
}

// 定时任务调度
export function scheduleOrderSync() {
  const task = new OrderSyncTask();

  // 每5分钟执行一次
  setInterval(() => {
    task.syncOrderStatuses();
  }, 5 * 60 * 1000);
}
```

### 4. 安全最佳实践

#### 输入验证和清理
```typescript
// lib/validation.ts
import { z } from 'zod';

// 订单创建验证模式
export const createOrderSchema = z.object({
  items: z.array(z.object({
    productId: z.string().cuid(),
    variantId: z.string().cuid().optional(),
    quantity: z.number().int().min(1).max(100),
    uid: z.string().optional(),
  })).min(1),
  shippingAddressId: z.string().cuid().optional(),
  referralCode: z.string().optional(),
});

// 产品查询验证
export const productQuerySchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  category: z.string().optional(),
  country: z.string().optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  search: z.string().max(100).optional(),
});

// 使用验证中间件
export function validateRequest<T>(schema: z.ZodSchema<T>) {
  return async (req: Request): Promise<T> => {
    try {
      const body = await req.json();
      return schema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map(e => e.message).join(', ')}`);
      }
      throw error;
    }
  };
}
```

#### 权限控制中间件
```typescript
// middleware/auth.ts
export function requireRole(roles: UserRole[]) {
  return async (req: Request) => {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      throw new Error('Authentication required');
    }

    if (!roles.includes(session.user.role)) {
      throw new Error('Insufficient permissions');
    }

    return session.user;
  };
}

// 使用示例
export async function GET(req: Request) {
  try {
    // 只允许管理员访问
    const user = await requireRole(['ADMIN'])(req);

    // 执行管理员操作
    const data = await getAdminData();
    return NextResponse.json(data);
  } catch (error) {
    return new NextResponse(error.message, { status: 403 });
  }
}
```

### 5. 性能优化技巧

#### 数据库查询优化
```typescript
// 优化的产品查询
async function getOptimizedProducts(filters: ProductFilters) {
  const whereCondition: any = {
    status: 'ACTIVE',
    off_shelve: false,
  };

  // 动态构建查询条件
  if (filters.category) {
    whereCondition.categoryId = filters.category;
  }

  if (filters.country) {
    whereCondition.OR = [
      { country: { contains: filters.country } },
      { countryCode: { contains: filters.country } },
    ];
  }

  if (filters.minPrice || filters.maxPrice) {
    whereCondition.price = {};
    if (filters.minPrice) whereCondition.price.gte = filters.minPrice;
    if (filters.maxPrice) whereCondition.price.lte = filters.maxPrice;
  }

  // 使用索引优化的查询
  return await prisma.product.findMany({
    where: whereCondition,
    select: {
      id: true,
      name: true,
      description: true,
      price: true,
      images: true,
      country: true,
      countryCode: true,
      dataSize: true,
      planType: true,
      category: {
        select: {
          id: true,
          name: true,
        },
      },
      variants: {
        select: {
          id: true,
          price: true,
          duration: true,
          durationType: true,
        },
      },
    },
    orderBy: [
      { createdAt: 'desc' },
    ],
    skip: (filters.page - 1) * filters.limit,
    take: filters.limit,
  });
}
```

## 总结

Yolloo Store 是一个功能完整、架构清晰的现代化 eSIM 电商平台。项目采用了以下关键技术和设计原则：

### 技术亮点
1. **全栈 TypeScript**: 确保类型安全和开发效率
2. **微服务架构**: Web端和移动端API分离，便于扩展
3. **企业级集成**: 与Odoo ERP和Boss服务深度集成
4. **现代化前端**: Next.js 14 + React 18 + Tailwind CSS
5. **可靠的数据层**: PostgreSQL + Prisma ORM + Redis缓存

### 业务特色
1. **多类型产品支持**: eSIM、设备、套餐等
2. **灵活的联盟营销**: 个人和组织两级结构
3. **完整的订单流程**: 从购物车到交付的全链路管理
4. **实时状态同步**: 与外部系统的双向数据同步
5. **移动端优化**: 专门的移动端API和优化

### 扩展性设计
1. **模块化架构**: 各业务模块相对独立
2. **配置化集成**: 支持多环境和多实例配置
3. **标准化接口**: RESTful API设计
4. **容器化部署**: Docker支持和多环境配置
5. **监控和日志**: 完整的错误处理和日志记录

这个项目为eSIM行业提供了一个完整的电商解决方案，具有很强的实用性和扩展性。
```
