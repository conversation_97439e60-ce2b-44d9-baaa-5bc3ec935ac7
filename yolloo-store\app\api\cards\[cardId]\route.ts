import { getServerSession } from "next-auth"
import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { DateFormatter } from "@/lib/utils"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function GET(
  request: Request,
  { params }: { params: { cardId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const card = await prisma.yollooCard.findFirst({
      where: {
        AND: [
          { id: params.cardId },
          { userId: session.user.id }
        ]
      },
      include: {
        esims: {
          include: {
            product: true,
            profile: true,
            plan: true
          }
        }
      }
    })

    if (!card) {
      return new NextResponse("Card not found", { status: 404 })
    }

    // Enhance the card data with simulated real-time information
    const enhancedCard = {
      ...card,
      dataUsage: null, // Temporarily display N/A instead of dummy data
      networkStatus: {
        signal: Math.floor(Math.random() * 100), // Random signal strength
        lastConnected: card.status === "Active" ? DateFormatter.iso(new Date()) : undefined
      },
      location: {
        country: "United States",
        region: "California"
      }
    }

    return NextResponse.json(enhancedCard)
  } catch (error) {
    console.error('[CARD_GET]', error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

// Delete (unbind) card
export async function DELETE(
  request: Request,
  { params }: { params: { cardId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const card = await prisma.yollooCard.findFirst({
      where: {
        AND: [
          { id: params.cardId },
          { userId: session.user.id }
        ]
      }
    })

    if (!card) {
      return new NextResponse("Card not found", { status: 404 })
    }

    // Update the card to unbind it from the user
    await prisma.yollooCard.update({
      where: {
        id: card.id
      },
      data: {
        userId: null,
        status: "Available"
      }
    })

    return new NextResponse(null, { status: 200 })
  } catch (error) {
    console.error('[CARD_DELETE]', error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

// Update card details
export async function PATCH(
  request: Request,
  { params }: { params: { cardId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const json = await request.json()
    const { customName } = json

    // Find the card to verify ownership
    const card = await prisma.yollooCard.findFirst({
      where: {
        AND: [
          { id: params.cardId },
          { userId: session.user.id }
        ]
      }
    })

    if (!card) {
      return new NextResponse("Card not found", { status: 404 })
    }

    // Update the card's custom name
    const updatedCard = await prisma.yollooCard.update({
      where: {
        id: card.id
      },
      data: {
        customName: customName
      }
    })

    return NextResponse.json(updatedCard)
  } catch (error) {
    console.error('[CARD_PATCH]', error)
    return new NextResponse("Internal error", { status: 500 })
  }
}