import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function GET() {
  try {
    // Find products with names containing "lite", "plus", and "max" and category name "esim-card"
    const products = await prisma.product.findMany({
      where: {
        OR: [
          { name: { contains: "lite", mode: "insensitive" } },
          { name: { contains: "plus", mode: "insensitive" } },
          { name: { contains: "max", mode: "insensitive" } },
        ],
        status: "ACTIVE",
        off_shelve: false,
        category: {
          name: "esim-card"
        },
      },
      select: {
        id: true,
        name: true,
      },
    })

    // Create a map of product links for esim-card products
    const productLinks = {
      lite: `/products/${products.find(p => p.name.toLowerCase().includes("lite"))?.id || ""}`,
      plus: `/products/${products.find(p => p.name.toLowerCase().includes("plus"))?.id || ""}`,
      max: `/products/${products.find(p => p.name.toLowerCase().includes("max"))?.id || ""}`,
    }

    return NextResponse.json(productLinks)
  } catch (error) {
    console.error("Error generating product links:", error)
    return NextResponse.json(
      { error: "Failed to generate product links" },
      { status: 500 }
    )
  }
}