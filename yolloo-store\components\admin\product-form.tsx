"use client"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Icons } from "@/components/icons"
import { toast } from "sonner"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { CategoryDialog } from "@/components/admin/category-dialog"

interface ProductVariant {
  id?: string
  price: number
  currency?: string
  productId?: string
  variantCode?: string | null
  duration?: number | null
  durationType?: string | null
  attributes: Record<string, any>
  createdAt?: Date
  updatedAt?: Date
}

interface ProductFormProps {
  initialData?: {
    id: string
    name: string
    description: string
    websiteDescription?: string
    price: number
    images: string[]
    categoryId: string
    stock: number
    sku: string
    specifications: Record<string, any>
    mcc?: string | null
    requiredUID: boolean
    off_shelve: boolean
    variants?: ProductVariant[]
    dataSize?: number | null
    planType?: string | null
    country?: string | null
    countryCode?: string | null
    status?: string
  }
  categories: {
    id: string
    name: string
  }[]
}

export function ProductForm({ initialData, categories: initialCategories }: ProductFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [images, setImages] = useState<string[]>(initialData?.images || [])
  const [requiredUID, setRequiredUID] = useState(initialData?.requiredUID || false)
  const [offShelve, setOffShelve] = useState(initialData?.off_shelve || false)
  const [variants, setVariants] = useState<ProductVariant[]>(initialData?.variants || [])
  const [showCategoryDialog, setShowCategoryDialog] = useState(false)
  const [activeTab, setActiveTab] = useState("basic")
  const [categories, setCategories] = useState(initialCategories)
  const [specifications, setSpecifications] = useState<Record<string, string>>(
    initialData?.specifications as Record<string, string> || {}
  )
  const [productStatus, setProductStatus] = useState(initialData?.status || "ACTIVE")
  const [newVariantIndex, setNewVariantIndex] = useState<number | null>(null)
  const variantEndRef = useRef<HTMLTableRowElement | null>(null)
  const variantsContainerRef = useRef<HTMLDivElement | null>(null)
  const [variantErrors, setVariantErrors] = useState<Record<number, string[]>>({})

  // eSIM 套餐的数量和单位选项
  const ESIM_QUANTITIES = ["1", "3", "5", "7", "10", "15", "30", "60", "90"]
  const ESIM_UNITS = ["Day", "Month"]
  const PLAN_TYPES = ["Daily", "Total"]
  const PRODUCT_STATUSES = ["ACTIVE", "INACTIVE", "OUT_OF_STOCK", "DELETED"]

  // Handle back button click to return to products list
  const handleBackClick = () => {
    router.push("/admin/products")
  }

  // Format MCC value for display - convert to comma-separated format
  const formatMccValue = (mcc: string | null | undefined) => {
    if (!mcc) return ""
    return mcc.split(/[,;]/).map(code => code.trim()).filter(Boolean).join(", ")
  }

  // Parse MCC input to handle both comma and semicolon separators
  const parseMccValue = (mcc: string | null) => {
    if (!mcc) return null
    return mcc.split(/[,;]/).map(code => code.trim()).filter(Boolean).join(";")
  }

  // Format country value for display - convert to comma-separated format
  const formatCountryValue = (country: string | null | undefined) => {
    if (!country) return ""
    return country.split(/[,;]/).map(code => code.trim()).filter(Boolean).join(", ")
  }

  // Parse country input to handle both comma and semicolon separators
  const parseCountryValue = (country: string | null) => {
    if (!country) return null
    return country.split(/[,;]/).map(code => code.trim()).filter(Boolean).join(";")
  }

  // Add form state variables to preserve data across tab changes
  const [name, setName] = useState(initialData?.name || "")
  const [sku, setSku] = useState(initialData?.sku || "")
  const [description, setDescription] = useState(initialData?.description || "")
  const [websiteDescription, setWebsiteDescription] = useState(initialData?.websiteDescription || "")
  const [price, setPrice] = useState(initialData?.price?.toString() || "0")
  const [stock, setStock] = useState(initialData?.stock?.toString() || "0")
  const [categoryId, setCategoryId] = useState(initialData?.categoryId || "")
  const [dataSize, setDataSize] = useState(initialData?.dataSize?.toString() || "")
  const [planType, setPlanType] = useState(initialData?.planType || "none")
  const [country, setCountry] = useState(formatCountryValue(initialData?.country) || "")
  const [countryCode, setCountryCode] = useState(formatCountryValue(initialData?.countryCode) || "")
  const [mcc, setMcc] = useState(formatMccValue(initialData?.mcc) || "")

  const handleAddVariant = () => {
    const newIndex = variants.length;
    setVariants([
      ...variants,
      {
        price: 0,
        currency: "USD",
        attributes: {},
        variantCode: "",
        duration: null,
        durationType: null
      },
    ])
    setNewVariantIndex(newIndex);
    
    // 设置一个短暂的延迟，确保DOM已更新
    setTimeout(() => {
      if (variantEndRef.current) {
        variantEndRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 100);
  }

  // 当新变体添加后，重置高亮状态
  useEffect(() => {
    if (newVariantIndex !== null) {
      const timer = setTimeout(() => {
        setNewVariantIndex(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [newVariantIndex]);

  const handleRemoveVariant = (index: number) => {
    setVariants(variants.filter((_, i) => i !== index))
  }

  const handleVariantChange = (index: number, field: string, value: string | number) => {
    setVariants(prevVariants => {
      const newVariants = [...prevVariants];
      
      // Make sure the variant at this index exists
      if (!newVariants[index]) return newVariants;
      
      // Handle attribute fields
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        
        // Ensure we have a valid variant at this index with attributes
        const variant = newVariants[index]!;
        
        // Initialize attributes if it doesn't exist
        if (!variant.attributes) {
          variant.attributes = {};
        }
        
        variant.attributes = {
          ...variant.attributes,
          [child as string]: value
        };
        
        // Update duration fields based on attributes for backward compatibility
        if (child === "billing_period") {
          variant.duration = typeof value === 'string' ? parseInt(value, 10) : value as number;
        } else if (child === "billing_period_unit") {
          variant.durationType = value === "Day" ? "day" : "month";
        }
      } else {
        // Update direct fields
        newVariants[index] = {
          ...newVariants[index]!,
          [field]: value
        } as ProductVariant;
      }
      
      return newVariants;
    });
  }

  const handleAddSpecification = () => {
    const key = prompt("Enter specification key")
    const value = prompt("Enter specification value")
    
    if (key && value) {
      setSpecifications({
        ...specifications,
        [key]: value
      })
    }
  }

  const handleRemoveSpecification = (key: string) => {
    const newSpecs = { ...specifications }
    delete newSpecs[key]
    setSpecifications(newSpecs)
  }

  // 添加新类别后刷新类别列表
  const handleCategorySuccess = async () => {
    try {
      const response = await fetch("/api/admin/categories");
      if (response.ok) {
        const newCategories = await response.json();
        setCategories(newCategories);
      }
    } catch (error) {
      console.error("Failed to refresh categories:", error);
    }
  };

  // 验证变体数据
  const validateVariants = () => {
    const errors: Record<number, string[]> = {};
    let hasErrors = false;
    
    variants.forEach((variant, index) => {
      const variantErrors: string[] = [];
      
      if (variant.price <= 0) {
        variantErrors.push("Price must be greater than 0");
      }
      
      if (variant.duration === null || variant.duration <= 0) {
        variantErrors.push("Duration must be specified and greater than 0");
      }
      
      if (!variant.durationType) {
        variantErrors.push("Duration type must be specified");
      }
      
      if (!variant.variantCode) {
        variantErrors.push("Variant code must be specified");
      }
      
      if (variantErrors.length > 0) {
        errors[index] = variantErrors;
        hasErrors = true;
      }
    });
    
    setVariantErrors(errors);
    return !hasErrors;
  };

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setIsLoading(true)

    // 如果有变体，验证变体数据
    if (variants.length > 0) {
      const isValid = validateVariants();
      if (!isValid) {
        setActiveTab("variants");
        setIsLoading(false);
        
        // 滚动到第一个有错误的变体
        const firstErrorIndex = Object.keys(variantErrors)[0];
        if (firstErrorIndex) {
          setTimeout(() => {
            if (variantsContainerRef.current) {
              variantsContainerRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
          }, 100);
        }
        
        toast.error("Please fix the errors in variants before saving");
        return;
      }
    }

    try {
      // Use state variables instead of form data
      const data = {
        name,
        description,
        websiteDescription,
        price: parseFloat(price),
        images,
        categoryId,
        stock: parseInt(stock),
        sku,
        specifications,
        mcc: !mcc || mcc.trim() === "" ? null : parseMccValue(mcc),
        requiredUID,
        off_shelve: offShelve,
        status: productStatus,
        
        // Additional fields - explicitly handle empty strings as null
        dataSize: !dataSize || dataSize.trim() === "" ? null : parseFloat(dataSize),
        planType: planType === "none" || !planType || planType.trim() === "" ? null : planType,
        country: !country || country.trim() === "" ? null : parseCountryValue(country),
        countryCode: !countryCode || countryCode.trim() === "" ? null : parseCountryValue(countryCode),
        
        // Variants
        variants: variants.map(variant => ({
          ...variant,
          currency: "USD",
          price: typeof variant.price === 'string' ? parseFloat(variant.price as string) : variant.price,
          duration: variant.duration,
          durationType: variant.durationType
        }))
      }

      if (initialData) {
        // Update existing product
        const response = await fetch(`/api/admin/products/${initialData.id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        })

        if (!response.ok) {
          throw new Error("Failed to update product")
        }

        // Show a more detailed success message
        toast.success("Product updated successfully. You can continue editing or go back to the products list.", {
          duration: 5000,
          action: {
            label: "Back",
            onClick: () => router.push("/admin/products")
          }
        })
        
        // Stay on the current page after update
        router.refresh()
        // Don't navigate back to products list
      } else {
        // Create new product
        const response = await fetch("/api/admin/products", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        })

        if (!response.ok) {
          throw new Error("Failed to create product")
        }

        toast.success("Product created successfully")
        router.refresh()
        router.push("/admin/products")
      }
    } catch (error) {
      console.error(error)
      toast.error("Something went wrong")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={onSubmit} className="space-y-8">
      <div className="flex items-center justify-between mb-6">
        <Button 
          type="button" 
          variant="outline" 
          onClick={handleBackClick}
          className="flex items-center gap-2"
        >
          <Icons.chevronLeft className="h-4 w-4" />
          Back to Products
        </Button>
        <h2 className="text-xl font-semibold">
          {initialData ? "Edit Product" : "Create New Product"}
        </h2>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="details">Product Details</TabsTrigger>
          <TabsTrigger value="variants">Variants</TabsTrigger>
          <TabsTrigger value="specifications">Specifications</TabsTrigger>
          <TabsTrigger value="images">Images</TabsTrigger>
        </TabsList>
        
        {/* Basic Info Tab */}
        <TabsContent value="basic" className="space-y-6 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Enter the essential information about your product
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
                  <Label htmlFor="name">Product Name</Label>
            <Input
              id="name"
              name="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              placeholder="Enter product name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="sku">SKU</Label>
            <Input
              id="sku"
              name="sku"
              value={sku}
              onChange={(e) => setSku(e.target.value)}
              required
              placeholder="Enter unique SKU"
            />
          </div>
        </div>
              
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter product description"
            className="min-h-[100px]"
          />
        </div>
              
        <div className="space-y-2">
          <Label htmlFor="websiteDescription">Website Description</Label>
          <Textarea
            id="websiteDescription"
            name="websiteDescription"
            value={websiteDescription}
            onChange={(e) => setWebsiteDescription(e.target.value)}
            placeholder="Enter detailed description for website display"
            className="min-h-[100px]"
          />
          <p className="text-xs text-muted-foreground">
            This description will be displayed on the product page
          </p>
        </div>
              
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
                  <Label htmlFor="price">Base Price (USD)</Label>
            <Input
              id="price"
              name="price"
              type="number"
              step="0.01"
              min="0"
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="stock">Stock</Label>
            <Input
              id="stock"
              name="stock"
              type="number"
              min="0"
              value={stock}
              onChange={(e) => setStock(e.target.value)}
              required
            />
          </div>
        </div>
              
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="categoryId">Category</Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowCategoryDialog(true)}
            >
              <Icons.add className="mr-2 h-4 w-4" />
              Add Category
            </Button>
          </div>
                <Select 
                  name="categoryId" 
                  value={categoryId}
                  onValueChange={(value) => setCategoryId(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
            {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status">Product Status</Label>
                <Select 
                  value={productStatus} 
                  onValueChange={setProductStatus}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {PRODUCT_STATUSES.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="requiredUID"
                      checked={requiredUID}
                      onCheckedChange={setRequiredUID}
                    />
                    <Label htmlFor="requiredUID">Require UID</Label>
                  </div>
                  <p className="text-xs text-muted-foreground pl-14">
                    Enable if this product requires a Yolloo Card UID for purchase
                  </p>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="off_shelve"
                      checked={offShelve}
                      onCheckedChange={setOffShelve}
                    />
                    <Label htmlFor="off_shelve">Off Shelf</Label>
                  </div>
                  <p className="text-xs text-muted-foreground pl-14">
                    Product will not be visible to customers when off shelf
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Product Details Tab */}
        <TabsContent value="details" className="space-y-6 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Product Details</CardTitle>
              <CardDescription>
                Additional information specific to eSIM products
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="dataSize">Data Size (MB)</Label>
                  <Input
                    id="dataSize"
                    name="dataSize"
                    type="number"
                    step="0.01"
                    min="0"
                    value={dataSize}
                    onChange={(e) => setDataSize(e.target.value)}
                    placeholder="Enter data size in MB"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="planType">Plan Type</Label>
                  <Select 
                    name="planType" 
                    value={planType}
                    onValueChange={(value) => setPlanType(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select plan type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {PLAN_TYPES.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="country">Countries</Label>
                <Input
                  id="country"
                  name="country"
                  value={country}
                  onChange={(e) => setCountry(e.target.value)}
                  placeholder="Enter countries separated by commas"
                />
                <p className="text-xs text-muted-foreground">
                  Countries where this product is available
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="countryCode">Country Codes</Label>
                <Input
                  id="countryCode"
                  name="countryCode"
                  value={countryCode}
                  onChange={(e) => setCountryCode(e.target.value)}
                  placeholder="Enter country codes separated by commas"
                />
                <p className="text-xs text-muted-foreground">
                  ISO country codes (e.g., US, JP, CN)
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="mcc">MCC (Mobile Country Code)</Label>
                <Input
                  id="mcc"
                  name="mcc"
                  placeholder="e.g., 440, 441; 442"
                  value={mcc}
                  onChange={(e) => setMcc(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Enter MCCs separated by commas or semicolons
                </p>
        </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Variants Tab */}
        <TabsContent value="variants" className="space-y-6 pt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div>
                <CardTitle>Product Variants</CardTitle>
                <CardDescription>
                  Manage different variants of this product (e.g., different durations)
                </CardDescription>
              </div>
              <Button
                type="button"
                variant="default"
                onClick={handleAddVariant}
                className="shrink-0"
              >
                <Icons.add className="mr-2 h-4 w-4" />
                Add Variant
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4" ref={variantsContainerRef}>
                {variants.length === 0 && (
                  <div className="flex flex-col items-center justify-center py-12 text-center border-2 border-dashed rounded-lg">
                    <Icons.variantIcon className="h-12 w-12 text-muted-foreground/50 mb-4" />
                    <h3 className="text-lg font-medium">No variants added yet</h3>
                    <p className="text-sm text-muted-foreground mt-1 mb-4">Create variants for different durations or pricing options</p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleAddVariant}
                    >
                      <Icons.add className="mr-2 h-4 w-4" />
                      Add Your First Variant
                    </Button>
                  </div>
                )}
                
                {variants.length > 0 && (
                  <div className="overflow-hidden border rounded-md">
                    <table className="w-full">
                      <thead>
                        <tr className="bg-muted/50">
                          <th className="text-left p-3 font-medium text-sm">Variant</th>
                          <th className="text-left p-3 font-medium text-sm">Duration</th>
                          <th className="text-left p-3 font-medium text-sm">Price (USD)</th>
                          <th className="text-left p-3 font-medium text-sm">Variant Code</th>
                          <th className="text-right p-3 font-medium text-sm">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y">
                        {variants.map((variant, index) => {
                          const hasErrors = variantErrors[index] && variantErrors[index].length > 0;
                          return (
                            <tr 
                              key={index} 
                              className={`hover:bg-muted/20 transition-colors ${newVariantIndex === index ? 'bg-primary/5 animate-pulse' : ''} ${hasErrors ? 'bg-destructive/5 border-l-4 border-destructive' : ''}`}
                              ref={newVariantIndex === index ? variantEndRef : null}
                            >
                              <td className="p-3">
                                <div className="font-medium">Variant {index + 1}</div>
                              </td>
                              <td className="p-3">
                                <div className="flex items-center gap-2">
                                  <Input
                                    type="number"
                                    min="0"
                                    value={variant.duration || ""}
                                    onChange={(e) => handleVariantChange(index, "duration", parseInt(e.target.value, 10) || 0)}
                                    placeholder="Duration"
                                    className={`w-20 ${variantErrors[index]?.includes("Duration must be specified and greater than 0") ? 'border-destructive' : ''}`}
                                  />
                                  <Select
                                    value={variant.durationType === "day" ? "Day" : variant.durationType === "month" ? "Month" : ""}
                                    onValueChange={(value) => handleVariantChange(index, "durationType", value === "Day" ? "day" : "month")}
                                  >
                                    <SelectTrigger className={`w-24 ${variantErrors[index]?.includes("Duration type must be specified") ? 'border-destructive' : ''}`}>
                                      <SelectValue placeholder="Unit" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {ESIM_UNITS.map((unit) => (
                                        <SelectItem key={unit} value={unit}>
                                          {unit}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </td>
                              <td className="p-3">
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  value={variant.price}
                                  onChange={(e) => handleVariantChange(index, "price", parseFloat(e.target.value) || 0)}
                                  className={`w-28 ${variantErrors[index]?.includes("Price must be greater than 0") ? 'border-destructive' : ''}`}
                                />
                              </td>
                              <td className="p-3">
                                <Input
                                  value={variant.variantCode || ""}
                                  onChange={(e) => handleVariantChange(index, "variantCode", e.target.value)}
                                  placeholder="Code"
                                  className={`w-full ${variantErrors[index]?.includes("Variant code must be specified") ? 'border-destructive' : ''}`}
                                />
                              </td>
                              <td className="p-3 text-right">
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemoveVariant(index)}
                                  className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                                >
                                  <Icons.trash className="h-4 w-4" />
                                  <span className="sr-only">Remove</span>
                                </Button>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                )}
                
                <div ref={variants.length > 0 ? variantsContainerRef : undefined}></div>
                
                {variants.length > 0 && (
                  <div className="flex justify-end mt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleAddVariant}
                      className="text-sm"
                    >
                      <Icons.add className="mr-2 h-3 w-3" />
                      Add Another Variant
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Specifications Tab */}
        <TabsContent value="specifications" className="space-y-6 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Product Specifications</CardTitle>
              <CardDescription>
                Add technical specifications and additional details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-end">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleAddSpecification}
                >
                  <Icons.add className="mr-2 h-4 w-4" />
                  Add Specification
                </Button>
        </div>

        <div className="space-y-4">
                {Object.keys(specifications).length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No specifications added yet. Click "Add Specification" to create one.
          </div>
                )}
                
                {Object.entries(specifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-4 border rounded-md">
                    <div className="space-y-1">
                      <p className="font-medium">{key}</p>
                      <p className="text-sm text-muted-foreground">{value}</p>
        </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveSpecification(key)}
                    >
                      <Icons.trash className="h-4 w-4" />
                    </Button>
          </div>
                ))}
        </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Images Tab */}
        <TabsContent value="images" className="space-y-6 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Product Images</CardTitle>
              <CardDescription>
                Add and manage product images
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            {images.map((image, index) => (
              <div key={index} className="relative aspect-square">
                <img
                  src={image}
                  alt={`Product image ${index + 1}`}
                      className="rounded-lg object-cover w-full h-full"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  className="absolute right-2 top-2"
                  onClick={() => {
                    setImages(images.filter((_, i) => i !== index))
                  }}
                >
                  <Icons.close className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <div className="flex aspect-square items-center justify-center rounded-lg border border-dashed">
              <Button
                type="button"
                variant="ghost"
                className="h-full w-full"
                onClick={() => {
                  const url = window.prompt("Enter image URL")
                  if (url) {
                    setImages([...images, url])
                  }
                }}
              >
                <Icons.add className="h-4 w-4" />
                    <span className="ml-2">Add Image</span>
              </Button>
            </div>
          </div>
              
              <div className="text-sm text-muted-foreground">
                <p>Tips for product images:</p>
                <ul className="list-disc pl-5 mt-2">
                  <li>Use high-quality images with clear backgrounds</li>
                  <li>Maintain consistent aspect ratios (preferably square)</li>
                  <li>First image will be used as the main product image</li>
                </ul>
        </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <div className="flex justify-end">
        <Button type="submit" disabled={isLoading} size="lg">
        {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
        {initialData ? "Update" : "Create"} Product
      </Button>
      </div>

      <CategoryDialog 
        open={showCategoryDialog}
        onOpenChange={setShowCategoryDialog}
        onSuccess={handleCategorySuccess}
      />
    </form>
  )
} 