import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Icons } from "@/components/icons";
import { ShoppingCart } from "lucide-react";
import { EsimBuyNowForm } from "@/components/esims/EsimBuyNowForm";
import { FormattedDescription } from "@/components/formatted-description";

interface Props {
  params: { productId: string };
}

export default async function EsimDetailPage({ params }: Props) {
  const product = await prisma.product.findUnique({
    where: { id: params.productId },
    select: {
      id: true,
      name: true,
      description: true,
      price: true,
      images: true,
      stock: true,
      status: true,
      country: true,
      countryCode: true,
      planType: true,
      dataSize: true,
      sku: true,
      variants: {
        select: {
          id: true,
          price: true,
          duration: true,
          durationType: true,
        }
      },
    },
  });

  if (!product) return notFound();

  return (
    <main className="max-w-3xl mx-auto px-4 py-10">
      <div className="rounded-3xl overflow-hidden shadow-xl bg-gradient-to-br from-white via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 border-0">
        {/* 顶部大图 */}
        <div className="relative w-full aspect-[16/7] bg-gradient-to-tr from-blue-100 to-violet-100 dark:from-gray-800 dark:to-gray-900 flex items-center justify-center">
          {product.images && product.images.length > 0 ? (
            <img
              src={product.images[0]}
              alt={product.name}
              className="object-cover w-full h-full transition-transform duration-300 hover:scale-105"
            />
          ) : (
            <Icons.image className="h-24 w-24 text-blue-200 dark:text-gray-700" />
          )}
          {/* 角标 */}
          {product.stock <= 10 && (
            <span className="absolute top-4 right-4 bg-gradient-to-r from-pink-500 to-yellow-400 text-white text-xs px-3 py-1 rounded-full shadow">
              Low Stock
            </span>
          )}
        </div>
        {/* 信息区块 */}
        <div className="p-8 flex flex-col gap-6">
          <h1 className="text-3xl font-extrabold mb-1 bg-gradient-to-r from-blue-500 to-violet-500 bg-clip-text text-transparent">
            {product.name}
          </h1>
          <div className="flex flex-wrap gap-3 items-center mb-2">
            {product.country && (
              <Badge variant="outline" className="text-base px-4 py-2 bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-400 dark:border-blue-800">
                {product.country}
              </Badge>
            )}
            {product.planType && (
              <Badge variant="outline" className="text-base px-4 py-2 bg-violet-50 text-violet-700 border-violet-200 dark:bg-violet-950 dark:text-violet-400 dark:border-violet-800">
                {product.planType}
              </Badge>
            )}
            {product.dataSize && (
              <Badge variant="outline" className="text-base px-4 py-2 bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800">
                {product.dataSize >= 1024 ? `${(product.dataSize / 1024).toFixed(2)} GB` : `${product.dataSize} MB`}
              </Badge>
            )}
            <Badge variant="outline" className="text-base px-4 py-2 bg-primary/10 text-primary border-primary/20">
              {product.stock > 0 ? `In Stock: ${product.stock}` : "Out of Stock"}
            </Badge>
          </div>
          <div className="text-4xl font-extrabold bg-gradient-to-r from-blue-500 to-violet-500 bg-clip-text text-transparent mb-2">
            ${product.price.toFixed(2)}
          </div>
          {/* Buy Now 表单 */}
          <EsimBuyNowForm
            product={{ id: product.id, stock: product.stock }}
            variants={product.variants && Array.isArray(product.variants)
              ? product.variants.map(v => ({
                  id: v.id,
                  price: typeof v.price === 'number' ? v.price : Number(v.price),
                  duration: v.duration,
                  durationType: v.durationType,
                }))
              : []}
          />
          <hr className="my-6 border-blue-100 dark:border-gray-800" />
          <div className="prose prose-lg dark:prose-invert text-muted-foreground mb-2 max-w-none">
            <FormattedDescription text={product.description || "No description available."} />
          </div>
        </div>
      </div>
    </main>
  );
}