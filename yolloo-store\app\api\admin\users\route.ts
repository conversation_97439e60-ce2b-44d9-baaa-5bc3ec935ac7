import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";
import { z } from "zod";
import bcrypt from "bcryptjs";

// 添加动态路由配置
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// Schema for validating new user input
const userCreateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  role: z.enum(["ADMIN", "CUSTOMER", "STAFF"]).default("CUSTOMER"),
});

// GET /api/admin/users - 管理员获取所有用户
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const users = await prisma.user.findMany({
      include: {
        affiliate: {
          include: {
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        accounts: {
          select: {
            provider: true,
          },
        },
        loginHistory: {
          orderBy: {
            loginTimestamp: 'desc'
          },
          take: 1,
          select: {
            loginTimestamp: true,
            loginMethod: true,
            ipAddress: true,
            userAgent: true
          }
        }
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Map the users to include the lastLoginTime derived from sessions
    const mappedUsers = users.map(user => {
      const lastLoginInfo = user.loginHistory.length > 0 ? user.loginHistory[0] : null;
      
      return {
        ...user,
        lastLoginTime: lastLoginInfo?.loginTimestamp || null,
        lastLoginMethod: lastLoginInfo?.loginMethod || null,
        lastLoginIp: lastLoginInfo?.ipAddress || null,
        loginHistory: undefined  // Remove the loginHistory array from the response
      };
    });

    return NextResponse.json({ users: mappedUsers });
  } catch (error) {
    console.error("[USERS_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/admin/users - 创建新用户
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    
    // Validate input data
    const validationResult = userCreateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Validation failed", errors: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { name, email, password, role } = validationResult.data;
    
    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });
    
    if (existingUser) {
      return NextResponse.json(
        { message: "User with this email already exists" },
        { status: 400 }
      );
    }
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create the new user
    const user = await prisma.user.create({
      data: {
        name,
        email,
        hashedPassword,
        role,
      },
    });
    
    // Remove sensitive data before returning
    const { hashedPassword: _, ...userWithoutPassword } = user;
    
    return NextResponse.json(userWithoutPassword, { status: 201 });
  } catch (error) {
    console.error("[USERS_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
} 