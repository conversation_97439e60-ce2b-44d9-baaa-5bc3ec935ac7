import { NextResponse } from 'next/server';
import { createOdooService } from '../../../services/odooService';
import { ODOO_CONFIG } from '../../../config/odoo';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


const odooService = createOdooService(ODOO_CONFIG);

export async function GET() {
    try {
        const response = await odooService.pullProducts();
        return NextResponse.json(response);
    } catch (error) {
        console.error('Error fetching products from Odoo:', error);
        return NextResponse.json(
            { 
                code: 500,
                msg: 'Failed to fetch products',
                data: null
            },
            { status: 500 }
        );
    }
} 