import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

export async function POST(req: Request) {
  try {
    // 验证管理员权限
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // 获取所有产品ID
    const products = await prisma.product.findMany({
      include: {
        category: true,
        variants: true,
        parameters: true,
      }
    });

    if (products.length === 0) {
      return NextResponse.json({
        message: "No products found to delete",
        deletedCount: 0,
        skippedCount: 0
      });
    }



    // 我们允许删除所有产品，包括有eSIM关联的产品
    const deleteableProducts = products;
    const cannotDeleteProducts: typeof products = [];

    console.log(`[DELETE_ALL_PRODUCTS] Products to delete: ${deleteableProducts.length}`);

    let deletedCount = 0;

    // 处理可以删除的产品
    for (const product of deleteableProducts) {
      try {
        // 使用事务来确保数据一致性
        await prisma.$transaction(async (tx) => {
          // 1. 检查产品是否有关联订单（通过productCode）
          const orderItems = await tx.orderItem.findMany({
            where: {
              productCode: product.sku
            },
            include: {
              order: true
            }
          });

          // 2. 如果有关联订单，直接保留OrderItem的productCode
          // 这样当产品再次创建时，订单可以重新关联到商品
          if (orderItems.length > 0) {
            console.log(`[DELETE_ALL_PRODUCTS] Product ${product.id} has ${orderItems.length} associated order items, preserving productCode`);
          }

          // 3. 删除所有关联的CartItem
          await tx.cartItem.deleteMany({
            where: {
              productId: product.id,
            },
          });

          // 4. 删除所有关联的WishlistItem
          await tx.wishlistItem.deleteMany({
            where: {
              productId: product.id,
            },
          });

          // 5. 删除所有关联的Review
          await tx.review.deleteMany({
            where: {
              productId: product.id,
            },
          });

          // 6. 删除所有关联的ProductParameter
          await tx.productParameter.deleteMany({
            where: {
              productId: product.id,
            },
          });

          // 7. 删除所有关联的ProductVariant
          await tx.productVariant.deleteMany({
            where: {
              productId: product.id,
            },
          });



          // 9. 检查是否有关联的Esim
          const esims = await tx.esim.findMany({
            where: {
              productId: product.id
            }
          });

          if (esims.length > 0) {
            console.log(`[DELETE_ALL_PRODUCTS] Product ${product.id} has ${esims.length} associated eSIMs`);

            // 创建一个新的产品记录，用于替换eSIM中的productId引用
            const placeholderProduct = await tx.product.create({
              data: {
                name: product.name + " (Placeholder for eSIM)",
                description: product.description,
                websiteDescription: product.websiteDescription,
                price: product.price,
                images: product.images,
                categoryId: product.categoryId,
                stock: 0,
                sku: product.sku + "_placeholder_esim_" + Date.now() + "_" + Math.floor(Math.random() * 10000),
                status: "DELETED",
                mcc: product.mcc,
                dataSize: product.dataSize,
                planType: product.planType,
                country: product.country,
                countryCode: product.countryCode,
              }
            });

            // 更新所有关联的eSIM，指向新的占位产品
            await tx.esim.updateMany({
              where: {
                productId: product.id
              },
              data: {
                productId: placeholderProduct.id
              }
            });
          }

          // 10. 删除产品
          await tx.product.delete({
            where: {
              id: product.id,
            },
          });
        });

        deletedCount++;
        console.log(`[DELETE_ALL_PRODUCTS] Successfully deleted product: ${product.name} (${product.id})`);
      } catch (error) {
        console.error(`[DELETE_ALL_PRODUCTS] Error deleting product ${product.id}:`, error);
        // 继续处理其他产品
      }
    }

    return NextResponse.json({
      message: "Products deletion completed",
      totalProducts: products.length,
      deletedCount: deletedCount,
      skippedCount: cannotDeleteProducts.length,
      skippedProducts: cannotDeleteProducts.map(p => ({ id: p.id, name: p.name }))
    });

  } catch (error) {
    console.error("[DELETE_ALL_PRODUCTS]", error);

    if (error instanceof Error) {
      return new NextResponse(`Error deleting products: ${error.message}`, { status: 500 });
    }

    return new NextResponse("Internal error", { status: 500 });
  }
}
