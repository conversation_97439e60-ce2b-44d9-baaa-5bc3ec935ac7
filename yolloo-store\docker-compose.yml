services:

  # 主应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yolloo-app
    ports:
      - "8000:8000"
    env_file:
      - .env
    restart: always
    networks:
      - my-network
    extra_hosts:
      - "openapi.vcs3-distribution.testing:***********"
      - "boss-server.vcs3-distribution.testing:***********"
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - mobile-api
      - my-postgres

  # 移动 API 服务
  mobile-api:
    build:
      context: .
      dockerfile: mobile-api/Dockerfile
    container_name: yolloo-mobile-api
    ports:
      - "4000:4000"
    env_file:
      - .env
    restart: always
    networks:
      - my-network
    extra_hosts:
      - "openapi.vcs3-distribution.testing:***********"
      - "boss-server.vcs3-distribution.testing:***********"
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:4000/api/mobile/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - my-postgres

networks:
  my-network:
    external: true