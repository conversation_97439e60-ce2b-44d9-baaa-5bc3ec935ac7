const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkOrderData() {
  try {
    console.log('=== 检查订单数据 ===\n');

    // 1. 检查订单项目总数
    const totalItems = await prisma.orderItem.count();
    console.log(`总订单项目数: ${totalItems}`);

    // 2. 检查有 variantText 的项目数
    const itemsWithVariantText = await prisma.orderItem.count({
      where: {
        variantText: {
          not: null,
          not: ''
        }
      }
    });
    console.log(`有 variantText 的项目数: ${itemsWithVariantText}`);

    // 3. 检查有 productCode 的项目数
    const itemsWithProductCode = await prisma.orderItem.count({
      where: {
        productCode: {
          not: null,
          not: ''
        }
      }
    });
    console.log(`有 productCode 的项目数: ${itemsWithProductCode}`);

    // 4. 查看最近的几个订单项目
    console.log('\n=== 最近的订单项目数据 ===');
    const recentItems = await prisma.orderItem.findMany({
      take: 5,
      orderBy: {
        order: {
          createdAt: 'desc'
        }
      },
      include: {
        order: {
          select: {
            id: true,
            createdAt: true,
            status: true
          }
        }
      }
    });

    recentItems.forEach((item, index) => {
      console.log(`\n项目 ${index + 1}:`);
      console.log(`  ID: ${item.id}`);
      console.log(`  订单ID: ${item.orderId}`);
      console.log(`  订单创建时间: ${item.order.createdAt}`);
      console.log(`  订单状态: ${item.order.status}`);
      console.log(`  productCode: ${item.productCode || 'NULL'}`);
      console.log(`  variantCode: ${item.variantCode || 'NULL'}`);
      console.log(`  variantText: ${item.variantText || 'NULL'}`);
      console.log(`  数量: ${item.quantity}`);
      console.log(`  价格: ${item.price}`);
    });

    // 5. 检查缺少 variantText 的项目
    console.log('\n=== 缺少 variantText 的项目 ===');
    const itemsWithoutVariantText = await prisma.orderItem.findMany({
      where: {
        OR: [
          { variantText: null },
          { variantText: '' }
        ]
      },
      take: 3,
      include: {
        order: {
          select: {
            id: true,
            createdAt: true,
            status: true
          }
        }
      }
    });

    if (itemsWithoutVariantText.length === 0) {
      console.log('没有找到缺少 variantText 的项目');
    } else {
      itemsWithoutVariantText.forEach((item, index) => {
        console.log(`\n缺少 variantText 的项目 ${index + 1}:`);
        console.log(`  ID: ${item.id}`);
        console.log(`  订单ID: ${item.orderId}`);
        console.log(`  productCode: ${item.productCode || 'NULL'}`);
        console.log(`  variantCode: ${item.variantCode || 'NULL'}`);
        console.log(`  variantText: ${item.variantText || 'NULL'}`);
      });
    }

    // 6. 检查产品和变体数据
    console.log('\n=== 产品和变体数据检查 ===');
    const productCount = await prisma.product.count();
    const variantCount = await prisma.productVariant.count();
    console.log(`产品总数: ${productCount}`);
    console.log(`变体总数: ${variantCount}`);

    // 7. 检查是否有产品可以匹配订单项目
    const sampleProduct = await prisma.product.findFirst({
      include: {
        variants: true
      }
    });

    if (sampleProduct) {
      console.log(`\n示例产品:`);
      console.log(`  SKU: ${sampleProduct.sku}`);
      console.log(`  名称: ${sampleProduct.name}`);
      console.log(`  变体数量: ${sampleProduct.variants.length}`);
      
      if (sampleProduct.variants.length > 0) {
        const variant = sampleProduct.variants[0];
        console.log(`  第一个变体:`);
        console.log(`    variantCode: ${variant.variantCode}`);
        console.log(`    duration: ${variant.duration}`);
        console.log(`    durationType: ${variant.durationType}`);
      }
    }

  } catch (error) {
    console.error('检查数据时出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkOrderData();
