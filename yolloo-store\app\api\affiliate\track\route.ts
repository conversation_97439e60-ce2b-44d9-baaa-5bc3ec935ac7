import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


/**
 * 计算并处理推广佣金
 * @param orderId 订单ID
 * @param referralCode 推广码
 * @param cardItemsTotal card商品总金额
 * @param affiliateProfile 推广员资料
 */
async function processCommission(orderId: string, referralCode: string, cardItemsTotal: number, affiliateProfile: any) {
  try {
    let commissionAmount = 0;
    let organizationCommissionId = null;
    let organizationCommissionAmount = 0;

    // 如果推广员属于组织，则处理组织佣金
    if (affiliateProfile.organizationId) {
      // 获取组织资料
      const organization = await prisma.affiliateOrganization.findUnique({
        where: { id: affiliateProfile.organizationId },
      });

      if (organization) {
        // 计算组织佣金总额
        organizationCommissionAmount = cardItemsTotal * organization.commissionRate;
        
        // 计算会员佣金 - 基于会员的commissionRate计算从组织佣金中分配的比例
        // 会员佣金 = 商品总价 * 会员佣金率
        commissionAmount = cardItemsTotal * affiliateProfile.commissionRate;
        
        // 组织实际获得的佣金 = 组织佣金总额 - 会员佣金
        const organizationActualCommission = organizationCommissionAmount - commissionAmount;
        
        // 创建组织佣金记录
        const orgCommission = await prisma.organizationCommission.create({
          data: {
            organizationId: organization.id,
            commissionAmount: organizationCommissionAmount,
            status: "PENDING",
          },
        });
        
        // 更新组织总收益 - 只加上组织实际获得的部分
        await prisma.affiliateOrganization.update({
          where: { id: organization.id },
          data: {
            totalEarnings: {
              increment: organizationActualCommission,
            },
          },
        });
        
        organizationCommissionId = orgCommission.id;
      }
    } else {
      // 非组织会员的佣金计算保持不变
      commissionAmount = cardItemsTotal * affiliateProfile.commissionRate;
    }

    // 创建推广记录
    const referral = await prisma.affiliateReferral.create({
      data: {
        affiliateId: affiliateProfile.id,
        orderId,
        commissionAmount,
        status: "PENDING",
        ...(organizationCommissionId && { organizationCommissionId }),
      },
    });

    // 更新订单的推广信息
    await prisma.order.update({
      where: { id: orderId },
      data: { referralCode },
    });

    // 更新会员总收益
    await prisma.affiliateProfile.update({
      where: { id: affiliateProfile.id },
      data: {
        totalEarnings: {
          increment: commissionAmount,
        },
      },
    });

    return {
      success: true,
      referral,
      commissionGenerated: true,
      cardItemsTotal,
      commissionAmount,
      organizationCommissionAmount: organizationCommissionAmount || 0,
      hasOrganization: !!organizationCommissionId
    };
  } catch (error) {
    console.error("[PROCESS_COMMISSION]", error);
    throw error;
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { orderId, referralCode } = body;

    if (!orderId || !referralCode) {
      return new NextResponse("Missing required fields", { status: 400 });
    }

    // 查找订单和会员资料
    const [order, affiliateProfile] = await Promise.all([
      prisma.order.findUnique({
        where: { id: orderId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  category: true
                }
              }
            }
          }
        }
      }),
      prisma.affiliateProfile.findUnique({
        where: { code: referralCode },
        include: {
          organization: true,
        }
      }),
    ]);

    if (!order || !affiliateProfile) {
      return new NextResponse("Order or affiliate not found", { status: 404 });
    }

    // 确保不是自己推广自己
    if (order.userId === affiliateProfile.userId) {
      return new NextResponse("Cannot refer yourself", { status: 400 });
    }

    // 筛选出类型为"card"的商品项
    const cardItems = order.items.filter(item => 
      item.product.category.name.toLowerCase() === "card"
    );

    // 如果没有"card"类型的商品，则不计算佣金
    if (cardItems.length === 0) {
      // 更新订单的推广信息，但不生成佣金
      await prisma.order.update({
        where: { id: orderId },
        data: { referralCode },
      });
      
      return NextResponse.json({ 
        success: true, 
        message: "Referral code applied, but no commission generated as there are no card products", 
        commissionGenerated: false 
      });
    }

    // 计算card商品总金额
    const cardItemsTotal = cardItems.reduce((total, item) => 
      total + (item.price * item.quantity), 0
    );
    
    // 处理推广佣金
    const result = await processCommission(orderId, referralCode, cardItemsTotal, affiliateProfile);

    return NextResponse.json(result);
  } catch (error) {
    console.error("[AFFILIATE_TRACK]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
} 