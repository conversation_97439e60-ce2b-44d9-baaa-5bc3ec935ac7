import * as React from 'react';

interface OrderConfirmationEmailProps {
  orderDetails: {
    orderId: string;
    items: Array<{
      name: string;
      quantity: number;
      price: number;
    }>;
    total: number;
  };
  customerName: string;
}

export const OrderConfirmationEmail: React.FC<Readonly<OrderConfirmationEmailProps>> = ({
  orderDetails,
  customerName,
}) => (
  <div style={{
    fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    lineHeight: 1.5,
    padding: '20px',
  }}>
    <h1 style={{ color: '#333', fontSize: '24px', marginBottom: '24px' }}>
      Thank you for your order, {customerName}!
    </h1>
    <p style={{ color: '#666', fontSize: '16px', margin: '16px 0' }}>
      Your order (#{orderDetails.orderId}) has been confirmed and is being processed.
    </p>
    <div style={{ margin: '32px 0' }}>
      <h2 style={{ color: '#333', fontSize: '20px', marginBottom: '16px' }}>Order Details:</h2>
      <table style={{ width: '100%', borderCollapse: 'collapse' }}>
        <thead>
          <tr style={{ backgroundColor: '#f3f4f6' }}>
            <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Item</th>
            <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e5e7eb' }}>Quantity</th>
            <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e5e7eb' }}>Price</th>
          </tr>
        </thead>
        <tbody>
          {orderDetails.items.map((item, index) => (
            <tr key={index}>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>{item.name}</td>
              <td style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e5e7eb' }}>{item.quantity}</td>
              <td style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e5e7eb' }}>${item.price.toFixed(2)}</td>
            </tr>
          ))}
          <tr>
            <td colSpan={2} style={{ padding: '12px', textAlign: 'right', fontWeight: 'bold' }}>Total:</td>
            <td style={{ padding: '12px', textAlign: 'right', fontWeight: 'bold' }}>${orderDetails.total.toFixed(2)}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <p style={{ color: '#666', fontSize: '16px', margin: '16px 0' }}>
      We will notify you when your order ships.
    </p>
    <div style={{ marginTop: '32px', borderTop: '1px solid #e5e7eb', paddingTop: '16px', color: '#666', fontSize: '14px' }}>
      <p>If you have any questions, please contact our customer support.</p>
      <p>Best regards,<br />Yolloo Store Team</p>
    </div>
  </div>
); 