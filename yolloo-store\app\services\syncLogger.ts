import { prisma } from '@/lib/prisma';
import { ProductSyncManager, SyncEventType } from './productSyncManager';
import { DateFormatter } from '@/lib/utils';

// 日志级别
export enum LogLevel {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  DEBUG = 'DEBUG'
}

// 日志记录器接口
export interface SyncLogger {
  log(level: LogLevel, message: string, data?: any): Promise<void>;
  info(message: string, data?: any): Promise<void>;
  warning(message: string, data?: any): Promise<void>;
  error(message: string, data?: any): Promise<void>;
  debug(message: string, data?: any): Promise<void>;
}

// 数据库日志记录器
export class DbSyncLogger implements SyncLogger {
  private syncId: string;
  private enabled: boolean;

  constructor(syncId: string, enabled: boolean = true) {
    this.syncId = syncId;
    this.enabled = enabled;
  }

  // 记录日志
  async log(level: LogLevel, message: string, data?: any): Promise<void> {
    if (!this.enabled) return;

    try {
      await prisma.syncLog.create({
        data: {
          syncId: this.syncId,
          level,
          message,
          data: data ? JSON.stringify(data) : null,
          timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('Failed to write sync log to database:', error);
      // 降级到控制台日志
      console.log(`[${level}][${this.syncId}] ${message}`, data || '');
    }
  }

  // 记录信息级别日志
  async info(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.INFO, message, data);
  }

  // 记录警告级别日志
  async warning(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.WARNING, message, data);
  }

  // 记录错误级别日志
  async error(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.ERROR, message, data);
  }

  // 记录调试级别日志
  async debug(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.DEBUG, message, data);
  }
}

// 控制台日志记录器
export class ConsoleSyncLogger implements SyncLogger {
  private syncId: string;

  constructor(syncId: string) {
    this.syncId = syncId;
  }

  // 记录日志
  async log(level: LogLevel, message: string, data?: any): Promise<void> {
    const timestamp = DateFormatter.iso(new Date());
    const logPrefix = `[${timestamp}][${level}][${this.syncId}]`;

    switch (level) {
      case LogLevel.ERROR:
        console.error(`${logPrefix} ${message}`, data || '');
        break;
      case LogLevel.WARNING:
        console.warn(`${logPrefix} ${message}`, data || '');
        break;
      case LogLevel.DEBUG:
        console.debug(`${logPrefix} ${message}`, data || '');
        break;
      case LogLevel.INFO:
      default:
        console.log(`${logPrefix} ${message}`, data || '');
        break;
    }
  }

  // 记录信息级别日志
  async info(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.INFO, message, data);
  }

  // 记录警告级别日志
  async warning(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.WARNING, message, data);
  }

  // 记录错误级别日志
  async error(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.ERROR, message, data);
  }

  // 记录调试级别日志
  async debug(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.DEBUG, message, data);
  }
}

// 组合日志记录器
export class CompositeSyncLogger implements SyncLogger {
  private loggers: SyncLogger[];

  constructor(loggers: SyncLogger[]) {
    this.loggers = loggers;
  }

  // 记录日志
  async log(level: LogLevel, message: string, data?: any): Promise<void> {
    await Promise.all(this.loggers.map(logger => logger.log(level, message, data)));
  }

  // 记录信息级别日志
  async info(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.INFO, message, data);
  }

  // 记录警告级别日志
  async warning(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.WARNING, message, data);
  }

  // 记录错误级别日志
  async error(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.ERROR, message, data);
  }

  // 记录调试级别日志
  async debug(message: string, data?: any): Promise<void> {
    return this.log(LogLevel.DEBUG, message, data);
  }
}

// 初始化同步日志记录
export function initSyncLogger(syncId: string): SyncLogger {
  // 创建控制台日志记录器
  const consoleLogger = new ConsoleSyncLogger(syncId);

  // 创建数据库日志记录器
  const dbLogger = new DbSyncLogger(syncId, process.env.NODE_ENV === 'production');

  // 创建组合日志记录器
  const logger = new CompositeSyncLogger([consoleLogger, dbLogger]);

  // 获取同步管理器实例
  const syncManager = ProductSyncManager.getInstance();

  // 订阅同步事件
  syncManager.on(SyncEventType.STARTED, async (data) => {
    await logger.info('同步开始', data);
  });

  syncManager.on(SyncEventType.COMPLETED, async (data) => {
    await logger.info('同步完成', data);
  });

  syncManager.on(SyncEventType.FAILED, async (data) => {
    await logger.error('同步失败', data);
  });

  syncManager.on(SyncEventType.PRODUCT_ERROR, async (data) => {
    await logger.error(`处理产品 ${data.sku} 时出错`, {
      productType: data.productType,
      error: data.error instanceof Error ? data.error.message : data.error
    });
  });

  syncManager.on(SyncEventType.TYPE_COMPLETED, async (data) => {
    await logger.info(`产品类型 ${data.productType} 同步完成`, data.stats);
  });

  return logger;
}
