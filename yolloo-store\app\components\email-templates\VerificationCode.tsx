import * as React from 'react';
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components';

export interface VerificationCodeEmailProps {
  verificationCode: string;
  expiryMinutes?: number;
  previewText?: string;
}

export const VerificationCodeEmail = ({
  verificationCode,
  expiryMinutes = 5,
  previewText = 'Your verification code for Yolloo Store',
}: VerificationCodeEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src="https://i.postimg.cc/F15k4vk9/logo.png"
            width="300"
            height="auto"
            alt="Yolloo Store"
            style={logo}
          />
          <Heading style={heading}>Email Verification</Heading>
          <Text style={paragraph}>
            You requested to sign in to your Yolloo Store account. Please use the verification code below:
          </Text>
          <Section style={codeContainer}>
            <Text style={codeText}>{verificationCode}</Text>
          </Section>
          <Text style={paragraph}>
            This verification code will expire in {expiryMinutes} minutes.
          </Text>
          <Text style={paragraph}>
            If you didn't request this code, please ignore this email or contact support if you have concerns.
          </Text>
          <Section style={footer}>
            <Text style={footerText}>
              © {new Date().getFullYear()} Yolloo Store. All rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '580px',
};

const logo = {
  marginTop: '32px',
};

const heading = {
  fontSize: '32px',
  lineHeight: '1.3',
  fontWeight: '700',
  color: '#484848',
  marginTop: '32px',
  marginBottom: '24px',
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '1.4',
  color: '#484848',
  marginBottom: '16px',
};

const codeContainer = {
  background: '#f4f4f4',
  borderRadius: '8px',
  padding: '24px',
  textAlign: 'center' as const,
  margin: '32px 0',
  border: '2px dashed #e1e1e1',
};

const codeText = {
  fontSize: '32px',
  fontWeight: 'bold',
  color: '#2563eb',
  letterSpacing: '8px',
  margin: '0',
  fontFamily: 'monospace',
};

const footer = {
  marginTop: '32px',
  paddingTop: '24px',
  borderTop: '1px solid #e1e1e1',
};

const footerText = {
  fontSize: '12px',
  color: '#8898aa',
  textAlign: 'center' as const,
  margin: '0',
};
