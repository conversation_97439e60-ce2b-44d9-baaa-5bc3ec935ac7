import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export const dynamic = 'force-dynamic';

// GET - Get analytics for an organization (for organization admins)
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const organizationId = params.id;
    
    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
      include: {
        _count: {
          select: { 
            members: true,
            visits: true,
            commissions: true
          }
        }
      }
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Check if the user is a member of this organization and has admin privileges
    const userProfile = await prisma.affiliateProfile.findFirst({
      where: {
        userId: session.user.id,
        organizationId: organizationId,
      },
    });
    
    if (!userProfile || !userProfile.isAdmin) {
      return NextResponse.json({ error: "You don't have permission to view this organization's analytics" }, { status: 403 });
    }
    
    // Get total visits
    const totalVisits = await prisma.affiliateVisit.count({
      where: { organizationId }
    });
    
    // Get total conversions (successful referrals)
    const totalConversions = await prisma.affiliateReferral.count({
      where: {
        status: "APPROVED",
        order: {
          status: { in: ["PAID", "PROCESSING", "SHIPPED", "DELIVERED"] }
        },
        affiliate: {
          organizationId
        }
      }
    });
    
    // Get total commissions
    const commissionsData = await prisma.organizationCommission.aggregate({
      where: { 
        organizationId,
        status: { in: ["APPROVED", "PAID"] }
      },
      _sum: {
        commissionAmount: true
      }
    });
    
    const totalCommissions = commissionsData._sum.commissionAmount || 0;
    
    // Get total commissions earned by members
    const memberCommissionsData = await prisma.affiliateReferral.aggregate({
      where: {
        affiliate: {
          organizationId
        },
        status: { in: ["APPROVED", "PAID"] }
      },
      _sum: {
        commissionAmount: true
      }
    });
    
    const memberCommissions = memberCommissionsData._sum.commissionAmount || 0;
    
    // Organization actual earnings = Total organization commissions - Member commissions
    const organizationActualEarnings = totalCommissions - memberCommissions;
    
    // Calculate conversion rate
    const conversionRate = totalVisits > 0 ? (totalConversions / totalVisits) * 100 : 0;
    
    // Get all members of the organization
    const members = await prisma.affiliateProfile.findMany({
      where: { organizationId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      }
    });
    
    const memberIds = members.map(member => member.id);
    
    // Get top performing members
    const topPerformers = await Promise.all(
      members.slice(0, 5).map(async (member) => {
        const referrals = await prisma.affiliateReferral.count({
          where: {
            affiliateId: member.id,
            status: { in: ["APPROVED", "PAID"] }
          }
        });
        
        return {
          id: member.id,
          user: {
            id: member.user.id,
            name: member.user.name,
            image: member.user.image
          },
          referrals,
          earnings: member.totalEarnings
        };
      })
    );
    
    // Sort top performers by earnings
    topPerformers.sort((a, b) => b.earnings - a.earnings);
    
    // Get member orders (same format as admin view)
    const memberOrders = await prisma.affiliateReferral.findMany({
      where: {
        affiliateId: { in: memberIds }
      },
      include: {
        affiliate: {
          include: {
            user: true
          }
        },
        order: {
          select: {
            id: true,
            total: true,
            status: true,
            createdAt: true,
            updatedAt: true
          }
        },
        organizationCommission: {
          select: {
            id: true,
            commissionAmount: true,
            status: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 200
    });
    
    // Calculate real monthly earnings data
    const monthlyEarnings = await calculateMonthlyEarnings(organizationId);
    
    // Prepare analytics data
    const analyticsData = {
      totalMembers: members.length,
      totalReferrals: totalConversions,
      totalEarnings: organizationActualEarnings,
      monthlyEarnings,
      topPerformers
    };
    
    // Admin-style stats (like in admin panel)
    const stats = {
      totalVisits,
      totalConversions,
      conversionRate,
      totalCommissions,
      memberCommissions,
      organizationActualEarnings
    };
    
    return NextResponse.json({
      analytics: analyticsData,
      stats,
      memberOrders,
      organization,
      needsRefresh: memberOrders.length === 0 && members.length > 0
    });
    
  } catch (error) {
    console.error("Error fetching organization analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch organization analytics" },
      { status: 500 }
    );
  }
}

// Helper function to calculate real monthly earnings data
async function calculateMonthlyEarnings(organizationId: string) {
  // Get current date and set to beginning of month
  const currentDate = new Date();
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const monthsData = [];
  
  // Calculate data for the last 6 months
  for (let i = 0; i < 6; i++) {
    // Create date for this month (0-5 months ago)
    const targetDate = new Date();
    targetDate.setMonth(currentDate.getMonth() - i);
    
    const startOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);
    const endOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0, 23, 59, 59);
    
    // Get approved organization commissions for this month
    const monthlyCommissions = await prisma.organizationCommission.aggregate({
      where: {
        organizationId,
        status: { in: ["APPROVED", "PAID"] },
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      },
      _sum: {
        commissionAmount: true
      }
    });
    
    // Get approved member commissions for this month
    const memberMonthlyCommissions = await prisma.affiliateReferral.aggregate({
      where: {
        affiliate: {
          organizationId
        },
        status: { in: ["APPROVED", "PAID"] },
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      },
      _sum: {
        commissionAmount: true
      }
    });
    
    const totalCommissions = monthlyCommissions._sum.commissionAmount || 0;
    const memberCommissions = memberMonthlyCommissions._sum.commissionAmount || 0;
    
    // Calculate organization's actual earnings for the month
    const actualEarnings = totalCommissions - memberCommissions;
    
    // Add month data
    monthsData.push({
      month: months[targetDate.getMonth()],
      year: targetDate.getFullYear(),
      amount: actualEarnings,
      totalCommissions,
      memberCommissions
    });
  }
  
  // Sort from oldest to newest
  return monthsData.reverse();
} 