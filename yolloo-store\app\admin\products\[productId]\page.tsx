import { notFound } from "next/navigation"
import { prisma } from "@/lib/prisma"
import { ProductForm } from "@/components/admin/product-form"
import { headers } from "next/headers"

interface EditProductPageProps {
  params: {
    productId: string
  }
}

async function getProduct(productId: string) {
  headers()
  
  const product = await prisma.product.findUnique({
    where: {
      id: productId,
    },
    include: {
      variants: {
        orderBy: [
          { durationType: 'asc' },
          { duration: 'asc' }
        ]
      },
    }
  })

  if (!product) {
    notFound()
  }

  const formattedVariants = product.variants.map((variant: any) => ({
    id: variant.id,
    price: Number(variant.price),
    currency: variant.currency,
    productId: variant.productId,
    variantCode: variant.variantCode,
    duration: variant.duration,
    durationType: variant.durationType,
    attributes: variant.attributes as Record<string, any>,
    createdAt: variant.createdAt,
    updatedAt: variant.updatedAt
  }))

  return {
    ...product,
    specifications: product.specifications as Record<string, any> || {},
    variants: formattedVariants
  }
}

async function getCategories() {
  headers()
  
  return await prisma.category.findMany({
    orderBy: {
      name: "asc",
    },
  })
}

export const revalidate = 0

export default async function EditProductPage({ params }: EditProductPageProps) {
  const [product, categories] = await Promise.all([
    getProduct(params.productId),
    getCategories(),
  ])

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Edit Product</h3>
        <p className="text-sm text-muted-foreground">
          Make changes to your product here
        </p>
      </div>
      <ProductForm initialData={product} categories={categories} />
    </div>
  )
} 