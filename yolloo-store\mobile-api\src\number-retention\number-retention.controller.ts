import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';
import { NumberRetentionService } from './number-retention.service';
import { NumberRetentionQueryDto } from './dto/number-retention-query.dto';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { Public } from '../common/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller('number-retention')
export class NumberRetentionController {
  constructor(private readonly numberRetentionService: NumberRetentionService) {}

  @Public()
  @Get()
  getNumberRetentionPackages(
    @Query() query: NumberRetentionQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.numberRetentionService.getNumberRetentionPackages(query, ctx);
  }

  @Public()
  @Get(':packageId')
  getPackageById(
    @Param('packageId') packageId: string,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.numberRetentionService.getPackageById(packageId, ctx);
  }
}
