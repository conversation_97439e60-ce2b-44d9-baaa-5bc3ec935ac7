import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // 获取用户已发货但未绑定卡的eSIM订单
    const orders = await prisma.order.findMany({
      where: {
        userId: session.user.id,
        status: "DELIVERED",
        items: {
          some: {
            product: {
              category: {
                name: "esim"
              }
            },
            uid: null, // 未绑定卡的订单
          }
        }
      },
      include: {
        items: {
          include: {
            product: {
              include: {
                category: true
              }
            }
          }
        }
      }
    })

    // 转换数据格式
    const availableEsims = orders.map(order => ({
      id: order.id,
      orderNumber: order.id,
      productName: order.items[0]?.product.name || "Unknown Product",
      status: order.status
    }))

    return NextResponse.json({ orders: availableEsims })
  } catch (error) {
    console.error("[AVAILABLE_ESIMS]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 