import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { DateUtils } from "@/lib/utils";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Helper function to check if user has admin access to the organization
async function hasAdminAccess(userId: string, organizationId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      role: true,
      affiliate: {
        select: {
          id: true,
          organizationId: true,
          isAdmin: true,
        },
      },
    },
  });

  if (!user) return false;

  // System admin has access to all organizations
  if (user.role === "ADMIN") return true;

  // Organization admin has access to their organization
  if (user.affiliate?.organizationId === organizationId && user.affiliate.isAdmin) {
    return true;
  }

  return false;
}

// POST - Create a general invite link
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const organizationId = params.id;

    // Check if user has admin access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);

    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // 查找该组织之前的所有通用邀请链接（没有绑定特定邮箱的链接）
    const previousGeneralInvites = await prisma.organizationInvite.findMany({
      where: {
        organizationId,
        email: null, // 通用邀请没有指定邮箱
        status: "PENDING" // 仅更新待处理的邀请
      },
    });

    // 如果存在之前的通用邀请链接，将其状态更新为过期
    if (previousGeneralInvites.length > 0) {
      await prisma.organizationInvite.updateMany({
        where: {
          id: {
            in: previousGeneralInvites.map(invite => invite.id)
          }
        },
        data: {
          status: "EXPIRED",
        },
      });
    }

    // Generate a unique invite code
    const inviteCode = `INV-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

    // Create the invite without a specific email (general link)
    const invite = await prisma.organizationInvite.create({
      data: {
        organizationId,
        inviteCode,
        // Set expiration date to 7 days from now
        expiresAt: DateUtils.addDays(new Date(), 7),
      },
    });

    return NextResponse.json(invite);
  } catch (error) {
    console.error("Error creating general invite:", error);
    return NextResponse.json(
      { error: "Failed to create general invite" },
      { status: 500 }
    );
  }
}