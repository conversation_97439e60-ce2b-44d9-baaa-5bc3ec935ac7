import { Controller, Get, Param, Res, NotFoundException } from '@nestjs/common';
import { Response } from 'express';
import { join } from 'path';
import { existsSync } from 'fs';
import { Public } from '../decorators/public.decorator';
import { DateFormatter } from '../utils';

/**
 * 静态资源控制器
 * 提供图片资源的访问和管理
 */
@Controller('static')
export class StaticController {
  private readonly publicPath = join(process.cwd(), 'public');

  /**
   * 获取图片资源
   * @param type 资源类型 (images, icons, etc.)
   * @param subPath 子路径
   * @param filename 文件名
   * @param res Express Response对象
   */
  @Public()
  @Get(':type/:subPath/:filename')
  async getImage(
    @Param('type') type: string,
    @Param('subPath') subPath: string,
    @Param('filename') filename: string,
    @Res() res: Response,
  ) {
    const filePath = join(this.publicPath, type, subPath, filename);

    if (!existsSync(filePath)) {
      throw new NotFoundException('Image not found');
    }

    // 设置缓存头
    res.set({
      'Cache-Control': 'public, max-age=31536000', // 1年缓存
      'ETag': `"${filename}"`,
    });

    return res.sendFile(filePath);
  }

  /**
   * 获取嵌套路径的图片资源
   * @param type 资源类型
   * @param subPath1 第一级子路径
   * @param subPath2 第二级子路径
   * @param filename 文件名
   * @param res Express Response对象
   */
  @Public()
  @Get(':type/:subPath1/:subPath2/:filename')
  async getNestedImage(
    @Param('type') type: string,
    @Param('subPath1') subPath1: string,
    @Param('subPath2') subPath2: string,
    @Param('filename') filename: string,
    @Res() res: Response,
  ) {
    const filePath = join(this.publicPath, type, subPath1, subPath2, filename);

    if (!existsSync(filePath)) {
      throw new NotFoundException('Image not found');
    }

    // 设置缓存头
    res.set({
      'Cache-Control': 'public, max-age=31536000', // 1年缓存
      'ETag': `"${filename}"`,
    });

    return res.sendFile(filePath);
  }

  /**
   * 健康检查 - 检查静态资源目录是否存在
   */
  @Public()
  @Get('health')
  checkHealth() {
    const imagesPath = join(this.publicPath, 'images');
    const exists = existsSync(imagesPath);

    return {
      status: exists ? 'ok' : 'error',
      message: exists ? 'Static assets directory is accessible' : 'Static assets directory not found',
      path: imagesPath,
      timestamp: DateFormatter.iso(new Date()),
    };
  }
}
