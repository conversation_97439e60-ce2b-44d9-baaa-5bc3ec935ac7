import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { randomBytes } from "crypto";
import { sendOrganizationInviteEmail } from "@/app/services/emailService";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for creating accounts and adding to organization
const createAccountsSchema = z.object({
  users: z.array(
    z.object({
      name: z.string().min(1, "Name is required"),
      email: z.string().email("Invalid email"),
      phone: z.string().optional(),
    })
  ),
  commissionRate: z.number().min(0).max(1).optional(),
  isAdmin: z.boolean().optional(),
  sendInvites: z.boolean().optional(),
});

// POST - Create accounts and add to organization (organization admin only)
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const organizationId = params.id;
    
    // Check if organization exists and user is admin
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
      include: {
        members: {
          where: {
            userId: session.user.id,
            isAdmin: true,
          },
        },
      },
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Check if current user is an admin of this organization
    if (organization.members.length === 0) {
      return NextResponse.json({ error: "You must be an admin of this organization to add members" }, { status: 403 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validationResult = createAccountsSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { users, commissionRate = 0.5, isAdmin = false, sendInvites = true } = validationResult.data;
    
    // Check for existing users by email
    const emails = users.map(user => user.email);
    const existingUsers = await prisma.user.findMany({
      where: { email: { in: emails } },
    });
    
    // Prepare results arrays
    const createdMembers: any[] = [];
    const errors: { email: string; name: string; message: string }[] = [];
    
    // Process each user
    await Promise.all(
      users.map(async (userData) => {
        try {
          // Check if user already exists
          const existingUser = existingUsers.find(
            u => u.email.toLowerCase() === userData.email.toLowerCase()
          );
          
          if (existingUser) {
            errors.push({
              email: userData.email,
              name: userData.name,
              message: "User with this email already exists",
            });
            return;
          }
          
          // Create new user
          const newUser = await prisma.user.create({
            data: {
              name: userData.name,
              email: userData.email,
              emailVerified: new Date(), // Auto verify for bulk creation
              role: 'CUSTOMER' // 设置角色为CUSTOMER
            },
          });
          
          // Generate affiliate code
          const code = `${userData.name.substring(0, 3)}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
          
          // Create affiliate profile and add to organization
          const newAffiliate = await prisma.affiliateProfile.create({
            data: {
              userId: newUser.id,
              code,
              organizationId,
              commissionRate,
              isAdmin,
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true,
                },
              },
            },
          });
          
          // Create invitation if requested
          if (sendInvites) {
            // Generate invite code
            const inviteCode = randomBytes(32).toString("hex");
            
            // Create invitation
            const invite = await prisma.organizationInvite.create({
              data: {
                email: userData.email,
                inviteCode,
                expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
                organizationId,
                status: "PENDING",
                commissionRate, // 使用传入的佣金率
                isAdmin, // 使用传入的管理员状态
              },
            });
            
            // Send the invitation email
            const organization = await prisma.affiliateOrganization.findUnique({
              where: { id: organizationId },
              select: { name: true }
            });
            
            if (organization) {
              const inviteUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invite/${inviteCode}`;
              await sendOrganizationInviteEmail(
                userData.email,
                organization.name,
                inviteUrl,
                invite.expiresAt, 
                session.user.name || undefined,
                isAdmin
              );
            }
          }
          
          createdMembers.push(newAffiliate);
        } catch (error) {
          console.error(`Error creating account for ${userData.email}:`, error);
          errors.push({
            email: userData.email,
            name: userData.name,
            message: "Failed to create account",
          });
        }
      })
    );
    
    return NextResponse.json({
      createdMembers,
      errors,
      totalCreated: createdMembers.length,
      totalFailed: errors.length,
    });
  } catch (error) {
    console.error("Error creating accounts:", error);
    return NextResponse.json(
      { error: "Failed to create accounts" },
      { status: 500 }
    );
  }
}