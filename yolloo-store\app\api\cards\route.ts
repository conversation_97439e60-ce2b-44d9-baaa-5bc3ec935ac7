import { getServerSession } from "next-auth"
import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Get all cards for the current user
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const cards = await prisma.yollooCard.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        esims: {
          include: {
            product: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(cards)
  } catch (error) {
    console.error('[CARDS_GET]', error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

// Create a new card
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const json = await request.json()
    const { number, type } = json

    if (!number || !type) {
      return new NextResponse("Missing required fields", { status: 400 })
    }

    // Check if card number already exists
    const existingCard = await prisma.yollooCard.findUnique({
      where: {
        number: number
      }
    })

    if (existingCard) {
      return new NextResponse("Card number already exists", { status: 400 })
    }

    const card = await prisma.yollooCard.create({
      data: {
        number,
        type,
        userId: session.user.id,
        status: "Inactive"
      }
    })

    return NextResponse.json(card)
  } catch (error) {
    console.error('[CARDS_POST]', error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 