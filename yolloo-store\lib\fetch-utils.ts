export async function safeFetch(url: string, options: RequestInit = {}) {
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // 如果请求体是 FormData，不设置 Content-Type，让浏览器自动处理
  if (!(options.body instanceof FormData)) {
    options.headers = {
      ...defaultHeaders,
      ...options.headers,
    };
  }

  try {
    const response = await fetch(url, {
      ...options,
      headers: options.headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
} 