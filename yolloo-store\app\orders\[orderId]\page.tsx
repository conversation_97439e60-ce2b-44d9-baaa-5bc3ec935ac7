import { getServerSession } from "next-auth/next"
import { redirect, notFound } from "next/navigation"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { odooService } from "@/lib/odoo"
import { OrderDetails } from "./order-details"
import type { Order, OrderItem, Product, ProductVariant, Address, OdooOrderStatus } from "@prisma/client"
import { z } from "zod"

interface OrderPageProps {
  params: {
    orderId: string
  }
  searchParams: {
    success?: string
    canceled?: string
  }
}

type OrderWithRelations = Order & {
  items: (OrderItem & {
    product?: Product | null;
    variant?: ProductVariant | null;
  })[];
  shippingAddress?: Address | null;
  shippingAddressSnapshot?: {
    name: string;
    phone: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  } | null;
  odooStatuses: OdooOrderStatus[];
  odooStatus: OdooOrderStatus | null; // 为了兼容性保留
}

// 定义shippingAddressSnapshot的验证模式
const ShippingAddressSnapshotSchema = z.object({
  name: z.string(),
  phone: z.string(),
  address1: z.string(),
  address2: z.string().nullable().optional(),
  city: z.string(),
  state: z.string(),
  postalCode: z.string(),
  country: z.string()
}).nullable();

async function getOrder(orderId: string): Promise<OrderWithRelations | null> {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect("/api/auth/signin")
  }

  const order = await prisma.order.findUnique({
    where: {
      id: orderId,
    },
    select: {
      id: true,
      userId: true,
      total: true,
      status: true,
      createdAt: true,
      updatedAt: true,
      addressId: true,
      paymentId: true,
      referralCode: true,
      shippingAddressSnapshot: true,
      items: {
        select: {
          id: true,
          orderId: true,
          productCode: true,
          variantCode: true,
          variantText: true,
          quantity: true,
          price: true,
          uid: true,
          lpaString: true,
          // 移除 product 和 variant 关系查询，因为我们已经移除了外键关系
        }
      },
      shippingAddress: true,
      payment: true,
      odooStatuses: {
        select: {
          id: true,
          orderId: true,
          variantCode: true,
          odooOrderRef: true,
          status: true,
          description: true,
          productName: true,
          isDigital: true,
          deliveredQty: true,
          trackingNumber: true,
          planState: true,
          uid: true,
          lastCheckedAt: true,
          createdAt: true,
          updatedAt: true
        }
      }
    }
  })

  if (!order || order.userId !== session.user.id) {
    notFound()
  }

  // 获取并更新 Odoo 订单状态
  try {
    // 首先从数据库获取该订单的所有customer_order_ref
    const existingOdooStatuses = await prisma.odooOrderStatus.findMany({
      where: { orderId: order.id },
      select: { variantCode: true, uid: true }
    });

    // 构建所有可能的customer_order_ref
    const customerOrderRefs: string[] = [];

    if (existingOdooStatuses.length > 0) {
      // 如果有现有的Odoo状态记录，使用这些记录构建customer_order_ref
      for (const status of existingOdooStatuses) {
        const variantCode = status.variantCode || 'default';
        const uid = status.uid || 'no-uid';
        const customerOrderRef = `${order.id}-${variantCode}:::${uid}`;
        customerOrderRefs.push(customerOrderRef);
      }
    } else {
      // 如果没有现有记录，尝试使用简单的订单ID（向后兼容）
      customerOrderRefs.push(order.id);
    }

    console.log(`[OrderDetails] Fetching Odoo status for order: ${order.id} with refs:`, customerOrderRefs);
    const odooResponse = await odooService.queryOrderStatusMultiple(customerOrderRefs);

    // 记录API响应，帮助调试
    console.log(`[OrderDetails] Odoo API response for order ${order.id}:`, JSON.stringify(odooResponse, null, 2));

    if (odooResponse?.status === 'success' || odooResponse?.status === 'ok') {
      const responseData = odooResponse.result?.data;

      // 处理所有订单详情（现在可能有多个）
      if (responseData && responseData.length > 0) {
        for (const orderDetail of responseData) {
          // 检查是否有订单行数据
          if (!orderDetail.order_lines || orderDetail.order_lines.length === 0) {
            console.log(`[OrderDetails] No order lines for order detail: ${orderDetail.customer_order_ref}`);
            continue;
          }

          // 从customer_order_ref中提取variantCode和uid
          let variantCode = "default";
          let uid: string | null = null;

          if (orderDetail.customer_order_ref.includes(':::')) {
            // 新格式：使用:::分隔符
            const mainParts = orderDetail.customer_order_ref.split('-');
            if (mainParts.length > 1) {
              const groupKeyPart = mainParts.slice(1).join('-');
              const [extractedVariantCode, extractedUid] = groupKeyPart.split(':::');
              variantCode = extractedVariantCode || "default";
              uid = extractedUid === 'no-uid' ? null : extractedUid;
            }
          }

          // 处理每个订单行
          for (const orderLine of orderDetail.order_lines) {
            console.log(`[OrderDetails] Updating Odoo status for order ${order.id}, variant ${variantCode}:`, {
              status: orderLine.status,
              description: orderLine.description,
              planState: orderLine.data?.[0]?.plan_state
            });

            // 处理UID数据 - 可能是单个UID或多个UID的数组
            let uids: string[] = [];
            let uidString: string | null = uid; // 使用从customer_order_ref提取的uid

            // 如果没有从customer_order_ref提取到uid，尝试从orderLine.data中提取
            if (!uidString && orderLine.data) {
              if (Array.isArray(orderLine.data)) {
                // 从所有data项中提取uid
                uids = orderLine.data
                  .filter((item: any) => item && item.uid)
                  .map((item: any) => item.uid);

                if (uids.length > 0) {
                  uidString = uids.join(',');
                }
              } else if (typeof orderLine.data === 'object' && orderLine.data.uid) {
                // 处理单个对象的情况
                uidString = orderLine.data.uid;
              }
            }

            // 确保UID是数字字符串格式
            const formattedUid = uidString ? uidString.replace(/[^0-9,]/g, '') : null;

            console.log(`[OrderDetails] Processing order ${order.id}, variant ${variantCode}, uid=${formattedUid || 'none'}`);

            // 查找是否已存在相同 orderId, variantCode, uid 的记录
            const existingStatus = await prisma.odooOrderStatus.findFirst({
              where: {
                orderId: order.id,
                variantCode: variantCode,
                uid: formattedUid
              }
            });

            if (existingStatus) {
              // 更新现有记录
              await prisma.odooOrderStatus.update({
                where: { id: existingStatus.id },
                data: {
                  status: orderLine.status,
                  description: orderLine.description,
                  productName: orderLine.product_name,
                  isDigital: orderLine.is_digital || false,
                  deliveredQty: orderLine.delivered_qty || 0,
                  trackingNumber: orderLine.tracking_number,
                  planState: orderLine.data?.[0]?.plan_state,
                  lastCheckedAt: new Date(),
                }
              });
            } else {
              // 创建新记录
              await prisma.odooOrderStatus.create({
                data: {
                  orderId: order.id,
                  variantCode: variantCode,
                  status: orderLine.status,
                  description: orderLine.description,
                  productName: orderLine.product_name,
                  isDigital: orderLine.is_digital || false,
                  deliveredQty: orderLine.delivered_qty || 0,
                  trackingNumber: orderLine.tracking_number,
                  planState: orderLine.data?.[0]?.plan_state,
                  uid: formattedUid,
                  lastCheckedAt: new Date(),
                }
              });
            }
          }
        }

        console.log(`[OrderDetails] Odoo status updated successfully for order ${order.id}`);
      } else {
        console.warn(`[OrderDetails] No order data found in Odoo response for order ${order.id}`);
      }
    } else {
      console.error(`[OrderDetails] Invalid Odoo API response for order ${order.id}:`, odooResponse);
    }
  } catch (error) {
    console.error(`[OrderDetails] Error fetching Odoo order status for order ${order.id}:`, error);
  }

  // 查询订单项目的商品信息，包括类别
  const itemsWithProductInfo = await Promise.all(
    order.items.map(async (item) => {
      if (!item.productCode) return { ...item, product: null };

      const product = await prisma.product.findFirst({
        where: { sku: item.productCode },
        include: { category: true }
      });

      return { ...item, product };
    })
  );

  // 使用Zod验证和解析shippingAddressSnapshot
  try {
    // 解析shippingAddressSnapshot
    const validatedShippingAddressSnapshot = ShippingAddressSnapshotSchema.parse(
      order.shippingAddressSnapshot
    );

    return {
      ...order,
      items: itemsWithProductInfo,
      shippingAddressSnapshot: validatedShippingAddressSnapshot,
      odooStatus: null // 为了兼容性，设置为null
    } as OrderWithRelations;
  } catch (error) {
    console.error(`[OrderDetails] Invalid shippingAddressSnapshot format for order ${order.id}:`, error);
    // 如果验证失败，可以返回null或提供一个默认值
    return {
      ...order,
      items: itemsWithProductInfo,
      shippingAddressSnapshot: null,
      odooStatus: null // 为了兼容性，设置为null
    } as OrderWithRelations;
  }
}

export default async function OrderPage({
  params,
  searchParams,
}: OrderPageProps) {
  const order = await getOrder(params.orderId)
  if (!order) return null

  return <OrderDetails order={order} searchParams={searchParams} />
}