import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// GET /api/admin/products - 管理员获取所有产品
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const category = searchParams.get("category") || "";
    const status = searchParams.get("status") || "";
    const offShelve = searchParams.get("offShelve") || "";
    const country = searchParams.get("country") || "";
    const sort = searchParams.get("sort") || "";
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    // Search by name, SKU, or product code
    if (search) {
      where.OR = [
        {
          name: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          sku: {
            contains: search,
            mode: "insensitive",
          },
        },
        // 添加对specifications中productCode的搜索支持
        {
          specifications: {
            path: ['odooProductCode'],
            string_contains: search
          }
        }
      ];
    }

    // Filter by category
    if (category) {
      where.categoryId = category;
    }

    // Filter by status
    if (status && status !== "all") {
      where.status = status;
    }

    // Filter by offShelve status
    if (offShelve === "true") {
      where.off_shelve = true;
    } else if (offShelve === "false") {
      where.off_shelve = false;
    }

    // Filter by country
    if (country && country !== "all") {
      // 使用包含搜索来匹配国家 (考虑分隔符)
      where.country = {
        contains: country,
        mode: "insensitive",
      };
    }

    // For variant count sorting, we need a different approach
    if (sort === "variants_asc" || sort === "variants_desc") {
      // First get all products with their variant counts
      const productsWithVariantCounts = await prisma.product.findMany({
        where,
        include: {
          category: true,
          variants: {
            orderBy: [
              { durationType: 'asc' },
              { duration: 'asc' }
            ]
          },
          _count: {
            select: { variants: true }
          }
        },
      });

      // Sort by variant count
      const sortedProducts = productsWithVariantCounts.sort((a, b) => {
        const countA = a._count.variants;
        const countB = b._count.variants;
        return sort === "variants_asc"
          ? countA - countB
          : countB - countA;
      });

      // Apply pagination manually
      const paginatedProducts = sortedProducts.slice(skip, skip + limit);

      // Get categories and total count
      const [total, categories] = await Promise.all([
        prisma.product.count({ where }),
        prisma.category.findMany({
          orderBy: {
            name: "asc",
          },
        }),
      ]);

      return NextResponse.json({
        products: paginatedProducts,
        total,
        pages: Math.ceil(total / limit),
        categories,
      });
    }

    // For other sorting options, use the standard approach
    const [products, total, categories] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: true,
          variants: {
            orderBy: [
              { durationType: 'asc' },
              { duration: 'asc' }
            ]
          },
        },
        orderBy: sort === "price_asc"
          ? { price: "asc" }
          : sort === "price_desc"
            ? { price: "desc" }
            : { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.product.count({ where }),
      prisma.category.findMany({
        orderBy: {
          name: "asc",
        },
      }),
    ]);

    return NextResponse.json({
      products,
      total,
      pages: Math.ceil(total / limit),
      categories,
    });
  } catch (error) {
    console.error("[PRODUCTS_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();

    // Get the category to determine product type
    const category = await prisma.category.findUnique({
      where: {
        id: body.categoryId
      }
    });

    if (!category) {
      return new NextResponse("Category not found", { status: 404 });
    }

    // 创建产品
    const product = await prisma.product.create({
      data: {
        name: body.name || "",
        description: body.description || "",
        websiteDescription: body.websiteDescription || "",
        price: body.price !== undefined && body.price !== null ?
          isNaN(parseFloat(body.price)) ? 0 : parseFloat(body.price) : 0,
        images: body.images || [],
        categoryId: body.categoryId,
        stock: body.stock !== undefined && body.stock !== null ?
          isNaN(parseInt(body.stock, 10)) ? 0 : parseInt(body.stock, 10) : 0,
        sku: body.sku || "",
        specifications: body.specifications || {},
        mcc: body.mcc || undefined,
        requiredUID: body.requiredUID !== undefined ? body.requiredUID : false,
        off_shelve: body.off_shelve !== undefined ? body.off_shelve : false,
        dataSize: body.dataSize || undefined,
        planType: body.planType || undefined,
        country: body.country || undefined,
        countryCode: body.countryCode || undefined,
        variants: body.variants && Array.isArray(body.variants) ? {
          create: body.variants
            .filter((variant: any) => variant) // 过滤掉null或undefined
            .map((variant: any) => ({
              price: variant.price !== undefined && variant.price !== null ?
                isNaN(Number(variant.price)) ? 0 : Number(variant.price) : 0,
              currency: variant.currency || "USD",
              attributes: variant.attributes || {},
              duration: variant.duration === null ? null :
                (variant.duration !== undefined ?
                  (isNaN(Number(variant.duration)) ? null : Number(variant.duration)) : null),
              durationType: variant.durationType || null,
              variantCode: variant.variantCode || null
            }))
        } : undefined
      },
      include: {
        variants: true,
        category: true
      }
    });

    // 删除产品记录相关代码已移除

    return NextResponse.json(product);
  } catch (error) {
    console.error("[PRODUCTS_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}