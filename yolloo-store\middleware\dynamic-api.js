/**
 * API路由动态渲染中间件
 * 确保所有API路由都使用动态渲染
 */

// 标记此文件用于处理所有API路由
export const config = {
  matcher: ['/api/:path*'],
};

// 中间件函数，为所有API请求设置动态渲染相关头部
export default function dynamicApiMiddleware(req) {
  // 创建响应对象
  const response = Response.next();
  
  // 为所有API请求添加动态渲染相关头部
  response.headers.set('Cache-Control', 'no-store, max-age=0');
  response.headers.set('X-Next-Cache-Tags', 'dynamic');
  response.headers.set('X-Next-Dynamic-Rendering', 'true');
  
  return response;
} 