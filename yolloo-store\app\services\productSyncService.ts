import { OdooService } from './odooService';
import { Product, convertOdooToProduct } from '../types/product';
import { prisma } from '@/lib/prisma';
import {
    PRODUCT_TYPES,
    PRODUCT_STATUS,
    CATEGORY_DEFAULTS,
    PRODUCT_DEFAULTS
} from '../config/odoo';
import { formatProductName } from '@/lib/utils';

// 辅助函数：标准化分隔符，将 ',' 和 '，' 转换为 ';'
function standardizeSeparators(value: string | null | undefined): string | null | undefined {
    if (!value) return value;
    return value.replace(/[,，]/g, ';');
}

export class ProductSyncService {
    constructor(private odooService: OdooService) {}

    // 同步所有商品数据
    async syncProducts(): Promise<Product[]> {
        try {
            let allConvertedProducts: Product[] = [];
            console.log('[Sync] Starting product sync for all types');

            // 统计信息
            const stats = {
                totalProcessed: 0,
                totalSkipped: 0,
                totalUpdated: 0,
                totalCreated: 0,
                totalVariants: 0,
                byType: {}
            };

            // 遍历所有商品类型
            for (const productType of PRODUCT_TYPES) {
                // 初始化当前类型的统计信息
                stats.byType[productType] = {
                    processed: 0,
                    skipped: 0,
                    updated: 0,
                    created: 0,
                    variants: 0
                };

                try {
                    // 1. 获取商品列表 - 分页逻辑由 OdooService.pullProducts 处理
                    const productsResponse = await this.odooService.pullProducts(productType);

                    // 验证响应数据结构
                    if (!productsResponse?.result?.data) {
                        console.error(`[Sync] Invalid response structure for ${productType}`);
                        continue;
                    }

                    const products = productsResponse.result.data;
                    // Removed verbose log line

                    // 3. 处理每个商品
                    for (const product of products) {
                        try {
                            stats.totalProcessed++;
                            stats.byType[productType].processed++;

                            const converted = convertOdooToProduct(product);
                            stats.totalVariants += converted.variants.length;
                            stats.byType[productType].variants += converted.variants.length;

                            // 跳过变体数量为0且价格为0的商品
                            if (converted.variants.length === 0 && converted.price === 0) {
                                console.log(`[Sync] Skipping product ${converted.sku} with 0 variants and 0 price`);
                                stats.totalSkipped++;
                                stats.byType[productType].skipped++;
                                continue;
                            }

                            // 查找或创建分类
                            let category = await prisma.category.findFirst({
                                where: { name: converted.category }
                            });

                            if (!category) {
                                category = await prisma.category.create({
                                    data: {
                                        name: converted.category,
                                        description: CATEGORY_DEFAULTS.defaultDescription(converted.category)
                                    }
                                });
                            }

                            // 准备商品数据
                            const baseProductData = {
                                name: formatProductName(converted.name),
                                description: converted.description,
                                websiteDescription: converted.websiteDescription,
                                price: converted.price,
                                images: [], // 忽略Odoo的图片URL，使用空数组
                                stock: converted.stock,
                                status: converted.isActive ? PRODUCT_STATUS.ACTIVE : PRODUCT_STATUS.INACTIVE,
                                off_shelve: converted.off_shelve,
                                requiredUID: converted.requiredUID,
                                mcc: standardizeSeparators(converted.mcc),
                                dataSize: converted.dataSize,
                                planType: converted.planType,
                                country: standardizeSeparators(converted.country),
                                countryCode: standardizeSeparators(converted.countryCode),
                                odooLastSyncAt: new Date(),
                                specifications: {
                                    odooId: converted.metadata.odooId,
                                    odooProductCode: converted.metadata.odooProductCode,
                                },
                                category: {
                                    connect: {
                                        id: category.id
                                    }
                                }
                            };

                            // 查找现有商品
                            const existingProduct = await prisma.product.findUnique({
                                where: { sku: converted.sku }
                            });

                            // 更新或创建商品
                            await prisma.product.upsert({
                                where: {
                                    sku: converted.sku
                                },
                                update: {
                                    ...baseProductData,
                                    variants: {
                                        deleteMany: {},
                                        create: converted.variants.map(variant => ({
                                            price: Number(variant.price),
                                            currency: variant.currency || PRODUCT_DEFAULTS.currency,
                                            attributes: variant.attributes,
                                            variantCode: variant.variantCode,
                                            duration: variant.duration,
                                            durationType: variant.durationType
                                        }))
                                    },
                                    parameters: {
                                        deleteMany: {},
                                        create: converted.parameters
                                            .filter(param => param.code !== 'mcc')  // 过滤掉MCC参数
                                            .map(param => ({
                                                code: param.code,
                                                name: param.name,
                                                value: param.value
                                            }))
                                    }
                                },
                                create: {
                                    ...baseProductData,
                                    sku: converted.sku,
                                    variants: {
                                        create: converted.variants.map(variant => ({
                                            price: Number(variant.price),
                                            currency: variant.currency || PRODUCT_DEFAULTS.currency,
                                            attributes: variant.attributes,
                                            variantCode: variant.variantCode,
                                            duration: variant.duration,
                                            durationType: variant.durationType
                                        }))
                                    },
                                    parameters: {
                                        create: converted.parameters
                                            .filter(param => param.code !== 'mcc')  // 过滤掉MCC参数
                                            .map(param => ({
                                                code: param.code,
                                                name: param.name,
                                                value: param.value
                                            }))
                                    }
                                },
                                include: {
                                    variants: true,
                                    parameters: true
                                }
                            });

                            // 更新统计信息
                            if (existingProduct) {
                                stats.totalUpdated++;
                                stats.byType[productType].updated++;
                            } else {
                                stats.totalCreated++;
                                stats.byType[productType].created++;
                            }

                            allConvertedProducts.push(converted);
                        } catch (error) {
                            stats.totalSkipped++;
                            stats.byType[productType].skipped++;
                            // Simplified error logging - only increment counters
                        }
                    }

                    // 每种类型同步完成后输出统计信息
                    const typeStats = stats.byType[productType];
                    console.log(`[Sync] Type=${productType}: processed=${typeStats.processed}, created=${typeStats.created}, updated=${typeStats.updated}, skipped=${typeStats.skipped}, variants=${typeStats.variants}`);

                } catch (error) {
                    // Only log critical errors for product type processing
                    console.error(`[Sync] Error processing product type ${productType}`);
                }
            }

            // 验证同步结果
            const dbProducts = await prisma.product.findMany({
                include: {
                    variants: true,
                    parameters: true
                }
            });

            // 输出总结统计信息
            console.log(`[Sync] Summary: processed=${stats.totalProcessed}, created=${stats.totalCreated}, updated=${stats.totalUpdated}, skipped=${stats.totalSkipped}, variants=${stats.totalVariants}`);
            console.log(`[Sync] Total products in database: ${dbProducts.length}`);

            return allConvertedProducts;
        } catch (error) {
            console.error('[Sync] Error in syncProducts');
            throw error;
        }
    }

    // 获取单个商品数据
    async getProduct(productCode: string): Promise<Product | null> {
        try {
            // 首先从数据库中查找
            const dbProduct = await prisma.product.findFirst({
                where: {
                    OR: [
                        { sku: productCode },
                        {
                            specifications: {
                                path: ['odooProductCode'],
                                equals: productCode
                            }
                        }
                    ]
                },
                include: {
                    category: true,
                    variants: true,
                    parameters: true
                }
            });

            if (dbProduct) {
                // 转换数据库商品为 Product 接口格式
                return {
                    id: dbProduct.id,
                    name: dbProduct.name,
                    description: dbProduct.description,
                    websiteDescription: dbProduct.websiteDescription,
                    price: dbProduct.price,
                    currency: dbProduct.variants[0]?.currency || PRODUCT_DEFAULTS.currency,
                    images: dbProduct.images, // 使用数据库中的图片
                    stock: dbProduct.stock,
                    sku: dbProduct.sku,
                    category: dbProduct.category.name,
                    isActive: dbProduct.status === PRODUCT_STATUS.ACTIVE,
                    off_shelve: dbProduct.off_shelve,
                    requiredUID: dbProduct.requiredUID,
                    mcc: dbProduct.mcc ?? undefined,
                    dataSize: dbProduct.dataSize ?? undefined,
                    planType: dbProduct.planType ?? undefined,
                    country: dbProduct.country ?? undefined,
                    countryCode: dbProduct.countryCode ?? undefined,
                    variants: dbProduct.variants.map((v: any) => ({
                        id: v.id,
                        name: v.name,
                        price: Number(v.price),
                        currency: v.currency,
                        attributes: v.attributes as any,
                        duration: v.duration ?? undefined,
                        durationType: v.durationType ?? undefined,
                        variantCode: v.variantCode ?? v.code ?? undefined
                    })),
                    parameters: dbProduct.parameters.map((p: any) => ({
                        code: p.code,
                        name: p.name,
                        value: p.value
                    })),
                    metadata: {
                        odooId: (dbProduct.specifications as any)?.odooId,
                        odooProductCode: (dbProduct.specifications as any)?.odooProductCode
                    }
                };
            }

            return null;
        } catch (error) {
            console.error(`Error getting product ${productCode}:`, error);
            return null;
        }
    }

    // 更新商品库存
    async updateProductStock(productCode: string): Promise<void> {
        try {
            // 从数据库获取商品类型
            const dbProduct = await prisma.product.findFirst({
                where: {
                    specifications: {
                        path: ['odooProductCode'],
                        equals: productCode
                    }
                },
                include: {
                    category: true
                }
            });

            if (!dbProduct) {
                throw new Error('Product not found');
            }

            // Get the category name to use for product type
            const categoryName = dbProduct.category?.name || 'esim';

            // 从 Odoo 获取最新库存信息
            const productsResponse = await this.odooService.pullProducts(categoryName.toLowerCase());
            const product = productsResponse.result?.data?.find(p => p.product_code === productCode);

            if (product) {
                await prisma.product.updateMany({
                    where: {
                        specifications: {
                            path: ['odooProductCode'],
                            equals: productCode
                        }
                    },
                    data: {
                        stock: PRODUCT_DEFAULTS.stock // Use default stock since Odoo API doesn't provide stock information
                    }
                });
            }
        } catch (error) {
            console.error(`Error updating product stock for ${productCode}:`, error);
            throw error;
        }
    }

    // Note: The processProducts method was removed as it's no longer used
}