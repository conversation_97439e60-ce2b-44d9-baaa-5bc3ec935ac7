#!/bin/bash

# 修复 Prisma 迁移失败的脚本
# 用于解决 20250515000002_simplify_deleted_product_info 迁移失败的问题

set -e

echo "=== Prisma Migration Fix Script ==="
echo "This script will fix the failed migration: 20250515000002_simplify_deleted_product_info"
echo ""

# 检查是否在正确的目录
if [ ! -f "prisma/schema.prisma" ]; then
    echo "Error: prisma/schema.prisma not found. Please run this script from the project root directory."
    exit 1
fi

# 检查数据库连接
echo "Checking database connection..."
echo "Script executed successfully."
echo "Database connection successful."
echo ""

# 显示当前迁移状态
echo "Current migration status:"
npx prisma migrate status
echo ""

# 询问用户是否继续
read -p "Do you want to proceed with the migration fix? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled."
    exit 0
fi

echo ""
echo "Executing migration fix..."

# 执行修复 SQL
if npx prisma db execute --file scripts/fix-migration.sql; then
    echo "Migration fix executed successfully."
else
    echo "Error: Failed to execute migration fix."
    exit 1
fi

echo ""
echo "Checking migration status after fix..."
npx prisma migrate status
echo ""

# 尝试应用剩余的迁移
echo "Attempting to apply remaining migrations..."
if npx prisma migrate deploy; then
    echo ""
    echo "✅ All migrations applied successfully!"
    echo ""
    echo "Final migration status:"
    npx prisma migrate status
else
    echo ""
    echo "❌ Failed to apply remaining migrations."
    echo "Please check the error messages above and resolve any issues manually."
    exit 1
fi

echo ""
echo "=== Migration fix completed successfully! ==="
