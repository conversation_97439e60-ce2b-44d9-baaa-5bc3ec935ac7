{"name": "yolloo-esim-store", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 -p 8000", "build": "node scripts/build.js", "start": "next start -H 0.0.0.0 -p 8000", "lint": "next lint", "create-admin": "node scripts/create-admin.js", "dev:mobile-api": "cd mobile-api && npm run start:dev", "build:mobile-api": "cd mobile-api && npm run build", "start:mobile-api": "cd mobile-api && npm run start", "build:components": "mkdir -p dist/components && cp -r components/ui dist/components/ && echo 'UI components copied to dist/components/ui'", "prisma:seed": "npx prisma db seed"}, "dependencies": {"@auth/prisma-adapter": "^2.7.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@prisma/client": "4.16.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.8", "@react-email/components": "^0.0.34", "@tanstack/react-table": "^8.21.2", "@types/jimp": "^0.2.1", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.8", "@types/qrcode": "^1.5.5", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.3", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cookies-next": "^4.1.0", "critters": "^0.0.23", "csv-parse": "^5.6.0", "date-fns": "^4.1.0", "encoding": "^0.1.13", "framer-motion": "^10.16.4", "google-auth-library": "^9.15.1", "ioredis": "^5.6.1", "jimp": "^1.6.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jsqr": "^1.4.0", "lucide-react": "^0.468.0", "next": "14.2.0", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "prisma": "4.16.2", "qrcode": "^1.5.4", "react": "18.3.0", "react-dom": "18.3.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.54.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-type-animation": "^3.2.0", "recharts": "^2.15.1", "resend": "^4.0.1", "sharp": "^0.33.5", "sonner": "^0.5.0", "stripe": "^17.4.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.2", "zustand": "^5.0.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/node": "20.11.30", "@types/react": "18.2.62", "@types/react-dom": "18.2.19", "autoprefixer": "^10.4.14", "eslint": "8.41.0", "eslint-config-next": "14.2.0", "postcss": "^8.4.31", "prisma": "4.16.2", "tailwindcss": "^3.3.2", "typescript": "5.0.4"}, "packageManager": "npm@10.9.0", "resolutions": {"@prisma/client": "4.16.2", "prisma": "4.16.2"}, "prisma": {"seed": "node prisma/seed.js"}}