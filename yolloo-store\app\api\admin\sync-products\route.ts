import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { createOdooService } from "@/app/services/odooService";
import { ProductSyncService } from "@/app/services/productSyncService";
import { ODOO_CONFIG } from "@/app/config/odoo";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function POST() {
    try {
        // 检查用户权限
        const session = await getServerSession(authOptions);
        if (!session || session.user.role !== "ADMIN") {
            return new NextResponse("Unauthorized", { status: 401 });
        }

        // 创建服务实例
        const odooService = createOdooService(ODOO_CONFIG);
        const productSyncService = new ProductSyncService(odooService);

        // 执行同步
        await productSyncService.syncProducts();

        return NextResponse.json({ message: "Products synced successfully" });
    } catch (error) {
        console.error("[SYNC_PRODUCTS]", error);
        return new NextResponse("Internal error", { status: 500 });
    }
} 