import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { PrismaService } from '../prisma.service';
import { firstValueFrom } from 'rxjs';

export interface SocialUserInfo {
  id: string;
  email: string;
  name: string;
  picture?: string;
  provider: 'google' | 'facebook' | 'apple' | 'wechat';
}

@Injectable()
export class SocialAuthService {
  private readonly logger = new Logger(SocialAuthService.name);

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private httpService: HttpService
  ) {}

  async verifyGoogleToken(token: string): Promise<SocialUserInfo> {
    try {
      const googleClientId = this.configService.get('GOOGLE_CLIENT_ID');
      if (!googleClientId) {
        throw new BadRequestException('Google authentication not configured');
      }

      // 验证Google ID Token
      const response = await firstValueFrom(
        this.httpService.get(
          `https://oauth2.googleapis.com/tokeninfo?id_token=${token}`
        )
      );

      const payload = response.data;

      // 验证token的audience
      if (payload.aud !== googleClientId) {
        throw new BadRequestException('Invalid Google token audience');
      }

      // 验证token是否过期
      if (payload.exp < Date.now() / 1000) {
        throw new BadRequestException('Google token expired');
      }

      return {
        id: payload.sub,
        email: payload.email,
        name: payload.name,
        picture: payload.picture,
        provider: 'google',
      };

    } catch (error) {
      this.logger.error('Google token verification failed:', error);
      throw new BadRequestException('Invalid Google token');
    }
  }

  async verifyFacebookToken(token: string): Promise<SocialUserInfo> {
    try {
      const facebookAppId = this.configService.get('FACEBOOK_APP_ID');
      const facebookAppSecret = this.configService.get('FACEBOOK_APP_SECRET');

      if (!facebookAppId || !facebookAppSecret) {
        throw new BadRequestException('Facebook authentication not configured');
      }

      // 验证Facebook Access Token
      const verifyResponse = await firstValueFrom(
        this.httpService.get(
          `https://graph.facebook.com/debug_token?input_token=${token}&access_token=${facebookAppId}|${facebookAppSecret}`
        )
      );

      const tokenData = verifyResponse.data.data;
      if (!tokenData.is_valid) {
        throw new BadRequestException('Invalid Facebook token');
      }

      // 获取用户信息
      const userResponse = await firstValueFrom(
        this.httpService.get(
          `https://graph.facebook.com/me?fields=id,name,email,picture&access_token=${token}`
        )
      );

      const userData = userResponse.data;

      return {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        picture: userData.picture?.data?.url,
        provider: 'facebook',
      };

    } catch (error) {
      this.logger.error('Facebook token verification failed:', error);
      throw new BadRequestException('Invalid Facebook token');
    }
  }

  async verifyAppleToken(token: string): Promise<SocialUserInfo> {
    try {
      // Apple Sign-In的验证比较复杂，需要验证JWT签名
      // 这里简化处理，实际应用中需要：
      // 1. 获取Apple的公钥
      // 2. 验证JWT签名
      // 3. 验证claims

      const appleTeamId = this.configService.get('APPLE_TEAM_ID');
      const appleClientId = this.configService.get('APPLE_CLIENT_ID');

      if (!appleTeamId || !appleClientId) {
        throw new BadRequestException('Apple authentication not configured');
      }

      // 解码JWT payload (不验证签名，仅用于演示)
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new BadRequestException('Invalid Apple token format');
      }

      const payload = JSON.parse(
        Buffer.from(parts[1], 'base64url').toString()
      );

      // 验证基本claims
      if (payload.aud !== appleClientId) {
        throw new BadRequestException('Invalid Apple token audience');
      }

      if (payload.exp < Date.now() / 1000) {
        throw new BadRequestException('Apple token expired');
      }

      return {
        id: payload.sub,
        email: payload.email,
        name: payload.name || 'Apple User',
        provider: 'apple',
      };

    } catch (error) {
      this.logger.error('Apple token verification failed:', error);
      throw new BadRequestException('Invalid Apple token');
    }
  }

  async verifyWechatCode(code: string): Promise<SocialUserInfo> {
    try {
      const wechatAppId = this.configService.get('WECHAT_APP_ID');
      const wechatAppSecret = this.configService.get('WECHAT_APP_SECRET');

      if (!wechatAppId || !wechatAppSecret) {
        throw new BadRequestException('WeChat authentication not configured');
      }

      // 通过code获取access_token
      const tokenResponse = await firstValueFrom(
        this.httpService.get(
          `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${wechatAppId}&secret=${wechatAppSecret}&code=${code}&grant_type=authorization_code`
        )
      );

      const tokenData = tokenResponse.data;
      if (tokenData.errcode) {
        throw new BadRequestException(`WeChat API error: ${tokenData.errmsg}`);
      }

      // 获取用户信息
      const userResponse = await firstValueFrom(
        this.httpService.get(
          `https://api.weixin.qq.com/sns/userinfo?access_token=${tokenData.access_token}&openid=${tokenData.openid}`
        )
      );

      const userData = userResponse.data;
      if (userData.errcode) {
        throw new BadRequestException(`WeChat API error: ${userData.errmsg}`);
      }

      return {
        id: userData.openid,
        email: `${userData.openid}@wechat.local`, // 微信不提供邮箱
        name: userData.nickname,
        picture: userData.headimgurl,
        provider: 'wechat',
      };

    } catch (error) {
      this.logger.error('WeChat code verification failed:', error);
      throw new BadRequestException('Invalid WeChat code');
    }
  }

  async findOrCreateSocialUser(socialInfo: SocialUserInfo): Promise<any> {
    try {
      // 查找是否已存在社交账号绑定
      let socialAccount = await this.prisma.socialAccount.findUnique({
        where: {
          provider_providerAccountId: {
            provider: socialInfo.provider,
            providerAccountId: socialInfo.id,
          },
        },
        include: {
          user: true,
        },
      });

      if (socialAccount) {
        // 更新社交账号信息
        await this.prisma.socialAccount.update({
          where: { id: socialAccount.id },
          data: {
            name: socialInfo.name,
            email: socialInfo.email,
            picture: socialInfo.picture,
            lastLoginAt: new Date(),
          },
        });

        return socialAccount.user;
      }

      // 检查是否存在相同邮箱的用户
      let user = await this.prisma.user.findUnique({
        where: { email: socialInfo.email },
      });

      if (!user) {
        // 创建新用户
        user = await this.prisma.user.create({
          data: {
            email: socialInfo.email,
            name: socialInfo.name,
            image: socialInfo.picture,
            emailVerified: new Date(), // 社交登录默认邮箱已验证
          },
        });
      }

      // 创建社交账号绑定
      await this.prisma.socialAccount.create({
        data: {
          userId: user.id,
          provider: socialInfo.provider,
          providerAccountId: socialInfo.id,
          name: socialInfo.name,
          email: socialInfo.email,
          picture: socialInfo.picture,
          lastLoginAt: new Date(),
        },
      });

      return user;

    } catch (error) {
      this.logger.error('Failed to find or create social user:', error);
      throw new BadRequestException('Failed to process social login');
    }
  }

  async unlinkSocialAccount(userId: string, provider: string): Promise<void> {
    try {
      const result = await this.prisma.socialAccount.deleteMany({
        where: {
          userId,
          provider,
        },
      });

      if (result.count === 0) {
        throw new BadRequestException('Social account not found');
      }

    } catch (error) {
      this.logger.error('Failed to unlink social account:', error);
      throw new BadRequestException('Failed to unlink social account');
    }
  }

  async getUserSocialAccounts(userId: string): Promise<any[]> {
    try {
      const socialAccounts = await this.prisma.socialAccount.findMany({
        where: { userId },
        select: {
          provider: true,
          name: true,
          email: true,
          picture: true,
          lastLoginAt: true,
        },
      });

      return socialAccounts;

    } catch (error) {
      this.logger.error('Failed to get user social accounts:', error);
      return [];
    }
  }
}
