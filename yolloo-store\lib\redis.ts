import Redis from 'ioredis';

let redis: Redis | null = null;

export function getRedisClient(): Redis {
  if (!redis) {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    redis = new Redis(redisUrl, {
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });

    redis.on('error', (err) => {
      console.error('Redis connection error:', err);
    });

    redis.on('connect', () => {
      console.log('Successfully connected to Redis');
    });
  }

  return redis;
}

export async function setVerificationCode(email: string, code: string, expireSeconds: number = 300): Promise<boolean> {
  try {
    const client = getRedisClient();
    const key = `verification_code:${email}`;
    await client.setex(key, expireSeconds, code);
    return true;
  } catch (error) {
    console.error('Error setting verification code:', error);
    return false;
  }
}

export async function getVerificationCode(email: string): Promise<string | null> {
  try {
    const client = getRedisClient();
    const key = `verification_code:${email}`;
    return await client.get(key);
  } catch (error) {
    console.error('Error getting verification code:', error);
    return null;
  }
}

export async function deleteVerificationCode(email: string): Promise<boolean> {
  try {
    const client = getRedisClient();
    const key = `verification_code:${email}`;
    await client.del(key);
    return true;
  } catch (error) {
    console.error('Error deleting verification code:', error);
    return false;
  }
}

export async function setRateLimit(identifier: string, limit: number, windowSeconds: number): Promise<boolean> {
  try {
    const client = getRedisClient();
    const key = `rate_limit:${identifier}`;
    const current = await client.get(key);
    const count = current ? parseInt(current) : 0;

    if (count >= limit) {
      return false;
    }

    if (count === 0) {
      await client.setex(key, windowSeconds, '1');
    } else {
      await client.incr(key);
    }

    return true;
  } catch (error) {
    console.error('Error setting rate limit:', error);
    return true; // Allow request if Redis fails
  }
}
