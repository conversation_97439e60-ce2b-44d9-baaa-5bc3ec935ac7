import { NextResponse } from 'next/server';
import { z } from 'zod';
import { setVerificationCode, setRateLimit } from '@/lib/redis';
import { sendVerificationCodeEmail } from '@/app/services/emailService';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// 验证码生成函数
function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 获取客户端IP地址
function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

// 输入验证schema
const sendCodeSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { email } = sendCodeSchema.parse(body);

    // 获取客户端IP进行速率限制
    const clientIP = getClientIP(request);
    const rateLimitKey = `send_code:${clientIP}:${email}`;
    
    // 速率限制：每个IP+邮箱组合每分钟最多发送1次，每小时最多5次
    const canSendPerMinute = await setRateLimit(rateLimitKey + ':minute', 1, 60);
    const canSendPerHour = await setRateLimit(rateLimitKey + ':hour', 5, 3600);
    
    if (!canSendPerMinute) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Please wait at least 1 minute before requesting another code' 
        },
        { status: 429 }
      );
    }
    
    if (!canSendPerHour) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Too many verification codes requested. Please try again later' 
        },
        { status: 429 }
      );
    }

    // 生成6位数验证码
    const verificationCode = generateVerificationCode();
    
    // 将验证码存储到Redis，5分钟过期
    const stored = await setVerificationCode(email, verificationCode, 300);
    
    if (!stored) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to generate verification code. Please try again' 
        },
        { status: 500 }
      );
    }

    // 发送验证码邮件
    try {
      const emailResult = await sendVerificationCodeEmail(email, verificationCode, 5);
      
      if (!emailResult.success) {
        console.error('Failed to send verification email:', emailResult.error);
        return NextResponse.json(
          { 
            success: false, 
            error: 'Failed to send verification code. Please try again' 
          },
          { status: 500 }
        );
      }
    } catch (emailError) {
      console.error('Error sending verification email:', emailError);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to send verification code. Please try again' 
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Verification code sent successfully. Please check your email.'
    });

  } catch (error) {
    console.error('Error in send-code API:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid email address' 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
