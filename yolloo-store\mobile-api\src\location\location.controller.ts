import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { LocationService } from './location.service';
import { ReverseGeocodeDto } from './dto/reverse-geocode.dto';
import { Public } from '../common/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller('location')
export class LocationController {
  constructor(private readonly locationService: LocationService) {}

  @Public()
  @Get('reverse-geocode')
  async reverseGeocode(
    @Query() query: ReverseGeocodeDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.locationService.reverseGeocode(query, ctx);
  }
}
