"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Icons } from "@/components/icons"
import { formatPrice, DateFormatter } from "@/lib/utils"
import { OrderActions } from "@/components/order-actions"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

type OrderStatus = "PENDING" | "PAID" | "SHIPPED" | "DELIVERED" | "CANCELLED"

interface OrderItem {
  id: string
  quantity: number
  price: number
  productCode: string
  variantCode?: string | null
  variantText?: string | null
}

interface Order {
  id: string
  status: OrderStatus
  total: number
  createdAt: Date
  items: OrderItem[]
}

interface OrdersResponse {
  orders: Order[]
  total: number
  totalOrders: number
  totalPages: number
  statusCounts: Array<{
    status: OrderStatus
    _count: number
  }>
}

export default function OrdersPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<OrdersResponse | null>(null)
  const [searchInput, setSearchInput] = useState(searchParams.get("search") || "")
  const searchTimeout = useRef<NodeJS.Timeout>()

  const page = Number(searchParams.get("page")) || 1
  const status = (searchParams.get("status") as OrderStatus | "all") || "all"
  const sort = searchParams.get("sort") || "newest"
  const search = searchParams.get("search") || ""

  useEffect(() => {
    async function fetchOrders() {
      try {
        setLoading(true)
        const params = new URLSearchParams({
          page: String(page),
          status,
          sort,
          ...(search && { search }),
        })

        const response = await fetch(`/api/orders?${params}`)
        if (!response.ok) throw new Error("Failed to fetch orders")

        const data = await response.json()
        setData(data)
      } catch (error) {
        console.error("Error fetching orders:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchOrders()
  }, [page, status, sort, search])

  const updateSearchParams = (updates: Record<string, string | null>) => {
    const params = new URLSearchParams(searchParams.toString())
    Object.entries(updates).forEach(([key, value]) => {
      if (value === null) {
        params.delete(key)
      } else {
        params.set(key, value)
      }
    })
    router.push(`/orders?${params.toString()}`)
  }

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Icons.spinner className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (!data) return null

  const { orders, total, totalOrders, totalPages, statusCounts } = data

  const getStatusCount = (status: OrderStatus) => {
    const found = statusCounts.find(s => s.status === status)
    return found ? found._count : 0
  }

  return (
    <div className="container py-8">
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">My Orders</h1>
            <p className="text-muted-foreground">
              View and manage your orders
            </p>
          </div>
          <Button asChild>
            <Link href="/products">Continue Shopping</Link>
          </Button>
        </div>

        <div className="flex gap-4 flex-col md:flex-row">
          <div className="flex-1">
            <Input
              placeholder="Search orders by ID or product name..."
              value={searchInput}
              onChange={(e) => {
                const value = e.target.value
                setSearchInput(value)

                // Clear previous timeout
                if (searchTimeout.current) {
                  clearTimeout(searchTimeout.current)
                }

                // Set new timeout
                searchTimeout.current = setTimeout(() => {
                  updateSearchParams({
                    search: value || null,
                    page: null
                  })
                }, 500) // 500ms delay
              }}
            />
          </div>
          <Select
            value={sort}
            onValueChange={(value) => {
              updateSearchParams({
                sort: value,
                page: null
              })
            }}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="oldest">Oldest First</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="w-full">
          <div className="flex gap-2 border-b pb-2 overflow-x-auto">
            <Button
              variant={status === "all" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => {
                updateSearchParams({
                  status: null,
                  page: null
                })
              }}
            >
              All ({totalOrders})
            </Button>
            <Button
              variant={status === "PENDING" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => {
                updateSearchParams({
                  status: "PENDING",
                  page: null
                })
              }}
            >
              Pending ({getStatusCount("PENDING")})
            </Button>
            <Button
              variant={status === "PAID" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => {
                updateSearchParams({
                  status: "PAID",
                  page: null
                })
              }}
            >
              Paid ({getStatusCount("PAID")})
            </Button>
            <Button
              variant={status === "SHIPPED" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => {
                updateSearchParams({
                  status: "SHIPPED",
                  page: null
                })
              }}
            >
              Shipped ({getStatusCount("SHIPPED")})
            </Button>
            <Button
              variant={status === "DELIVERED" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => {
                updateSearchParams({
                  status: "DELIVERED",
                  page: null
                })
              }}
            >
              Delivered ({getStatusCount("DELIVERED")})
            </Button>
            <Button
              variant={status === "CANCELLED" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => {
                updateSearchParams({
                  status: "CANCELLED",
                  page: null
                })
              }}
            >
              Cancelled ({getStatusCount("CANCELLED")})
            </Button>
          </div>

          <div className="mt-6">
            {orders.length === 0 ? (
              <Card className="p-6">
                <div className="text-center">
                  <Icons.package className="mx-auto h-12 w-12 text-muted-foreground/50" />
                  <p className="mt-4 text-lg font-semibold">No orders found</p>
                  <p className="text-muted-foreground">
                    {search
                      ? "Try adjusting your search terms"
                      : status === "all"
                      ? "You haven't placed any orders yet"
                      : `You don't have any ${status.toLowerCase()} orders`}
                  </p>
                </div>
              </Card>
            ) : (
              <div className="space-y-4">
                {orders.map((order) => (
                  <div
                    key={order.id}
                    className="w-full"
                  >
                    <Card
                      className="group relative p-4 sm:p-6 rounded-xl shadow hover:shadow-lg transition cursor-pointer"
                      onClick={() => router.push(`/orders/${order.id}`)}
                      tabIndex={0}
                      role="button"
                      aria-label={`View details for order ${order.id}`}
                    >
                      {/* 顶部：订单号、下单时间、状态 */}
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-base">Order #{order.id.slice(0, 8)}</span>
                          <span className="text-xs text-muted-foreground">{DateFormatter.forUserSafe(order.createdAt)}</span>
                        </div>
                        <Badge
                          variant={order.status === "DELIVERED" || order.status === "SHIPPED" || order.status === "PAID" ? "success" : order.status === "PENDING" ? "warning" : "destructive"}
                          className="h-6 px-3 text-xs whitespace-nowrap self-end sm:self-auto sm:ml-auto"
                        >
                          {order.status}
                        </Badge>
                      </div>
                      {/* 中部：商品列表 */}
                      <div className="mt-3 flex flex-col gap-2">
                        {order.items.map((item) => {
                          return (
                            <div key={item.id} className="flex items-center gap-3">
                              <div className="relative h-12 w-12 min-w-[3rem] rounded-lg overflow-hidden border bg-gray-100 flex items-center justify-center">
                                <Icons.package className="h-6 w-6 text-gray-400" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <span className="block font-medium truncate text-sm">
                                  {item.variantText || 'Unknown Product'}
                                </span>
                              </div>
                              <span className="text-xs text-muted-foreground">x{item.quantity}</span>
                              <span className="ml-2 font-medium text-sm">{formatPrice(item.price * item.quantity)}</span>
                            </div>
                          );
                        })}
                      </div>
                      {/* 底部：总价和操作按钮 */}
                      <div className="mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 border-t pt-4">
                        <span className="font-bold text-lg text-primary">{formatPrice(order.total)}</span>
                        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto relative z-20" onClick={e => e.stopPropagation()} onKeyDown={e => e.stopPropagation()}>
                          <OrderActions order={order} />
                        </div>
                      </div>
                      {/* 覆盖层，保证整个卡片可点，但不覆盖操作按钮 */}
                      <span className="absolute inset-0 z-10 pointer-events-none" />
                    </Card>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {totalPages > 1 && (
          <div className="flex justify-center">
            <div className="flex items-center gap-2">
              {page > 1 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    updateSearchParams({
                      page: String(page - 1)
                    })
                  }}
                >
                  <Icons.chevronLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>
              )}
              <p className="text-sm">
                Page {page} of {totalPages}
              </p>
              {page < totalPages && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    updateSearchParams({
                      page: String(page + 1)
                    })
                  }}
                >
                  Next
                  <Icons.chevronRight className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}