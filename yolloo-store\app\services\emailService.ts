import { Resend } from 'resend';
import { OrderConfirmationEmail } from '../components/email-templates/OrderConfirmation';
import { PasswordResetEmail } from '../components/email-templates/PasswordReset';
import { WelcomeSubscriptionEmail } from '../components/email-templates/WelcomeSubscription';
import { OrganizationInviteEmail } from '../components/email-templates/OrganizationInvite';
import { VerificationCodeEmail } from '../components/email-templates/VerificationCode';
import React from 'react';
import { format } from 'date-fns';

if (!process.env.RESEND_API_KEY) {
  throw new Error('Missing RESEND_API_KEY environment variable');
}

const resend = new Resend(process.env.RESEND_API_KEY);

export interface EmailData {
  to: string;
  subject: string;
  react?: React.ReactElement;
  html?: string;
  text?: string;
}

export async function sendEmail({ to, subject, react, html, text }: EmailData) {
  try {
    const data = await resend.emails.send({
      from: 'Yolloo Store <<EMAIL>>', // Resend
      to,
      subject,
      react,
      html,
      text,
    });
    return { success: true, data };
  } catch (error) {
    console.error('Email sending failed:', error);
    return { success: false, error };
  }
}

export interface OrderDetails {
  orderId: string;
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  total: number;
}

export async function sendOrderConfirmationEmail(
  to: string,
  customerName: string,
  orderDetails: OrderDetails
) {
  const subject = `Order Confirmation - Yolloo Store`;
  return sendEmail({
    to,
    subject,
    react: React.createElement(OrderConfirmationEmail, { orderDetails, customerName })
  });
}

export async function sendPasswordResetEmail(
  to: string,
  name: string,
  resetLink: string
) {
  const subject = `Reset Your Password - Yolloo Store`;
  return sendEmail({
    to,
    subject,
    react: React.createElement(PasswordResetEmail, { name, resetLink })
  });
}

export async function sendWelcomeSubscriptionEmail(
  to: string,
  discountCode: string
) {
  const subject = `Welcome to Yolloo Store - Your Discount Code`;
  return sendEmail({
    to,
    subject,
    react: React.createElement(WelcomeSubscriptionEmail, { discountCode })
  });
}

export async function sendOrganizationInviteEmail(
  to: string,
  organizationName: string,
  inviteUrl: string,
  expiresAt: Date,
  inviterName?: string,
  isAdmin?: boolean
) {
  const subject = `Invitation to Join ${organizationName} on Yolloo Store`;
  const expireDate = format(expiresAt, 'MMMM dd, yyyy');

  return sendEmail({
    to,
    subject,
    react: React.createElement(OrganizationInviteEmail, {
      organizationName,
      inviteUrl,
      expireDate,
      inviterName,
      isAdmin
    })
  });
}

export async function sendVerificationCodeEmail(
  to: string,
  verificationCode: string,
  expiryMinutes: number = 5
) {
  const subject = `Your verification code for Yolloo Store`;
  return sendEmail({
    to,
    subject,
    react: React.createElement(VerificationCodeEmail, {
      verificationCode,
      expiryMinutes
    })
  });
}