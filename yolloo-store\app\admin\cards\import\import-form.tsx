"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Icons } from "@/components/icons"
import { toast } from "sonner"
import * as XLSX from 'xlsx'

export function ImportForm() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [file, setFile] = useState<File | null>(null)

  const downloadCsvTemplate = () => {
    const header = "UID"
    const csvContent = header + "\n"
    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = "cards-template.csv"
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const downloadExcelTemplate = () => {
    const workbook = XLSX.utils.book_new()
    const wsData = [["UID"]]
    const ws = XLSX.utils.aoa_to_sheet(wsData)
    XLSX.utils.book_append_sheet(workbook, ws, "Cards")
    XLSX.writeFile(workbook, "cards-template.xlsx")
  }

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    if (!file) return

    setIsLoading(true)

    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch("/api/admin/cards/import", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        const error = await response.text()
        throw new Error(error || "Failed to import cards")
      }

      const data = await response.json()
      
      // 显示详细的导入结果
      toast.success(
        `Import completed:
        • Total records: ${data.total}
        • Successfully created: ${data.created}
        • Duplicates in input: ${data.duplicatesInInput}
        • Already in database: ${data.existingInDB}`,
        {
          duration: 5000,
        }
      )
      
      router.refresh()
      router.push("/admin/cards")
    } catch (error) {
      console.error(error)
      toast.error(error instanceof Error ? error.message : "Something went wrong")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={onSubmit} className="space-y-8">
      <div className="space-y-2">
        <Label htmlFor="file">Import File</Label>
        <Input
          id="file"
          type="file"
          accept=".csv,.xlsx,.xls"
          onChange={(e) => setFile(e.target.files?.[0] || null)}
          required
        />
        <p className="text-sm text-muted-foreground">
          Support CSV or Excel file. The file should have a header row with "UID" column.
          Duplicate UIDs and existing UIDs will be skipped automatically.
        </p>
      </div>
      <div className="flex items-center gap-4">
        <Button type="submit" disabled={isLoading}>
          {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
          Import Cards
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={downloadCsvTemplate}
        >
          <Icons.download className="mr-2 h-4 w-4" />
          Download CSV Template
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={downloadExcelTemplate}
        >
          <Icons.download className="mr-2 h-4 w-4" />
          Download Excel Template
        </Button>
      </div>
    </form>
  )
} 