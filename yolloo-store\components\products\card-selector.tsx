"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { toast } from "sonner"
import { Icons } from "@/components/icons"
import { DateFormatter } from "@/lib/utils"

interface YollooCard {
  id: string
  number: string
  status: "Active" | "Inactive" | "Expired"
  type: string
  customName?: string
  activationDate?: string
  expiryDate?: string
}

interface CardSelectorProps {
  onSelect: (uid: string) => void
  selectedUid?: string
}

export function CardSelector({ onSelect, selectedUid }: CardSelectorProps) {
  const { data: session, status } = useSession()
  const [cards, setCards] = useState<YollooCard[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchCards() {
      try {
        const response = await fetch('/api/cards')
        if (response.ok) {
          const data = await response.json()
          setCards(data)
        } else {
          toast.error("Failed to load cards")
        }
      } catch (error) {
        toast.error("Error loading cards")
      } finally {
        setLoading(false)
      }
    }

    if (status === "authenticated") {
      fetchCards()
    }
  }, [status])

  if (status === "loading" || loading) {
    return (
      <div className="flex items-center justify-center h-24">
        <Icons.spinner className="h-6 w-6 animate-spin" />
      </div>
    )
  }

  if (status === "unauthenticated") {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Please sign in to select a card</p>
            <Button className="mt-4" asChild>
              <a href={`/auth/signin?callbackUrl=${encodeURIComponent(window.location.pathname)}`}>
                Sign In
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (cards.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">You don't have any cards yet</p>
            <Button className="mt-4" asChild>
              <a href="/cards">Add a Card</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // 格式化卡号显示，每5位数字后添加一个空格
  const formatCardNumber = (number: string) => {
    return number.replace(/(\d{5})(?=\d)/g, '$1 ');
  };

  return (
    <Card className="border-2 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-xl">Select a Card</CardTitle>
        <CardDescription>Choose a card to bind this eSIM to</CardDescription>
      </CardHeader>
      <CardContent>
        <RadioGroup
          value={selectedUid}
          onValueChange={onSelect}
          className="grid gap-4"
        >
          {cards.map((card) => {
            const isSelected = selectedUid === card.number;
            return (
              <div
                key={card.id}
                className={`
                  relative rounded-xl border-2 p-4 transition-all duration-200 cursor-pointer
                  ${isSelected
                    ? 'border-primary bg-primary/5 shadow-md'
                    : 'border-muted hover:border-primary/30 hover:bg-accent/30'}
                `}
                onClick={() => onSelect(card.number)}
              >
                <div className="absolute right-4 top-4">
                  <RadioGroupItem value={card.number} id={card.id} className="sr-only" />
                  <div className={`
                    w-5 h-5 rounded-full border-2 flex items-center justify-center
                    ${isSelected
                      ? 'border-primary bg-primary text-primary-foreground'
                      : 'border-muted-foreground'}
                  `}>
                    {isSelected && <Icons.check className="h-3 w-3" />}
                  </div>
                </div>

                <div className="flex flex-col space-y-2 pr-6">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                    <div>
                      <Label
                        htmlFor={card.id}
                        className="text-base font-medium cursor-pointer"
                      >
                        {card.customName || `${card.type === "physical" ? "Physical" : "Virtual"} Card`}
                      </Label>
                      <div className="mt-1 font-mono text-sm sm:text-base text-primary font-medium">
                        UID: {formatCardNumber(card.number)}
                      </div>
                    </div>
                    <span className={`
                      text-sm px-3 py-1.5 rounded-full self-start
                      ${card.status === 'Active'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        : card.status === 'Inactive'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'}
                    `}>
                      {card.status}
                    </span>
                  </div>

                  {card.activationDate && (
                    <p className="text-sm text-muted-foreground">
                      Activated: {DateFormatter.forUserSafe(card.activationDate)}
                      {card.expiryDate && ` • Expires: ${DateFormatter.forUserSafe(card.expiryDate)}`}
                    </p>
                  )}
                </div>
              </div>
            );
          })}
        </RadioGroup>
      </CardContent>
    </Card>
  )
}