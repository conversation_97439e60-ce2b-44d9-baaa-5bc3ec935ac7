import { getServerSession } from "next-auth"
import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function POST(
  request: Request,
  { params }: { params: { cardId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const card = await prisma.yollooCard.findFirst({
      where: {
        AND: [
          { id: params.cardId },
          { userId: session.user.id }
        ]
      }
    })

    if (!card) {
      return new NextResponse("Card not found", { status: 404 })
    }

    if (card.status === "Active") {
      return new NextResponse("Card is already active", { status: 400 })
    }

    // Activate the card
    const activatedCard = await prisma.yollooCard.update({
      where: {
        id: card.id
      },
      data: {
        status: "Active",
        activationDate: new Date() // 这里使用new Date()是正确的，因为Prisma会自动处理UTC存储
      },
      include: {
        esims: {
          include: {
            product: true
          }
        }
      }
    })

    return NextResponse.json(activatedCard)
  } catch (error) {
    console.error('[CARD_ACTIVATE]', error)
    return new NextResponse("Internal error", { status: 500 })
  }
}