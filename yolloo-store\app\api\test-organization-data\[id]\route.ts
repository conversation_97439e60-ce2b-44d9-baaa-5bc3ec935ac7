import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export const dynamic = 'force-dynamic';

// GET - Testing API to get specific organization data
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }
    
    const organizationId = params.id;
    
    // Get organization data
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
      include: {
        _count: {
          select: {
            members: true,
            visits: true,
            commissions: true
          }
        }
      }
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Get all organization members
    const members = await prisma.affiliateProfile.findMany({
      where: { organizationId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    const memberIds = members.map(member => member.id);
    
    // Get all referrals by these members
    const referrals = await prisma.affiliateReferral.findMany({
      where: {
        affiliateId: { in: memberIds }
      },
      include: {
        order: true,
        organizationCommission: true
      }
    });
    
    // Get all organization commission records
    const commissions = await prisma.organizationCommission.findMany({
      where: { organizationId },
      include: {
        referrals: true
      }
    });
    
    // Get all visits for this organization
    const visits = await prisma.affiliateVisit.findMany({
      where: { organizationId },
      take: 50
    });
    
    return NextResponse.json({
      organization,
      members: {
        count: members.length,
        data: members.map(m => ({
          id: m.id,
          name: m.user?.name,
          email: m.user?.email
        }))
      },
      referrals: {
        count: referrals.length,
        data: referrals.map(r => ({
          id: r.id,
          orderId: r.orderId,
          amount: r.commissionAmount,
          status: r.status,
          hasOrgCommission: !!r.organizationCommissionId
        }))
      },
      commissions: {
        count: commissions.length,
        data: commissions.map(c => ({
          id: c.id,
          amount: c.commissionAmount,
          status: c.status,
          referralCount: c.referrals.length
        }))
      },
      visits: {
        count: visits.length
      }
    });
  } catch (error) {
    console.error("Error in test API:", error);
    return NextResponse.json(
      { error: "Failed to fetch test data" },
      { status: 500 }
    );
  }
} 