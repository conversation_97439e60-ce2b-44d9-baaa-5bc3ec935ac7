import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// POST - Current user leaves their organization
export async function POST(req: NextRequest) {
  try {
    // 1. Check for authentication
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to perform this action" },
        { status: 401 }
      );
    }
    
    // 2. Get the user's affiliate profile
    const affiliateProfile = await prisma.affiliateProfile.findUnique({
      where: { userId: session.user.id },
      include: {
        organization: {
          select: {
            name: true,
          },
        },
      },
    });
    
    if (!affiliateProfile) {
      return NextResponse.json(
        { error: "You don't have an affiliate profile" },
        { status: 404 }
      );
    }
    
    // 3. Check if the user is in an organization
    if (!affiliateProfile.organizationId) {
      return NextResponse.json(
        { error: "You are not a member of any organization" },
        { status: 400 }
      );
    }
    
    // 4. Check if user is an admin
    if (affiliateProfile.isAdmin) {
      // 检查组织是否有其他管理员
      const otherAdminsCount = await prisma.affiliateProfile.count({
        where: {
          organizationId: affiliateProfile.organizationId,
          isAdmin: true,
          id: { not: affiliateProfile.id },
        },
      });
      
      if (otherAdminsCount === 0) {
        return NextResponse.json(
          { error: "You are the only admin of this organization. Please transfer admin rights or delete the organization instead." },
          { status: 400 }
        );
      }
    }
    
    // 5. Remove user from organization
    const organizationName = affiliateProfile.organization?.name;
    
    const updatedProfile = await prisma.affiliateProfile.update({
      where: { id: affiliateProfile.id },
      data: {
        organizationId: null,
        isAdmin: false,
      },
    });
    
    // 6. Return success
    return NextResponse.json({
      success: true,
      message: `You have successfully left ${organizationName || "the organization"}`,
      profile: updatedProfile,
    });
  } catch (error) {
    console.error("Error leaving organization:", error);
    return NextResponse.json(
      { error: "Failed to leave organization" },
      { status: 500 }
    );
  }
} 