'use client'

import { useCart } from '@/lib/hooks/use-cart'
import { formatPrice } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Trash } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { Input } from '@/components/ui/input'
import { useState, useEffect, ChangeEvent, KeyboardEvent } from 'react'

export function Cart() {
  const { items, removeItem, updateQuantity } = useCart()
  const total = items.reduce((sum, item) => sum + item.price * item.quantity, 0)
  const [quantityInputs, setQuantityInputs] = useState<Record<string, string>>({})

  // Initialize quantity inputs
  useEffect(() => {
    const initialInputs: Record<string, string> = {}
    items.forEach(item => {
      initialInputs[item.id] = item.quantity.toString()
    })
    setQuantityInputs(initialInputs)
  }, [items])

  const handleQuantityInputChange = (e: ChangeEvent<HTMLInputElement>, itemId: string) => {
    // Only allow numeric input
    const value = e.target.value.replace(/[^0-9]/g, '')
    setQuantityInputs(prev => ({ ...prev, [itemId]: value }))
  }

  const handleQuantityInputBlur = (itemId: string, productId: string, stock: number) => {
    const value = quantityInputs[itemId] || '1'
    let newQuantity = parseInt(value, 10)

    // Handle invalid input
    if (isNaN(newQuantity) || value === '') {
      const currentItem = items.find(item => item.id === itemId)
      newQuantity = currentItem ? currentItem.quantity : 1
      setQuantityInputs(prev => ({ ...prev, [itemId]: newQuantity.toString() }))
    } else if (newQuantity > stock) {
      newQuantity = stock
      setQuantityInputs(prev => ({ ...prev, [itemId]: stock.toString() }))
    } else if (newQuantity < 1) {
      newQuantity = 1
      setQuantityInputs(prev => ({ ...prev, [itemId]: '1' }))
    }

    // Find the item to get its variant and uid
    const currentItem = items.find(item => item.id === itemId)
    updateQuantity(productId, newQuantity, currentItem?.variant?.id, currentItem?.uid)
  }

  const handleQuantityInputKeyDown = (e: KeyboardEvent<HTMLInputElement>, itemId: string, productId: string, stock: number) => {
    if (e.key === 'Enter') {
      handleQuantityInputBlur(itemId, productId, stock)
      e.currentTarget.blur()
    }
  }

  if (items.length === 0) {
    return (
      <div className="flex h-[450px] w-full flex-col items-center justify-center space-y-4">
        <h2 className="text-2xl font-bold">Your cart is empty</h2>
        <p className="text-muted-foreground">Add some items to your cart to get started.</p>
        <Link href="/products">
          <Button>Continue Shopping</Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <ul className="space-y-4">
        {items.map((item) => (
          <li key={item.id} className="flex items-center space-x-4 rounded-lg border p-4">
            <div className="relative h-20 w-20 overflow-hidden rounded-md">
              {item.image ? (
                <Image
                  src={item.image}
                  alt={item.name}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center bg-secondary">
                  <span className="text-sm text-muted-foreground">No image</span>
                </div>
              )}
            </div>
            <div className="flex-1 space-y-1">
              <h3 className="font-medium">{item.name}</h3>
              <p className="text-sm">
                {formatPrice(item.price)} × {item.quantity}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  disabled={item.quantity <= 1}
                  onClick={() => {
                    const newQuantity = item.quantity - 1
                    updateQuantity(item.productId, newQuantity, item.variant?.id, item.uid)
                    setQuantityInputs(prev => ({ ...prev, [item.id]: newQuantity.toString() }))
                  }}
                >
                  -
                </Button>
                <div className="w-12 h-8 flex items-center justify-center rounded-md border bg-background overflow-hidden">
                  <Input
                    type="text"
                    value={quantityInputs[item.id] || item.quantity.toString()}
                    onChange={(e) => handleQuantityInputChange(e, item.id)}
                    onBlur={() => handleQuantityInputBlur(item.id, item.productId, item.stock)}
                    onKeyDown={(e) => handleQuantityInputKeyDown(e, item.id, item.productId, item.stock)}
                    className="h-full w-full border-0 text-center p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                    aria-label="Product quantity"
                  />
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  disabled={item.quantity >= item.stock}
                  onClick={() => {
                    const newQuantity = item.quantity + 1
                    updateQuantity(item.productId, newQuantity, item.variant?.id, item.uid)
                    setQuantityInputs(prev => ({ ...prev, [item.id]: newQuantity.toString() }))
                  }}
                >
                  +
                </Button>
              </div>
              <p className="font-medium">
                {formatPrice(item.price * item.quantity)}
              </p>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => removeItem(item.productId, item.variant?.id, item.uid)}
              >
                <Trash className="h-4 w-4" />
                <span className="sr-only">Remove</span>
              </Button>
            </div>
          </li>
        ))}
      </ul>
      <div className="space-y-4 rounded-lg border p-6">
        <div className="flex items-center justify-between text-base font-medium">
          <p>Subtotal</p>
          <p>{formatPrice(total)}</p>
        </div>
        <div className="flex items-center justify-between text-base font-medium">
          <p>Total</p>
          <p>{formatPrice(total)}</p>
        </div>

      </div>
    </div>
  )
}