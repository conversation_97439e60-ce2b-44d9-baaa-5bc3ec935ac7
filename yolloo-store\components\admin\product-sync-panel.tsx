"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Loader2, AlertCircle, CheckCircle2, Plus, X, Save, Edit, Trash2 } from "lucide-react"
import { PRODUCT_TYPES as DEFAULT_PRODUCT_TYPES } from "@/app/config/odoo"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"

interface SyncStats {
  totalProcessed: number
  totalSkipped: number
  totalUpdated: number
  totalCreated: number
  totalVariants: number
  byType: Record<string, {
    processed: number
    skipped: number
    updated: number
    created: number
    variants: number
  }>
  startTime: string
  endTime?: string
  duration?: number
}

interface SyncResult {
  message: string
  stats: SyncStats
  count: number
  errors: string[]
}

export function ProductSyncPanel() {
  const [activeTab, setActiveTab] = useState("standard")

  // 标准同步状态
  const [standardLoading, setStandardLoading] = useState(false)
  const [standardResult, setStandardResult] = useState<SyncResult | null>(null)
  const [standardError, setStandardError] = useState<string | null>(null)
  const [standardSelectedTypes, setStandardSelectedTypes] = useState<string[]>([])
  const [standardCategoryOverride, setStandardCategoryOverride] = useState("")
  const [standardSkipEmptyVariantsAndZeroPrice, setStandardSkipEmptyVariantsAndZeroPrice] = useState(true)

  // QR码同步状态
  const [qrLoading, setQrLoading] = useState(false)
  const [qrResult, setQrResult] = useState<SyncResult | null>(null)
  const [qrError, setQrError] = useState<string | null>(null)
  const [qrSelectedTypes, setQrSelectedTypes] = useState<string[]>([])
  const [qrSkipEmptyVariantsAndZeroPrice, setQrSkipEmptyVariantsAndZeroPrice] = useState(true)

  // 产品类型管理
  const [productTypes, setProductTypes] = useState<string[]>([])
  const [newType, setNewType] = useState("")
  const [editingType, setEditingType] = useState<{index: number, value: string} | null>(null)
  const [isTypesDialogOpen, setIsTypesDialogOpen] = useState(false)

  // 获取当前标签页的状态
  const loading = activeTab === "standard" ? standardLoading : qrLoading
  const result = activeTab === "standard" ? standardResult : qrResult
  const error = activeTab === "standard" ? standardError : qrError
  const selectedTypes = activeTab === "standard" ? standardSelectedTypes : qrSelectedTypes
  const setSelectedTypes = activeTab === "standard" ? setStandardSelectedTypes : setQrSelectedTypes
  const skipEmptyVariantsAndZeroPrice = activeTab === "standard" ? standardSkipEmptyVariantsAndZeroPrice : qrSkipEmptyVariantsAndZeroPrice
  const setSkipEmptyVariantsAndZeroPrice = activeTab === "standard" ? setStandardSkipEmptyVariantsAndZeroPrice : setQrSkipEmptyVariantsAndZeroPrice

  // 初始化产品类型
  useEffect(() => {
    // 从本地存储加载自定义产品类型
    const savedTypes = localStorage.getItem('customProductTypes')
    if (savedTypes) {
      try {
        const parsedTypes = JSON.parse(savedTypes)
        if (Array.isArray(parsedTypes) && parsedTypes.length > 0) {
          setProductTypes(parsedTypes)
          return
        }
      } catch (e) {
        console.error('Failed to parse saved product types:', e)
      }
    }

    // 如果没有保存的类型或解析失败，使用默认类型
    setProductTypes([...DEFAULT_PRODUCT_TYPES])
  }, [])

  // 切换标签页时清除错误
  useEffect(() => {
    if (activeTab === "standard") {
      setStandardError(null)
    } else {
      setQrError(null)
    }
  }, [activeTab])

  // 保存产品类型到本地存储
  const saveProductTypes = (types: string[]) => {
    localStorage.setItem('customProductTypes', JSON.stringify(types))
    setProductTypes(types)
  }

  // 添加新产品类型
  const addProductType = () => {
    if (!newType.trim()) return

    // 检查是否已存在
    if (productTypes.includes(newType.trim())) {
      if (activeTab === "standard") {
        setStandardError(`产品类型 "${newType.trim()}" 已存在`)
      } else {
        setQrError(`产品类型 "${newType.trim()}" 已存在`)
      }
      return
    }

    const updatedTypes = [...productTypes, newType.trim()]
    saveProductTypes(updatedTypes)
    setNewType("")
  }

  // 更新产品类型
  const updateProductType = () => {
    if (!editingType || !editingType.value.trim()) return

    // 检查是否已存在（排除当前编辑的项）
    if (productTypes.some((t, i) => i !== editingType.index && t === editingType.value.trim())) {
      if (activeTab === "standard") {
        setStandardError(`产品类型 "${editingType.value.trim()}" 已存在`)
      } else {
        setQrError(`产品类型 "${editingType.value.trim()}" 已存在`)
      }
      return
    }

    const updatedTypes = [...productTypes]
    updatedTypes[editingType.index] = editingType.value.trim()
    saveProductTypes(updatedTypes)
    setEditingType(null)
  }

  // 删除产品类型
  const deleteProductType = (index: number) => {
    const typeToDelete = productTypes[index]

    // 如果该类型已被选中，从选中列表中移除
    if (standardSelectedTypes.includes(typeToDelete)) {
      setStandardSelectedTypes(standardSelectedTypes.filter(t => t !== typeToDelete))
    }
    if (qrSelectedTypes.includes(typeToDelete)) {
      setQrSelectedTypes(qrSelectedTypes.filter(t => t !== typeToDelete))
    }

    const updatedTypes = productTypes.filter((_, i) => i !== index)
    saveProductTypes(updatedTypes)
  }

  // 重置为默认产品类型
  const resetToDefaultTypes = () => {
    saveProductTypes([...DEFAULT_PRODUCT_TYPES])

    // 更新选中的类型，移除不在默认列表中的类型
    setStandardSelectedTypes(standardSelectedTypes.filter(type => DEFAULT_PRODUCT_TYPES.includes(type)))
    setQrSelectedTypes(qrSelectedTypes.filter(type => DEFAULT_PRODUCT_TYPES.includes(type)))
  }

  const handleTypeToggle = (type: string) => {
    if (selectedTypes.includes(type)) {
      setSelectedTypes(selectedTypes.filter(t => t !== type))
    } else {
      setSelectedTypes([...selectedTypes, type])
    }
  }

  const handleSelectAllTypes = () => {
    if (selectedTypes.length === productTypes.length) {
      setSelectedTypes([])
    } else {
      setSelectedTypes([...productTypes])
    }
  }

  const startStandardSync = async () => {
    setStandardLoading(true)
    setStandardResult(null)
    setStandardError(null)

    try {
      const response = await fetch("/api/admin/products/sync", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          productTypes: standardSelectedTypes.length > 0 ? standardSelectedTypes : undefined,
          categoryOverride: standardCategoryOverride || undefined,
          skipEmptyVariantsAndZeroPrice: standardSkipEmptyVariantsAndZeroPrice,
          // Send complete product type list for server-side recording
          allProductTypes: productTypes
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Synchronization failed")
      }

      const data = await response.json()
      setStandardResult(data)
    } catch (err) {
      setStandardError(err instanceof Error ? err.message : "Unknown error")
    } finally {
      setStandardLoading(false)
    }
  }

  const startQrSync = async () => {
    setQrLoading(true)
    setQrResult(null)
    setQrError(null)

    try {
      const response = await fetch("/api/admin/products/sync-qr-products", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          productTypes: qrSelectedTypes.length > 0 ? qrSelectedTypes : undefined,
          skipEmptyVariantsAndZeroPrice: qrSkipEmptyVariantsAndZeroPrice,
          // Send complete product type list for server-side recording
          allProductTypes: productTypes
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Synchronization failed")
      }

      const data = await response.json()
      setQrResult(data)
    } catch (err) {
      setQrError(err instanceof Error ? err.message : "Unknown error")
    } finally {
      setQrLoading(false)
    }
  }

  // Choose sync function based on current tab
  const startSync = () => {
    if (activeTab === "standard") {
      return startStandardSync()
    } else {
      return startQrSync()
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Product Synchronization</CardTitle>
        <CardDescription>
          Synchronize product data from Odoo to the system
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="standard">Standard Sync</TabsTrigger>
            <TabsTrigger value="qr">QR Code Products Sync</TabsTrigger>
          </TabsList>
          <TabsContent value="standard">
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label>Product Types</Label>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSelectAllTypes}
                    >
                      {selectedTypes.length === productTypes.length ? "Deselect All" : "Select All"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsTypesDialogOpen(true)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Manage Types
                    </Button>
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {productTypes.map(type => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={`type-${type}`}
                        checked={selectedTypes.includes(type)}
                        onCheckedChange={() => handleTypeToggle(type)}
                      />
                      <Label htmlFor={`type-${type}`}>{type}</Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Product type management dialog */}
              <Dialog open={isTypesDialogOpen} onOpenChange={setIsTypesDialogOpen}>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Manage Product Types</DialogTitle>
                    <DialogDescription>
                      Add, edit or delete product types. These types will be saved in your local browser.
                    </DialogDescription>
                  </DialogHeader>

                  <div className="space-y-4 py-4">
                    {/* Add new type */}
                    <div className="flex items-center gap-2">
                      <Input
                        placeholder="Enter new product type"
                        value={newType}
                        onChange={(e) => setNewType(e.target.value)}
                        className="flex-1"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={addProductType}
                        disabled={!newType.trim()}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add
                      </Button>
                    </div>

                    {/* Type list */}
                    <div className="border rounded-md">
                      <ScrollArea className="h-[200px]">
                        <div className="p-2 space-y-2">
                          {productTypes.length === 0 ? (
                            <p className="text-sm text-muted-foreground text-center py-4">
                              No product types
                            </p>
                          ) : (
                            productTypes.map((type, index) => (
                              <div
                                key={`${type}-${index}`}
                                className="flex items-center justify-between p-2 border rounded-md"
                              >
                                {editingType && editingType.index === index ? (
                                  <div className="flex-1 flex items-center gap-2">
                                    <Input
                                      value={editingType.value}
                                      onChange={(e) => setEditingType({
                                        ...editingType,
                                        value: e.target.value
                                      })}
                                      className="flex-1"
                                      autoFocus
                                    />
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={updateProductType}
                                    >
                                      <Save className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => setEditingType(null)}
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </div>
                                ) : (
                                  <>
                                    <span className="flex-1">{type}</span>
                                    <div className="flex items-center gap-1">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setEditingType({
                                          index,
                                          value: type
                                        })}
                                      >
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => deleteProductType(index)}
                                        disabled={productTypes.length <= 1}
                                      >
                                        <Trash2 className="h-4 w-4 text-red-500" />
                                      </Button>
                                    </div>
                                  </>
                                )}
                              </div>
                            ))
                          )}
                        </div>
                      </ScrollArea>
                    </div>
                  </div>

                  <DialogFooter className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={resetToDefaultTypes}
                    >
                      Reset to Default
                    </Button>
                    <Button onClick={() => setIsTypesDialogOpen(false)}>
                      Done
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <div>
                <Label htmlFor="category-override">Force Category Name (Optional)</Label>
                <Input
                  id="category-override"
                  placeholder="Leave empty to use default category"
                  value={standardCategoryOverride}
                  onChange={(e) => setStandardCategoryOverride(e.target.value)}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="skip-empty"
                  checked={standardSkipEmptyVariantsAndZeroPrice}
                  onCheckedChange={(checked) => setStandardSkipEmptyVariantsAndZeroPrice(!!checked)}
                />
                <Label htmlFor="skip-empty">Skip products with 0 variants and 0 price</Label>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="qr">
            <div className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>QR Code Product Synchronization</AlertTitle>
                <AlertDescription>
                  This option will use the Odoo configuration for QR code products and force categorize all products as "qr_code"
                </AlertDescription>
              </Alert>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label>Product Types</Label>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSelectAllTypes}
                    >
                      {selectedTypes.length === productTypes.length ? "Deselect All" : "Select All"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsTypesDialogOpen(true)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Manage Types
                    </Button>
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {productTypes.map(type => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={`qr-type-${type}`}
                        checked={selectedTypes.includes(type)}
                        onCheckedChange={() => handleTypeToggle(type)}
                      />
                      <Label htmlFor={`qr-type-${type}`}>{type}</Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="qr-skip-empty"
                  checked={qrSkipEmptyVariantsAndZeroPrice}
                  onCheckedChange={(checked) => setQrSkipEmptyVariantsAndZeroPrice(!!checked)}
                />
                <Label htmlFor="qr-skip-empty">Skip products with 0 variants and 0 price</Label>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Synchronization Failed</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <div className="mt-4 space-y-4">
            <Alert variant="default" className="bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Synchronization Successful</AlertTitle>
              <AlertDescription className="text-green-700">
                {result.message}
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-sm font-medium">Processed</CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <p className="text-2xl font-bold">{result.stats.totalProcessed}</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-sm font-medium">Created</CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <p className="text-2xl font-bold text-green-600">{result.stats.totalCreated}</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-sm font-medium">Updated</CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <p className="text-2xl font-bold text-blue-600">{result.stats.totalUpdated}</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-sm font-medium">Skipped</CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <p className="text-2xl font-bold text-amber-600">{result.stats.totalSkipped}</p>
                </CardContent>
              </Card>
            </div>

            {result.stats.byType && (
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Statistics by Type</h3>
                {Object.entries(result.stats.byType).map(([type, stats]) => (
                  <div key={type} className="border rounded-md p-3">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{type}</h4>
                      <Badge variant="outline">{stats.processed} products</Badge>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Created: {stats.created}</span>
                        <span>Updated: {stats.updated}</span>
                        <span>Skipped: {stats.skipped}</span>
                        <span>Variants: {stats.variants}</span>
                      </div>
                      <Progress value={(stats.created + stats.updated) / stats.processed * 100} />
                    </div>
                  </div>
                ))}
              </div>
            )}

            {result.errors && result.errors.length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-2">Errors ({result.errors.length})</h3>
                <div className="max-h-40 overflow-y-auto border rounded-md p-2 bg-red-50">
                  {result.errors.map((err, i) => (
                    <p key={i} className="text-sm text-red-600">{err}</p>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button
          onClick={startSync}
          disabled={loading}
          className="w-full"
        >
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {loading ? "Synchronizing..." : "Start Synchronization"}
        </Button>
      </CardFooter>
    </Card>
  )
}
