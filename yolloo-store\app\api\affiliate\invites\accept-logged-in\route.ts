import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for accepting an invite for logged-in user (simplified)
const acceptInviteLoggedInSchema = z.object({
  inviteCode: z.string(),
});

// POST - Accept an invite for logged-in users only
export async function POST(req: NextRequest) {
  try {
    // 1. Check for authentication
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to accept this invitation" },
        { status: 401 }
      );
    }
    
    // 2. Validate the request body
    const body = await req.json();
    const validationResult = acceptInviteLoggedInSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { inviteCode } = validationResult.data;
    
    // 3. Find the invite
    const invite = await prisma.organizationInvite.findUnique({
      where: { inviteCode },
      include: {
        organization: true,
      },
    });
    
    if (!invite) {
      return NextResponse.json({ error: "Invite not found" }, { status: 404 });
    }
    
    // 4. Check if invite is still valid
    if (invite.status !== "PENDING") {
      return NextResponse.json(
        { error: `Invite is ${invite.status.toLowerCase()}` },
        { status: 400 }
      );
    }
    
    // 5. Check if invite has expired
    if (invite.expiresAt && invite.expiresAt < new Date()) {
      // Update invite status to EXPIRED
      await prisma.organizationInvite.update({
        where: { id: invite.id },
        data: { status: "EXPIRED" },
      });
      
      return NextResponse.json(
        { error: "Invite has expired" },
        { status: 400 }
      );
    }
    
    // 6. Check if this is a general invite (no specific email) or if the email matches
    const isGeneralInvite = !invite.email;
    const loggedInEmail = session.user.email;
    
    // If this is not a general invite, check that the logged-in user's email matches the invite email
    if (!isGeneralInvite && invite.email !== loggedInEmail) {
      return NextResponse.json(
        { error: "This invite was sent to a different email address" },
        { status: 403 }
      );
    }
    
    // 7. Check if user has an affiliate profile
    let affiliateProfile = await prisma.affiliateProfile.findFirst({
      where: { userId: session.user.id },
    });
    
    // 8. If user doesn't have an affiliate profile, create one
    if (!affiliateProfile) {
      // Generate a unique affiliate code
      const userName = session.user.name || "User";
      const code = `${userName.substring(0, 3).toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
      
      affiliateProfile = await prisma.affiliateProfile.create({
        data: {
          userId: session.user.id,
          code,
          commissionRate: invite.organization.commissionRate,
          discountRate: invite.organization.discountRate,
        },
      });
    }
    
    // 9. Check if affiliate is already in an organization
    if (affiliateProfile.organizationId) {
      // 如果用户尝试加入的组织就是当前已加入的组织，返回成功
      if (affiliateProfile.organizationId === invite.organizationId) {
        return NextResponse.json({
          success: true,
          message: `You are already a member of ${invite.organization.name}`,
          affiliate: affiliateProfile,
        });
      }
      
      return NextResponse.json(
        { error: "You are already a member of an organization" },
        { status: 400 }
      );
    }
    
    // 10. Add affiliate to organization
    const updatedAffiliate = await prisma.affiliateProfile.update({
      where: { id: affiliateProfile.id },
      data: {
        organizationId: invite.organizationId,
        commissionRate: invite.commissionRate || invite.organization.commissionRate,
        isAdmin: invite.isAdmin || false,
      },
    });
    
    // 12. Return success response
    return NextResponse.json({
      success: true,
      message: `You have successfully joined ${invite.organization.name}`,
      affiliate: updatedAffiliate,
    });
  } catch (error) {
    console.error("Error accepting invite (logged-in flow):", error);
    return NextResponse.json(
      { error: "Failed to accept invite" },
      { status: 500 }
    );
  }
} 