-- 修复失败的迁移脚本
-- 这个脚本用于修复 20250515000002_simplify_deleted_product_info 迁移失败的问题

-- 首先检查当前数据库状态
DO $$
DECLARE
    deleted_product_info_exists BOOLEAN;
    temp_deleted_product_info_exists BOOLEAN;
    deleted_product_id_column_exists BOOLEAN;
BEGIN
    -- 检查 DeletedProductInfo 表是否存在
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'DeletedProductInfo'
    ) INTO deleted_product_info_exists;
    
    -- 检查 TempDeletedProductInfo 表是否存在
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'TempDeletedProductInfo'
    ) INTO temp_deleted_product_info_exists;
    
    -- 检查 OrderItem 表中是否有 deletedProductId 列
    SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'OrderItem' 
        AND column_name = 'deletedProductId'
    ) INTO deleted_product_id_column_exists;
    
    RAISE NOTICE 'Current database state:';
    RAISE NOTICE 'DeletedProductInfo table exists: %', deleted_product_info_exists;
    RAISE NOTICE 'TempDeletedProductInfo table exists: %', temp_deleted_product_info_exists;
    RAISE NOTICE 'OrderItem.deletedProductId column exists: %', deleted_product_id_column_exists;
    
    -- 清理可能存在的临时表
    IF temp_deleted_product_info_exists THEN
        RAISE NOTICE 'Dropping temporary table TempDeletedProductInfo...';
        DROP TABLE "TempDeletedProductInfo";
    END IF;
    
    -- 如果 DeletedProductInfo 表存在但结构不正确，尝试修复
    IF deleted_product_info_exists THEN
        -- 删除可能存在的外键约束
        BEGIN
            ALTER TABLE "OrderItem" DROP CONSTRAINT IF EXISTS "OrderItem_deletedProductId_fkey";
            RAISE NOTICE 'Dropped foreign key constraint if it existed';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Foreign key constraint may not exist or already dropped';
        END;
        
        -- 由于后续迁移会删除整个表，我们直接删除它
        RAISE NOTICE 'Dropping DeletedProductInfo table as it will be removed in subsequent migrations...';
        DROP TABLE IF EXISTS "DeletedProductInfo";
    END IF;
    
    -- 如果 deletedProductId 列存在，删除它
    IF deleted_product_id_column_exists THEN
        RAISE NOTICE 'Dropping deletedProductId column from OrderItem...';
        ALTER TABLE "OrderItem" DROP COLUMN IF EXISTS "deletedProductId";
    END IF;
    
    RAISE NOTICE 'Database cleanup completed successfully';
END $$;

-- 标记失败的迁移为已完成
UPDATE "_prisma_migrations" 
SET finished_at = NOW(), 
    logs = 'Manually resolved migration conflict - cleaned up DeletedProductInfo table and related constraints'
WHERE migration_name = '20250515000002_simplify_deleted_product_info'
AND finished_at IS NULL;

-- 检查迁移状态
SELECT migration_name, started_at, finished_at, logs 
FROM "_prisma_migrations" 
WHERE migration_name LIKE '%deleted_product_info%'
ORDER BY started_at;
