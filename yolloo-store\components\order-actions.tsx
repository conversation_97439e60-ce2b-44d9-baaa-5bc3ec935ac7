"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { toast } from "sonner"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface Order {
  id: string
  status: string
}

interface OrderActionsProps {
  order: Order
}

export function OrderActions({ order }: OrderActionsProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [showDialog, setShowDialog] = useState(false)

  async function cancelOrder() {
    setIsLoading(true)

    try {
      const res = await fetch(`/api/orders/${order.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: "CANCELLED" }),
      })

      if (!res.ok) {
        throw new Error("Failed to cancel order")
      }

      toast.success("Order cancelled successfully")
      window.location.reload()
    } catch (error) {
      toast.error("Failed to cancel order")
    } finally {
      setIsLoading(false)
      setShowDialog(false)
    }
  }

  return (
    <>
      <div className="flex flex-col w-full gap-2 sm:flex-row sm:w-auto">
        {order.status === "PENDING" && (
          <>
            <Button
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                router.push(`/orders/${order.id}`);
              }}
              className="w-full sm:w-auto relative z-20"
            >
              Complete Payment
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={isLoading}
              onClick={(e) => {
                e.stopPropagation();
                setShowDialog(true);
              }}
              className="w-full sm:w-auto relative z-20"
            >
              {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
              Cancel Order
            </Button>
          </>
        )}
      </div>

      <AlertDialog open={showDialog} onOpenChange={setShowDialog}>
        <AlertDialogContent className="z-50">
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently cancel your order.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={cancelOrder}
              disabled={isLoading}
            >
              {isLoading ? "Cancelling..." : "Confirm"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}