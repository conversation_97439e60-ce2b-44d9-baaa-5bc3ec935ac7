const { spawn } = require('child_process');

// 设置更大的内存限制
process.env.NODE_OPTIONS = '--max-old-space-size=8192';

// 设置 Next.js 环境变量
process.env.NEXT_PUBLIC_RENDERING_MODE = 'dynamic';
process.env.NEXT_TELEMETRY_DISABLED = '1';

console.log('Starting build with custom settings...');
console.log(`Node options: ${process.env.NODE_OPTIONS}`);

// 运行 Next.js 构建
const buildProcess = spawn('npx', ['next', 'build'], {
  stdio: 'inherit',
  shell: true,
  env: { ...process.env, NODE_ENV: 'production' }
});

buildProcess.on('error', (err) => {
  console.error('Failed to start build process:', err);
  process.exit(1);
});

buildProcess.on('close', (code) => {
  if (code !== 0) {
    console.error(`Build process exited with code ${code}`);
    process.exit(code);
  }
  console.log('Build completed successfully');
});

// 处理终止信号
process.on('SIGINT', () => {
  buildProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  buildProcess.kill('SIGTERM');
  process.exit(0);
});
