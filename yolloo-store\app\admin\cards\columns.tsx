"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import Link from "next/link"
import { toast } from "sonner"
import { ChevronDown, ChevronUp } from "lucide-react"
import { DateFormatter } from "@/lib/utils"

export type Card = {
  id: string
  number: string
  status: string
  type: string
  activationDate: Date | null
  expiryDate: Date | null
  userId: string | null
  createdAt: Date
  updatedAt: Date
  user: {
    name: string | null
    email: string | null
  } | null
}

const SelectCell = ({ table }: { table: any }) => (
  <Checkbox
    checked={table.getIsAllPageRowsSelected()}
    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
    aria-label="Select all"
  />
)

const SelectRowCell = ({ row }: { row: any }) => (
  <Checkbox
    checked={row.getIsSelected()}
    onCheckedChange={(value) => row.toggleSelected(!!value)}
    aria-label="Select row"
  />
)

const StatusCell = ({ status }: { status: string }) => (
  <Badge
    variant={
      status === "Active"
        ? "success"
        : status === "Inactive"
        ? "secondary"
        : "destructive"
    }
  >
    {status}
  </Badge>
)

// Custom header with sort indicator
const SortableHeader = ({
  column,
  title
}: {
  column: any,
  title: string
}) => {
  return (
    <div
      className="flex items-center cursor-pointer hover:text-accent-foreground"
      onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    >
      {title}
      {column.getIsSorted() === "asc" ? (
        <ChevronUp className="ml-1 h-4 w-4" />
      ) : column.getIsSorted() === "desc" ? (
        <ChevronDown className="ml-1 h-4 w-4" />
      ) : (
        <ChevronUp className="ml-1 h-4 w-4 opacity-0" />
      )}
    </div>
  );
};

async function deleteCard(id: string) {
  try {
    const response = await fetch(`/api/admin/cards/${id}`, {
      method: "DELETE",
    })

    if (!response.ok) {
      throw new Error("Failed to delete card")
    }

    window.location.reload()
    toast.success("Card deleted successfully")
  } catch (error) {
    console.error(error)
    toast.error("Failed to delete card")
  }
}

export const columns: ColumnDef<Card>[] = [
  {
    id: "select",
    header: SelectCell,
    cell: SelectRowCell,
  },
  {
    accessorKey: "number",
    header: ({ column }) => <SortableHeader column={column} title="Card Number" />,
    // Special sorting for card numbers (convert to BigInt for comparison)
    sortingFn: (rowA, rowB) => {
      const numA = rowA.getValue("number") as string;
      const numB = rowB.getValue("number") as string;

      try {
        // Use BigInt to handle 20-digit card numbers
        return BigInt(numA) > BigInt(numB) ? 1 : BigInt(numA) < BigInt(numB) ? -1 : 0;
      } catch {
        // Fallback to string comparison if unable to convert to BigInt
        return numA.localeCompare(numB);
      }
    }
  },
  {
    accessorKey: "type",
    header: ({ column }) => <SortableHeader column={column} title="Type" />,
  },
  {
    accessorKey: "status",
    header: ({ column }) => <SortableHeader column={column} title="Status" />,
    cell: ({ row }) => <StatusCell status={row.original.status} />,
  },
  {
    accessorKey: "user",
    header: ({ column }) => <SortableHeader column={column} title="Bound User" />,
    cell: ({ row }) => {
      const user = row.original.user
      return user ? (
        <div className="flex flex-col">
          <span>{user.name}</span>
          <span className="text-sm text-muted-foreground">{user.email}</span>
        </div>
      ) : (
        <span className="text-muted-foreground">Not bound</span>
      )
    },
    // Custom sorting for user column
    sortingFn: (rowA, rowB) => {
      const userA = rowA.original.user;
      const userB = rowB.original.user;

      // Handle null values
      if (!userA && !userB) return 0;
      if (!userA) return -1;
      if (!userB) return 1;

      // Sort by name, or email if name is null
      const nameA = userA.name || userA.email || "";
      const nameB = userB.name || userB.email || "";

      return nameA.localeCompare(nameB);
    }
  },
  {
    accessorKey: "activationDate",
    header: ({ column }) => <SortableHeader column={column} title="Activation Date" />,
    cell: ({ row }) => {
      const date = row.original.activationDate
      return date ? DateFormatter.short(date) : "Not activated"
    },
    // Custom sorting for dates
    sortingFn: (rowA, rowB) => {
      const dateA = rowA.original.activationDate;
      const dateB = rowB.original.activationDate;

      // Handle null values
      if (!dateA && !dateB) return 0;
      if (!dateA) return -1;
      if (!dateB) return 1;

      return new Date(dateA).getTime() - new Date(dateB).getTime();
    }
  },
  {
    accessorKey: "expiryDate",
    header: ({ column }) => <SortableHeader column={column} title="Expiry Date" />,
    cell: ({ row }) => {
      const date = row.original.expiryDate
      return date ? DateFormatter.short(date) : "No expiry"
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => <SortableHeader column={column} title="Created Date" />,
    cell: ({ row }) => {
      const date = row.original.createdAt
      return date ? DateFormatter.short(date) : "Unknown"
    },
    // Hide this column by default
    enableHiding: true,
    meta: {
      hidden: true
    }
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const card = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <Icons.ellipsis className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem asChild>
              <Link href={`/admin/cards/${card.id}`}>
                <Icons.edit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-red-600"
              onClick={() => deleteCard(card.id)}
            >
              <Icons.trash className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]