import { Test, TestingModule } from '@nestjs/testing';
import { CardsService } from './cards.service';
import { PrismaService } from '../prisma.service';
import { CARD_STATUS } from '../common/constants/app.constants';

describe('CardsService', () => {
  let service: CardsService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    yollooCard: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    esim: {
      findFirst: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CardsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<CardsService>(CardsService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getUserCards', () => {
    it('should return empty cards when no cards exist', async () => {
      mockPrismaService.yollooCard.findMany.mockResolvedValue([]);

      const result = await service.getUserCards('user1');

      expect(result.cards).toHaveLength(0);
      expect(result.cards).toEqual([]);
    });
  });

  describe('registerCard', () => {
    it('should create card with pending activation status', async () => {
      const mockCard = {
        id: 'card1',
        number: '**********',
        status: CARD_STATUS.INACTIVE,
      };

      mockPrismaService.yollooCard.findUnique.mockResolvedValue(null);
      mockPrismaService.yollooCard.create.mockResolvedValue(mockCard);

      const result = await service.registerCard('user1', {
        cardNumber: '**********',
        customName: 'Test Card',
      });

      expect(result.status).toBe(CARD_STATUS.INACTIVE);
      expect(result.message).toContain('请激活后使用');
    });
  });

  describe('activateCard', () => {
    it('should activate a pending card', async () => {
      const mockCard = {
        id: 'card1',
        status: CARD_STATUS.INACTIVE,
      };

      const mockUpdatedCard = {
        id: 'card1',
        number: '**********',
        status: CARD_STATUS.ACTIVE,
        activationDate: new Date(),
        expiryDate: new Date(),
      };

      mockPrismaService.yollooCard.findFirst.mockResolvedValue(mockCard);
      mockPrismaService.yollooCard.update.mockResolvedValue(mockUpdatedCard);

      const result = await service.activateCard('user1', 'card1');

      expect(result.status).toBe(CARD_STATUS.ACTIVE);
      expect(result.message).toBe('卡片激活成功');
    });
  });
});
