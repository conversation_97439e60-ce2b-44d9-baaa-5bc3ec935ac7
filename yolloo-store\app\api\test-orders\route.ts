import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export const dynamic = 'force-dynamic';

// GET - Testing API to get all order records
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }
    
    // Get all affiliate profiles
    const affiliateProfiles = await prisma.affiliateProfile.findMany({
      take: 5
    });
    
    // Get all orders
    const orders = await prisma.order.findMany({
      take: 20,
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    // Get all affiliate referrals
    const referrals = await prisma.affiliateReferral.findMany({
      take: 20,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        affiliate: {
          include: {
            user: true
          }
        },
        order: true
      }
    });
    
    // Get all organization commissions
    const orgCommissions = await prisma.organizationCommission.findMany({
      take: 20,
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return NextResponse.json({
      affiliateProfiles: affiliateProfiles.length,
      orders: orders.length,
      referrals: referrals,
      orgCommissions: orgCommissions.length
    });
  } catch (error) {
    console.error("Error in test API:", error);
    return NextResponse.json(
      { error: "Failed to fetch test data" },
      { status: 500 }
    );
  }
} 