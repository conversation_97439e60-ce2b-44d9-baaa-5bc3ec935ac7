import { Injectable, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

@Injectable()
export class RedisService implements OnModuleDestroy {
  private redisClient: Redis | null = null;
  private isConnected = false;
  private isBuildTime = process.env.NODE_ENV === 'production' && !process.env.IS_RUNTIME;

  constructor(private configService: ConfigService) {
    // 在构建阶段，不尝试连接Redis
    if (this.isBuildTime) {
      console.log('Build time detected, Redis connection deferred until runtime');
      return;
    }

    const redisUrl = this.configService.get<string>('REDIS_URL');
    if (!redisUrl) {
      console.warn('REDIS_URL not found in environment variables. Using default connection.');
    }

    try {
      this.redisClient = new Redis(redisUrl || 'redis://localhost:6379');
      
      this.redisClient.on('error', (err) => {
        console.error('Redis connection error:', err);
        this.isConnected = false;
      });
      
      this.redisClient.on('connect', () => {
        console.log('Successfully connected to Redis');
        this.isConnected = true;
      });
    } catch (error) {
      console.error('Failed to initialize Redis client:', error);
      // 不抛出错误，允许应用继续启动
      this.redisClient = null;
    }
  }

  getClient(): Redis | null {
    return this.redisClient;
  }

  isRedisConnected(): boolean {
    return this.isConnected;
  }

  async get(key: string): Promise<string | null> {
    if (!this.redisClient || this.isBuildTime) {
      return null;
    }
    
    try {
      return await this.redisClient.get(key);
    } catch (error) {
      console.error(`Error getting key ${key} from Redis:`, error);
      return null;
    }
  }

  async set(key: string, value: string, expireSeconds?: number): Promise<boolean> {
    if (!this.redisClient || this.isBuildTime) {
      return false;
    }
    
    try {
      if (expireSeconds) {
        await this.redisClient.setex(key, expireSeconds, value);
      } else {
        await this.redisClient.set(key, value);
      }
      return true;
    } catch (error) {
      console.error(`Error setting key ${key} in Redis:`, error);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    if (!this.redisClient || this.isBuildTime) {
      return false;
    }
    
    try {
      await this.redisClient.del(key);
      return true;
    } catch (error) {
      console.error(`Error deleting key ${key} from Redis:`, error);
      return false;
    }
  }

  async onModuleDestroy() {
    if (this.redisClient) {
      await this.redisClient.quit();
    }
  }
}
