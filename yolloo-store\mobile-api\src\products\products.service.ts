import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { ProductSearchDto } from './dto/product-search.dto';
import { RequestContext } from '../common/interfaces/context.interface';

@Injectable()
export class ProductsService {
  constructor(private prisma: PrismaService) {}

  async getCategories(ctx: RequestContext) {
    // Log the context for demonstration
    console.log('Context in getCategories:', ctx);
    // 从数据库获取产品分类
    const categories = await this.prisma.category.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        image: true,
      },
    });

    // 转换字段名称以匹配API响应格式
    const formattedCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description,
      imageUrl: category.image, // 使用image字段
    }));

    return { categories: formattedCategories };
  }

  async getProductsByCategory(categoryId: string, page = 1, pageSize = 20, ctx: RequestContext) {
    // Log the context for demonstration
    console.log('Context in getProductsByCategory:', ctx);
    const skip = (page - 1) * pageSize;

    // 查询指定分类的产品
    const [products, total] = await Promise.all([
      this.prisma.product.findMany({
        where: { categoryId },
        skip,
        take: pageSize,
        select: {
          id: true,
          name: true,
          description: true,
          price: true,
          images: true,
          dataSize: true,
          planType: true,
          websiteDescription: true,
          specifications: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: 'asc', // 改为升序，让先保存的产品（单一country）显示在前面
        },
      }),
      this.prisma.product.count({
        where: { categoryId },
      }),
    ]);

    // 转换产品数据格式
    const formattedProducts = products.map(product => {
      // 从规格中获取国家信息
      const specs = typeof product.specifications === 'string'
        ? JSON.parse(product.specifications)
        : product.specifications;
      const countries = specs?.countries || [];

      return {
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        currency: ctx.currency, // 使用上下文中的货币
        imageUrl: product.images && product.images.length > 0 ? product.images[0] : null,
        dataSize: product.dataSize || 0,
        planType: product.planType || 'Total',
        duration: 30, // 默认30天，需要从specifications中获取
        countries: countries,
        rating: 0, // 默认评分
        reviewCount: 0, // 默认评论数
      };
    });

    return {
      products: formattedProducts,
      pagination: {
        total,
        page,
        pageSize,
        hasMore: skip + products.length < total,
      },
    };
  }

  async searchProducts(searchDto: ProductSearchDto, ctx: RequestContext) {
    // Log the context for demonstration
    console.log('Context in searchProducts:', ctx);
    const { query, countries, minPrice, maxPrice, dataSize, planType, page = 1, pageSize = 20 } = searchDto;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: any = {};

    if (query) {
      where.OR = [
        { name: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
      ];
    }

    if (minPrice !== undefined) {
      where.price = { ...where.price, gte: minPrice };
    }

    if (maxPrice !== undefined) {
      where.price = { ...where.price, lte: maxPrice };
    }

    if (dataSize !== undefined) {
      where.dataSize = { gte: dataSize };
    }

    if (planType) {
      where.planType = planType;
    }

    if (countries) {
      const countryList = countries.split(',').map(c => c.trim());
      // 使用JSON查询匹配规格中的国家
      where.specifications = {
        path: ['countries'],
        array_contains: countryList,
      };
    }

    // 查询产品
    const [products, total] = await Promise.all([
      this.prisma.product.findMany({
        where,
        skip,
        take: pageSize,
        select: {
          id: true,
          name: true,
          description: true,
          price: true,
          images: true,
          dataSize: true,
          planType: true,
          websiteDescription: true,
          specifications: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: 'asc', // 改为升序，让先保存的产品（单一country）显示在前面
        },
      }),
      this.prisma.product.count({ where }),
    ]);

    // 转换产品数据格式
    const formattedProducts = products.map(product => {
      // 从规格中获取国家信息
      const specs = typeof product.specifications === 'string'
        ? JSON.parse(product.specifications)
        : product.specifications;
      const countries = specs?.countries || [];

      return {
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        currency: ctx.currency, // 使用上下文中的货币
        imageUrl: product.images && product.images.length > 0 ? product.images[0] : null,
        dataSize: product.dataSize || 0,
        planType: product.planType || 'Total',
        duration: 30, // 默认30天，需要从specifications中获取
        countries: countries,
        rating: 0, // 默认评分
        reviewCount: 0, // 默认评论数
      };
    });

    return {
      products: formattedProducts,
      pagination: {
        total,
        page,
        pageSize,
        hasMore: skip + products.length < total,
      },
    };
  }

  async getProductById(productId: string, ctx: RequestContext) {
    // Log the context for demonstration
    console.log('Context in getProductById:', ctx);
    // 查询产品详情
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
      include: {
        variants: true,
        reviews: {
          take: 5,
          include: {
            user: true,
          },
        },
      },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // 获取产品评论的用户信息
    const reviewsWithUserInfo = product.reviews.map(review => ({
      id: review.id,
      rating: review.rating,
      comment: review.comment || '',
      user: {
        name: review.user?.name || 'Anonymous',
        image: review.user?.image || null,
      },
      date: review.createdAt.toISOString(),
    }));

    // 格式化变体数据
    const formattedVariants = product.variants.map(variant => ({
      id: variant.id,
      attributes: variant.attributes || {
        duration: variant.duration || 30,
        dataSize: 0,
      },
      price: Number(variant.price),
      currency: variant.currency,
    }));

    // 从规格中获取国家信息
    const specs = typeof product.specifications === 'string'
      ? JSON.parse(product.specifications)
      : product.specifications;
    const countries = specs?.countries || [];

    // 解析规格信息
    const specifications = product.specifications ?
      (typeof product.specifications === 'string' ?
        JSON.parse(product.specifications) :
        product.specifications) :
      {};

    // 构建产品详情响应
    return {
      id: product.id,
      name: product.name,
      description: product.description,
      fullDescription: product.websiteDescription || product.description,
      price: product.price,
      currency: ctx.currency, // 使用上下文中的货币
      images: product.images || [],
      dataSize: product.dataSize || 0,
      planType: product.planType || 'Total',
      duration: 30, // 默认30天
      countries: countries,
      countryCount: countries.length,
      specifications: {
        network: specifications.network || '4G/5G',
        activation: specifications.activation || '即时',
        provider: specifications.provider || '全球网络',
      },
      variants: formattedVariants,
      reviews: reviewsWithUserInfo,
      rating: 0, // 默认评分
      reviewCount: product.reviews.length,
      activationInstructions: '购买后扫描二维码立即激活您的eSIM。',
    };
  }
}
