import { UserPackagesService } from './user-packages.service';
import { PrismaService } from '../prisma.service';

describe('UserPackagesService', () => {
  let service: UserPackagesService;

  const mockPrismaService = {
    packageUsage: {
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    orderItem: {
      findFirst: jest.fn(),
    },
    product: {
      findFirst: jest.fn(),
    },
    order: {
      count: jest.fn(),
      findMany: jest.fn(),
    },
  };

  beforeEach(() => {
    service = new UserPackagesService(mockPrismaService as any);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getPackageUsageStats', () => {
    it('should return usage statistics for a package', async () => {
      const userId = 'user123';
      const packageId = 'package123';
      const query = { period: 'daily' as const };
      const ctx = { language: 'zh-CN', theme: 'light', currency: 'CNY' };

      const result = await service.getPackageUsageStats(userId, packageId, query, ctx);

      expect(result).toBeDefined();
      // 由于没有真实数据，应该返回错误
      if ('error' in result) {
        expect(result.error).toBe('Package not found');
        expect(result.packageId).toBe(packageId);
      } else {
        // 如果有真实数据，验证结构
        expect(result.packageInfo).toBeDefined();
        expect(result.usageOverview).toBeDefined();
        expect(result.actions).toBeDefined();
      }
    });
  });

  describe('getPackageUsageHistory', () => {
    it('should return usage history for a package', async () => {
      const userId = 'user123';
      const packageId = 'package123';
      const query = { period: 'daily' as const, page: 1, pageSize: 10 };
      const ctx = { language: 'zh-CN', theme: 'light', currency: 'CNY' };

      const result = await service.getPackageUsageHistory(userId, packageId, query, ctx);

      expect(result).toBeDefined();
      expect(result.period).toBe('daily');
      expect(result.chartTitle).toBe('2025/1/26');
      expect(result.chartData).toBeDefined();
      expect(result.chartData).toHaveLength(7);
      expect(result.summary).toBeDefined();
      expect(result.chartConfig).toBeDefined();
    });
  });
});
