"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import axios from "axios"
import Link from "next/link"
import { format } from "date-fns"
import { DateFormatter } from "@/lib/utils"
import { toast } from "sonner"
import {
  Loader2,
  Check,
  MoreHorizontal,
  Eye,
  Trash,
  Settings,
  FileText,
  Plus,
  ChevronUp,
  ChevronDown
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import { Icons } from "@/components/icons"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Constants
const ITEMS_PER_PAGE = 10

const USER_ROLE_MAP = {
  ADMIN: { label: "Admin", variant: "destructive" as const },
  CUSTOMER: { label: "Customer", variant: "secondary" as const },
  STAFF: { label: "Staff", variant: "outline" as const },
} as const

// Types
interface User {
  id: string
  name: string | null
  email: string
  image: string | null
  role: string
  createdAt: Date
  emailVerified: Date | null
  hashedPassword: string | null
  lastLoginTime: Date | null
  lastLoginMethod: string | null
  lastLoginIp: string | null
  accounts: {
    provider: string
  }[] | null
  affiliate?: {
    id: string
    organization?: {
      id: string
      name: string
    } | null
  } | null
}

export default function UsersPage() {
  // State for users
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // State for search and filters
  const [searchQuery, setSearchQuery] = useState("")
  const [roleFilter, setRoleFilter] = useState("all")

  // State for sorting
  const [sortField, setSortField] = useState<string>("createdAt")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")

  // State for add user dialog
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    password: "",
    role: "CUSTOMER"
  })
  const [isCreating, setIsCreating] = useState(false)

  // State for delete dialog
  const [userToDelete, setUserToDelete] = useState<User | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  // Router for navigation
  const router = useRouter()

  // Fetch users on initial load
  useEffect(() => {
    fetchUsers()
  }, [])

  // Apply filters when search or role filter changes
  useEffect(() => {
    filterUsers()
  }, [searchQuery, roleFilter, users, sortField, sortDirection])

  // Update pagination when filtered users change
  useEffect(() => {
    setTotalPages(Math.ceil(filteredUsers.length / ITEMS_PER_PAGE))
    setCurrentPage(1) // Reset to first page when filters change
  }, [filteredUsers])

  // Handle sorting when a column header is clicked
  const handleSort = (field: string) => {
    // If clicking the same field, toggle direction
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // If clicking a new field, set it as the sort field and default to ascending
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const fetchUsers = async () => {
    try {
      setIsLoading(true)
      const response = await axios.get("/api/admin/users")

      // Handle different response structures
      if (Array.isArray(response.data)) {
        // Direct array of users
        setUsers(response.data)
      } else if (response.data && typeof response.data === 'object') {
        // Could be { users: [...] } or other structure
        if (Array.isArray(response.data.users)) {
          setUsers(response.data.users)
        } else if (Array.isArray(response.data.data)) {
          setUsers(response.data.data)
        } else {
          // Try to extract user array from object
          const possibleUserArrays = Object.values(response.data).filter(Array.isArray)
          if (possibleUserArrays.length > 0) {
            // Use the first array found
            setUsers(possibleUserArrays[0])
          } else {
            console.warn("Could not find user array in API response:", response.data)
            setUsers([])
          }
        }
      } else {
        console.warn("API response format unexpected:", response.data)
        setUsers([])
      }
    } catch (error) {
      console.error("Error fetching users:", error)
      toast.error("Failed to load users")
      setUsers([])
    } finally {
      setIsLoading(false)
    }
  }

  const filterUsers = () => {
    if (!Array.isArray(users)) {
      setFilteredUsers([])
      return
    }

    let filtered = [...users]

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        user =>
          (user.name?.toLowerCase().includes(query)) ||
          user.email.toLowerCase().includes(query) ||
          user.id.toLowerCase().includes(query)
      )
    }

    // Apply role filter
    if (roleFilter && roleFilter !== "all") {
      filtered = filtered.filter(user => user.role === roleFilter)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let valueA: any;
      let valueB: any;

      // Determine values to compare based on sort field
      switch (sortField) {
        case "name":
          valueA = a.name || "";
          valueB = b.name || "";
          break;
        case "email":
          valueA = a.email;
          valueB = b.email;
          break;
        case "role":
          valueA = a.role;
          valueB = b.role;
          break;
        case "affiliate":
          valueA = a.affiliate ? "Yes" : "No";
          valueB = b.affiliate ? "Yes" : "No";
          break;
        case "organization":
          valueA = a.affiliate?.organization?.name || "";
          valueB = b.affiliate?.organization?.name || "";
          break;
        case "lastLogin":
          valueA = a.lastLoginTime ? new Date(a.lastLoginTime).getTime() : 0;
          valueB = b.lastLoginTime ? new Date(b.lastLoginTime).getTime() : 0;
          break;
        case "password":
          valueA = a.hashedPassword ? "Set" : "Not Set";
          valueB = b.hashedPassword ? "Set" : "Not Set";
          break;
        case "loginMethod":
          valueA = a.accounts?.some(account => account.provider === "google") ? "Google" : "Email";
          valueB = b.accounts?.some(account => account.provider === "google") ? "Google" : "Email";
          break;
        case "createdAt":
          valueA = new Date(a.createdAt).getTime();
          valueB = new Date(b.createdAt).getTime();
          break;
        default:
          valueA = new Date(a.createdAt).getTime();
          valueB = new Date(b.createdAt).getTime();
      }

      // Compare values based on sort direction
      if (sortDirection === "asc") {
        if (typeof valueA === "string" && typeof valueB === "string") {
          return valueA.localeCompare(valueB);
        }
        return valueA - valueB;
      } else {
        if (typeof valueA === "string" && typeof valueB === "string") {
          return valueB.localeCompare(valueA);
        }
        return valueB - valueA;
      }
    });

    setFilteredUsers(filtered)
  }

  const getPaginatedUsers = () => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
    const endIndex = startIndex + ITEMS_PER_PAGE
    return filteredUsers.slice(startIndex, endIndex)
  }

  const handleCreateUser = async () => {
    try {
      setIsCreating(true)

      // Validate form
      if (!newUser.name || !newUser.email || !newUser.password) {
        toast.error("Please fill in all required fields")
        return
      }

      const response = await axios.post("/api/admin/users", newUser)

      // Add the new user to the list with appropriate defaults for the new fields
      const createdUser = {
        ...response.data,
        hashedPassword: 'set', // Indicate that password is set
        lastLoginTime: null,
        accounts: [] // No accounts initially
      }

      setUsers(prevUsers => [createdUser, ...prevUsers])

      // Reset form and close dialog
      setNewUser({
        name: "",
        email: "",
        password: "",
        role: "CUSTOMER"
      })
      setIsAddDialogOpen(false)

      toast.success("User created successfully")
    } catch (error: any) {
      console.error("Error creating user:", error)
      toast.error(error.response?.data?.message || "Failed to create user")
    } finally {
      setIsCreating(false)
    }
  }

  const handleUpdateUserRole = async (userId: string, role: string) => {
    try {
      const response = await axios.patch(`/api/admin/users/${userId}`, { role })

      // Update user in the list
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === userId ? { ...user, role } : user
        )
      )

      toast.success("User role updated successfully")
    } catch (error) {
      console.error("Error updating user role:", error)
      toast.error("Failed to update user role")
    }
  }

  const handleDeleteUser = async () => {
    if (!userToDelete) return

    try {
      setIsDeleting(true)
      await axios.delete(`/api/admin/users/${userToDelete.id}`)

      // Remove user from the list
      setUsers(prevUsers =>
        prevUsers.filter(user => user.id !== userToDelete.id)
      )

      setUserToDelete(null)
      toast.success("User deleted successfully")
    } catch (error) {
      console.error("Error deleting user:", error)
      toast.error("Failed to delete user")
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Users</h2>
        <p className="text-muted-foreground">
            Manage users and their permissions.
        </p>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative w-full max-w-sm">
          <FileText className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search by name, email or ID..."
            className="pl-8 w-full"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex items-center space-x-2 w-full sm:w-auto">
          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <Settings className="mr-2 h-4 w-4" />
                <span>{roleFilter === "all" ? "All Roles" : USER_ROLE_MAP[roleFilter as keyof typeof USER_ROLE_MAP]?.label || roleFilter}</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="ADMIN">Admin</SelectItem>
              <SelectItem value="CUSTOMER">Customer</SelectItem>
              <SelectItem value="STAFF">Staff</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="icon"
            onClick={() => {
              setSearchQuery("")
              setRoleFilter("all")
            }}
            disabled={!searchQuery && roleFilter === "all"}
          >
            <Icons.refresh className="h-4 w-4" />
            <span className="sr-only">Reset filters</span>
          </Button>
        </div>
                  </div>

      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex justify-center items-center h-[400px]">
              <Loader2 className="h-8 w-8 animate-spin" />
                </div>
          ) : filteredUsers.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-[400px] text-center p-8">
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-muted">
                <Icons.user className="h-10 w-10 text-muted-foreground" />
              </div>
              <h3 className="mt-4 text-lg font-semibold">No users found</h3>
              <p className="mb-4 mt-2 text-sm text-muted-foreground max-w-xs">
                {searchQuery || roleFilter !== "all"
                  ? "No users match your search criteria. Try adjusting your filters."
                  : "No users have been created yet. Create your first user to get started."}
              </p>
              {searchQuery || roleFilter !== "all" ? (
                <Button
                  onClick={() => {
                    setSearchQuery("")
                    setRoleFilter("all")
                  }}
                >
                  Reset Filters
                </Button>
              ) : (
                <Button onClick={() => setIsAddDialogOpen(true)}>Create User</Button>
              )}
            </div>
          ) : (
              <div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("name")}
                    >
                      <div className="flex items-center">
                        User
                        {sortField === "name" ? (
                          sortDirection === "asc" ? (
                            <ChevronUp className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          )
                        ) : (
                          <ChevronUp className="ml-1 h-4 w-4 opacity-0" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("email")}
                    >
                      <div className="flex items-center">
                        Email
                        {sortField === "email" ? (
                          sortDirection === "asc" ? (
                            <ChevronUp className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          )
                        ) : (
                          <ChevronUp className="ml-1 h-4 w-4 opacity-0" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("role")}
                    >
                      <div className="flex items-center">
                        Role
                        {sortField === "role" ? (
                          sortDirection === "asc" ? (
                            <ChevronUp className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          )
                        ) : (
                          <ChevronUp className="ml-1 h-4 w-4 opacity-0" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("affiliate")}
                    >
                      <div className="flex items-center">
                        Affiliate
                        {sortField === "affiliate" ? (
                          sortDirection === "asc" ? (
                            <ChevronUp className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          )
                        ) : (
                          <ChevronUp className="ml-1 h-4 w-4 opacity-0" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("organization")}
                    >
                      <div className="flex items-center">
                        Organization
                        {sortField === "organization" ? (
                          sortDirection === "asc" ? (
                            <ChevronUp className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          )
                        ) : (
                          <ChevronUp className="ml-1 h-4 w-4 opacity-0" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("lastLogin")}
                    >
                      <div className="flex items-center">
                        Last Login
                        {sortField === "lastLogin" ? (
                          sortDirection === "asc" ? (
                            <ChevronUp className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          )
                        ) : (
                          <ChevronUp className="ml-1 h-4 w-4 opacity-0" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("password")}
                    >
                      <div className="flex items-center">
                        Password
                        {sortField === "password" ? (
                          sortDirection === "asc" ? (
                            <ChevronUp className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          )
                        ) : (
                          <ChevronUp className="ml-1 h-4 w-4 opacity-0" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("loginMethod")}
                    >
                      <div className="flex items-center">
                        Login Method
                        {sortField === "loginMethod" ? (
                          sortDirection === "asc" ? (
                            <ChevronUp className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          )
                        ) : (
                          <ChevronUp className="ml-1 h-4 w-4 opacity-0" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-10">
                        <div className="flex justify-center">
                          <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                        </div>
                        <div className="mt-2 text-sm text-gray-500">Loading users...</div>
                      </TableCell>
                    </TableRow>
                  ) : getPaginatedUsers().length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-10">
                        <div className="text-sm text-gray-500">No users found</div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    getPaginatedUsers().map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage src={user.image || undefined} />
                              <AvatarFallback>
                                {user.name?.substring(0, 2) || user.email.substring(0, 2)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{user.name || "Anonymous"}</div>
                              <div className="text-xs text-gray-500 truncate w-32" title={user.id}>
                                ID: {user.id.substring(0, 8)}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Badge variant={USER_ROLE_MAP[user.role as keyof typeof USER_ROLE_MAP]?.variant || "outline"}>
                            {USER_ROLE_MAP[user.role as keyof typeof USER_ROLE_MAP]?.label || user.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {user.affiliate ? (
                            <Badge variant="success">Yes</Badge>
                          ) : (
                            <Badge variant="outline">No</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {user.affiliate?.organization ? (
                            <Badge variant="secondary">{user.affiliate.organization.name}</Badge>
                          ) : (
                            ""
                          )}
                        </TableCell>
                        <TableCell>
                          {user.lastLoginTime ? (
                            <div>
                              <div>{DateFormatter.withTimezone(user.lastLoginTime)}</div>
                              {user.lastLoginIp && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  IP: {user.lastLoginIp}
                                </div>
                              )}
                            </div>
                          ) : (
                            <span className="text-muted-foreground">Never</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {user.hashedPassword ? (
                            <Badge variant="success">Set</Badge>
                          ) : (
                            <Badge variant="destructive">Not Set</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {user.accounts?.some(account => account.provider === "google") ? (
                            <Badge variant="secondary" className="flex items-center gap-1">
                              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                              </svg>
                              Google
                            </Badge>
                          ) : (
                            <Badge variant="outline">Email</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/admin/users/${user.id}`)}
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Actions</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem disabled={user.role === "ADMIN"} onSelect={() => handleUpdateUserRole(user.id, "ADMIN")}>
                                  {user.role === "ADMIN" && <Check className="mr-2 h-4 w-4" />}
                                  Make Admin
                                </DropdownMenuItem>
                                <DropdownMenuItem disabled={user.role === "STAFF"} onSelect={() => handleUpdateUserRole(user.id, "STAFF")}>
                                  {user.role === "STAFF" && <Check className="mr-2 h-4 w-4" />}
                                  Make Staff
                                </DropdownMenuItem>
                                <DropdownMenuItem disabled={user.role === "CUSTOMER"} onSelect={() => handleUpdateUserRole(user.id, "CUSTOMER")}>
                                  {user.role === "CUSTOMER" && <Check className="mr-2 h-4 w-4" />}
                                  Make Customer
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-destructive focus:text-destructive"
                                  onSelect={() => setUserToDelete(user)}
                                >
                                  <Trash className="mr-2 h-4 w-4" />
                                  Delete User
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-center space-x-2 py-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <div className="text-sm">
                    Page {currentPage} of {totalPages}
                  </div>
                <Button
                    variant="outline"
                  size="sm"
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                >
                    Next
                </Button>
              </div>
              )}
            </div>
          )}
        </CardContent>
          </Card>

      {/* Add User Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
            <DialogDescription>
              Create a new user account with defined permissions.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                placeholder="Enter full name"
                value={newUser.name}
                onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
              />
      </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={newUser.email}
                onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="Create a password"
                value={newUser.password}
                onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
              />
              <p className="text-xs text-muted-foreground">
                Password must be at least 8 characters long.
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="role">Role</Label>
              <Select
                value={newUser.role}
                onValueChange={(value) => setNewUser({ ...newUser, role: value })}
              >
                <SelectTrigger id="role">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ADMIN">Admin</SelectItem>
                  <SelectItem value="STAFF">Staff</SelectItem>
                  <SelectItem value="CUSTOMER">Customer</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateUser} disabled={isCreating}>
              {isCreating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Confirmation */}
      <AlertDialog open={!!userToDelete} onOpenChange={(open) => !open && setUserToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete User</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {userToDelete?.name || "this user"}? This action cannot be undone and all associated data will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteUser}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete User
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}