"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus } from "lucide-react"
import { toast } from "sonner"

export function AddCardDialog() {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [uid, setUid] = useState("")
  const [displayUid, setDisplayUid] = useState("")
  const [uidError, setUidError] = useState<string>("")

  // Format UID with hyphens after every 5 digits for display
  const formatUid = (uid: string): string => {
    // Remove all non-digit characters
    const digitsOnly = uid.replace(/\D/g, '')
    
    // Add hyphens after every 5 digits
    return digitsOnly.replace(/(\d{5})(?=\d)/g, '$1-')
  }

  const validateUid = (uid: string) => {
    // Clear previous error
    setUidError("")
    
    // Remove any hyphens for validation
    const cleanedUid = uid.replace(/-/g, '')
    
    // Check if empty
    if (!cleanedUid) {
      setUidError("UID cannot be empty")
      return false
    }
    
    // Check if contains only numbers
    if (!/^\d+$/.test(cleanedUid)) {
      setUidError("UID must contain only numbers")
      return false
    }
    
    // Check length (must be exactly 20 digits)
    if (cleanedUid.length !== 20) {
      setUidError("UID must be exactly 20 digits")
      return false
    }
    
    return true
  }

  const handleUidChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value
    
    // Only allow digits (0-9) in the input
    // This will prevent spaces, special characters, and other non-digit inputs
    const digitsOnly = inputValue.replace(/\D/g, '')
    
    // Limit to 20 digits maximum
    const limitedDigits = digitsOnly.slice(0, 20)
    
    // Store the raw value (digits only)
    setUid(limitedDigits)
    
    // Format for display with hyphens after every 5 digits
    const formattedValue = formatUid(limitedDigits)
    setDisplayUid(formattedValue)
    
    // Validate the raw value
    validateUid(limitedDigits)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!uid || !validateUid(uid)) return

    try {
      setLoading(true)
      const response = await fetch("/api/cards/bind", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ uid }),
      })

      if (!response.ok) {
        const error = await response.text()
        throw new Error(error)
      }

      toast.success("Card bound successfully")
      setOpen(false)
      // 刷新页面以显示新卡
      window.location.reload()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to bind card")
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add New Card
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add New Card</DialogTitle>
          <DialogDescription>
            Enter your card UID to bind it to your account
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="uid">Card UID</Label>
              <Input
                id="uid"
                placeholder="Enter card number (numbers only, must be 20 digits)"
                value={displayUid}
                onChange={handleUidChange}
                disabled={loading}
                className={uidError ? "border-destructive" : ""}
              />
              {uidError && (
                <p className="text-sm text-destructive">
                  {uidError}
                </p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              disabled={loading || !uid || !!uidError}
            >
              {loading ? "Binding..." : "Bind Card"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 