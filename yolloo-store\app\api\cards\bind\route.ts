import { getServerSession } from "next-auth"
import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const json = await request.json()
    const { uid } = json

    if (!uid) {
      return new NextResponse("UID is required", { status: 400 })
    }

    // Find the card
    const card = await prisma.yollooCard.findUnique({
      where: {
        number: uid,
      }
    })

    if (!card) {
      return new NextResponse("Card not found", { status: 404 })
    }

    // Check if card is already bound to a user
    if (card.userId) {
      return new NextResponse("Card is already bound to a user", { status: 400 })
    }

    // Bind the card to the user
    const updatedCard = await prisma.yollooCard.update({
      where: {
        id: card.id
      },
      data: {
        userId: session.user.id,
        status: "Active"
      },
      include: {
        esims: {
          include: {
            product: true
          }
        }
      }
    })

    return NextResponse.json(updatedCard)
  } catch (error) {
    console.error('[CARD_BIND]', error)
    return new NextResponse("Internal error", { status: 500 })
  }
}