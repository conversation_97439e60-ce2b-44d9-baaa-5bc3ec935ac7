import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for organization update
const updateOrgSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters").optional(),
  description: z.string().optional(),
  logo: z.string().optional(),
  commissionRate: z.number().min(0).max(1).optional(),
  discountRate: z.number().min(0).max(1).optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED"]).optional(),
});

// Helper function to check if user has access to the organization
async function hasAccess(userId: string, organizationId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { 
      role: true,
      affiliate: {
        select: {
          id: true,
          organizationId: true,
          isAdmin: true,
        },
      },
    },
  });

  if (!user) return false;
  
  // Admin has access to all organizations
  if (user.role === "ADMIN") return true;
  
  // Organization admin has access to their organization
  if (user.affiliate?.organizationId === organizationId && user.affiliate.isAdmin) {
    return true;
  }
  
  return false;
}

// GET - Get organization details
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const organizationId = params.id;
    
    // Get the affiliate profile of the current user
    const affiliateProfile = await prisma.affiliateProfile.findUnique({
      where: { userId: session.user.id },
      include: {
        organization: true,
      },
    });
    
    // Get the organization
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
      include: {
        _count: {
          select: { members: true },
        },
        // Include members who are admins
        members: {
          where: {
            isAdmin: true
          },
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        }
      },
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Check if the user is a member of this organization
    const isMember = await prisma.affiliateProfile.findFirst({
      where: {
        userId: session.user.id,
        organizationId: organizationId,
      },
    });
    
    // Check if the user is an admin of this organization
    const isAdmin = isMember?.isAdmin || false;
    
    // Map administrators to a simpler format
    const administrators = organization.members.map(admin => ({
      id: admin.user.id,
      name: admin.user.name,
      email: admin.user.email,
      image: admin.user.image
    }));
    
    // Return limited information about the organization
    return NextResponse.json({
      organization: {
        id: organization.id,
        name: organization.name,
        description: organization.description,
        logo: organization.logo,
        code: organization.code,
        commissionRate: organization.commissionRate,
        discountRate: organization.discountRate,
        status: organization.status,
        createdAt: organization.createdAt,
        _count: organization._count,
        administrators: administrators
      },
      isMember: !!isMember,
      isAdmin: isAdmin,
    });
  } catch (error) {
    console.error("[ORGANIZATION_GET]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

// PATCH - Update organization
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const organizationId = params.id;
    
    // Check if user has access to update this organization
    const hasPermission = await hasAccess(session.user.id, organizationId);
    
    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validationResult = updateOrgSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    // Update organization
    const updatedOrganization = await prisma.affiliateOrganization.update({
      where: { id: organizationId },
      data: validationResult.data,
    });
    
    return NextResponse.json(updatedOrganization);
  } catch (error) {
    console.error("Error updating organization:", error);
    return NextResponse.json(
      { error: "Failed to update organization" },
      { status: 500 }
    );
  }
}

// DELETE - Delete organization (admin only)
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only admin can delete organizations
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }
    
    const organizationId = params.id;
    
    // First update all members to remove organization association
    await prisma.affiliateProfile.updateMany({
      where: { organizationId },
      data: {
        organizationId: null,
        isAdmin: false,
      },
    });
    
    // Delete the organization
    await prisma.affiliateOrganization.delete({
      where: { id: organizationId },
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting organization:", error);
    return NextResponse.json(
      { error: "Failed to delete organization" },
      { status: 500 }
    );
  }
} 