"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import axios from "axios";
import { Loader2 } from "lucide-react";
import { OrganizationIcons } from "@/components/organization-icons";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { format } from "date-fns"
import { DateFormatter } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

// Define the form schema for creating a withdrawal request
const withdrawalSchema = z.object({
  amount: z.number().min(1, "Amount must be at least 1").max(100000, "Amount cannot exceed 100,000"),
  paymentMethod: z.string().min(1, "Payment method is required"),
  paymentDetails: z.string().min(3, "Payment details are required"),
});

type WithdrawalFormValues = z.infer<typeof withdrawalSchema>;

// Define types for the withdrawal data
interface Withdrawal {
  id: string;
  amount: number;
  status: "PENDING" | "APPROVED" | "REJECTED";
  paymentMethod: string;
  paymentDetails: string;
  createdAt: string;
  updatedAt: string;
  processedAt?: string;
  notes?: string;
}

interface Organization {
  id: string;
  name: string;
  totalEarnings: number;
  availableBalance: number;
}

export default function OrganizationWithdrawalsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [withdrawals, setWithdrawals] = useState<Withdrawal[]>([]);
  const [isWithdrawalDialogOpen, setIsWithdrawalDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  const { register, handleSubmit, formState: { errors }, reset, setValue } = useForm<WithdrawalFormValues>({
    resolver: zodResolver(withdrawalSchema),
    defaultValues: {
      amount: 0,
      paymentMethod: "",
      paymentDetails: "",
    }
  });

  // Fetch organization and withdrawals on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch organization details
        const orgResponse = await axios.get(`/api/affiliate/organizations/${params.id}`);
        setOrganization({
          id: orgResponse.data.id,
          name: orgResponse.data.name,
          totalEarnings: orgResponse.data.totalEarnings || 0,
          availableBalance: orgResponse.data.availableBalance || 0,
        });

        // Check if user is admin
        if (orgResponse.data.members) {
          const currentUserMember = orgResponse.data.members.find((member: any) => member.isAdmin);
          setIsAdmin(!!currentUserMember);
        }

        // Fetch withdrawals
        const withdrawalsResponse = await axios.get(`/api/affiliate/organizations/${params.id}/withdrawals`);
        setWithdrawals(withdrawalsResponse.data);

      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to fetch organization data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.id]);

  // Handle withdrawal request submission
  const onSubmitWithdrawal = async (data: WithdrawalFormValues) => {
    if (!organization) return;

    if (data.amount > organization.availableBalance) {
      toast.error("Withdrawal amount exceeds available balance");
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await axios.post(`/api/affiliate/organizations/${params.id}/withdrawals`, data);
      setWithdrawals((prev) => [response.data, ...prev]);
      toast.success("Withdrawal request submitted successfully");
      setIsWithdrawalDialogOpen(false);
      reset();

      // Update available balance
      setOrganization((prev) => {
        if (!prev) return null;
        return {
          ...prev,
          availableBalance: prev.availableBalance - data.amount
        };
      });
    } catch (error) {
      console.error("Error submitting withdrawal request:", error);
      toast.error("Failed to submit withdrawal request");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get status badge for withdrawal
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>;
      case "APPROVED":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Approved</Badge>;
      case "REJECTED":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <h3 className="text-lg font-medium">Organization not found</h3>
        <p className="text-sm text-muted-foreground">
          The organization you are looking for does not exist or you do not have access to it.
        </p>
        <Button asChild className="mt-4">
          <Link href="/affiliate/organization">
            <OrganizationIcons.arrowLeft className="mr-2 h-4 w-4" />
            Back to Organizations
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <Button asChild variant="outline" size="sm" className="mb-4">
          <Link href={`/affiliate/organization/${params.id}`}>
            <OrganizationIcons.arrowLeft className="mr-2 h-4 w-4" />
            Back to Organization
          </Link>
        </Button>

        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold">Withdrawals</h1>
            <p className="text-sm text-muted-foreground">
              Manage withdrawal requests for {organization.name}
            </p>
          </div>

          {isAdmin && (
            <Dialog open={isWithdrawalDialogOpen} onOpenChange={setIsWithdrawalDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <OrganizationIcons.dollarSign className="mr-2 h-4 w-4" />
                  Request Withdrawal
                </Button>
              </DialogTrigger>
              <DialogContent>
                <form onSubmit={handleSubmit(onSubmitWithdrawal)}>
                  <DialogHeader>
                    <DialogTitle>Request Withdrawal</DialogTitle>
                    <DialogDescription>
                      Submit a new withdrawal request for your organization earnings.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Available Balance:</span>
                      <span className="font-bold">${organization.availableBalance.toFixed(2)}</span>
                    </div>
                    <Separator />
                    <div className="grid gap-2">
                      <Label htmlFor="amount">Amount</Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2">$</span>
                        <Input
                          id="amount"
                          type="number"
                          step="0.01"
                          min="1"
                          max={organization.availableBalance}
                          placeholder="0.00"
                          className="pl-7"
                          {...register("amount", { valueAsNumber: true })}
                        />
                      </div>
                      {errors.amount && (
                        <p className="text-sm text-red-500">{errors.amount.message}</p>
                      )}
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="paymentMethod">Payment Method</Label>
                      <Select
                        onValueChange={(value) => setValue("paymentMethod", value)}
                        defaultValue=""
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="paypal">PayPal</SelectItem>
                          <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                          <SelectItem value="stripe">Stripe</SelectItem>
                          <SelectItem value="crypto">Cryptocurrency</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.paymentMethod && (
                        <p className="text-sm text-red-500">{errors.paymentMethod.message}</p>
                      )}
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="paymentDetails">Payment Details</Label>
                      <Textarea
                        id="paymentDetails"
                        placeholder="Enter your payment details (e.g., PayPal email, bank account info)"
                        {...register("paymentDetails")}
                      />
                      {errors.paymentDetails && (
                        <p className="text-sm text-red-500">{errors.paymentDetails.message}</p>
                      )}
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsWithdrawalDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Submit Request
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Earnings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${organization.totalEarnings?.toFixed(2) || "0.00"}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Available Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${organization.availableBalance?.toFixed(2) || "0.00"}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Pending Withdrawals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${withdrawals.filter(w => w.status === "PENDING").reduce((sum, w) => sum + w.amount, 0).toFixed(2)}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Withdrawal History</CardTitle>
          <CardDescription>
            View and manage your organization withdrawal requests.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {withdrawals.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed">
              <p className="text-sm text-muted-foreground">No withdrawal requests yet.</p>
              {isAdmin && (
                <Button onClick={() => setIsWithdrawalDialogOpen(true)} className="mt-2" variant="outline" size="sm">
                  <OrganizationIcons.dollarSign className="mr-2 h-4 w-4" />
                  Request Withdrawal
                </Button>
              )}
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Payment Method</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Processed</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {withdrawals.map((withdrawal) => (
                    <TableRow key={withdrawal.id}>
                      <TableCell className="font-medium">
                        {DateFormatter.custom(withdrawal.createdAt, "MMM d, yyyy")}
                      </TableCell>
                      <TableCell>${withdrawal.amount.toFixed(2)}</TableCell>
                      <TableCell className="capitalize">{withdrawal.paymentMethod.replace("_", " ")}</TableCell>
                      <TableCell>{getStatusBadge(withdrawal.status)}</TableCell>
                      <TableCell>
                        {withdrawal.processedAt
                          ? DateFormatter.custom(withdrawal.processedAt, "MMM d, yyyy")
                          : "-"}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="mt-8">
        <h3 className="text-lg font-medium mb-4">Withdrawal Guidelines</h3>
        <div className="space-y-4 text-sm">
          <div className="flex gap-2">
            <OrganizationIcons.alertTriangle className="h-5 w-5 text-muted-foreground flex-shrink-0" />
            <p>Minimum withdrawal amount is $50.00.</p>
          </div>
          <div className="flex gap-2">
            <OrganizationIcons.alertTriangle className="h-5 w-5 text-muted-foreground flex-shrink-0" />
            <p>Withdrawals are processed within 3-5 business days after approval.</p>
          </div>
          <div className="flex gap-2">
            <OrganizationIcons.alertTriangle className="h-5 w-5 text-muted-foreground flex-shrink-0" />
            <p>Ensure your payment details are accurate to avoid processing delays.</p>
          </div>
          <div className="flex gap-2">
            <OrganizationIcons.alertTriangle className="h-5 w-5 text-muted-foreground flex-shrink-0" />
            <p>For any issues with withdrawals, please contact support.</p>
          </div>
        </div>
      </div>
    </div>
  );
}