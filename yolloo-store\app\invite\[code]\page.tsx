import { redirect } from "next/navigation";
import { InviteAcceptForm } from "@/components/invites/invite-accept-form";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { Badge } from "@/components/ui/badge";
import { prisma } from "@/lib/prisma";

interface PageProps {
  params: {
    code: string;
  };
}

export const metadata = {
  title: "Accept Invitation",
  description: "Accept invitation to organization",
};

export default async function InvitePage({ params }: PageProps) {
  const { code } = params;
  
  // Verify the invite
  const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/affiliate/invites/verify?code=${code}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
    cache: "no-store",
  });
  
  const data = await response.json();
  
  // If the invite is not valid, redirect to error page
  if (!data.valid) {
    return redirect("/invite/error?error=" + encodeURIComponent(data.error || "Invalid invite"));
  }
  
  // Get current session if any
  const session = await getServerSession(authOptions);
  const isLoggedIn = !!session?.user;
  
  // If logged in but not as the invited email, show warning
  const isWrongUser = isLoggedIn && data.invite.email && session?.user?.email !== data.invite.email;
  const isGeneralInvite = data.invite.isGeneralInvite;
  
  // Get additional info about the invite
  const invite = await prisma.organizationInvite.findUnique({
    where: { inviteCode: code },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          code: true,
          logo: true,
          description: true,
          commissionRate: true,
          discountRate: true,
        }
      },
      affiliate: {
        select: {
          user: {
            select: {
              name: true,
              email: true,
              image: true
            }
          }
        }
      }
    }
  });

  const commissionRate = invite?.commissionRate || invite?.organization.commissionRate || 0.5;
  const inviterName = invite?.affiliate?.user?.name;
  
  return (
    <div className="container max-w-md mx-auto py-10 px-4 sm:px-6">
      <div className="space-y-6">
        <div className="space-y-4 text-center">
          {data.invite.organization?.logo && (
            <div className="flex justify-center mb-6">
              <img
                src={data.invite.organization.logo}
                alt={data.invite.organization.name}
                className="w-20 h-20 object-contain"
              />
            </div>
          )}
          <h1 className="text-3xl font-bold">Join Organization {data.invite.organization.name}</h1>
          
          <div className="space-y-2">
            <p className="text-muted-foreground">
              You've been invited to join <strong>{data.invite.organization.name}</strong> as an affiliate member.
            </p>
            
            {invite?.organization.description && (
              <p className="text-sm text-muted-foreground border rounded-md p-3 bg-muted/30">
                "{invite.organization.description}"
              </p>
            )}
            
            <div className="flex flex-col gap-2 items-center mt-4">
              {inviterName && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Invited by:</span> <strong>{inviterName}</strong>
                </div>
              )}
              
              <div className="flex gap-2 items-center">
                <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">
                  {(commissionRate * 100).toFixed(0)}% Commission Rate
                </Badge>
                
                {invite?.organization.discountRate && invite.organization.discountRate > 0 && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">
                    {(invite.organization.discountRate * 100).toFixed(0)}% Customer Discount
                  </Badge>
                )}
              </div>
            </div>
            
            {data.invite.email && (
              <p className="text-sm mt-2">
                This invitation was sent to <strong>{data.invite.email}</strong>
              </p>
            )}
          </div>
        </div>
        
        {isWrongUser ? (
          <div className="bg-yellow-50 p-4 rounded-md border border-yellow-200">
            <p className="text-sm text-yellow-800">
              You're logged in as <strong>{session.user.email}</strong> but this invitation was sent to <strong>{data.invite.email}</strong>.
              Please log out and sign in with the correct account, or continue to create a new account with the invited email.
            </p>
          </div>
        ) : (
          <InviteAcceptForm 
            inviteCode={code} 
            email={data.invite.email || ""}
            userExists={data.userExists}
            isLoggedIn={isLoggedIn}
            isGeneralInvite={isGeneralInvite}
            currentUserEmail={session?.user?.email || ""}
            commissionRate={commissionRate}
          />
        )}
      </div>
    </div>
  );
} 