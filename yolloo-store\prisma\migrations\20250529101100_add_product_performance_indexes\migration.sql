-- 为Product表添加性能优化索引
-- Migration created: 2025-05-29 18:11:00

-- 复合索引：状态 + 下架状态 + 创建时间（用于主要查询）
CREATE INDEX IF NOT EXISTS "Product_status_off_shelve_createdAt_idx" ON "Product"("status", "off_shelve", "createdAt");

-- 复合索引：分类 + 状态 + 下架状态（用于分类过滤）
CREATE INDEX IF NOT EXISTS "Product_categoryId_status_off_shelve_idx" ON "Product"("categoryId", "status", "off_shelve");

-- 单字段索引：价格（用于价格排序）
CREATE INDEX IF NOT EXISTS "Product_price_idx" ON "Product"("price");

-- 单字段索引：产品名称（用于名称搜索和排序）
CREATE INDEX IF NOT EXISTS "Product_name_idx" ON "Product"("name");

-- 单字段索引：国家（用于国家过滤）
CREATE INDEX IF NOT EXISTS "Product_country_idx" ON "Product"("country");

-- GIN索引：specifications JSON字段（用于JSON查询优化）
CREATE INDEX IF NOT EXISTS "Product_specifications_gin_idx" ON "Product" USING GIN ("specifications");

-- 部分索引：只为活跃且未下架的产品创建索引（减少索引大小）
CREATE INDEX IF NOT EXISTS "Product_active_products_idx" ON "Product"("createdAt") 
WHERE "status" = 'ACTIVE' AND "off_shelve" = false;
