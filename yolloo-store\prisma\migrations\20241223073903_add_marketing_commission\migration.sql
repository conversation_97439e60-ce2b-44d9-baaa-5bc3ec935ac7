/*
  Warnings:

  - You are about to drop the `Commission` table. If the table is not empty, all the data it contains will be lost.

*/
-- <PERSON>reate<PERSON>num
CREATE TYPE "MarketingCommissionStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'PAID');

-- DropForeignKey
ALTER TABLE "Commission" DROP CONSTRAINT "Commission_marketerId_fkey";

-- DropForeignKey
ALTER TABLE "Commission" DROP CONSTRAINT "Commission_orderId_fkey";

-- DropForeignKey
ALTER TABLE "Commission" DROP CONSTRAINT "Commission_reviewedById_fkey";

-- DropTable
DROP TABLE "Commission";

-- CreateTable
CREATE TABLE "MarketingCommission" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "status" "MarketingCommissionStatus" NOT NULL DEFAULT 'PENDING',
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MarketingCommission_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "MarketingCommission" ADD CONSTRAINT "MarketingCommission_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
