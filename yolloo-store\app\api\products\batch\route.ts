import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const ids = searchParams.get('ids')

    if (!ids) {
      return new NextResponse("Product IDs are required", { status: 400 })
    }

    const productIds = ids.split(',')

    const products = await prisma.product.findMany({
      where: {
        id: {
          in: productIds
        }
      },
      select: {
        id: true,
        category: true
      }
    })

    return NextResponse.json(products)
  } catch (error) {
    console.error("[PRODUCTS_BATCH_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 