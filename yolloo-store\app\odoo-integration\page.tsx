import Image from "next/image";

export default function Home() {
  return (
    <main className="min-h-screen bg-white text-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-5xl mx-auto">
          <h1 className="text-4xl font-bold mb-8 font-sans">Odoo Integration Service</h1>
          
          {/* 正式API部分 */}
          <div className="mb-12">
            <h2 className="text-2xl font-semibold mb-4 font-sans">Production APIs:</h2>
            <ul className="space-y-6">
              <li>
                <h3 className="font-semibold mb-2 font-sans">Products:</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">GET /api/odoo/products</code>
                    <span className="ml-2 text-gray-600">- Get product list</span>
                  </li>
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">POST /api/odoo/products/price</code>
                    <span className="ml-2 text-gray-600">- Get product prices</span>
                  </li>
                </ul>
              </li>
              <li>
                <h3 className="font-semibold mb-2 font-sans">Orders:</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">POST /api/odoo/orders</code>
                    <span className="ml-2 text-gray-600">- Create new order</span>
                  </li>
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">PUT /api/odoo/orders</code>
                    <span className="ml-2 text-gray-600">- Update order status</span>
                  </li>
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">POST /api/odoo/orders/status</code>
                    <span className="ml-2 text-gray-600">- Query order status</span>
                  </li>
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">POST /api/odoo/orders/qrcode</code>
                    <span className="ml-2 text-gray-600">- Get order QR code</span>
                  </li>
                </ul>
              </li>
              <li>
                <h3 className="font-semibold mb-2 font-sans">Invoices:</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">POST /api/odoo/invoices</code>
                    <span className="ml-2 text-gray-600">- Get invoice orders</span>
                  </li>
                </ul>
              </li>
              <li>
                <h3 className="font-semibold mb-2 font-sans">Webhook:</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">POST /api/odoo/webhook</code>
                    <span className="ml-2 text-gray-600">- Receive Odoo push data</span>
                  </li>
                </ul>
              </li>
            </ul>
          </div>

          {/* 测试API部分 */}
          <div className="mb-12">
            <h2 className="text-2xl font-semibold mb-4 font-sans">Test APIs (Development Only):</h2>
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-r">
              <p className="text-yellow-700">
                These test APIs are for development and testing purposes only. They contain pre-configured test data and should not be used in production.
              </p>
            </div>
            <ul className="space-y-6">
              <li>
                <h3 className="font-semibold mb-2 font-sans">Environment Test:</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">GET /api/test/env</code>
                    <span className="ml-2 text-gray-600">- Test environment variables</span>
                  </li>
                </ul>
              </li>
              <li>
                <h3 className="font-semibold mb-2 font-sans">Feature Tests:</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">GET /api/test/webhook</code>
                    <span className="ml-2 text-gray-600">- Test webhook with sample product data</span>
                  </li>
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">GET /api/test/flow</code>
                    <span className="ml-2 text-gray-600">- Test complete business flow (create order → status update → get QR)</span>
                  </li>
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">GET /api/test/order</code>
                    <span className="ml-2 text-gray-600">- Test order creation with sample data</span>
                  </li>
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">GET /api/test/price</code>
                    <span className="ml-2 text-gray-600">- Test product price query with sample product code</span>
                  </li>
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">GET /api/test/qrcode</code>
                    <span className="ml-2 text-gray-600">- Test QR code generation with sample order</span>
                  </li>
                  <li className="flex items-center">
                    <code className="bg-gray-100 px-2 py-1 rounded font-mono">GET /api/test/invoice</code>
                    <span className="ml-2 text-gray-600">- Test invoice query with sample parameters</span>
                  </li>
                </ul>
              </li>
            </ul>

            <div className="mt-8 bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r">
              <h3 className="font-semibold mb-2 font-sans">How to use test APIs:</h3>
              <ol className="space-y-2 text-blue-700 ml-4 list-decimal">
                <li>Start with <code className="bg-blue-100 px-2 py-0.5 rounded font-mono">/api/test/env</code> to verify your environment setup</li>
                <li>Use <code className="bg-blue-100 px-2 py-0.5 rounded font-mono">/api/test/flow</code> to test the complete business flow</li>
                <li>Individual feature tests can be used to debug specific functionality</li>
                <li>All test APIs use GET method and return sample responses</li>
                <li>No request body needed for test APIs - they use pre-configured test data</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
