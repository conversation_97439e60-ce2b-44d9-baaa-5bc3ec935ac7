import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { hash } from "bcryptjs";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for accepting an invite for existing user
const acceptInviteSchema = z.object({
  inviteCode: z.string(),
});

// Schema for accepting an invite for new user
const acceptInviteWithRegisterSchema = z.object({
  inviteCode: z.string(),
  name: z.string().min(2),
  password: z.string().min(8),
});

// Schema for accepting a general invite
const acceptGeneralInviteSchema = z.object({
  inviteCode: z.string(),
  email: z.string().email(),
  name: z.string().min(2),
  password: z.string().min(8),
});

// POST - Accept an invite
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const inviteCode = body.inviteCode;
    
    if (!inviteCode) {
      return NextResponse.json({ error: "Invite code is required" }, { status: 400 });
    }
    
    // Find the invite
    const invite = await prisma.organizationInvite.findUnique({
      where: { inviteCode },
      include: {
        organization: true,
      },
    });
    
    if (!invite) {
      return NextResponse.json({ error: "Invite not found" }, { status: 404 });
    }
    
    // Check if invite is still valid
    if (invite.status !== "PENDING") {
      return NextResponse.json(
        { error: `Invite is ${invite.status.toLowerCase()}` },
        { status: 400 }
      );
    }
    
    // Check if invite has expired
    if (invite.expiresAt && invite.expiresAt < new Date()) {
      // Update invite status to EXPIRED
      await prisma.organizationInvite.update({
        where: { id: invite.id },
        data: { status: "EXPIRED" },
      });
      
      return NextResponse.json(
        { error: "Invite has expired" },
        { status: 400 }
      );
    }
    
    // Check if this is a general invite (no specific email)
    const isGeneralInvite = !invite.email;
    
    let email = invite.email;
    
    // For general invites, user must provide email
    if (isGeneralInvite) {
      // Validate registration data for general invite
      const validationResult = acceptGeneralInviteSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: "Email, name and password are required for registration", details: validationResult.error.errors },
          { status: 400 }
        );
      }
      
      email = validationResult.data.email;
      
      // Check if email is already registered
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });
      
      if (existingUser) {
        // User exists, this will be handled below as if they were logged in
      }
    } else if (!email) {
      return NextResponse.json({ error: "Invite has no associated email" }, { status: 400 });
    }
    
    let user = await prisma.user.findUnique({
      where: { email },
    });
    
    // If user doesn't exist, register a new user
    if (!user) {
      // For specific email invites
      if (!isGeneralInvite) {
        // Validate registration data
        const validationResult = acceptInviteWithRegisterSchema.safeParse(body);
        if (!validationResult.success) {
          return NextResponse.json(
            { error: "Name and password are required for registration", details: validationResult.error.errors },
            { status: 400 }
          );
        }
        
        const { name, password } = validationResult.data;
        
        // Hash the password
        const hashedPassword = await hash(password, 10);
        
        // Create the user
        user = await prisma.user.create({
          data: {
            name,
            email,
            hashedPassword,
            role: "CUSTOMER",
          },
        });
      } else {
        // For general invites
        const { name, password } = body;
        
        // Hash the password
        const hashedPassword = await hash(password, 10);
        
        // Create the user
        user = await prisma.user.create({
          data: {
            name,
            email,
            hashedPassword,
            role: "CUSTOMER",
          },
        });
      }
    } else {
      // If user exists, we should check if there's an authenticated session
      const session = await getServerSession(authOptions);
      
      // If there's no session or the session user doesn't match the invite email
      if (!session?.user || session.user.email !== email) {
        return NextResponse.json(
          { error: "You must be logged in as the invited user to accept this invitation" },
          { status: 401 }
        );
      }
    }
    
    // Check if user has an affiliate profile
    let affiliateProfile = await prisma.affiliateProfile.findUnique({
      where: { userId: user.id },
    });
    
    // If user doesn't have an affiliate profile, create one
    if (!affiliateProfile) {
      // Generate a unique affiliate code
      const code = `${user.name?.substring(0, 3) || "AFF"}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
      
      affiliateProfile = await prisma.affiliateProfile.create({
        data: {
          userId: user.id,
          code,
          commissionRate: invite.organization.commissionRate,
          discountRate: invite.organization.discountRate,
        },
      });
    }
    
    // Check if affiliate is already in an organization
    if (affiliateProfile.organizationId) {
      // 如果用户尝试加入的组织就是当前已加入的组织，返回成功
      if (affiliateProfile.organizationId === invite.organizationId) {
        return NextResponse.json({
          success: true,
          message: `You are already a member of ${invite.organization.name}`,
          affiliate: affiliateProfile,
        });
      }
      
      return NextResponse.json(
        { error: "You are already a member of an organization" },
        { status: 400 }
      );
    }
    
    // Add affiliate to organization with default organization settings
    // Note: After database migration, we should retrieve and use commissionRate and isAdmin from the invite
    const updatedAffiliate = await prisma.affiliateProfile.update({
      where: { id: affiliateProfile.id },
      data: {
        organizationId: invite.organizationId,
        commissionRate: invite.organization.commissionRate,
        isAdmin: false, // Default to non-admin, can be changed later by organization admin
      },
    });
    
    return NextResponse.json({
      success: true,
      message: `You have joined ${invite.organization.name}`,
      affiliate: updatedAffiliate,
    });
  } catch (error) {
    console.error("Error accepting invite:", error);
    return NextResponse.json(
      { error: "Failed to accept invite" },
      { status: 500 }
    );
  }
} 