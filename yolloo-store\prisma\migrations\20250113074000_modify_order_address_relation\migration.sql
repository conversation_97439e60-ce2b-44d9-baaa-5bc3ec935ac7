-- 修改订单表，添加地址快照字段
ALTER TABLE "Order" ADD COLUMN "shippingAddressSnapshot" JSONB;

-- 将现有订单的地址信息复制到快照字段
UPDATE "Order" o
SET "shippingAddressSnapshot" = jsonb_build_object(
  'name', a."name",
  'phone', a."phone",
  'address1', a."address1",
  'address2', a."address2",
  'city', a."city",
  'state', a."state",
  'postalCode', a."postalCode",
  'country', a."country"
)
FROM "Address" a
WHERE o."addressId" = a.id;

-- 删除订单和地址之间的外键约束
ALTER TABLE "Order" DROP CONSTRAINT IF EXISTS "Order_addressId_fkey";

-- 修改 addressId 为可选字段
ALTER TABLE "Order" ALTER COLUMN "addressId" DROP NOT NULL; 