import {
  <PERSON>,
  Post,
  Get,
  Delete,
  Param,
  Query,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  Req,
  Res,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { UploadService } from './upload.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import * as path from 'path';
import * as fs from 'fs';

@Controller('uploads')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post('image')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(
    @UploadedFile() file: Express.Multer.File,
    @Query('category') category: string = 'temp',
    @Req() req: any
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const validCategories = ['products', 'users', 'categories', 'rewards', 'temp'];
    if (!validCategories.includes(category)) {
      throw new BadRequestException('Invalid category');
    }

    return await this.uploadService.uploadImage(
      file,
      category as any,
      req.user?.id
    );
  }

  @Post('images')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FilesInterceptor('files', 10)) // 最多10个文件
  async uploadMultipleImages(
    @UploadedFiles() files: Express.Multer.File[],
    @Query('category') category: string = 'temp',
    @Req() req: any
  ) {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files uploaded');
    }

    const validCategories = ['products', 'users', 'categories', 'rewards', 'temp'];
    if (!validCategories.includes(category)) {
      throw new BadRequestException('Invalid category');
    }

    return await this.uploadService.uploadMultipleImages(
      files,
      category as any,
      req.user?.id
    );
  }

  @Get(':id')
  async getUpload(@Param('id') id: string) {
    return await this.uploadService.getUploadById(id);
  }

  @Get(':id/file')
  async serveFile(@Param('id') id: string, @Res() res: Response) {
    try {
      const upload = await this.uploadService.getUploadById(id);
      const filePath = path.join(process.cwd(), 'uploads', upload.path);

      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ message: 'File not found' });
      }

      res.setHeader('Content-Type', upload.mimeType);
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1年缓存
      
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

    } catch (error) {
      return res.status(404).json({ message: 'Upload not found' });
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  async deleteUpload(@Param('id') id: string, @Req() req: any) {
    return await this.uploadService.deleteUpload(id, req.user?.id);
  }

  @Get('user/my-uploads')
  @UseGuards(JwtAuthGuard)
  async getUserUploads(
    @Req() req: any,
    @Query('category') category?: string
  ) {
    return await this.uploadService.getUserUploads(req.user.id, category);
  }

  @Post(':id/move')
  @UseGuards(JwtAuthGuard)
  async moveUpload(
    @Param('id') id: string,
    @Query('category') category: string,
    @Req() req: any
  ) {
    const validCategories = ['products', 'users', 'categories', 'rewards'];
    if (!validCategories.includes(category)) {
      throw new BadRequestException('Invalid category');
    }

    // 验证用户权限
    const upload = await this.uploadService.getUploadById(id);
    if (upload.uploadedBy !== req.user.id) {
      throw new BadRequestException('Unauthorized');
    }

    return await this.uploadService.moveUploadToCategory(id, category as 'products' | 'users' | 'categories' | 'rewards' | 'temp');
  }

  @Post('cleanup-temp')
  @UseGuards(JwtAuthGuard)
  async cleanupTempFiles(@Query('hours') hours?: string) {
    const hoursNum = hours ? parseInt(hours) : 24;
    return await this.uploadService.cleanupTempFiles(hoursNum);
  }
}
