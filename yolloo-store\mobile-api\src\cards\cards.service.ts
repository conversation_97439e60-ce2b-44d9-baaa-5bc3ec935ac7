import { BadRequestException, Injectable, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { RegisterCardDto } from './dto/register-card.dto';
import { AddEsimDto } from './dto/add-esim.dto';
import { CARD_STATUS, CARD_STATUS_NAMES } from '../common/constants/app.constants';
import { DateFormatter, DateUtils } from '../common/utils';

@Injectable()
export class CardsService {
  private readonly logger = new Logger(CardsService.name);

  constructor(private prisma: PrismaService) {}

  async getUserCards(userId: string) {
    try {
      this.logger.log(`Fetching cards for user: ${userId}`);

      // 查询用户的卡片
      const cards = await this.prisma.yollooCard.findMany({
        where: { userId },
        include: {
          esims: true,
        },
      });

      // 如果没有卡片，返回空数组
      if (cards.length === 0) {
        this.logger.warn(`No cards found for user ${userId}`);
        return { cards: [] };
      }

      this.logger.log(`Found ${cards.length} cards for user ${userId}`);

    // 格式化卡片数据
    const formattedCards = cards.map(card => ({
      id: card.id,
      number: card.number,
      status: card.status,
      type: card.type,
      customName: card.customName,
      activationDate: card.activationDate?.toISOString(),
      expiryDate: card.expiryDate?.toISOString(),
      esimCount: card.esims.length,
    }));

    return { cards: formattedCards };
    } catch (error) {
      this.logger.error(`Error fetching cards for user ${userId}:`, error);
      // 如果数据库查询失败，返回空数组
      return { cards: [] };
    }
  }



  async getCardById(userId: string, cardId: string) {
    try {
      this.logger.log(`Fetching card ${cardId} for user ${userId}`);

      // 查询卡片详情
      const card = await this.prisma.yollooCard.findFirst({
        where: {
          id: cardId,
          userId,
        },
        include: {
          esims: {
            include: {
              product: true,
            },
          },
        },
      });

      if (!card) {
        this.logger.warn(`Card ${cardId} not found for user ${userId}`);
        throw new NotFoundException('Card not found');
      }

      this.logger.log(`Found card ${cardId} with ${card.esims.length} eSIMs`);

    // 格式化eSIM数据
    const formattedEsims = card.esims.map(esim => ({
      id: esim.id,
      iccid: esim.iccid,
      status: esim.status,
      activationDate: esim.activationDate ? DateFormatter.iso(esim.activationDate) : null,
      expiryDate: esim.expiryDate ? DateFormatter.iso(esim.expiryDate) : null,
      product: esim.product
        ? {
            id: esim.product.id,
            name: esim.product.name,
            description: esim.product.description,
          }
        : null,
    }));

    return {
      id: card.id,
      number: card.number,
      status: card.status,
      type: card.type,
      customName: card.customName,
      activationDate: card.activationDate ? DateFormatter.iso(card.activationDate) : null,
      expiryDate: card.expiryDate ? DateFormatter.iso(card.expiryDate) : null,
      esims: formattedEsims,
    };
    } catch (error) {
      this.logger.error(`Error fetching card ${cardId} for user ${userId}:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error('Failed to fetch card details');
    }
  }

  async registerCard(userId: string, registerCardDto: RegisterCardDto) {
    try {
      this.logger.log(`Registering card ${registerCardDto.cardNumber} for user ${userId}`);

      // 检查卡号是否已被注册
      const existingCard = await this.prisma.yollooCard.findUnique({
        where: { number: registerCardDto.cardNumber },
      });

      if (existingCard) {
        this.logger.warn(`Card number ${registerCardDto.cardNumber} already registered`);
        throw new BadRequestException('此卡号已被注册');
      }

    // 创建新卡片 - 默认为待激活状态
    const card = await this.prisma.yollooCard.create({
      data: {
        userId,
        number: registerCardDto.cardNumber,
        status: CARD_STATUS.INACTIVE,
        type: 'Standard',
        customName: registerCardDto.customName,
        // 注册时不设置激活日期和过期日期，等待激活
      },
    });

    this.logger.log(`Card ${card.number} registered successfully for user ${userId}`);

    return {
      id: card.id,
      number: card.number,
      status: card.status,
      message: '卡片注册成功，请激活后使用',
    };
    } catch (error) {
      this.logger.error(`Error registering card for user ${userId}:`, error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new Error('Failed to register card');
    }
  }

  async activateCard(userId: string, cardId: string) {
    // 查询卡片是否存在且属于当前用户
    const card = await this.prisma.yollooCard.findFirst({
      where: {
        id: cardId,
        userId,
      },
    });

    if (!card) {
      throw new NotFoundException('卡片不存在');
    }

    // 检查卡片状态
    if (card.status === CARD_STATUS.ACTIVE) {
      throw new BadRequestException('卡片已激活');
    }

    if (card.status === CARD_STATUS.EXPIRED) {
      throw new BadRequestException('卡片已过期，无法激活');
    }

    // 激活卡片
    const activationDate = new Date();
    const expiryDate = DateUtils.addDays(activationDate, 365); // 一年后过期

    const updatedCard = await this.prisma.yollooCard.update({
      where: { id: cardId },
      data: {
        status: CARD_STATUS.ACTIVE,
        activationDate,
        expiryDate,
      },
    });

    return {
      id: updatedCard.id,
      number: updatedCard.number,
      status: updatedCard.status,
      activationDate: updatedCard.activationDate ? DateFormatter.iso(updatedCard.activationDate) : null,
      expiryDate: updatedCard.expiryDate ? DateFormatter.iso(updatedCard.expiryDate) : null,
      message: '卡片激活成功',
    };
  }

  async addEsimToCard(userId: string, cardId: string, addEsimDto: AddEsimDto) {
    // 检查卡片是否存在且属于当前用户
    const card = await this.prisma.yollooCard.findFirst({
      where: {
        id: cardId,
        userId,
      },
    });

    if (!card) {
      throw new NotFoundException('Card not found');
    }

    // 检查产品是否存在
    const product = await this.prisma.product.findUnique({
      where: { id: addEsimDto.productId },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // 检查ICCID是否已被使用
    const existingEsim = await this.prisma.esim.findUnique({
      where: { iccid: addEsimDto.iccid },
    });

    if (existingEsim) {
      throw new BadRequestException('此ICCID已被使用');
    }

    // 创建新的eSIM
    const esim = await this.prisma.esim.create({
      data: {
        iccid: addEsimDto.iccid,
        status: 'Inactive',
        yollooCardId: cardId,
        productId: addEsimDto.productId,
      },
    });

    return {
      id: esim.id,
      iccid: esim.iccid,
      status: esim.status,
      message: 'eSIM添加成功',
    };
  }

  async activateEsim(userId: string, esimId: string) {
    // 查询eSIM是否存在且属于当前用户
    const esim = await this.prisma.esim.findFirst({
      where: {
        id: esimId,
        yollooCard: {
          userId,
        },
      },
    });

    if (!esim) {
      throw new NotFoundException('eSIM not found');
    }

    // 检查eSIM状态
    if (esim.status === CARD_STATUS.ACTIVE) {
      throw new BadRequestException('eSIM已激活');
    }

    // 激活eSIM
    const activationDate = new Date();
    const expiryDate = DateUtils.addDays(activationDate, 30); // 30天后过期

    const updatedEsim = await this.prisma.esim.update({
      where: { id: esimId },
      data: {
        status: CARD_STATUS.ACTIVE,
        activationDate,
        expiryDate,
      },
    });

    return {
      id: updatedEsim.id,
      status: updatedEsim.status,
      activationDate: updatedEsim.activationDate ? DateFormatter.iso(updatedEsim.activationDate) : null,
      expiryDate: updatedEsim.expiryDate ? DateFormatter.iso(updatedEsim.expiryDate) : null,
      qrCodeUrl: 'https://example.com/qr-code.png',
      message: 'eSIM激活成功',
    };
  }
}
