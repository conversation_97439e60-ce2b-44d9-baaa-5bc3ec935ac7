import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import crypto from "crypto";
import { sendPasswordResetEmail } from "@/app/services/emailService";
import { DateUtils } from "@/lib/utils";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for batch user creation
const batchUserSchema = z.array(
  z.object({
    name: z.string().min(2, "Name must be at least 2 characters"),
    email: z.string().email("Invalid email address"),
    password: z.string().min(8, "Password must be at least 8 characters").optional(),
    role: z.enum(["ADMIN", "CUSTOMER", "STAFF"]).optional(),
  })
);

// POST - Batch create users (admin only)
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const body = await req.json();

    // Validate input
    const validationResult = batchUserSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }

    const usersToCreate = validationResult.data;

    // Check for duplicate emails in the batch
    const emails = usersToCreate.map(user => user.email);
    const uniqueEmails = new Set(emails);

    if (uniqueEmails.size !== emails.length) {
      return NextResponse.json(
        { error: "Duplicate emails found in batch" },
        { status: 400 }
      );
    }

    // Check for existing users with the same emails
    const existingUsers = await prisma.user.findMany({
      where: {
        email: { in: emails },
      },
      select: {
        email: true,
      },
    });

    if (existingUsers.length > 0) {
      return NextResponse.json(
        {
          error: "Some emails already exist",
          existingEmails: existingUsers.map(u => u.email)
        },
        { status: 400 }
      );
    }

    // Create users and password reset tokens in a transaction
    const result = await prisma.$transaction(async (tx) => {
      const createdUsers = [];
      const resetLinks = [];

      // Create each user
      for (const userData of usersToCreate) {
        // Create user
        const newUser = await tx.user.create({
          data: {
            name: userData.name,
            email: userData.email,
            role: userData.role || "CUSTOMER",
          },
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        });

        createdUsers.push(newUser);

        // Generate a password reset token for this user
        const token = crypto.randomBytes(32).toString("hex");

        // Set expiration date (7 days from now)
        const expiresAt = DateUtils.addDays(new Date(), 7);

        // Create password reset token
        await tx.passwordResetToken.create({
          data: {
            userId: newUser.id,
            token,
            expiresAt,
          },
        });

        // Generate reset link
        const resetLink = `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password?token=${token}`;
        resetLinks.push({
          userId: newUser.id,
          email: newUser.email,
          name: newUser.name || 'User',
          resetLink,
          token
        });
      }

      return { users: createdUsers, resetLinks };
    });

    // Send password reset emails to all newly created users
    try {
      const emailPromises = result.resetLinks.map(async (userInfo) => {
        return sendPasswordResetEmail(
          userInfo.email,
          userInfo.name,
          userInfo.resetLink
        );
      });

      await Promise.allSettled(emailPromises);
    } catch (emailError) {
      console.error("Error sending password reset emails:", emailError);
      // Continue execution even if email sending fails
      // We still want to return the created users and reset links
    }

    return NextResponse.json({
      users: result.users,
      resetLinks: result.resetLinks
    });
  } catch (error) {
    console.error("Error batch creating users:", error);
    return NextResponse.json(
      { error: "Failed to batch create users" },
      { status: 500 }
    );
  }
}