# 文章攻略API文档

## 概述

文章攻略API提供了流量使用攻略、套餐推荐等内容的接口，支持分类筛选、分页查询和详情查看。

## 接口列表

### 1. 获取文章分类列表

**接口地址：** `GET /articles/categories`

**请求参数：**
- `type` (可选): 分类类型筛选

**响应示例：**
```json
{
  "categories": [
    {
      "id": "data-usage-guide",
      "name": "流量使用攻略",
      "description": "如何省流量 | 套餐推荐",
      "icon": "https://example.com/icons/data-usage.png",
      "articleCount": 8
    },
    {
      "id": "hot-packages",
      "name": "热门流量套餐",
      "description": "月包 | 季包 | 年包",
      "icon": "https://example.com/icons/hot-packages.png",
      "articleCount": 12
    },
    {
      "id": "5g-promotion",
      "name": "5G流量包大促销",
      "description": "首次开通 最高减500",
      "icon": "https://example.com/icons/5g-promo.png",
      "articleCount": 6
    },
    {
      "id": "weekly-special",
      "name": "周四流量特惠日",
      "description": "抢100GB流量包仅需50元！",
      "icon": "https://example.com/icons/weekly-special.png",
      "articleCount": 4
    }
  ],
  "context": {
    "language": "zh-CN",
    "theme": "light",
    "currency": "USD"
  }
}
```

### 2. 获取文章列表

**接口地址：** `GET /articles`

**请求参数：**
- `category` (可选): 文章分类ID
- `country` (可选): 国家筛选
- `tag` (可选): 标签筛选
- `page` (可选): 页码，默认1
- `pageSize` (可选): 每页数量，默认10
- `limit` (可选): 限制返回数量

**响应示例：**
```json
{
  "articles": [
    {
      "id": "1",
      "title": "了解你的数据需求",
      "summary": "出行前评估你需要多少流量。观看视频或音乐消耗更多数据，而浏览或消息使用较少。",
      "category": "data-usage-guide",
      "imageUrl": "https://example.com/articles/001.jpg",
      "content": "出行前评估你需要多少流量是非常重要的...",
      "author": {
        "id": "1",
        "name": "数据专家",
        "avatar": "https://example.com/author1.jpg"
      },
      "publishDate": "2023-12-01T00:00:00Z",
      "readCount": 2580,
      "likeCount": 186,
      "tags": ["流量攻略", "使用技巧"]
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 10,
    "total": 8,
    "totalPages": 1
  },
  "filters": {
    "category": "data-usage-guide",
    "country": null,
    "tag": null
  },
  "context": {
    "language": "zh-CN",
    "theme": "light",
    "currency": "USD"
  }
}
```

### 3. 获取文章详情

**接口地址：** `GET /articles/:id`

**路径参数：**
- `id`: 文章ID

**响应示例：**
```json
{
  "article": {
    "id": "1",
    "title": "了解你的数据需求",
    "summary": "出行前评估你需要多少流量。观看视频或音乐消耗更多数据，而浏览或消息使用较少。",
    "category": "data-usage-guide",
    "imageUrl": "https://example.com/articles/001.jpg",
    "content": "出行前评估你需要多少流量是非常重要的...",
    "fullContent": "# 了解你的数据需求\n\n出行前评估你需要多少流量是非常重要的...",
    "author": {
      "id": "1",
      "name": "数据专家",
      "avatar": "https://example.com/author1.jpg"
    },
    "publishDate": "2023-12-01T00:00:00Z",
    "readCount": 2580,
    "likeCount": 186,
    "tags": ["流量攻略", "使用技巧"]
  },
  "relatedArticles": [
    {
      "id": "2",
      "title": "选择合适的数据套餐",
      "summary": "根据旅行时长和目的地选择最适合的套餐类型。",
      "imageUrl": "https://example.com/articles/002.jpg"
    }
  ],
  "context": {
    "language": "zh-CN",
    "theme": "light",
    "currency": "USD"
  }
}
```

### 4. 增强的旅行攻略接口

**接口地址：** `GET /home/<USER>

**请求参数：**
- `category` (可选): 文章分类ID
- `country` (可选): 国家筛选
- `page` (可选): 页码，默认1
- `pageSize` (可选): 每页数量，默认10
- `limit` (可选): 限制返回数量

**响应示例：**
```json
{
  "travelTips": [
    {
      "id": "1",
      "title": "了解你的数据需求",
      "summary": "出行前评估你需要多少流量。观看视频或音乐消耗更多数据，而浏览或消息使用较少。",
      "category": "data-usage-guide",
      "imageUrl": "https://example.com/tips/001.jpg",
      "content": "出行前评估你需要多少流量是非常重要的...",
      "link": "/articles/1",
      "readCount": 2580,
      "publishDate": "2023-12-01T00:00:00Z",
      "tags": ["流量攻略", "使用技巧"]
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 10,
    "total": 6,
    "totalPages": 1
  },
  "filters": {
    "category": null,
    "country": null
  },
  "context": {
    "language": "zh-CN",
    "theme": "light",
    "currency": "USD"
  }
}
```

## 文章分类说明

### data-usage-guide (流量使用攻略)
- 数据需求评估
- 套餐选择指南
- 使用技巧分享

### hot-packages (热门流量套餐)
- 亚洲热门套餐
- 欧洲多国套餐
- 美洲套餐推荐

### 5g-promotion (5G流量包大促销)
- 5G网络使用指南
- 5G套餐优惠活动
- 5G技术介绍

### weekly-special (周四流量特惠日)
- 特惠活动详解
- 抢购技巧分享
- 限时优惠信息

## 错误响应

当文章不存在时：
```json
{
  "error": "Article not found",
  "articleId": "999"
}
```

## 注意事项

1. 所有接口都支持多语言，根据请求头中的语言设置返回对应内容
2. 图片URL为示例地址，实际使用时需要配置正确的图片服务
3. 分页参数page从1开始
4. limit参数会覆盖分页设置，直接返回指定数量的结果
