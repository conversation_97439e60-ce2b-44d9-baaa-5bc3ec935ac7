import { NextRequest, NextResponse } from 'next/server';
import { qrOdooService } from '@/lib/odooServiceFactory';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

export async function POST(request: NextRequest) {
    try {
        const { customer_order_ref } = await request.json();
        console.log('Getting QR code for order:', customer_order_ref);
        const response = await qrOdooService.getOrderQRCode(customer_order_ref);
        console.log('Full QR code response:', JSON.stringify(response, null, 2));

        if (response?.result?.data?.[0]) {
            console.log('QR code data for first item:', JSON.stringify(response.result.data[0], null, 2));
            if (response.result.data[0].qrcode) {
                console.log('QR codes:', JSON.stringify(response.result.data[0].qrcode, null, 2));
            }
        }

        return NextResponse.json(response);
    } catch (error) {
        console.error('Error getting QR code from Odoo:', error);
        return NextResponse.json(
            { error: 'Failed to get QR code' },
            { status: 500 }
        );
    }
}