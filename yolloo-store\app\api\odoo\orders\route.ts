import { NextRequest, NextResponse } from 'next/server';
import { createOdooService } from '../../../services/odooService';
import { OdooOrder } from '../../../types/odoo';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


const odooService = createOdooService({
    address: process.env.ODOO_ADDRESS || '',
    channelId: process.env.ODOO_CHANNEL_ID || '',
    channelLanguage: process.env.ODOO_CHANNEL_LANGUAGE || 'zh_CN',
    authSecret: process.env.ODOO_AUTH_SECRET || '',
    signMethod: process.env.ODOO_SIGN_METHOD || 'MD5',
});

export async function POST(request: NextRequest) {
    try {
        const orderData: OdooOrder = await request.json();
        const response = await odooService.createOrder(orderData);
        return NextResponse.json(response);
    } catch (error) {
        console.error('Error creating order in Odoo:', error);
        return NextResponse.json(
            { error: 'Failed to create order' },
            { status: 500 }
        );
    }
}

export async function PUT(request: NextRequest) {
    try {
        const { order_lines } = await request.json();
        const response = await odooService.handleOrderStatusPush(order_lines);
        return NextResponse.json(response);
    } catch (error) {
        console.error('Error updating order status in Odoo:', error);
        return NextResponse.json(
            { error: 'Failed to update order status' },
            { status: 500 }
        );
    }
} 