import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name);
  private readonly uploadDir = path.join(process.cwd(), 'uploads');
  private readonly allowedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/gif'
  ];
  private readonly maxFileSize = 5 * 1024 * 1024; // 5MB

  constructor(private prisma: PrismaService) {
    this.ensureUploadDirectory();
  }

  private ensureUploadDirectory() {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
      this.logger.log(`Created upload directory: ${this.uploadDir}`);
    }

    // 创建子目录
    const subDirs = ['products', 'users', 'categories', 'rewards', 'temp'];
    subDirs.forEach(dir => {
      const dirPath = path.join(this.uploadDir, dir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
    });
  }

  async uploadImage(
    file: Express.Multer.File,
    category: 'products' | 'users' | 'categories' | 'rewards' | 'temp' = 'temp',
    userId?: string
  ) {
    // 验证文件
    this.validateFile(file);

    // 生成唯一文件名
    const fileExtension = path.extname(file.originalname);
    const fileName = `${crypto.randomUUID()}${fileExtension}`;
    const relativePath = `${category}/${fileName}`;
    const fullPath = path.join(this.uploadDir, relativePath);

    try {
      // 保存文件
      fs.writeFileSync(fullPath, file.buffer);

      // 记录到数据库
      const uploadRecord = await this.prisma.upload.create({
        data: {
          fileName: fileName,
          originalName: file.originalname,
          mimeType: file.mimetype,
          size: file.size,
          path: relativePath,
          category: category,
          uploadedBy: userId,
          url: `/uploads/${relativePath}`,
        },
      });

      this.logger.log(`File uploaded successfully: ${fileName}`);

      return {
        id: uploadRecord.id,
        fileName: fileName,
        originalName: file.originalname,
        url: uploadRecord.url,
        size: file.size,
        mimeType: file.mimetype,
        category: category,
        uploadedAt: uploadRecord.createdAt,
      };

    } catch (error) {
      this.logger.error(`Failed to upload file: ${error.message}`);
      // 清理文件（如果已创建）
      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
      }
      throw new BadRequestException('Failed to upload file');
    }
  }

  async uploadMultipleImages(
    files: Express.Multer.File[],
    category: 'products' | 'users' | 'categories' | 'rewards' | 'temp' = 'temp',
    userId?: string
  ) {
    const results: any[] = [];
    const errors: any[] = [];

    for (const file of files) {
      try {
        const result = await this.uploadImage(file, category, userId);
        results.push(result);
      } catch (error) {
        errors.push({
          fileName: file.originalname,
          error: error.message,
        });
      }
    }

    return {
      success: results,
      errors: errors,
      total: files.length,
      uploaded: results.length,
      failed: errors.length,
    };
  }

  async getUploadById(uploadId: string) {
    const upload = await this.prisma.upload.findUnique({
      where: { id: uploadId },
    });

    if (!upload) {
      throw new BadRequestException('Upload not found');
    }

    return upload;
  }

  async deleteUpload(uploadId: string, userId?: string) {
    const upload = await this.prisma.upload.findUnique({
      where: { id: uploadId },
    });

    if (!upload) {
      throw new BadRequestException('Upload not found');
    }

    // 检查权限（如果提供了userId）
    if (userId && upload.uploadedBy !== userId) {
      throw new BadRequestException('Unauthorized to delete this upload');
    }

    try {
      // 删除物理文件
      const fullPath = path.join(this.uploadDir, upload.path);
      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
      }

      // 删除数据库记录
      await this.prisma.upload.delete({
        where: { id: uploadId },
      });

      this.logger.log(`Upload deleted successfully: ${uploadId}`);

      return { success: true, message: 'Upload deleted successfully' };

    } catch (error) {
      this.logger.error(`Failed to delete upload: ${error.message}`);
      throw new BadRequestException('Failed to delete upload');
    }
  }

  async getUserUploads(userId: string, category?: string) {
    const where: any = { uploadedBy: userId };
    if (category) {
      where.category = category;
    }

    const uploads = await this.prisma.upload.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });

    return uploads;
  }

  async cleanupTempFiles(olderThanHours: number = 24) {
    const cutoffDate = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);

    const tempUploads = await this.prisma.upload.findMany({
      where: {
        category: 'temp',
        createdAt: { lt: cutoffDate },
      },
    });

    let deletedCount = 0;
    for (const upload of tempUploads) {
      try {
        await this.deleteUpload(upload.id);
        deletedCount++;
      } catch (error) {
        this.logger.warn(`Failed to cleanup temp file ${upload.id}: ${error.message}`);
      }
    }

    this.logger.log(`Cleaned up ${deletedCount} temporary files`);
    return { deletedCount };
  }

  private validateFile(file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `Invalid file type. Allowed types: ${this.allowedMimeTypes.join(', ')}`
      );
    }

    if (file.size > this.maxFileSize) {
      throw new BadRequestException(
        `File too large. Maximum size: ${this.maxFileSize / 1024 / 1024}MB`
      );
    }
  }

  getImageUrl(uploadId: string): string {
    return `/api/uploads/${uploadId}`;
  }

  async moveUploadToCategory(uploadId: string, newCategory: 'products' | 'users' | 'categories' | 'rewards' | 'temp') {
    const upload = await this.getUploadById(uploadId);

    const oldPath = path.join(this.uploadDir, upload.path);
    const newFileName = path.basename(upload.path);
    const newRelativePath = `${newCategory}/${newFileName}`;
    const newPath = path.join(this.uploadDir, newRelativePath);

    try {
      // 移动文件
      fs.renameSync(oldPath, newPath);

      // 更新数据库
      const updatedUpload = await this.prisma.upload.update({
        where: { id: uploadId },
        data: {
          category: newCategory,
          path: newRelativePath,
          url: `/uploads/${newRelativePath}`,
        },
      });

      return updatedUpload;

    } catch (error) {
      this.logger.error(`Failed to move upload: ${error.message}`);
      throw new BadRequestException('Failed to move upload');
    }
  }
}
