"use client";

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { getCookie } from 'cookies-next';

export function AffiliateTracker() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const trackVisit = async () => {
      try {
        // 获取推广码，优先使用cookie中的值，其次使用URL中的ref参数
        const cookieRefCode = getCookie('referralCode') as string;
        const urlRefCode = searchParams.get('ref');
        const referralCode = cookieRefCode || urlRefCode;
        
        if (!referralCode) {
          console.log('No referral code found');
          return;
        }

        // 获取来源信息
        let source = 'direct';
        let referrer = '';

        // 如果是从外部网站来的
        if (document.referrer) {
          try {
            const referrerUrl = new URL(document.referrer);
            source = referrerUrl.hostname;
            referrer = document.referrer;
          } catch (e) {
            console.error('Invalid referrer URL:', e);
          }
        }

        // 如果URL中有utm参数
        const utmSource = searchParams.get('utm_source');
        if (utmSource) {
          source = utmSource;
        }

        // 发送访问记录
        const response = await fetch('/api/affiliate/visit', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            referralCode,
            path: pathname,
            source,
            referrer,
            userAgent: navigator.userAgent,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Failed to track visit: ${errorData.error || response.statusText}`);
        }
        
        console.log('Affiliate visit tracked successfully');
      } catch (error) {
        console.error('Failed to track affiliate visit:', error);
      }
    };

    // 只在页面加载和路径变化时追踪
    trackVisit();
  }, [pathname, searchParams]);

  return null; // 这是一个不可见的追踪组件
} 