import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";
import { Product, convertOdooToProduct } from "@/app/types/product";
import { OdooService } from "@/app/services/odooService";
import { PRODUCT_TYPES, PRODUCT_STATUS, PRODUCT_DEFAULTS } from "@/app/config/odoo";

// 新 Odoo 账号配置（从 .env 读取）
const ODOO_QR_CONFIG = {
  address: process.env.ODOO_QR_ADDRESS || '',
  channelId: process.env.ODOO_QR_CHANNEL_ID || '',
  channelLanguage: process.env.ODOO_QR_CHANNEL_LANGUAGE || 'en_US',
  authSecret: process.env.ODOO_QR_AUTH_SECRET || '',
  signMethod: process.env.ODOO_QR_SIGN_METHOD || 'md5',
} as const;

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// 同步请求接口
interface SyncRequest {
  productTypes?: string[];
  skipEmptyVariantsAndZeroPrice?: boolean;
  allProductTypes?: string[]; // 所有可用的产品类型
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // 解析请求体
    let requestData: SyncRequest = {};
    try {
      requestData = await req.json();
    } catch (e) {
      // 如果请求体为空或无效JSON，使用默认值
    }

    const {
      productTypes,
      skipEmptyVariantsAndZeroPrice = true,
      allProductTypes
    } = requestData;

    // 如果客户端提供了自定义产品类型列表，记录到日志中
    if (allProductTypes && Array.isArray(allProductTypes) && allProductTypes.length > 0) {
      console.log(`[QR_SYNC] Using custom product types: ${allProductTypes.join(', ')}`);
    }

    // 查找/创建 qr_code 分类
    let qrCategory = await prisma.category.findFirst({ where: { name: 'qr_code' } });
    if (!qrCategory) {
      qrCategory = await prisma.category.create({
        data: {
          name: 'qr_code',
          description: 'Category for QR code products',
        },
      });
    }

    const odooService = new OdooService(ODOO_QR_CONFIG);
    let allConvertedProducts: Product[] = [];
    const stats = {
      totalProcessed: 0,
      totalSkipped: 0,
      totalUpdated: 0,
      totalCreated: 0,
      totalVariants: 0,
      byType: {} as Record<string, any>,
    };

    // 使用请求中的产品类型列表，如果没有提供则使用所有类型
    const typesToSync = productTypes && productTypes.length > 0 ? productTypes : PRODUCT_TYPES;

    for (const productType of typesToSync) {
      stats.byType[productType] = {
        processed: 0,
        skipped: 0,
        updated: 0,
        created: 0,
        variants: 0,
      };
      try {
        const productsResponse = await odooService.pullProducts(productType);
        if (!productsResponse?.result?.data) continue;
        const products = productsResponse.result.data;
        for (const product of products) {
          try {
            stats.totalProcessed++;
            stats.byType[productType].processed++;
            // 转换并强制分类为 qr_code
            const converted = { ...convertOdooToProduct(product), category: 'qr_code' };
            stats.totalVariants += converted.variants.length;
            stats.byType[productType].variants += converted.variants.length;

            // 跳过变体数量为0且价格为0的商品
            if (skipEmptyVariantsAndZeroPrice && converted.variants.length === 0 && converted.price === 0) {
              console.log(`[QR_SYNC] Skipping product ${converted.sku} with 0 variants and 0 price`);
              stats.totalSkipped++;
              stats.byType[productType].skipped++;
              continue;
            }

            // upsert 商品
            const baseProductData = {
              name: converted.name,
              description: converted.description,
              websiteDescription: converted.websiteDescription,
              price: converted.price,
              images: [],
              stock: converted.stock,
              status: converted.isActive ? PRODUCT_STATUS.ACTIVE : PRODUCT_STATUS.INACTIVE,
              off_shelve: converted.off_shelve,
              requiredUID: converted.requiredUID,
              mcc: converted.mcc,
              dataSize: converted.dataSize,
              planType: converted.planType,
              country: converted.country,
              countryCode: converted.countryCode,
              odooLastSyncAt: new Date(),
              specifications: {
                odooId: converted.metadata.odooId,
                odooProductCode: converted.metadata.odooProductCode,
                sourceType: 'qr', // 标记为QR码产品
              },
              category: {
                connect: { id: qrCategory.id },
              },
            };

            // 只查找和更新QR账号的产品
            const existingProduct = await prisma.product.findFirst({
              where: {
                sku: converted.sku,
                specifications: {
                  path: ['sourceType'],
                  equals: 'qr'
                }
              }
            });

            await prisma.product.upsert({
              where: { sku: converted.sku },
              update: {
                ...baseProductData,
                variants: {
                  deleteMany: {},
                  create: converted.variants.map(variant => ({
                    price: Number(variant.price),
                    currency: variant.currency || PRODUCT_DEFAULTS.currency,
                    attributes: variant.attributes,
                    variantCode: variant.variantCode,
                    duration: variant.duration,
                    durationType: variant.durationType,
                  })),
                },
                parameters: {
                  deleteMany: {},
                  create: converted.parameters
                    .filter(param => param.code !== 'mcc')
                    .map(param => ({
                      code: param.code,
                      name: param.name,
                      value: param.value,
                    })),
                },
              },
              create: {
                ...baseProductData,
                sku: converted.sku,
                variants: {
                  create: converted.variants.map(variant => ({
                    price: Number(variant.price),
                    currency: variant.currency || PRODUCT_DEFAULTS.currency,
                    attributes: variant.attributes,
                    variantCode: variant.variantCode,
                    duration: variant.duration,
                    durationType: variant.durationType,
                  })),
                },
                parameters: {
                  create: converted.parameters
                    .filter(param => param.code !== 'mcc')
                    .map(param => ({
                      code: param.code,
                      name: param.name,
                      value: param.value,
                    })),
                },
              },
              include: {
                variants: true,
                parameters: true,
              },
            });

            if (existingProduct) {
              stats.totalUpdated++;
              stats.byType[productType].updated++;
            } else {
              stats.totalCreated++;
              stats.byType[productType].created++;
            }
            allConvertedProducts.push(converted);
          } catch (error) {
            stats.totalSkipped++;
            stats.byType[productType].skipped++;
          }
        }
      } catch (error) {
        // 跳过该类型
      }
    }

    return NextResponse.json({
      message: 'QR Odoo products sync completed',
      stats,
      count: allConvertedProducts.length,
    });
  } catch (error) {
    console.error('[QR_PRODUCTS_SYNC]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}