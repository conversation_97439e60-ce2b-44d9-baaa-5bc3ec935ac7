import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";

export const dynamic = 'force-dynamic';

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // 使用单个查询获取所有需要的数据
    const affiliateProfile = await prisma.affiliateProfile.findUnique({
      where: {
        userId: session.user.id,
      },
      include: {
        referrals: {
          include: {
            order: true,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10,
        },
        withdrawals: {
          orderBy: {
            createdAt: "desc",
          },
          take: 5,
        },
      },
    });

    if (!affiliateProfile) {
      return new NextResponse("Affiliate profile not found", { status: 404 });
    }

    // 获取统计数据
    const [totalReferrals, totalSubscribers, totalRegisteredUsers] = await Promise.all([
      prisma.affiliateReferral.count({
        where: {
          affiliateId: affiliateProfile.id,
        },
      }),
      prisma.preSaleSubscription.count({
        where: {
          referralCode: affiliateProfile.code,
        },
      }),
      prisma.preSaleSubscription.count({
        where: {
          referralCode: affiliateProfile.code,
          convertedToUser: true,
        },
      }),
    ]);

    const response = NextResponse.json({
      profile: {
        code: affiliateProfile.code,
        status: affiliateProfile.status,
        commissionRate: affiliateProfile.commissionRate,
        discountRate: affiliateProfile.discountRate,
        totalEarnings: affiliateProfile.totalEarnings,
      },
      stats: {
        totalReferrals,
        totalSubscribers,
        totalRegisteredUsers,
      },
      recentReferrals: affiliateProfile.referrals,
      recentWithdrawals: affiliateProfile.withdrawals,
    });

    // 添加缓存控制头
    response.headers.set('Cache-Control', 'private, max-age=10');

    return response;
  } catch (error) {
    console.error("[AFFILIATE_DASHBOARD]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
} 