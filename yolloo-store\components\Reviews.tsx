import { Star } from 'lucide-react'

interface Review {
  id: string
  rating: number
  comment: string
  user: {
    name: string
  }
}

export default function Reviews({ reviews }: { reviews: Review[] }) {
  return (
    <div className="space-y-4">
      {reviews.map((review) => (
        <div key={review.id} className="bg-white shadow-md rounded-lg p-4">
          <div className="flex items-center mb-2">
            <p className="font-semibold mr-2">{review.user.name}</p>
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-4 w-4 ${i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                />
              ))}
            </div>
          </div>
          <p className="text-gray-600">{review.comment}</p>
        </div>
      ))}
    </div>
  )
}

