# 构建阶段
FROM node:22-alpine AS builder
WORKDIR /app
RUN apk add --no-cache openssl
COPY mobile-api/package.json ./
COPY mobile-api/package-lock.json ./
COPY prisma ./prisma/
RUN npm install
COPY mobile-api/ .
# 确保 Prisma 客户端正确生成
RUN npx prisma generate
# 验证生成的客户端
RUN ls -la node_modules/.prisma/client/
RUN npm run build

# 运行阶段
FROM node:22-alpine AS runner
WORKDIR /app
RUN apk add --no-cache openssl postgresql-client
# 安装 ts-node 全局，用于运行 seed 脚本
RUN npm install -g ts-node
COPY --from=builder /app ./
RUN rm -f .env || true

EXPOSE 4000

# 复制启动脚本并设置权限
COPY mobile-api/start.sh /app/start.sh
RUN chmod +x /app/start.sh

CMD ["/app/start.sh"]