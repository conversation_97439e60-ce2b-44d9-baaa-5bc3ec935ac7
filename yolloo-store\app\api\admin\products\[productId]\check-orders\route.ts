import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function GET(
  req: Request,
  { params }: { params: { productId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // 首先获取产品的sku
    const product = await prisma.product.findUnique({
      where: { id: params.productId },
      select: { sku: true }
    });

    if (!product) {
      return new NextResponse("Product not found", { status: 404 });
    }

    // 检查商品是否存在于任何订单中（使用productCode而不是旧的productId）
    const orderCount = await prisma.orderItem.count({
      where: {
        productCode: product.sku
      }
    })

    return NextResponse.json({
      hasOrders: orderCount > 0
    })
  } catch (error) {
    console.error("[PRODUCT_CHECK_ORDERS]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}