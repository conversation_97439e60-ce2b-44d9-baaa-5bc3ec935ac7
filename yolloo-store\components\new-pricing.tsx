'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import Image from 'next/image'
import clsx from 'clsx'
import { motion } from 'framer-motion'

const PricingCard = ({
  title,
  description,
  price,
  originalPrice,
  imageSrc,
  isPopular = false,
  isPlus = false,
  productLink,
  buttonText
}: {
  title: string
  description: string
  price: number
  originalPrice?: number
  imageSrc: string
  isPopular?: boolean
  isPlus?: boolean
  productLink: string
  buttonText: string
}) => {
  const { status } = useSession()
  const isAuthenticated = status === "authenticated"

  // Determine the link destination based on authentication status
  const linkHref = isAuthenticated ? productLink : `/auth/signin?callbackUrl=${encodeURIComponent(productLink)}`

  return (
    <div className={clsx(
      "relative p-8 rounded-3xl transition-all duration-500 hover:-translate-y-2 flex flex-col",
      isPopular
        ? "bg-gradient-to-br from-[#D63A5A] to-[#B82E4E] shadow-[0_12px_40px_rgba(214,58,90,0.3)]"
        : isPlus
        ? "shadow-[0_8px_32px_rgba(0,0,0,0.1)] hover:shadow-[0_12px_40px_rgba(0,0,0,0.15)]"
        : "bg-white shadow-[0_8px_32px_rgba(0,0,0,0.1)] hover:shadow-[0_12px_40px_rgba(0,0,0,0.15)]"
    )}
    style={isPlus ? { backgroundColor: '#FFC7CD' } : {}}>
      {/* Badge */}
      <div className={clsx(
        "inline-flex items-center justify-center px-4 py-2 rounded-full text-sm font-semibold mb-6",
        isPopular
          ? "bg-white/20 text-white"
          : "bg-[#FFE8ED] text-[#D63A5A]"
      )}>
        {title}
      </div>

      {/* Description */}
      <p className={clsx(
        "text-sm mb-6 font-medium text-center",
        isPopular ? "text-white/90" : "text-gray-600"
      )}>
        {description}
      </p>

      {/* Character Image */}
      <div className="flex justify-center mb-8">
        <div className="w-24 h-24 relative">
          <Image
            src={imageSrc}
            alt={`${title} character`}
            fill
            className="object-contain"
          />
        </div>
      </div>

      {/* Price */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-2">
          <span className={clsx(
            "text-4xl font-bold",
            isPopular ? "text-white" : "text-[#D63A5A]"
          )}>
            ${price}
          </span>
          <span className={clsx(
            "text-lg",
            isPopular ? "text-white/80" : "text-gray-500"
          )}>
            USD
          </span>
        </div>
      </div>

      {/* Get Card Button */}
      <Link
        href={linkHref}
        className={clsx(
          "block w-full py-4 px-6 text-center rounded-full font-semibold transition-all duration-300 transform hover:-translate-y-1 text-lg",
          isPopular
            ? "bg-white text-[#D63A5A] hover:bg-gray-50 shadow-lg hover:shadow-xl"
            : "bg-gradient-to-r from-[#D63A5A] to-[#F799A6] text-white hover:from-[#C23350] hover:to-[#E88A97] shadow-lg hover:shadow-xl"
        )}
      >
        {buttonText}
      </Link>
    </div>
  )
}

export default function NewPricing() {
  const [productLinks, setProductLinks] = useState({
    lite: "",
    plus: "",
    max: ""
  });

  useEffect(() => {
    const fetchProductLinks = async () => {
      try {
        const response = await fetch("/api/products/get-card-links");
        const data = await response.json();
        setProductLinks(data);
      } catch (error) {
        console.error("Error fetching product links:", error);
      }
    };

    fetchProductLinks();
  }, []);

  return (
    <section id="pricing" className="py-20 bg-gradient-to-b from-[#FFF0F5] to-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center justify-center px-6 py-2 bg-[#FFE8ED] rounded-full text-[#D63A5A] font-semibold text-sm mb-6">
            Pricing Cards
          </div>

          <h2 className="text-4xl sm:text-5xl font-bold mb-6 text-[#D63A5A] leading-tight">
            Choose Your Perfect Yolloo Card
          </h2>

          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Select your ideal Yolloo card with eSIM capabilities. Our cards allow you to store
            and manage multiple eSIM profiles, making international travel seamless and
            affordable.
          </p>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <PricingCard
              title="Lite"
              description="Perfect for light travelers"
              price={12}
              imageSrc="/Frame 113.svg"
              productLink={productLinks.lite}
              buttonText="Get Card"
            />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <PricingCard
              title="Plus"
              description="Best for regular travelers"
              price={21}
              imageSrc="/Frame 121.svg"
              isPlus
              productLink={productLinks.plus}
              buttonText="Get Card"
            />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <PricingCard
              title="Max"
              description="Ultimate flexibility"
              price={23}
              imageSrc="/Frame 123.svg"
              isPopular
              productLink={productLinks.max}
              buttonText="Get Card"
            />
          </motion.div>
        </div>

        {/* Disclaimer */}
        <div className="text-center mt-12">
          <p className="text-sm text-gray-500">
            * Physical card supported with all plans. eSIM data plans are sold separately.
          </p>
        </div>
      </div>
    </section>
  )
}
