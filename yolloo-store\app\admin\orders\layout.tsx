import React from "react"
import { prisma } from "@/lib/prisma"
import { OrdersProvider } from "./orders-provider"

async function getOrders() {
  return await prisma.order.findMany({
    orderBy: {
      createdAt: "desc",
    },
    include: {
      items: {
        select: {
          id: true,
          quantity: true,
          price: true,
          productCode: true,
          variantCode: true,
          variantText: true,
        },
      },
      shippingAddress: true,
      user: true,
    },
  })
}

export default async function OrdersLayout({ children }: { children: React.ReactNode }) {
  const orders = await getOrders()

  if (!children) {
    return null
  }

  return <OrdersProvider orders={orders}>{children}</OrdersProvider>
}