'use client';

import { useEffect, useState } from 'react';
import { toast, Toaster } from 'react-hot-toast';
import { useSearchParams } from 'next/navigation';
import Cookies from 'js-cookie';
import Yolloo<PERSON>ogo from "@/components/YollooLogo"
import { DateUtils, safeParseDate } from "@/lib/utils"

export default function PresalePage() {
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle referral code in cookies
  useEffect(() => {
    const refCode = searchParams.get('ref');
    if (refCode) {
      // Set cookie with 30 days expiry
      Cookies.set('referralCode_local', refCode, { expires: 30 });
    }
  }, [searchParams]);

  // 定义目标日期和计算剩余天数
  const targetDate = safeParseDate(process.env.NEXT_PUBLIC_PRESALE_TARGET_DATE || '2025-01-08T00:00:00') || new Date('2025-01-08T00:00:00');
  const now = new Date();
  const daysLeft = Math.max(0, DateUtils.daysBetween(now, targetDate));

  const scrollToNextSection = () => {
    const nextSection = document.getElementById('subscription-section');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleSubscribe = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }

    const email = (e.target as HTMLFormElement).email.value;
    const referralCode = Cookies.get('referralCode_local');

    try {
      setIsSubmitting(true);
      console.log(email, referralCode);

      const response = await fetch('/api/presale/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          referralCode,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Welcome to YOLLOO! Your exclusive 30% discount code will be sent to ' + email, {
          duration: 5000,
          position: 'top-center',
          style: {
            background: 'rgba(0, 0, 0, 0.8)',
            color: '#fff',
            border: '1px solid rgba(220, 38, 38, 0.3)',
            backdropFilter: 'blur(8px)',
          },
        });
        (e.target as HTMLFormElement).reset();
      } else {
        toast.error(data.message || 'Something went wrong. Please try again.', {
          duration: 5000,
          position: 'top-center',
          style: {
            background: 'rgba(0, 0, 0, 0.8)',
            color: '#fff',
            border: '1px solid rgba(220, 38, 38, 0.3)',
            backdropFilter: 'blur(8px)',
          },
        });
      }
    } catch (error) {
      toast.error('Failed to subscribe. Please try again later.', {
        duration: 5000,
        position: 'top-center',
        style: {
          background: 'rgba(0, 0, 0, 0.8)',
          color: '#fff',
          border: '1px solid rgba(220, 38, 38, 0.3)',
          backdropFilter: 'blur(8px)',
        },
      });
    } finally {
      setIsSubmitting(false);
      // Add a delay before allowing next submission
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  };

  return (
    <div className="overflow-x-hidden">
      <Toaster />
      <div className="relative min-h-[100dvh] w-full bg-gradient-to-b from-black via-red-900 to-black overflow-hidden">
        {/* Logo */}
        <div className="absolute top-6 left-6">
          <YollooLogo className="h-7 sm:h-12 w-auto" />
        </div>

        {/* Main Content */}
        <div className="relative z-10 flex flex-col items-center justify-center min-h-[100dvh] text-white px-4">
          <div className="text-center space-y-12 md:space-y-32 -mt-8">
            {/* Slogan */}
            <div className="space-y-0.5 md:space-y-2">
              <h2 className="text-4xl sm:text-5xl md:text-7xl font-bold italic text-white-500 tracking-wider animate-pulse">
                ONE CARD,
              </h2>
              <h2 className="text-4xl sm:text-5xl md:text-7xl font-bold italic text-white-500 tracking-wider animate-pulse">
                ONE WORLD
              </h2>
            </div>

            {/* Card Visual */}
            <div className="w-64 h-40 sm:w-96 sm:h-56 md:w-[32rem] md:h-72 mx-auto relative">
              <div className="absolute inset-0 bg-gradient-to-br from-red-600 to-red-900 rounded-xl shadow-2xl transform rotate-12 transition-transform hover:rotate-0 duration-500">
                {/* Embossed YOLLOO text */}
                <div className="absolute bottom-8 right-8 text-xl sm:text-2xl md:text-3xl font-bold tracking-wider text-transparent bg-clip-text bg-gradient-to-b from-red-500/40 to-red-700/40 select-none" style={{ textShadow: '1px 1px 2px rgba(255,255,255,0.1), -1px -1px 2px rgba(0,0,0,0.2)' }}>
                  YOLLOO
                </div>
              </div>
            </div>

            {/* Countdown */}
            <div className="space-y-2 md:space-y-8">
              <div className="text-7xl sm:text-8xl md:text-9xl font-bold text-red-500 tracking-tight">
                {daysLeft}
              </div>
              <div className="text-xl sm:text-2xl md:text-4xl font-semibold tracking-widest">
                DAYS LEFT
              </div>
            </div>
          </div>
        </div>

        {/* Background Animation */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,0,0,0.1)_0%,transparent_50%)] animate-pulse"></div>

        {/* Scroll Down Arrow */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-center items-center h-24">
          <button
            onClick={scrollToNextSection}
            className="cursor-pointer animate-bounce z-20"
            aria-label="Scroll to subscription section"
          >
            <div className="w-10 h-10 md:w-12 md:h-12 flex items-center justify-center rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-300 group">
              <svg
                className="w-5 h-5 md:w-6 md:h-6 text-white transform rotate-90 group-hover:scale-110 transition-transform duration-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={3}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </div>
          </button>
        </div>
      </div>

      {/* Subscription Form Section */}
      <div id="subscription-section" className="relative z-10 min-h-[100dvh] flex flex-col items-center justify-center px-4 py-16 bg-gradient-to-b from-black via-red-800 to-black">
        <div className="w-full max-w-xl space-y-6 md:space-y-10 p-6 md:p-12 bg-black/30 backdrop-blur-lg rounded-2xl border border-red-500/20 shadow-2xl hover:border-red-500/40 transition-all duration-500">
          <div className="text-center space-y-3 md:space-y-4">
            <h2 className="text-2xl md:text-4xl font-bold text-white tracking-tight">Join the Exclusive Launch</h2>
            <p className="text-base md:text-lg font-light leading-relaxed text-gray-200 px-2">
              Be the first to experience YOLLOO and receive an exclusive early-access discount.
            </p>
            <div className="mt-2 inline-block px-4 py-1.5 md:px-6 md:py-2 bg-red-500/20 rounded-full">
              <span className="text-sm md:text-base text-red-300 font-semibold">🎉 Special Offer: 30% OFF for Early Birds</span>
            </div>
          </div>

          <form onSubmit={handleSubscribe} className="mt-4 md:mt-8 space-y-6">
            <div className="relative group">
              <input
                id="email"
                name="email"
                type="email"
                required
                className="block w-full px-4 py-3.5 md:px-6 md:py-5 rounded-xl bg-black/40 border-2 border-red-500/30 text-white placeholder:text-gray-300 focus:outline-none focus:border-red-500/60 focus:ring-1 focus:ring-red-500/60 transition-all duration-300 text-base"
                placeholder="Enter your email address"
              />
              <button
                type="submit"
                disabled={isSubmitting}
                className="absolute right-1.5 top-1/2 -translate-y-1/2 px-3 md:px-8 py-2 md:py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg text-sm md:text-base font-medium hover:from-red-400 hover:to-red-500 transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:ring-offset-2 focus:ring-offset-black disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <span className="flex items-center space-x-2">
                    <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                    </svg>
                    <span>Subscribing...</span>
                  </span>
                ) : (
                  'Subscribe'
                )}
              </button>
            </div>
          </form>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-8 justify-items-center text-xs md:text-sm text-gray-300">
            <div className="flex items-center space-x-2">
              <svg className="w-4 md:w-5 h-4 md:h-5 text-red-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
              </svg>
              <span>Exclusive Offers</span>
            </div>
            <div className="flex items-center space-x-2">
              <svg className="w-4 md:w-5 h-4 md:h-5 text-red-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
              </svg>
              <span>Early Access</span>
            </div>
            <div className="flex items-center space-x-2">
              <svg className="w-4 md:w-5 h-4 md:h-5 text-red-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
              </svg>
              <span>Special Updates</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}