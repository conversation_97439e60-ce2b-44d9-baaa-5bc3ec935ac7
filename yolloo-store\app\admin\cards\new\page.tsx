import { Metada<PERSON> } from "next"
import { getServerSession } from "next-auth"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { CardForm } from "../card-form"

export const metadata: Metadata = {
  title: "New Card",
  description: "Add a new Yolloo Card",
}

export default async function NewCardPage() {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== "ADMIN") {
    redirect("/auth/signin")
  }

  return (
    <div className="flex flex-col gap-4 p-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">New Card</h2>
        <p className="text-muted-foreground">
          Add a new Yolloo Card to the system
        </p>
      </div>
      <CardForm />
    </div>
  )
} 