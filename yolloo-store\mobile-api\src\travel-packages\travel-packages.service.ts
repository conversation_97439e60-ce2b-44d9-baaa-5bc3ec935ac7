import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { RatingService } from '../rating/rating.service';
import { TravelPackagesQueryDto, TravelPackageOrderDto } from './dto/travel-packages-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';

@Injectable()
export class TravelPackagesService {
  private readonly logger = new Logger(TravelPackagesService.name);

  constructor(
    private prisma: PrismaService,
    private ratingService: RatingService
  ) {}

  async getTravelPackages(query: TravelPackagesQueryDto, ctx: RequestContext) {
    console.log('Context in getTravelPackages:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 构建查询条件 - 查找旅游套餐相关的产品
      const whereConditions: any = {
        status: 'ACTIVE',
        off_shelve: false,
        // 查找包含旅游、套餐、travel、package等关键词的产品
        OR: [
          { name: { contains: '旅游', mode: 'insensitive' } },
          { name: { contains: 'travel', mode: 'insensitive' } },
          { name: { contains: 'package', mode: 'insensitive' } },
          { name: { contains: '套餐', mode: 'insensitive' } },
          { description: { contains: '旅游', mode: 'insensitive' } },
          { description: { contains: 'travel', mode: 'insensitive' } },
          { description: { contains: 'package', mode: 'insensitive' } },
          { description: { contains: '套餐', mode: 'insensitive' } },
        ],
      };

      // 添加筛选条件
      if (query.destination) {
        whereConditions.AND = whereConditions.AND || [];
        whereConditions.AND.push({
          OR: [
            { country: { contains: query.destination, mode: 'insensitive' } },
            { countryCode: { contains: query.destination, mode: 'insensitive' } },
            { name: { contains: query.destination, mode: 'insensitive' } },
          ],
        });
      }

      if (query.region) {
        whereConditions.AND = whereConditions.AND || [];
        whereConditions.AND.push({
          OR: [
            { name: { contains: query.region, mode: 'insensitive' } },
            { description: { contains: query.region, mode: 'insensitive' } },
          ],
        });
      }

      // 查询产品总数
      const total = await this.prisma.product.count({
        where: whereConditions,
      });

      // 如果没有找到旅游套餐产品，返回空结果
      if (total === 0) {
        return {
          packages: [],
          pagination: {
            total: 0,
            page: query.page!,
            pageSize: query.pageSize!,
            hasMore: false,
          },
          filters: {
            regions: [],
            durations: [],
            dataSizes: [],
          },
          context: {
            language: ctx.language,
            theme: ctx.theme,
            currency: ctx.currency,
          },
        };
      }

      // 查询产品列表
      const skip = (query.page! - 1) * query.pageSize!;
      const products = await this.prisma.product.findMany({
        where: whereConditions,
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          variants: {
            select: {
              id: true,
              price: true,
              currency: true,
            },
            orderBy: {
              price: 'asc',
            },
          },
          reviews: {
            select: {
              rating: true,
            },
          },
        },
        skip,
        take: query.pageSize!,
        orderBy: this.buildOrderBy(query.sortBy, query.sortOrder),
      });

      // 格式化产品数据为旅游套餐格式
      const formattedPackages = products.map(product => this.formatProductAsPackage(product, ctx, isZh));

      return {
        packages: formattedPackages,
        pagination: {
          total,
          page: query.page!,
          pageSize: query.pageSize!,
          hasMore: skip + formattedPackages.length < total,
        },
        filters: {
          destination: query.destination,
          region: query.region,
          duration: query.duration,
          dataSize: query.dataSize,
        },
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };

    } catch (error) {
      this.logger.error('Error fetching travel packages:', error);
      throw new Error('Failed to fetch travel packages');
    }
  }

  async getPackageById(packageId: string, ctx: RequestContext) {
    console.log('Context in getPackageById:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 查询具体的产品
      const product = await this.prisma.product.findUnique({
        where: { id: packageId },
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          variants: {
            select: {
              id: true,
              price: true,
              currency: true,
            },
            orderBy: {
              price: 'asc',
            },
          },
          reviews: {
            select: {
              rating: true,
              comment: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 10,
          },
        },
      });

      if (!product) {
        throw new Error('Travel package not found');
      }

      // 格式化为详细的旅游套餐数据
      const packageDetails = this.formatProductAsDetailedPackage(product, ctx, isZh);

      return {
        package: packageDetails,
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };

    } catch (error) {
      this.logger.error('Error fetching package details:', error);
      throw new Error('Failed to fetch travel package details');
    }
  }

  async createTravelOrder(orderData: TravelPackageOrderDto, ctx: RequestContext) {
    console.log('Context in createTravelOrder:', ctx);
    
    const isZh = ctx.language.startsWith('zh');
    
    // Mock order creation
    const order = {
      id: `travel_${Date.now()}`,
      packageId: orderData.packageId,
      startDate: orderData.startDate,
      countries: orderData.countries,
      status: 'pending',
      statusText: isZh ? '待支付' : 'Pending Payment',
      createdAt: new Date().toISOString(),
      estimatedDeliveryTime: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes
    };

    return {
      order,
      message: isZh ? '旅游套餐订单创建成功，请完成支付' : 'Travel package order created successfully, please complete payment',
    };
  }

  private async formatProductAsPackage(product: any, ctx: RequestContext, isZh: boolean) {
    // 使用RatingService计算真实评分
    const ratingData = await this.ratingService.calculateProductRating(product.id);

    // 获取最低价格（从变体中）
    const lowestPrice = product.variants.length > 0
      ? Math.min(...product.variants.map((v: any) => Number(v.price)))
      : Number(product.price);

    // 获取货币
    const currency = product.variants.length > 0
      ? product.variants[0].currency || ctx.currency
      : ctx.currency;

    // 解析规格中的国家信息
    let countries: string[] = [];
    let features: string[] = [];
    let duration = 7; // 默认7天

    try {
      const specs = typeof product.specifications === 'string'
        ? JSON.parse(product.specifications)
        : product.specifications;

      countries = specs?.countries || [];
      features = specs?.features || [];
      duration = specs?.duration || 7;
    } catch (error) {
      this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
    }

    // 如果规格中没有国家信息，尝试从country字段解析
    if (countries.length === 0 && product.country) {
      countries = product.country.split(/[,;]/).map((c: string) => c.trim()).filter((c: string) => c);
    }

    // 默认特性
    if (features.length === 0) {
      features = [
        isZh ? '高速流量' : 'High-speed data',
        isZh ? '全国覆盖' : 'Nationwide coverage',
        isZh ? '即插即用' : 'Plug and play',
        isZh ? '24/7客服支持' : '24/7 customer support'
      ];
    }

    // 确定目的地和地区
    const destination = countries.length > 0 ? countries[0].toLowerCase() : 'unknown';
    const region = this.getRegionFromCountries(countries);

    return {
      id: product.id,
      name: product.name,
      description: product.description,
      destination: destination,
      region: region,
      countries: countries,
      duration: duration,
      dataSize: product.dataSize ? this.formatDataSize(product.dataSize) : 'unlimited',
      price: lowestPrice,
      originalPrice: lowestPrice * 1.2, // 假设原价比现价高20%
      currency: currency,
      features: features,
      networkType: '4G/5G',
      coverage: countries.length > 0 ? (isZh ? `${countries.join('、')}全境` : `All of ${countries.join(', ')}`) : (isZh ? '全球覆盖' : 'Global coverage'),
      imageUrl: product.images && product.images.length > 0
        ? product.images[0]
        : '/images/defaults/travel-package-placeholder.jpg',
      isPopular: ratingData.averageRating >= 4.5,
      rating: ratingData.averageRating,
      reviewCount: ratingData.totalReviews,
    };
  }

  private async formatProductAsDetailedPackage(product: any, ctx: RequestContext, isZh: boolean) {
    const basicPackage = await this.formatProductAsPackage(product, ctx, isZh);

    return {
      ...basicPackage,
      detailedInfo: {
        activation: isZh ? '到达目的地后自动激活' : 'Auto-activation upon arrival at destination',
        validity: isZh ? `激活后${basicPackage.duration}天有效` : `Valid for ${basicPackage.duration} days after activation`,
        speed: isZh ? '下载速度最高150Mbps' : 'Download speed up to 150Mbps',
        compatibility: isZh ? '支持所有eSIM设备' : 'Compatible with all eSIM devices',
      },
      includedServices: [
        isZh ? '免费接收短信' : 'Free incoming SMS',
        isZh ? '热点分享功能' : 'Hotspot sharing',
        isZh ? '多设备支持' : 'Multi-device support',
        isZh ? '实时流量查询' : 'Real-time data usage check',
      ],
      variants: product.variants.map((variant: any) => ({
        id: variant.id,
        price: Number(variant.price),
        currency: variant.currency,
      })),
      reviews: product.reviews.map((review: any) => ({
        rating: review.rating,
        comment: review.comment,
        createdAt: review.createdAt,
      })),
    };
  }

  private buildOrderBy(sortBy?: string, sortOrder?: 'asc' | 'desc') {
    const order = sortOrder || 'asc';

    switch (sortBy) {
      case 'price':
        return { price: order };
      case 'rating':
        return { createdAt: order as 'asc' | 'desc' }; // 由于评分需要计算，暂时用创建时间排序
      case 'name':
        return { name: order };
      default:
        return { createdAt: 'desc' as 'asc' | 'desc' };
    }
  }

  private getRegionFromCountries(countries: string[]): string {
    if (countries.length === 0) return 'global';

    const asianCountries = ['japan', 'korea', 'china', 'thailand', 'singapore', 'malaysia', 'vietnam', 'philippines'];
    const europeanCountries = ['france', 'germany', 'italy', 'spain', 'uk', 'netherlands', 'switzerland', 'austria'];
    const americanCountries = ['usa', 'canada', 'mexico', 'brazil', 'argentina'];

    const firstCountry = countries[0].toLowerCase();

    if (asianCountries.some(country => firstCountry.includes(country))) {
      return 'asia';
    } else if (europeanCountries.some(country => firstCountry.includes(country))) {
      return 'europe';
    } else if (americanCountries.some(country => firstCountry.includes(country))) {
      return 'america';
    }

    return 'global';
  }

  private formatDataSize(dataSize: number): string {
    if (dataSize >= 1024) {
      const sizeInGB = dataSize / 1024;
      return sizeInGB % 1 === 0 ? `${sizeInGB}GB` : `${sizeInGB.toFixed(1)}GB`;
    } else {
      return `${dataSize}MB`;
    }
  }
}
