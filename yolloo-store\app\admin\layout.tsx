import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { AdminNav } from "@/components/admin/admin-nav"
import Link from "next/link"
import { Icons } from "@/components/icons"

interface AdminLayoutProps {
  children: React.ReactNode
}

export default async function AdminLayout({ children }: AdminLayoutProps) {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== "ADMIN") {
    redirect("/auth/signin")
  }

  return (
    <div className="flex min-h-screen bg-gray-50/50">
      <aside className="fixed inset-y-0 z-50 flex w-72 flex-col bg-white shadow-sm">
        <div className="flex h-16 items-center border-b px-6">
          <Link href="/" className="flex items-center gap-2 font-semibold">
            <Icons.logo className="h-6 w-6" />
            <span className="text-lg">Yolloo Admin</span>
          </Link>
        </div>
        <div className="flex-1 overflow-auto py-6">
          <AdminNav />
        </div>
        <div className="border-t p-6">
          <div className="flex items-center gap-3 rounded-lg bg-gray-50 p-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
              <Icons.user className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1 overflow-hidden">
              <p className="text-sm font-medium">{session.user.name}</p>
              <p className="truncate text-xs text-muted-foreground">
                {session.user.email}
              </p>
            </div>
          </div>
        </div>
      </aside>
      <main className="flex-1 pl-72">
        <div className="container max-w-7xl space-y-6 p-8">
          {children}
        </div>
      </main>
    </div>
  )
} 