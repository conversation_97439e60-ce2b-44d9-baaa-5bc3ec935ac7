import { Metadata } from "next"
import { getServerSession } from "next-auth"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { ImportForm } from "./import-form"

export const metadata: Metadata = {
  title: "Import Cards",
  description: "Import multiple Yolloo Cards",
}

export default async function ImportCardsPage() {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== "ADMIN") {
    redirect("/auth/signin")
  }

  return (
    <div className="flex flex-col gap-4 p-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Import Cards</h2>
        <p className="text-muted-foreground">
          Import multiple Yolloo Cards from a CSV file
        </p>
      </div>
      <ImportForm />
    </div>
  )
} 