import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export const dynamic = 'force-dynamic';

// GET - Get analytics for an organization (admin only)
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }
    
    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { 
            members: true,
            visits: true,
            commissions: true
          }
        }
      }
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Get total visits
    const totalVisits = await prisma.affiliateVisit.count({
      where: { organizationId: params.id }
    });
    
    // Get total conversions (successful referrals)
    const totalConversions = await prisma.affiliateReferral.count({
      where: {
        status: "APPROVED",
        order: {
          status: { in: ["PAID", "PROCESSING", "SHIPPED", "DELIVERED"] }
        },
        affiliate: {
          organizationId: params.id
        }
      }
    });
    
    // Get total commissions
    const commissionsData = await prisma.organizationCommission.aggregate({
      where: { 
        organizationId: params.id,
        status: { in: ["APPROVED", "PAID"] }
      },
      _sum: {
        commissionAmount: true
      }
    });
    
    const totalCommissions = commissionsData._sum.commissionAmount || 0;
    
    // 获取分配给成员的佣金总额
    const memberCommissionsData = await prisma.affiliateReferral.aggregate({
      where: {
        affiliate: {
          organizationId: params.id
        },
        status: { in: ["APPROVED", "PAID"] }
      },
      _sum: {
        commissionAmount: true
      }
    });
    
    const memberCommissions = memberCommissionsData._sum.commissionAmount || 0;
    
    // 计算组织实际收益 = 总佣金 - 分配给成员的佣金
    const organizationActualEarnings = totalCommissions - memberCommissions;
    
    // Calculate conversion rate
    const conversionRate = totalVisits > 0 ? (totalConversions / totalVisits) * 100 : 0;
    
    // 获取所有成员的ID
    const members = await prisma.affiliateProfile.findMany({
      where: { organizationId: params.id },
      select: { id: true }
    });

    const memberIds = members.map(member => member.id);

    // 查询所有关联订单 - 不受限于状态
    const memberOrders = await prisma.affiliateReferral.findMany({
      where: {
        affiliateId: { in: memberIds }
        // 不添加任何过滤条件，确保获取所有订单
      },
      include: {
        affiliate: {
          include: {
            user: true
          }
        },
        order: {
          select: {
            id: true,
            total: true,
            status: true,
            createdAt: true,
            updatedAt: true
          }
        },
        organizationCommission: {
          select: {
            id: true,
            commissionAmount: true,
            status: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 200 // 增加返回数量，确保看到更多订单
    });
    
    // 如果没有订单记录，尝试查询没有关联组织佣金的订单
    if (memberOrders.length === 0) {
      // 检查是否有成员存在订单但没有关联到组织佣金
      const unlinkedReferrals = await prisma.affiliateReferral.findMany({
        where: {
          affiliateId: { in: memberIds },
          organizationCommissionId: null // 没有关联组织佣金的订单
        },
        include: {
          affiliate: {
            include: {
              user: true
            }
          },
          order: true
        },
        take: 50
      });
      
      // 如果发现这类订单，为它们创建组织佣金记录
      if (unlinkedReferrals.length > 0) {
        console.log(`Found ${unlinkedReferrals.length} orders without organization commission links`);
        
        // 这里我们只返回数据，不进行修复，由刷新API执行修复
        // 这样可以保持GET API的幂等性
      }
    }
    
    // Calculate real monthly earnings data
    const monthlyEarnings = await calculateMonthlyEarnings(params.id);

    return NextResponse.json({
      stats: {
        totalVisits,
        totalConversions,
        conversionRate,
        totalCommissions: totalCommissions,
        memberCommissions: memberCommissions,
        organizationActualEarnings: organizationActualEarnings
      },
      memberOrders,
      organization: {
        ...organization,
        monthlyEarnings
      },
      needsRefresh: memberOrders.length === 0 && members.length > 0
    });
    
  } catch (error) {
    console.error("Error fetching organization analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch organization analytics" },
      { status: 500 }
    );
  }
}

// 优化月度收益计算，限制同时进行的数据库查询
const calculateMonthlyEarnings = async (organizationId: string) => {
  // Get current date and set to beginning of month
  const currentDate = new Date();
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const monthsData = [];
  
  // 将计算限制在最近3个月，减少数据库压力
  const numberOfMonths = 6;
  
  // Calculate data for the last few months
  for (let i = 0; i < numberOfMonths; i++) {
    // Create date for this month (0-5 months ago)
    const targetDate = new Date();
    targetDate.setMonth(currentDate.getMonth() - i);
    
    const startOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);
    const endOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0, 23, 59, 59);
    
    // 使用Promise.all并行进行查询，提高效率
    const [monthlyCommissions, memberMonthlyCommissions] = await Promise.all([
      // Get approved organization commissions for this month
      prisma.organizationCommission.aggregate({
        where: {
          organizationId,
          status: { in: ["APPROVED", "PAID"] },
          createdAt: {
            gte: startOfMonth,
            lte: endOfMonth
          }
        },
        _sum: {
          commissionAmount: true
        }
      }),
      
      // Get approved member commissions for this month
      prisma.affiliateReferral.aggregate({
        where: {
          affiliate: {
            organizationId
          },
          status: { in: ["APPROVED", "PAID"] },
          createdAt: {
            gte: startOfMonth,
            lte: endOfMonth
          }
        },
        _sum: {
          commissionAmount: true
        }
      })
    ]);
    
    const totalCommissions = monthlyCommissions._sum.commissionAmount || 0;
    const memberCommissions = memberMonthlyCommissions._sum.commissionAmount || 0;
    
    // Calculate organization's actual earnings for the month
    const actualEarnings = totalCommissions - memberCommissions;
    
    // Add month data
    monthsData.push({
      month: months[targetDate.getMonth()],
      year: targetDate.getFullYear(),
      amount: actualEarnings,
      totalCommissions,
      memberCommissions
    });
  }
  
  // Sort from oldest to newest
  return monthsData.reverse();
} 