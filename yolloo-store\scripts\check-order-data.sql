-- 检查订单数据的脚本
-- 用于诊断迁移后订单显示问题

-- 1. 检查 OrderItem 表的结构
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'OrderItem' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. 查看最近的几个订单项目数据
SELECT
    id,
    "orderId",
    "productCode",
    "variantCode",
    "variantText",
    quantity,
    price,
    uid
FROM "OrderItem"
ORDER BY "orderId" DESC
LIMIT 10;

-- 3. 检查有多少订单项目缺少 variantText
SELECT
    COUNT(*) as total_items,
    COUNT("variantText") as items_with_variant_text,
    COUNT("productCode") as items_with_product_code,
    COUNT("variantCode") as items_with_variant_code
FROM "OrderItem";

-- 4. 查看缺少 variantText 的订单项目
SELECT
    oi.id,
    oi."orderId",
    oi."productCode",
    oi."variantCode",
    oi."variantText",
    o."createdAt" as order_created_at
FROM "OrderItem" oi
JOIN "Order" o ON oi."orderId" = o.id
WHERE oi."variantText" IS NULL OR oi."variantText" = ''
ORDER BY o."createdAt" DESC
LIMIT 10;

-- 5. 检查是否还有产品表中对应的数据
SELECT
    p.id,
    p.sku,
    p.name,
    COUNT(oi.id) as order_items_count
FROM "Product" p
LEFT JOIN "OrderItem" oi ON p.sku = oi."productCode"
GROUP BY p.id, p.sku, p.name
HAVING COUNT(oi.id) > 0
ORDER BY order_items_count DESC
LIMIT 10;

-- 6. 检查产品变体表
SELECT
    pv.id,
    pv."variantCode",
    pv.duration,
    pv."durationType",
    p.name as product_name,
    COUNT(oi.id) as order_items_count
FROM "ProductVariant" pv
JOIN "Product" p ON pv."productId" = p.id
LEFT JOIN "OrderItem" oi ON pv."variantCode" = oi."variantCode"
GROUP BY pv.id, pv."variantCode", pv.duration, pv."durationType", p.name
HAVING COUNT(oi.id) > 0
ORDER BY order_items_count DESC
LIMIT 10;
