'use client'

import { useState, useRef, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { CreditCard, Upload, Download } from "lucide-react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { toast } from "sonner"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import jsQR from "jsqr"

interface Order {
  id: string
  orderNumber: string
  productName: string
  status: string
}

interface ExternalProduct {
  id: string
  price: number
  currency: string
  variant?: {
    id: string
    code: string
    name: string
    price: number
    currency: string
  }
}

interface AddEsimDialogProps {
  cardId: string
  cardNumber: string
  onSuccess?: () => void
}

export function AddEsimDialog({ cardId, cardNumber, onSuccess }: AddEsimDialogProps) {
  const router = useRouter()
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  // Always third party now
  const [lpaString, setLpaString] = useState("")
  const [uploadMethod, setUploadMethod] = useState<"qr" | "text">("qr")
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [externalProduct, setExternalProduct] = useState<ExternalProduct | null>(null)

  // 获取外部数据商品信息
  useEffect(() => {
    const fetchExternalProduct = async () => {
      try {
        const response = await fetch('/api/products/external-data')
        if (!response.ok) {
          throw new Error('Failed to fetch external product')
        }
        const data = await response.json()
        // Convert the first variant to the variant field
        if (data.variants?.[0]) {
          data.variant = data.variants[0]
          delete data.variants
        }
        setExternalProduct(data)
      } catch (error) {
        console.error('Failed to fetch external product:', error)
      }
    }

    fetchExternalProduct()
  }, [])

  // Removed fetchAvailableOrders as we only support third-party eSIMs now

  // 处理QR码上传
  const handleQRUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    try {
      setLoading(true)

      // 创建图片对象
      const img = new Image()
      const canvas = canvasRef.current
      if (!canvas) return

      img.onload = () => {
        // 设置canvas大小为图片大小
        canvas.width = img.width
        canvas.height = img.height

        // 在canvas上绘制图片
        const ctx = canvas.getContext("2d")
        if (!ctx) return

        ctx.drawImage(img, 0, 0)

        // 获取图片数据
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)

        // 使用jsQR解析QR码
        const code = jsQR(imageData.data, imageData.width, imageData.height)

        if (code) {
          if (code.data.startsWith("LPA:")) {
            setLpaString(code.data)
            toast.success("QR code parsed successfully")
          } else {
            toast.error("Invalid eSIM QR code")
          }
        } else {
          toast.error("No QR code found in image")
        }
        setLoading(false)
      }

      img.onerror = () => {
        toast.error("Failed to load image")
        setLoading(false)
      }

      // 读取文件并设置为图片源
      const reader = new FileReader()
      reader.onload = (e) => {
        img.src = e.target?.result as string
      }
      reader.readAsDataURL(file)

    } catch (error) {
      toast.error("Failed to parse QR code")
      setLoading(false)
    }
  }

  // Removed handleNewEsimSubmit as we only support third-party eSIMs now

  // 处理第三方eSIM添加（直接创建订单）
  const handleThirdPartySubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!lpaString || !lpaString.startsWith("LPA:")) {
      toast.error("Please enter a valid LPA string")
      return
    }

    try {
      setLoading(true)

      // 使用新的免付费接口
      const response = await fetch("/api/esims/external-activation", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cardNumber,
          lpaString
        }),
      })

      if (!response.ok) {
        const errorText = await response.text();
        // 尝试解析错误消息为JSON
        try {
          const errorJson = JSON.parse(errorText);
          throw new Error(errorJson.message || errorText);
        } catch (e) {
          // 如果不是JSON格式，直接使用文本
          throw new Error(errorText);
        }
      }

      const data = await response.json()

      toast.success("eSIM activation initiated successfully")
      setOpen(false)
      onSuccess?.() // 触发成功回调
    } catch (error) {
      console.error("Error activating eSIM:", error);
      // 更友好的错误消息处理
      let errorMessage = "Failed to activate eSIM";
      if (error instanceof Error) {
        if (error.message.includes("Default address not found")) {
          errorMessage = "Processing your request...";
          toast.success(errorMessage);
          // 关闭对话框，因为后端会自动创建虚拟地址并继续处理
          setOpen(false);
          onSuccess?.();
          return;
        } else {
          errorMessage = error.message;
        }
      }
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }

  const formatPrice = (price: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(price)
  }

  const handleAddClick = () => {
    setOpen(true)
  }

  return (
    <>
      <canvas ref={canvasRef} style={{ display: 'none' }} />
      <Button variant="outline" onClick={handleAddClick}>
        <Download className="mr-2 h-4 w-4 text-yellow-600" />
        Add 3rd-Party eSIM
        {externalProduct && (
          <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded-full">
            <span className="line-through">{formatPrice(externalProduct.price, externalProduct.currency)}</span>
          </span>
        )}
      </Button>

        <Dialog open={open} onOpenChange={setOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                Add Third-Party eSIM
              </DialogTitle>
              <DialogDescription>
                Upload a QR code or enter the activation code to add a third-party eSIM.
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleThirdPartySubmit}>
              <div className="grid gap-4 py-4">
                <RadioGroup
                  value={uploadMethod}
                  onValueChange={(value) => setUploadMethod(value as "qr" | "text")}
                  className="grid grid-cols-2 gap-4"
                >
                  <div>
                    <RadioGroupItem
                      value="qr"
                      id="qr"
                      className="peer sr-only"
                    />
                    <Label
                      htmlFor="qr"
                      className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                    >
                      <Upload className="mb-2 h-6 w-6" />
                      Upload QR Code
                    </Label>
                  </div>
                  <div>
                    <RadioGroupItem
                      value="text"
                      id="text"
                      className="peer sr-only"
                    />
                    <Label
                      htmlFor="text"
                      className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                    >
                      <CreditCard className="mb-2 h-6 w-6" />
                      Enter Code
                    </Label>
                  </div>
                </RadioGroup>

                {uploadMethod === "qr" ? (
                  <div className="space-y-2">
                    <Label htmlFor="qr-upload">Upload QR Code Image</Label>
                    <Input
                      id="qr-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleQRUpload}
                      disabled={loading}
                    />
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Label htmlFor="lpa-string">Activation Code</Label>
                    <Input
                      id="lpa-string"
                      value={lpaString}
                      onChange={(e) => setLpaString(e.target.value)}
                      placeholder="Enter LPA activation code"
                      required
                    />
                  </div>
                )}

                {lpaString && (
                  <div className="rounded-lg bg-green-50 p-3 text-sm text-green-800">
                    <div className="font-medium mb-1">LPA String detected:</div>
                    <div className="font-mono text-xs break-all p-2 rounded">
                      {lpaString}
                    </div>
                  </div>
                )}

                <div className="rounded-lg bg-gradient-to-r from-yellow-50 to-yellow-100 p-4 text-yellow-800 border border-yellow-200 shadow-sm">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl">🎉</span>
                    <h4 className="font-semibold">Special Offer!</h4>
                  </div>
                  <p className="text-sm">
                    As a valued member, you're eligible for our limited-time promotion:
                    <span className="block mt-1 font-medium">Free eSIM activation service - No additional charges!</span>
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setOpen(false)}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading || !lpaString}
                >
                  {loading ? "Processing..." : "Proceed"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
    </>
  )
}