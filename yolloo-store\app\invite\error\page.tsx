import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function InviteErrorPage({ 
  searchParams 
}: { 
  searchParams: { error?: string } 
}) {
  const error = searchParams.error || "Invalid invitation link";
  
  return (
    <div className="container max-w-md mx-auto py-10 px-4 sm:px-6">
      <div className="space-y-6 text-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="mx-auto h-16 w-16 text-red-500"
        >
          <circle cx="12" cy="12" r="10" />
          <line x1="12" y1="8" x2="12" y2="12" />
          <line x1="12" y1="16" x2="12.01" y2="16" />
        </svg>
        <h1 className="text-3xl font-bold">Invitation Error</h1>
        <p className="text-muted-foreground">{error}</p>
        <div className="pt-4">
          <Button asChild>
            <Link href="/">Return to Homepage</Link>
          </Button>
        </div>
      </div>
    </div>
  );
} 