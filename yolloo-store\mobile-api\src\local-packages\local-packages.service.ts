import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { LocalPackagesQueryDto, LocalPackageOrderDto } from './dto/local-packages-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
import { DateFormatter, DateUtils } from '../common/utils';

@Injectable()
export class LocalPackagesService {
  private readonly logger = new Logger(LocalPackagesService.name);

  constructor(private prisma: PrismaService) {}

  async getLocalPackages(query: LocalPackagesQueryDto, ctx: RequestContext) {
    console.log('Context in getLocalPackages:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 构建查询条件 - 查找本地套餐相关的产品
      const whereConditions: any = {
        status: 'ACTIVE',
        off_shelve: false,
        // 查找本地套餐相关的产品
        OR: [
          // 通过分类查找
          {
            category: {
              name: {
                in: ['Local Packages', '本地套餐', 'City Packages', '城市套餐', '地方套餐']
              }
            }
          },
          // 通过产品名称查找
          { name: { contains: '本地', mode: 'insensitive' } },
          { name: { contains: 'local', mode: 'insensitive' } },
          { name: { contains: '城市', mode: 'insensitive' } },
          { name: { contains: 'city', mode: 'insensitive' } },
          { name: { contains: '地区', mode: 'insensitive' } },
          { name: { contains: 'regional', mode: 'insensitive' } },
          // 通过描述查找
          { description: { contains: '本地', mode: 'insensitive' } },
          { description: { contains: 'local', mode: 'insensitive' } },
          { description: { contains: '城市', mode: 'insensitive' } },
          { description: { contains: 'city', mode: 'insensitive' } },
          { description: { contains: '地区专用', mode: 'insensitive' } },
          { description: { contains: 'area exclusive', mode: 'insensitive' } },
          // 通过国家/地区字段查找（中国城市）
          {
            AND: [
              { country: { contains: 'china', mode: 'insensitive' } },
              { countryCode: { equals: 'CN' } }
            ]
          },
          // 通过城市名称查找
          {
            OR: [
              { name: { contains: '北京', mode: 'insensitive' } },
              { name: { contains: 'beijing', mode: 'insensitive' } },
              { name: { contains: '上海', mode: 'insensitive' } },
              { name: { contains: 'shanghai', mode: 'insensitive' } },
              { name: { contains: '广州', mode: 'insensitive' } },
              { name: { contains: 'guangzhou', mode: 'insensitive' } },
              { name: { contains: '深圳', mode: 'insensitive' } },
              { name: { contains: 'shenzhen', mode: 'insensitive' } },
            ]
          }
        ],
      };

      // 添加城市筛选条件
      if (query.city) {
        whereConditions.AND = whereConditions.AND || [];
        whereConditions.AND.push({
          OR: [
            { name: { contains: query.city, mode: 'insensitive' } },
            { description: { contains: query.city, mode: 'insensitive' } },
            { country: { contains: query.city, mode: 'insensitive' } },
          ],
        });
      }

      // 添加省份筛选条件
      if (query.province) {
        whereConditions.AND = whereConditions.AND || [];
        whereConditions.AND.push({
          OR: [
            { name: { contains: query.province, mode: 'insensitive' } },
            { description: { contains: query.province, mode: 'insensitive' } },
            { country: { contains: query.province, mode: 'insensitive' } },
          ],
        });
      }

      // 添加计划类型筛选条件
      if (query.planType) {
        whereConditions.planType = { equals: query.planType, mode: 'insensitive' };
      }

      // 查询产品总数
      const total = await this.prisma.product.count({
        where: whereConditions,
      });

      // 如果没有找到本地套餐产品，返回空结果
      if (total === 0) {
        return {
          packages: [],
          pagination: {
            total: 0,
            page: query.page!,
            pageSize: query.pageSize!,
            hasMore: false,
          },
          filters: await this.getLocalPackageFilters(ctx),
          context: {
            language: ctx.language,
            theme: ctx.theme,
            currency: ctx.currency,
          },
        };
      }

      // 查询产品列表
      const skip = (query.page! - 1) * query.pageSize!;
      const products = await this.prisma.product.findMany({
        where: whereConditions,
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          variants: {
            select: {
              id: true,
              price: true,
              currency: true,
            },
            orderBy: {
              price: 'asc',
            },
          },
          reviews: {
            select: {
              rating: true,
            },
          },
        },
        skip,
        take: query.pageSize!,
        orderBy: this.buildOrderBy(query.sortBy, query.sortOrder),
      });

      // 格式化产品数据为本地套餐格式
      const formattedPackages = products.map(product => this.formatProductAsLocalPackage(product, ctx, isZh));

      return {
        packages: formattedPackages,
        pagination: {
          total,
          page: query.page!,
          pageSize: query.pageSize!,
          hasMore: skip + formattedPackages.length < total,
        },
        filters: await this.getLocalPackageFilters(ctx),
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };

    } catch (error) {
      this.logger.error('Error fetching local packages:', error);
      throw new Error('Failed to fetch local packages');
    }
  }

  async getPackageById(packageId: string, ctx: RequestContext) {
    console.log('Context in getPackageById:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 查询具体的产品
      const product = await this.prisma.product.findUnique({
        where: { id: packageId },
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          variants: {
            select: {
              id: true,
              price: true,
              currency: true,
            },
            orderBy: {
              price: 'asc',
            },
          },
          reviews: {
            select: {
              rating: true,
              comment: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 10,
          },
        },
      });

      if (!product) {
        throw new Error('Local package not found');
      }

      // 格式化为详细的本地套餐数据
      const packageDetails = this.formatProductAsDetailedLocalPackage(product, ctx, isZh);

      return packageDetails;

    } catch (error) {
      this.logger.error('Error fetching local package details:', error);
      throw new Error('Failed to fetch local package details');
    }
  }



  async createLocalOrder(orderData: LocalPackageOrderDto, ctx: RequestContext) {
    console.log('Context in createLocalOrder:', ctx);

    const isZh = ctx.language.startsWith('zh');

    // Mock order creation
    const order = {
      id: `local_${Date.now()}`,
      packageId: orderData.packageId,
      city: orderData.city,
      province: orderData.province,
      status: 'pending',
      statusText: isZh ? '待支付' : 'Pending Payment',
      createdAt: DateFormatter.iso(new Date()),
      estimatedActivationTime: DateFormatter.iso(DateUtils.addMinutes(new Date(), 5)),
    };

    return {
      order,
      message: isZh ? '本地套餐订单创建成功，请完成支付' : 'Local package order created successfully, please complete payment',
    };
  }

  private formatProductAsLocalPackage(product: any, ctx: RequestContext, isZh: boolean) {
    // 计算平均评分
    const avgRating = product.reviews.length > 0
      ? product.reviews.reduce((sum: number, review: any) => sum + review.rating, 0) / product.reviews.length
      : 0;

    // 获取最低价格（从变体中）
    const lowestPrice = product.variants.length > 0
      ? Math.min(...product.variants.map((v: any) => Number(v.price)))
      : Number(product.price);

    // 获取货币
    const currency = product.variants.length > 0
      ? product.variants[0].currency || ctx.currency
      : ctx.currency;

    // 解析规格中的城市、省份信息
    let city = '';
    let province = '';
    let features: string[] = [];
    let validity = '1天';

    try {
      const specs = typeof product.specifications === 'string'
        ? JSON.parse(product.specifications)
        : product.specifications;

      city = specs?.city || '';
      province = specs?.province || '';
      features = specs?.features || [];
      validity = specs?.validity || '1天';
    } catch (error) {
      this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
    }

    // 从产品名称或描述中提取城市信息
    if (!city) {
      const cityNames = ['北京', 'beijing', '上海', 'shanghai', '广州', 'guangzhou', '深圳', 'shenzhen', '杭州', 'hangzhou', '南京', 'nanjing'];
      for (const cityName of cityNames) {
        if (product.name.toLowerCase().includes(cityName.toLowerCase()) ||
            product.description.toLowerCase().includes(cityName.toLowerCase())) {
          city = cityName.includes('北京') || cityName === 'beijing' ? 'beijing' :
                cityName.includes('上海') || cityName === 'shanghai' ? 'shanghai' :
                cityName.includes('广州') || cityName === 'guangzhou' ? 'guangzhou' :
                cityName.includes('深圳') || cityName === 'shenzhen' ? 'shenzhen' :
                cityName.includes('杭州') || cityName === 'hangzhou' ? 'hangzhou' :
                cityName.includes('南京') || cityName === 'nanjing' ? 'nanjing' : 'unknown';
          break;
        }
      }
    }

    // 确定省份
    if (!province) {
      if (city === 'beijing') province = 'beijing';
      else if (city === 'shanghai') province = 'shanghai';
      else if (city === 'guangzhou' || city === 'shenzhen') province = 'guangdong';
      else if (city === 'hangzhou') province = 'zhejiang';
      else if (city === 'nanjing') province = 'jiangsu';
    }

    // 默认特性
    if (features.length === 0) {
      features = [
        isZh ? `${this.getCityDisplayName(city, isZh)}地区专用` : `${this.getCityDisplayName(city, false)} area exclusive`,
        isZh ? '高速流量' : 'High-speed data',
        isZh ? '即时激活' : 'Instant activation',
        isZh ? '本地优化' : 'Locally optimized'
      ];
    }

    // 确定计划类型
    const planType = product.planType || this.determinePlanType(product.name, product.description);

    return {
      id: product.id,
      name: product.name,
      description: product.description,
      city: city,
      province: province,
      planType: planType,
      dataSize: product.dataSize ? this.formatDataSize(product.dataSize) : 'unlimited',
      price: lowestPrice,
      originalPrice: lowestPrice * 1.25, // 假设原价比现价高25%
      currency: currency,
      features: features,
      coverage: isZh ? `${this.getCityDisplayName(city, isZh)}全境` : `All of ${this.getCityDisplayName(city, false)}`,
      networkType: '4G/5G',
      validity: validity,
      imageUrl: product.images && product.images.length > 0
        ? product.images[0]
        : `/images/products/${city}-package.jpg`,
      isPopular: avgRating >= 4.5,
      rating: Math.round(avgRating * 10) / 10,
      reviewCount: product.reviews.length,
    };
  }

  private formatProductAsDetailedLocalPackage(product: any, ctx: RequestContext, isZh: boolean) {
    const basicPackage = this.formatProductAsLocalPackage(product, ctx, isZh);

    return {
      ...basicPackage,
      detailedInfo: {
        activation: isZh ? '购买后立即激活' : 'Instant activation after purchase',
        coverage: isZh ? `${basicPackage.city}全覆盖` : `Full coverage in ${basicPackage.city}`,
        speed: isZh ? '5G网络下载速度最高1Gbps' : '5G network download speed up to 1Gbps',
        usage: isZh ? '适合短期出差、旅游使用' : 'Perfect for short business trips and tourism',
      },
      restrictions: [
        isZh ? `仅限${this.getCityDisplayName(basicPackage.city, isZh)}地区使用` : `Valid only in ${this.getCityDisplayName(basicPackage.city, false)} area`,
        isZh ? `${basicPackage.validity}后自动失效` : `Automatically expires after ${basicPackage.validity}`,
        isZh ? '不可转让或退款' : 'Non-transferable and non-refundable',
      ],
      variants: product.variants.map((variant: any) => ({
        id: variant.id,
        price: Number(variant.price),
        currency: variant.currency,
      })),
      reviews: product.reviews.map((review: any) => ({
        rating: review.rating,
        comment: review.comment,
        createdAt: review.createdAt,
      })),
    };
  }

  private async getLocalPackageFilters(ctx: RequestContext) {
    const isZh = ctx.language.startsWith('zh');

    try {
      // 从数据库获取实际的筛选选项
      const planTypes = await this.prisma.product.findMany({
        where: {
          status: 'ACTIVE',
          off_shelve: false,
          planType: { not: null },
        },
        select: { planType: true },
        distinct: ['planType'],
      });

      const formattedPlanTypes = planTypes
        .filter(p => p.planType)
        .map(p => ({
          value: p.planType!,
          label: isZh
            ? (p.planType === 'daily' ? '日套餐' : p.planType === 'weekly' ? '周套餐' : p.planType === 'monthly' ? '月套餐' : p.planType!)
            : (p.planType === 'daily' ? 'Daily' : p.planType === 'weekly' ? 'Weekly' : p.planType === 'monthly' ? 'Monthly' : p.planType!),
        }));

      return {
        cities: [
          { id: 'beijing', name: isZh ? '北京' : 'Beijing' },
          { id: 'shanghai', name: isZh ? '上海' : 'Shanghai' },
          { id: 'guangzhou', name: isZh ? '广州' : 'Guangzhou' },
          { id: 'shenzhen', name: isZh ? '深圳' : 'Shenzhen' },
          { id: 'hangzhou', name: isZh ? '杭州' : 'Hangzhou' },
          { id: 'nanjing', name: isZh ? '南京' : 'Nanjing' },
        ],
        planTypes: formattedPlanTypes.length > 0 ? formattedPlanTypes : [
          { value: 'daily', label: isZh ? '日套餐' : 'Daily' },
          { value: 'weekly', label: isZh ? '周套餐' : 'Weekly' },
          { value: 'monthly', label: isZh ? '月套餐' : 'Monthly' },
        ],
        dataSizes: [
          { value: '1GB', label: '1GB' },
          { value: '3GB', label: '3GB' },
          { value: '5GB', label: '5GB' },
          { value: '10GB', label: '10GB' },
          { value: '20GB', label: '20GB' },
          { value: 'unlimited', label: isZh ? '无限流量' : 'Unlimited' },
        ],
      };

    } catch (error) {
      this.logger.error('Error fetching local package filters:', error);

      // 返回默认筛选选项
      return {
        cities: [
          { id: 'beijing', name: isZh ? '北京' : 'Beijing' },
          { id: 'shanghai', name: isZh ? '上海' : 'Shanghai' },
          { id: 'guangzhou', name: isZh ? '广州' : 'Guangzhou' },
          { id: 'shenzhen', name: isZh ? '深圳' : 'Shenzhen' },
          { id: 'hangzhou', name: isZh ? '杭州' : 'Hangzhou' },
          { id: 'nanjing', name: isZh ? '南京' : 'Nanjing' },
        ],
        planTypes: [
          { value: 'daily', label: isZh ? '日套餐' : 'Daily' },
          { value: 'weekly', label: isZh ? '周套餐' : 'Weekly' },
          { value: 'monthly', label: isZh ? '月套餐' : 'Monthly' },
        ],
        dataSizes: [
          { value: '1GB', label: '1GB' },
          { value: '3GB', label: '3GB' },
          { value: '5GB', label: '5GB' },
          { value: '10GB', label: '10GB' },
          { value: '20GB', label: '20GB' },
          { value: 'unlimited', label: isZh ? '无限流量' : 'Unlimited' },
        ],
      };
    }
  }

  private buildOrderBy(sortBy?: string, sortOrder?: 'asc' | 'desc') {
    const order = sortOrder || 'asc';

    switch (sortBy) {
      case 'price':
        return { price: order };
      case 'rating':
        return { createdAt: order as 'asc' | 'desc' }; // 由于评分需要计算，暂时用创建时间排序
      case 'name':
        return { name: order };
      default:
        return { createdAt: 'desc' as 'asc' | 'desc' };
    }
  }

  private getCityDisplayName(city: string, isZh: boolean): string {
    const cityMap: { [key: string]: { zh: string; en: string } } = {
      beijing: { zh: '北京', en: 'Beijing' },
      shanghai: { zh: '上海', en: 'Shanghai' },
      guangzhou: { zh: '广州', en: 'Guangzhou' },
      shenzhen: { zh: '深圳', en: 'Shenzhen' },
      hangzhou: { zh: '杭州', en: 'Hangzhou' },
      nanjing: { zh: '南京', en: 'Nanjing' },
    };

    return cityMap[city] ? (isZh ? cityMap[city].zh : cityMap[city].en) : city;
  }

  private determinePlanType(name: string, description: string): string {
    const text = (name + ' ' + description).toLowerCase();

    if (text.includes('日') || text.includes('daily')) return 'daily';
    if (text.includes('周') || text.includes('weekly')) return 'weekly';
    if (text.includes('月') || text.includes('monthly')) return 'monthly';

    return 'daily'; // 默认为日套餐
  }

  private formatDataSize(dataSize: number): string {
    if (dataSize >= 1024) {
      const sizeInGB = dataSize / 1024;
      return sizeInGB % 1 === 0 ? `${sizeInGB}GB` : `${sizeInGB.toFixed(1)}GB`;
    } else {
      return `${dataSize}MB`;
    }
  }
}
