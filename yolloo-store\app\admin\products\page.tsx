"use client"

import { useState, useEffect, use<PERSON><PERSON>back, Fragment } from 'react'
import { use<PERSON><PERSON><PERSON>, useSearchParams } from 'next/navigation'
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Icons } from "@/components/icons"
import { SearchIcon, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, MoreHorizontal, AlertTriangle } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input"
import { toast } from "sonner"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { DateFormatter } from "@/lib/utils"
import { BulkPriceUpdateDialog } from "@/components/admin/bulk-price-update-dialog"

interface Product {
  id: string
  name: string
  description: string
  price: number
  images: string[]
  stock: number
  status: string
  category: {
    id: string
    name: string
  }
  createdAt: Date
  hasOrders?: boolean
  sku: string
  requiredUID: boolean
  mcc?: string | null
  off_shelve: boolean
  dataSize?: number | null
  planType?: string | null
  country?: string | null
  countryCode?: string | null
  variants: {
    id: string
    name: string
    price: number
    currency: string
    duration?: number | null
    durationType?: string | null
  }[]
  odooLastSyncAt?: Date | null
}

interface Category {
  id: string
  name: string
}

interface ProductsResponse {
  products: Product[]
  total: number
  totalPages: number
  categories: Category[]
}

export default function ProductsPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState<string | null>(null)
  const [isSyncing, setIsSyncing] = useState(false)
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [uniqueCountries, setUniqueCountries] = useState<string[]>([])
  const [isLoadingProducts, setIsLoadingProducts] = useState(true)
  const [deleteProductId, setDeleteProductId] = useState<string | null>(null)
  const [editingPrice, setEditingPrice] = useState<{ id: string; price: number } | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalProducts, setTotalProducts] = useState(0)
  const [searchQuery, setSearchQuery] = useState("")
  const [showDeleteAllDialog, setShowDeleteAllDialog] = useState(false)
  const [isDeletingAll, setIsDeletingAll] = useState(false)
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [showBulkPriceDialog, setShowBulkPriceDialog] = useState(false)
  const [filters, setFilters] = useState({
    search: "",
    category: "all",
    status: "all",
    stock: "all",
    offShelve: "all",
    country: "all",
    page: 1,
    limit: 12,
    sort: "newest"
  })

  // 新增全局处理状态，防止并发
  const isProcessing = isSyncing || isDeletingAll;

  useEffect(() => {
    fetchProducts()
  }, [
    currentPage,
    searchQuery,
    filters.category,
    filters.status,
    filters.stock,
    filters.offShelve,
    filters.country,
    filters.limit,
    filters.sort
  ])

  // 初始加载国家列表
  useEffect(() => {
    async function fetchCountries() {
      try {
        const response = await fetch('/api/admin/products/countries')
        if (response.ok) {
          const data = await response.json()
          setUniqueCountries(data.countries || [])
        }
      } catch (error) {
        console.error('Error fetching countries:', error)
      }
    }

    fetchCountries()
  }, [])

  const fetchProducts = async () => {
    try {
      const queryParams = new URLSearchParams()

      // 添加过滤参数
      if (searchQuery) queryParams.append("search", searchQuery)
      if (filters.category !== "all") queryParams.append("category", filters.category)
      if (filters.status !== "all") queryParams.append("status", filters.status)
      if (filters.offShelve !== "all") queryParams.append("offShelve", filters.offShelve)
      if (filters.country !== "all") queryParams.append("country", filters.country)
      if (filters.sort) queryParams.append("sort", filters.sort)

      // 分页参数
      queryParams.append("page", currentPage.toString())
      queryParams.append("limit", filters.limit.toString())

      // 获取产品数据
      const response = await fetch(`/api/admin/products?${queryParams.toString()}`)

      if (!response.ok) {
        throw new Error("Failed to fetch products")
      }

      const data = await response.json()
      setProducts(data.products)
      setTotalPages(data.pages)
      setTotalProducts(data.total)
      setCategories(data.categories || [])
    } catch (error) {
      console.error("Error loading products:", error)
      toast.error("Failed to load products")
    } finally {
      setIsLoadingProducts(false)
    }
  }

  // Reset page when filters change (except for page property)
  useEffect(() => {
    setCurrentPage(1);
  }, [
    searchQuery,
    filters.category,
    filters.status,
    filters.stock,
    filters.offShelve,
    filters.country
  ]);

  // Update filters.page when currentPage changes
  useEffect(() => {
    setFilters(prev => ({ ...prev, page: currentPage }))
  }, [currentPage])

  const syncProducts = async () => {
    setIsSyncing(true);
    let odooSuccess = false;
    let qrSuccess = false;
    try {
      // 1. 先同步老账号商品
      const response = await fetch('/api/admin/sync-products', {
        method: 'POST'
      });
      if (response.ok) {
        toast.success('Odoo products synced successfully');
        odooSuccess = true;
      } else {
        toast.error('Failed to sync Odoo products');
      }
      // 2. 同步新账号商品
      const qrResponse = await fetch('/api/admin/products/sync-qr-products', {
        method: 'POST'
      });
      if (qrResponse.ok) {
        toast.success('QR Odoo products synced successfully');
        qrSuccess = true;
      } else {
        toast.error('Failed to sync QR Odoo products');
      }
      // 只要有一个成功就刷新
      if (odooSuccess || qrSuccess) {
        await fetchProducts();
      }
    } catch (error) {
      console.error(error);
      toast.error('Failed to sync products');
    } finally {
      setIsSyncing(false);
    }
  };

  async function updateProductStatus(productId: string, status: string) {
    setIsLoading(productId)
    try {
      const response = await fetch(`/api/admin/products/${productId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      })

      if (!response.ok) {
        throw new Error("Failed to update product status")
      }

      toast.success("Product status updated successfully")
      fetchProducts()
    } catch (error) {
      console.error(error)
      toast.error("Something went wrong")
    } finally {
      setIsLoading(null)
    }
  }

  async function deleteProduct(productId: string) {
    try {
      // 检查是否是强制删除
      if (productId.startsWith('force_')) {
        const actualProductId = productId.replace('force_', '');

        // 执行强制删除
        const response = await fetch(`/api/admin/products/${actualProductId}/force-delete`, {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error("Failed to force delete product");
        }

        toast.success("Product has been force deleted successfully");
      } else {
        // 首先检查商品是否有关联订单
        const checkResponse = await fetch(`/api/admin/products/${productId}/check-orders`);
        const { hasOrders } = await checkResponse.json();

        if (hasOrders) {
          // 如果有订单，执行软删除（将状态改为DELETED）
          const response = await fetch(`/api/admin/products/${productId}`, {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ status: "DELETED" }),
          });

          if (!response.ok) {
            throw new Error("Failed to delete product");
          }

          toast.success("Product has been archived due to existing orders");
        } else {
          // 如果没有订单，执行硬删除
          const response = await fetch(`/api/admin/products/${productId}`, {
            method: "DELETE",
          });

          if (!response.ok) {
            throw new Error("Failed to delete product");
          }

          toast.success("Product deleted successfully");
        }
      }

      fetchProducts();
    } catch (error) {
      console.error(error);
      toast.error("Failed to delete product");
    } finally {
      setDeleteProductId(null);
    }
  }

  async function updateProductPrice(productId: string, newPrice: number) {
    try {
      const response = await fetch(`/api/admin/products/${productId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ price: newPrice }),
      })

      if (!response.ok) {
        throw new Error("Failed to update price")
      }

      toast.success("Price updated successfully")
      fetchProducts()
      setEditingPrice(null)
    } catch (error) {
      console.error(error)
      toast.error("Failed to update price")
    }
  }

  async function batchDeleteProducts(productIds: string[]) {
    try {
      // 检查每个产品是否有关联订单
      const checkPromises = productIds.map(id =>
        fetch(`/api/admin/products/${id}/check-orders`).then(res => res.json())
      )
      const checkResults = await Promise.all(checkPromises)

      // 分离需要软删除和硬删除的产品
      const softDeleteIds = productIds.filter((id, index) => checkResults[index].hasOrders)
      const hardDeleteIds = productIds.filter((id, index) => !checkResults[index].hasOrders)

      // 执行软删除（更新状态为DELETED）
      if (softDeleteIds.length > 0) {
        const softDeletePromises = softDeleteIds.map(id =>
          fetch(`/api/admin/products/${id}`, {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ status: "DELETED" }),
          })
        )
        await Promise.all(softDeletePromises)
      }

      // 执行硬删除
      if (hardDeleteIds.length > 0) {
        const hardDeletePromises = hardDeleteIds.map(id =>
          fetch(`/api/admin/products/${id}`, {
            method: "DELETE",
          })
        )
        await Promise.all(hardDeletePromises)
      }

      toast.success("Selected products have been processed")
      setSelectedProducts([])
      fetchProducts()
    } catch (error) {
      console.error(error)
      toast.error("Failed to process selected products")
    }
  }

  async function deleteAllProducts() {
    try {
      setIsDeletingAll(true)

      const response = await fetch('/api/admin/products/delete-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to delete all products')
      }

      const result = await response.json()

      toast.success(
        `Products processed: ${result.deletedCount} deleted, ${result.skippedCount} skipped`
      )

      // 重新获取商品列表
      fetchProducts()
    } catch (error) {
      console.error(error)
      toast.error('Failed to delete all products')
    } finally {
      setIsDeletingAll(false)
      setShowDeleteAllDialog(false)
    }
  }

  // 获取每个国家的产品数量
  const getCountryCount = (country: string) => {
    return products.filter(p => {
      if (!p.country) return false;
      // 检查产品国家字符串是否包含此国家
      const productCountries = p.country.split(/[,;]/).map(c => c.trim()).filter(Boolean);
      return productCountries.includes(country);
    }).length;
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和主要操作区 */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 bg-white dark:bg-gray-950 p-6 rounded-lg border shadow-sm">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Products</h2>
          <p className="text-muted-foreground mt-1">
            Manage your product catalog and inventory
          </p>
        </div>
        <div className="flex flex-wrap gap-3">
          {selectedProducts.length > 0 && (
            <Button
              variant="destructive"
              onClick={() => setDeleteProductId("batch")}
              className="flex items-center"
            >
              <Icons.trash className="mr-2 h-4 w-4" />
              Delete Selected ({selectedProducts.length})
            </Button>
          )}
          <Button
            onClick={() => setShowBulkPriceDialog(true)}
            disabled={isProcessing}
            variant="outline"
            className="flex items-center bg-green-50 hover:bg-green-100 text-green-700 border-green-200 hover:border-green-300"
          >
            <Icons.dollarSign className="mr-2 h-4 w-4" />
            Bulk Price Update
          </Button>
          <Button
            onClick={syncProducts}
            disabled={isProcessing}
            variant="outline"
            className="flex items-center"
          >
            {isSyncing ? (
              <>
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <Icons.refresh className="mr-2 h-4 w-4" />
                Sync with Odoo
              </>
            )}
          </Button>
          <Button
            onClick={() => router.push("/admin/products/new")}
            className="bg-primary hover:bg-primary/90 text-white flex items-center"
          >
            <Icons.add className="mr-2 h-4 w-4" />
            Add Product
          </Button>
          <Button
            onClick={() => setShowDeleteAllDialog(true)}
            disabled={isProcessing}
            variant="destructive"
            className="flex items-center"
          >
            <Icons.trash className="mr-2 h-4 w-4" />
            Delete All Products
          </Button>
        </div>
      </div>

      {/* 搜索和筛选区域 */}
      <div className="bg-white dark:bg-gray-950 p-6 rounded-lg border shadow-sm space-y-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-medium">Search & Filters</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setFilters({
                search: "",
                category: "all",
                status: "all",
                stock: "all",
                offShelve: "all",
                country: "all",
                page: 1,
                limit: 12,
                sort: "newest"
              })
              setSearchQuery("")
              setCurrentPage(1)
            }}
            className="text-sm flex items-center"
          >
            <Icons.refresh className="mr-2 h-3.5 w-3.5" />
            Reset Filters
          </Button>
        </div>

        {/* 搜索框 */}
        <div className="relative">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by name, SKU, product code, or description..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-background"
          />
        </div>

        {/* 筛选器网格 - 两行布局 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* 国家筛选 */}
          <div>
            <Label htmlFor="country">Country</Label>
            <Select
              value={filters.country}
              onValueChange={(value) => setFilters(prev => ({ ...prev, country: value, page: 1 }))}
            >
              <SelectTrigger id="country">
                <SelectValue placeholder="Select country" />
              </SelectTrigger>
              <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
                <SelectItem value="all">All Destinations</SelectItem>
                {uniqueCountries.map((country) => (
                  <SelectItem key={country} value={country}>
                    {country}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 分类筛选 */}
          <div>
            <Label htmlFor="category">Category</Label>
            <Select
              value={filters.category}
              onValueChange={(value) => setFilters(prev => ({ ...prev, category: value, page: 1 }))}
            >
              <SelectTrigger id="category">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 状态筛选 */}
          <div>
            <Label htmlFor="status">Status</Label>
            <Select
              value={filters.status}
              onValueChange={(value) => setFilters(prev => ({ ...prev, status: value, page: 1 }))}
            >
              <SelectTrigger id="status">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="INACTIVE">Inactive</SelectItem>
                <SelectItem value="OUT_OF_STOCK">Out of Stock</SelectItem>
                <SelectItem value="DELETED">Deleted</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 上下架状态 */}
          <div>
            <Label htmlFor="offShelve">Shelf Status</Label>
            <Select
              value={filters.offShelve}
              onValueChange={(value) => setFilters(prev => ({ ...prev, offShelve: value, page: 1 }))}
            >
              <SelectTrigger id="shelf">
                <SelectValue placeholder="Select shelf status" />
              </SelectTrigger>
              <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="false">On Shelf</SelectItem>
                <SelectItem value="true">Off Shelf</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 排序筛选 */}
          <div>
            <Label htmlFor="sort">Sort By</Label>
            <Select
              value={filters.sort}
              onValueChange={(value) => setFilters(prev => ({ ...prev, sort: value, page: 1 }))}
            >
              <SelectTrigger id="sort">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="price_asc">Price: Low to High</SelectItem>
                <SelectItem value="price_desc">Price: High to Low</SelectItem>
                <SelectItem value="variants_asc">Variants: Fewest First</SelectItem>
                <SelectItem value="variants_desc">Variants: Most First</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* 产品列表 */}
      <div className="bg-white dark:bg-gray-950 p-6 rounded-lg border shadow-sm space-y-4">
        {isLoadingProducts ? (
          <div className="flex items-center justify-center p-12">
            <div className="flex flex-col items-center">
              <Icons.spinner className="h-10 w-10 animate-spin text-primary" />
              <p className="mt-4 text-muted-foreground">Loading products...</p>
            </div>
          </div>
        ) : products.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-12 text-center">
            <div className="bg-muted/30 p-4 rounded-full">
            <Icons.package className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="mt-6 text-xl font-semibold">No products found</h3>
            <p className="mt-2 text-muted-foreground max-w-md">
              No products match your current filters. Try adjusting your search criteria or add a new product.
            </p>
            <Button
              className="mt-6"
              onClick={() => router.push("/admin/products/new")}
            >
              <Icons.add className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </div>
        ) : (
          <>
            {/* 选择全部和产品数量指示器 */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
              <Checkbox
                checked={products.length > 0 && products.every(product => selectedProducts.includes(product.id))}
                onCheckedChange={(checked) => {
                  if (checked) {
                    const newSelectedProducts = [...selectedProducts]
                    products.forEach(product => {
                      if (!newSelectedProducts.includes(product.id)) {
                        newSelectedProducts.push(product.id)
                      }
                    })
                    setSelectedProducts(newSelectedProducts)
                  } else {
                    setSelectedProducts(prev => prev.filter(id => !products.find(product => product.id === id)))
                  }
                }}
                  className="rounded-md"
              />
              <span className="text-sm font-medium">Select All</span>
            </div>
              <div className="text-sm text-muted-foreground">
                Showing {products.length} of {totalProducts} products
              </div>
            </div>

            {/* 产品卡片列表 */}
            <div className="space-y-4">
            {products.map((product) => (
                <Card key={product.id} className="overflow-hidden hover:border-primary/5 transition-colors duration-300">
                  <div className="p-6 grid gap-6 md:grid-cols-[auto_1fr_auto] relative">
                  {/* 左侧：基本信息和图片 */}
                    <div className="flex items-start gap-4">
                      <Checkbox
                        checked={selectedProducts.includes(product.id)}
                        onCheckedChange={(checked) => {
                          setSelectedProducts(prev =>
                            checked
                              ? [...prev, product.id]
                              : prev.filter(id => id !== product.id)
                          )
                        }}
                        className="mt-1 rounded-md"
                      />

                      {product.images && product.images.length > 0 ? (
                        <div className="relative w-20 h-20 rounded-lg overflow-hidden border bg-muted/20 group">
                          <img
                            src={product.images[0]}
                            alt={product.name}
                            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                          />
                          {product.images.length > 1 && (
                            <div className="absolute bottom-1 right-1 bg-background/80 text-xs rounded-full h-5 w-5 flex items-center justify-center">
                              +{product.images.length - 1}
                      </div>
                          )}
                    </div>
                      ) : (
                        <div className="flex items-center justify-center w-20 h-20 rounded-lg border bg-muted/20">
                          <Icons.image className="h-8 w-8 text-muted-foreground/50" />
                        </div>
                      )}

                      <div className="space-y-1">
                        <h3 className="font-medium text-lg leading-tight truncate max-w-[300px]" title={product.name}>
                          {product.name}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          <span className="font-mono">SKU:{product.sku}</span>
                        </p>
                        <div className="flex flex-wrap items-center gap-2 mt-2">
                          {product.category && (
                            <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20" title={product.category.name}>
                              <span className="truncate max-w-[100px] inline-block align-bottom">{product.category.name}</span>
                            </Badge>
                          )}
                          {product.status === "ACTIVE" ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800">
                              <span className="flex items-center">
                                <span className="w-1.5 h-1.5 rounded-full bg-green-500 mr-1 animate-pulse"></span>
                                Active
                              </span>
                            </Badge>
                          ) : product.status === "INACTIVE" ? (
                            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-400 dark:border-amber-800">
                              <span className="flex items-center">
                                <span className="w-1.5 h-1.5 rounded-full bg-amber-500 mr-1"></span>
                                Inactive
                              </span>
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-400 dark:border-red-800">
                              <span className="flex items-center">
                                <span className="w-1.5 h-1.5 rounded-full bg-red-500 mr-1"></span>
                                Archived
                              </span>
                            </Badge>
                          )}
                          {product.off_shelve &&
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-400 dark:border-red-800">
                              <span className="flex items-center">
                                <Icons.eyeOff className="h-3 w-3 mr-1" />
                                Off Shelf
                              </span>
                            </Badge>}
                        </div>
                      </div>
                  </div>

                  {/* 中间：详细信息 */}
                    <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-8 gap-y-3 mt-2 md:mt-0">
                      {/* 价格 */}
                      <div>
                        <p className="text-xs uppercase font-medium text-muted-foreground">Price</p>
                        <p className="text-base font-medium mt-1 flex items-center">
                          <span className="text-lg font-semibold text-primary">${product.price.toFixed(2)}</span>
                        </p>
                      </div>

                      {/* 变体 */}
                      <div>
                        <p className="text-xs uppercase font-medium text-muted-foreground">Variants</p>
                        <div className="flex items-center gap-2 mt-1">
                          <p className="text-base font-medium">
                            {product.variants ? product.variants.length : 0}
                          </p>
                          {product.variants && product.variants.length > 0 && (
                            <Badge variant="outline" className="text-xs bg-violet-50 text-violet-700 border-violet-200 dark:bg-violet-950 dark:text-violet-400 dark:border-violet-800">
                              {product.variants.length > 1 ? 'variants' : 'variant'}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Plan Type */}
                      {product.planType && (
                        <div>
                          <p className="text-xs uppercase font-medium text-muted-foreground">Plan Type</p>
                          <p className="text-base font-medium mt-1 truncate max-w-[150px]" title={product.planType}>
                            {product.planType}
                          </p>
                        </div>
                      )}

                      {/* Data Size */}
                      {product.dataSize && (
                        <div>
                          <p className="text-xs uppercase font-medium text-muted-foreground">Data Size</p>
                          <p className="text-base font-medium mt-1 flex items-center">
                            <span>
                              {product.dataSize >= 1024
                                ? `${(product.dataSize / 1024).toFixed(2)}`
                                : `${product.dataSize}`}
                            </span>
                            {product.dataSize >= 1024 && (
                              <Badge variant="outline" className="ml-2 text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-400 dark:border-blue-800">
                                GB
                              </Badge>
                            )}
                            {product.dataSize < 1024 && (
                              <Badge variant="outline" className="ml-2 text-xs bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-950 dark:text-indigo-400 dark:border-indigo-800">
                                MB
                              </Badge>
                            )}
                          </p>
                        </div>
                      )}

                      {/* 国家 */}
                      {product.country && (
                        <div>
                          <p className="text-xs uppercase font-medium text-muted-foreground">Country</p>
                          <p className="text-base font-medium mt-1 truncate max-w-[150px]"
                             title={`${product.country}${product.countryCode ? ` (${product.countryCode})` : ''}`}>
                            {product.country}
                            {product.countryCode && ` (${product.countryCode})`}
                          </p>
                        </div>
                      )}

                      {/* MCC */}
                      {product.mcc && (
                        <div>
                          <p className="text-xs uppercase font-medium text-muted-foreground">MCC</p>
                          <p className="text-base font-medium mt-1 truncate max-w-[150px]" title={product.mcc}>
                            {product.mcc}
                          </p>
                        </div>
                      )}

                      {/* 库存 */}
                      <div>
                        <p className="text-xs uppercase font-medium text-muted-foreground">Stock</p>
                        <div className="flex items-center gap-2 mt-1">
                          <p className="text-base font-medium">
                            {product.stock}
                          </p>
                          {product.stock <= 0 ? (
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-400 dark:border-red-800 text-xs">
                              Out of stock
                            </Badge>
                          ) : product.stock <= 10 && (
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-400 dark:border-red-800 text-xs">
                              Low
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* 创建时间 */}
                      <div>
                        <p className="text-xs uppercase font-medium text-muted-foreground">Created</p>
                        <p className="text-sm mt-1">
                          {DateFormatter.withTimezone(product.createdAt)}
                        </p>
                      </div>

                    </div>

                  {/* 右侧：操作按钮 */}
                    <div className="flex flex-row md:flex-col justify-end gap-2 mt-4 md:mt-0">
                    <Button
                            variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/products/${product.id}`)}
                        className="flex items-center hover:bg-primary/5 hover:text-primary hover:border-primary/30 transition-colors"
                        >
                            <Icons.edit className="mr-2 h-4 w-4" />
                            Edit
                        </Button>

                    {product.status === "DELETED" ? (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateProductStatus(product.id, "ACTIVE")}
                            className="flex items-center hover:bg-green-50 hover:text-green-700 hover:border-green-300 transition-colors"
                          >
                            <Icons.undo className="mr-2 h-4 w-4" />
                            Restore
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => setDeleteProductId(`force_${product.id}`)}
                            className="flex items-center hover:bg-red-600 transition-colors"
                          >
                            <Icons.trash className="mr-2 h-4 w-4" />
                            Force Delete
                          </Button>
                        </>
                    ) : (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={isLoading === product.id}
                            onClick={() => updateProductStatus(
                              product.id,
                              product.status === "ACTIVE" ? "INACTIVE" : "ACTIVE"
                            )}
                            className={`flex items-center transition-colors ${
                              product.status === "ACTIVE"
                                ? "hover:bg-amber-50 hover:text-amber-700 hover:border-amber-300"
                                : "hover:bg-green-50 hover:text-green-700 hover:border-green-300"
                            }`}
                        >
                            {isLoading === product.id ? (
                            <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                            ) : product.status === "ACTIVE" ? (
                                <Icons.eyeOff className="mr-2 h-4 w-4" />
                            ) : (
                                <Icons.eye className="mr-2 h-4 w-4" />
                            )}
                            {product.status === "ACTIVE" ? "Deactivate" : "Activate"}
                        </Button>

                        <Button
                            variant="destructive"
                          size="sm"
                          onClick={() => setDeleteProductId(product.id)}
                            className="flex items-center hover:bg-red-600 transition-colors"
                        >
                            <Icons.trash className="mr-2 h-4 w-4" />
                            Delete
                        </Button>
                      </>
                    )}
                    </div>

                    {/* 移动设备上的响应式布局调整 */}
                    <div className="md:hidden w-full h-px bg-border my-2 col-span-full"></div>
                </div>
              </Card>
            ))}
            </div>

            {/* 分页控制 */}
            <div className="flex justify-center mt-6">
              <div className="flex items-center gap-2 bg-muted/20 p-1 rounded-lg">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentPage(1)}
                  disabled={currentPage === 1}
                  className="h-8 w-8 p-0 flex items-center justify-center"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="h-8 w-8 p-0 flex items-center justify-center"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {/* Page numbers */}
                <div className="flex items-center gap-1">
                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter(page => {
                      if (totalPages <= 7) {
                        // Show all pages if 7 or fewer
                        return true;
                      } else {
                        // Show pages around current page
                        if (currentPage <= 4) {
                          // Near the beginning
                          return page <= 5 || page === totalPages;
                        } else if (currentPage >= totalPages - 3) {
                          // Near the end
                          return page === 1 || page > totalPages - 5;
                        } else {
                          // In the middle
                          return page === 1 || page === totalPages || Math.abs(page - currentPage) <= 1;
                        }
                      }
                    })
                    .map((page, index, array) => {
                      // Add ellipsis where needed
                      const needsEllipsisBefore = index > 0 && page - array[index - 1] > 1;
                      const needsEllipsisAfter = index < array.length - 1 && array[index + 1] - page > 1;

                      return (
                        <Fragment key={page}>
                          {needsEllipsisBefore && (
                            <div className="h-8 w-8 flex items-center justify-center">
                              <MoreHorizontal className="h-4 w-4" />
                            </div>
                          )}
                          <Button
                            variant={currentPage === page ? "default" : "ghost"}
                            size="sm"
                            onClick={() => setCurrentPage(page)}
                            className="h-8 w-8 p-0 flex items-center justify-center"
                          >
                            {page}
                          </Button>
                          {needsEllipsisAfter && (
                            <div className="h-8 w-8 flex items-center justify-center">
                              <MoreHorizontal className="h-4 w-4" />
                            </div>
                          )}
                        </Fragment>
                      );
                    })}
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="h-8 w-8 p-0 flex items-center justify-center"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentPage(totalPages)}
                  disabled={currentPage === totalPages}
                  className="h-8 w-8 p-0 flex items-center justify-center"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </>
        )}
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={!!deleteProductId} onOpenChange={() => setDeleteProductId(null)}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-xl">
              {deleteProductId && deleteProductId.startsWith('force_') ? (
                <span className="flex items-center gap-2 text-destructive">
                  <AlertTriangle className="h-5 w-5" />
                  Force Delete Product
                </span>
              ) : (
                "Are you sure?"
              )}
            </AlertDialogTitle>
            <AlertDialogDescription className="text-muted-foreground mt-2">
              {deleteProductId === "batch" ? (
                <>
                  <p>You are about to process <span className="font-medium text-foreground">{selectedProducts.length}</span> products.</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Products with orders will be archived</li>
                    <li>Products without orders will be permanently deleted</li>
                  </ul>
                </>
              ) : deleteProductId && deleteProductId.startsWith('force_') ? (
                <>
                  <p className="font-medium text-destructive mb-2">This is a destructive action that cannot be easily undone!</p>
                  <p>You are about to <span className="font-medium text-foreground">force delete</span> this product, even though it may have associated orders or eSIMs.</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>For associated orders, a placeholder record will be created to maintain order history</li>
                    <li>For associated eSIMs, a placeholder product will be created to maintain eSIM functionality</li>
                    <li>The original product will be permanently deleted</li>
                  </ul>
                  <p className="mt-3 font-medium">Are you absolutely sure you want to continue?</p>
                </>
              ) : (
                <>
                  <p>This action cannot be easily undone.</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>If this product has orders, it will be archived</li>
                    <li>If this product has no orders, it will be permanently deleted</li>
                  </ul>
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="mt-4 gap-2">
            <AlertDialogCancel className="mt-0">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (deleteProductId === "batch") {
                  batchDeleteProducts(selectedProducts)
                } else if (deleteProductId) {
                  deleteProduct(deleteProductId)
                }
              }}
              className="bg-destructive hover:bg-destructive/90 text-destructive-foreground"
            >
              {deleteProductId === "batch"
                ? "Delete Selected"
                : deleteProductId && deleteProductId.startsWith('force_')
                  ? "Force Delete Product"
                  : "Delete Product"
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 删除所有产品确认对话框 */}
      <AlertDialog open={showDeleteAllDialog} onOpenChange={setShowDeleteAllDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-xl text-destructive">
              <AlertTriangle className="h-5 w-5" />
              Warning: Delete All Products
            </AlertDialogTitle>
            <AlertDialogDescription className="text-muted-foreground mt-2">
              <p className="font-medium text-destructive mb-2">This is a destructive action that cannot be easily undone!</p>
              <p>You are about to <span className="font-medium text-foreground">permanently delete all products</span> in the system.</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>All products will be permanently deleted from the database</li>
                <li>For products with orders, their information will be preserved for order history</li>
                <li>Products linked to eSIMs will be skipped to maintain data integrity</li>
              </ul>
              <p className="mt-3 font-medium">Are you absolutely sure you want to continue?</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="mt-4 gap-2">
            <AlertDialogCancel className="mt-0">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={deleteAllProducts}
              disabled={isDeletingAll}
              className="bg-destructive hover:bg-destructive/90 text-destructive-foreground"
            >
              {isDeletingAll ? (
                <>
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Delete All Products"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 批量调价对话框 */}
      <BulkPriceUpdateDialog
        open={showBulkPriceDialog}
        onOpenChange={setShowBulkPriceDialog}
        categories={categories}
        onSuccess={fetchProducts}
      />
    </div>
  )
}

