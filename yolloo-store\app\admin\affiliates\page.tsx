"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

interface PurchaseItem {
  productName: string
  quantity: number
  price: number
}

interface Purchase {
  orderId: string
  orderDate: string
  customer: {
    name: string | null
    email: string | null
  }
  total: number
  items: PurchaseItem[]
}

interface Statistics {
  presaleSubscriptions: number
  convertedSubscriptions: number
  conversionRate: string
  purchases: Purchase[]
  totalPurchases: number
  totalPurchaseAmount: number
}

interface Affiliate {
  id: string
  user: {
    name: string | null
    email: string | null
  }
  code: string
  commissionRate: number
  discountRate: number
  totalEarnings: number
  status: string
  statistics: Statistics
}

// Pagination constants
const ITEMS_PER_PAGE = 5 // Number of affiliates per page

export default function AffiliatesPage() {
  const [affiliates, setAffiliates] = useState<Affiliate[]>([])
  const [filteredAffiliates, setFilteredAffiliates] = useState<Affiliate[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  useEffect(() => {
    fetchAffiliates()
  }, [])

  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredAffiliates(affiliates)
    } else {
      const query = searchQuery.toLowerCase()
      const filtered = affiliates.filter((affiliate) => {
        const userName = affiliate.user.name?.toLowerCase() || ""
        const userEmail = affiliate.user.email?.toLowerCase() || ""
        const code = affiliate.code.toLowerCase()
        return userName.includes(query) || userEmail.includes(query) || code.includes(query)
      })
      setFilteredAffiliates(filtered)
    }
    
    // Reset to first page when search query changes
    setCurrentPage(1)
  }, [searchQuery, affiliates])
  
  // Update total pages when filtered affiliates change
  useEffect(() => {
    setTotalPages(Math.ceil(filteredAffiliates.length / ITEMS_PER_PAGE))
  }, [filteredAffiliates])

  // Get affiliates for the current page
  const getPaginatedAffiliates = () => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
    const endIndex = startIndex + ITEMS_PER_PAGE
    return filteredAffiliates.slice(startIndex, endIndex)
  }

  const fetchAffiliates = async () => {
    try {
      const response = await fetch('/api/admin/affiliates')
      const data = await response.json()
      setAffiliates(data)
      setFilteredAffiliates(data)
    } catch (error) {
      console.error('Error fetching affiliates:', error)
      toast.error('Failed to load affiliates')
    } finally {
      setLoading(false)
    }
  }

  const updateCommissionRate = async (affiliateId: string, newRate: number) => {
    if (newRate < 0 || newRate > 1) {
      toast.error('Commission rate must be between 0 and 1')
      return
    }

    setUpdating(affiliateId)
    try {
      const response = await fetch(`/api/admin/affiliates/${affiliateId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ commissionRate: newRate }),
      })

      if (!response.ok) {
        throw new Error('Failed to update commission rate')
      }

      setAffiliates(affiliates.map(affiliate => 
        affiliate.id === affiliateId 
          ? { ...affiliate, commissionRate: newRate }
          : affiliate
      ))

      toast.success('Commission rate updated successfully')
    } catch (error) {
      console.error('Error updating commission rate:', error)
      toast.error('Failed to update commission rate')
    } finally {
      setUpdating(null)
    }
  }

  const updateDiscountRate = async (affiliateId: string, newRate: number) => {
    if (newRate < 0 || newRate > 1) {
      toast.error('Discount rate must be between 0 and 1')
      return
    }

    setUpdating(affiliateId)
    try {
      const response = await fetch(`/api/admin/affiliates/${affiliateId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ discountRate: newRate }),
      })

      if (!response.ok) {
        throw new Error('Failed to update discount rate')
      }

      setAffiliates(affiliates.map(affiliate => 
        affiliate.id === affiliateId 
          ? { ...affiliate, discountRate: newRate }
          : affiliate
      ))

      toast.success('Discount rate updated successfully')
    } catch (error) {
      console.error('Error updating discount rate:', error)
      toast.error('Failed to update discount rate')
    } finally {
      setUpdating(null)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-10 flex items-center justify-center">
        <Icons.spinner className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Affiliate Management</h2>
          <p className="text-muted-foreground">
            Manage affiliate partners and their commission rates
          </p>
        </div>
      </div>

      <div className="mb-6">
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search by name, email or code..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              onClick={() => setSearchQuery("")}
              size="sm"
            >
              <Icons.close className="h-4 w-4" />
            </Button>
          )}
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          Showing {filteredAffiliates.length > 0 
            ? `${(currentPage - 1) * ITEMS_PER_PAGE + 1}-${Math.min(currentPage * ITEMS_PER_PAGE, filteredAffiliates.length)}` 
            : "0"} of {filteredAffiliates.length} affiliates
        </p>
      </div>

      <div className="space-y-6">
        {getPaginatedAffiliates().map((affiliate) => (
          <Card key={affiliate.id} className="w-full">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>
                    {affiliate.user.name ? `${affiliate.user.name} (${affiliate.user.email})` : affiliate.user.email}
                  </CardTitle>
                  <CardDescription>Code: {affiliate.code}</CardDescription>
                </div>
                <Badge variant={affiliate.status === "ACTIVE" ? "success" : "secondary"}>
                  {affiliate.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Commission Rate</p>
                  <div className="flex items-center space-x-2">
                    <Input
                      type="number"
                      value={affiliate.commissionRate}
                      onChange={(e) => {
                        const newRate = parseFloat(e.target.value)
                        if (!isNaN(newRate)) {
                          updateCommissionRate(affiliate.id, newRate)
                        }
                      }}
                      step="0.01"
                      min="0"
                      max="1"
                      className="w-24"
                    />
                    <span className="text-sm text-muted-foreground">
                      ({(affiliate.commissionRate * 100).toFixed(1)}%)
                    </span>
                  </div>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Discount Rate</p>
                  <div className="flex items-center space-x-2">
                    <Input
                      type="number"
                      value={affiliate.discountRate}
                      onChange={(e) => {
                        const newRate = parseFloat(e.target.value)
                        if (!isNaN(newRate)) {
                          updateDiscountRate(affiliate.id, newRate)
                        }
                      }}
                      step="0.01"
                      min="0"
                      max="1"
                      className="w-24"
                    />
                    <span className="text-sm text-muted-foreground">
                      ({(affiliate.discountRate * 100).toFixed(1)}%)
                    </span>
                  </div>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Total Earnings</p>
                  <p className="text-2xl font-bold">
                    ${affiliate.totalEarnings.toFixed(2)}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                  <h3 className="font-semibold mb-2">Presale Statistics</h3>
                  <div className="space-y-2">
                    <p>Total Subscriptions: {affiliate.statistics.presaleSubscriptions}</p>
                    <p>Converted to Users: {affiliate.statistics.convertedSubscriptions}</p>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Purchase Statistics</h3>
                  <div className="space-y-2">
                    <p>Total Orders: {affiliate.statistics.totalPurchases}</p>
                    <p>Total Amount: ${affiliate.statistics.totalPurchaseAmount.toFixed(2)}</p>
                  </div>
                </div>
              </div>

              {affiliate.statistics.purchases.length > 0 && (
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="purchases">
                    <AccordionTrigger>
                      Purchase Details ({affiliate.statistics.purchases.length} orders)
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="mt-4">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Order Date</TableHead>
                              <TableHead>Customer</TableHead>
                              <TableHead>Products</TableHead>
                              <TableHead>Total</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {affiliate.statistics.purchases.map((purchase) => (
                              <TableRow key={purchase.orderId}>
                                <TableCell>
                                  {format(new Date(purchase.orderDate), "PPP")}
                                </TableCell>
                                <TableCell>
                                  {purchase.customer.name || purchase.customer.email}
                                </TableCell>
                                <TableCell>
                                  <ul className="list-disc list-inside">
                                    {purchase.items.map((item, index) => (
                                      <li key={index}>
                                        {item.productName} x{item.quantity} (${item.price})
                                      </li>
                                    ))}
                                  </ul>
                                </TableCell>
                                <TableCell>${purchase.total.toFixed(2)}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              )}
            </CardContent>
          </Card>
        ))}
        
        {filteredAffiliates.length === 0 && (
          <div className="text-center py-10 text-muted-foreground">
            No affiliates found matching your search.
          </div>
        )}
      </div>
      
      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 py-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <div className="text-sm text-muted-foreground mx-4">
            Page {currentPage} of {totalPages}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
} 