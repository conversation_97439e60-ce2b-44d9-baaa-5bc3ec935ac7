import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { OdooOrder } from "@/app/types/odoo"
import { getOdooServiceForOrder } from "@/lib/odooServiceFactory"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// POST endpoint for manually creating an Odoo order
export async function POST(
  req: Request,
  { params }: { params: { orderId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Verify the user is an admin
    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Get order details with all necessary information
    const order = await prisma.order.findUnique({
      where: { id: params.orderId },
      select: {
        id: true,
        userId: true,
        total: true,
        status: true,
        addressId: true,
        shippingAddressSnapshot: true,
        paymentId: true,
        createdAt: true,
        updatedAt: true,
        referralCode: true,
        items: {
          select: {
            id: true,
            quantity: true,
            price: true,
            uid: true,
            lpaString: true,
            productCode: true,
            variantCode: true,
            variantText: true
          }
        },
        shippingAddress: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!order) {
      return new NextResponse("Order not found", { status: 404 })
    }

    // Get shipping address (either from snapshot or address record)
    const shippingAddress = order.shippingAddressSnapshot || order.shippingAddress

    if (!shippingAddress) {
      return new NextResponse("Shipping address is required", { status: 400 })
    }

    // Format address from snapshot
    const address = typeof shippingAddress === 'object' ? shippingAddress : JSON.parse(shippingAddress as unknown as string)

    // 获取订单项目的产品信息
    const orderItemsWithProducts = await Promise.all(
      order.items.map(async (item) => {
        if (!item.productCode) return { ...item, product: null };

        const product = await prisma.product.findFirst({
          where: { sku: item.productCode },
          include: { category: true }
        });

        return { ...item, product };
      })
    );

    // 准备订单行数据（用于日志记录）
    console.log(`Preparing order lines for ${orderItemsWithProducts.length} items`)

    // 首先按 variantCode 分组
    const itemsByVariantCode: Record<string, Array<any>> = {};
    for (const item of orderItemsWithProducts) {
      const variantCode = item.variantCode || 'default';
      if (!itemsByVariantCode[variantCode]) {
        itemsByVariantCode[variantCode] = [];
      }
      itemsByVariantCode[variantCode].push(item);
    }

    // 然后，对于每个 variantCode 组，再按 UID 分组
    // 这样可以保持 OdooOrderStatus 表的唯一约束 [orderId, variantCode]
    const itemsByGroup: Record<string, Array<any>> = {};
    for (const [variantCode, items] of Object.entries(itemsByVariantCode)) {
      // 按 UID 分组
      const itemsByUid: Record<string, Array<any>> = {};
      for (const item of items) {
        const uid = item.uid || 'no-uid';
        if (!itemsByUid[uid]) {
          itemsByUid[uid] = [];
        }
        itemsByUid[uid].push(item);
      }

      // 为每个 UID 创建一个组
      for (const [uid, uidItems] of Object.entries(itemsByUid)) {
        // 使用更可靠的分隔符（:::）来避免与UUID中的连字符冲突
        const key = `${variantCode}:::${uid}`;
        itemsByGroup[key] = uidItems;
      }
    }

    // 确定使用哪个 Odoo 服务（所有项目使用同一个服务）
    const isQrOrder = orderItemsWithProducts.some((item: any) =>
      item.product?.category?.name?.toLowerCase() === 'qr_code'
    );
    const selectedOdooService = isQrOrder ?
      getOdooServiceForOrder(orderItemsWithProducts.filter((item: any) => item.product?.category?.name?.toLowerCase() === 'qr_code')) :
      getOdooServiceForOrder(orderItemsWithProducts.filter((item: any) => item.product?.category?.name?.toLowerCase() !== 'qr_code'));

    // 记录使用的服务类型
    const serviceType = isQrOrder ? "QR Odoo Service" : "Standard Odoo Service"
    console.log(`Using ${serviceType} for order ${order.id}`)

    // 准备通用的订单信息
    const commonOrderInfo = {
      shipping_address: {
        name: address.name || "",
        city: `${address.city || ""} ${address.state || ""}`,
        zip: address.postalCode || "",
        address: `${address.address2 || ""} ${address.address1 || ""}, ${address.city || ""} ${address.state || ""}, ${address.country || ""}`,
        phone: address.phone || "",
        country: address.country || ""
      },
      payment_method: "prepaid",
      email: (order as any).user?.email || "",
    };

    // 为每组创建一个 Odoo 订单
    const odooResponses = [];

    try {
      for (const [groupKey, items] of Object.entries(itemsByGroup)) {
        // 从分组键中提取 variantCode 和 uid，使用更可靠的分隔符
        const [variantCode, uid] = groupKey.includes(':::')
          ? groupKey.split(':::')
          : [groupKey.split('-')[0], groupKey.split('-').slice(1).join('-')]; // 向后兼容旧格式
        console.log(`Creating Odoo order for group ${groupKey} (variant: ${variantCode}, uid: ${uid}) with ${items.length} items`);

        const variantOdooOrder: OdooOrder = {
          customer_order_ref: `${order.id}-${groupKey}`,
          ...commonOrderInfo,
          note: `Order manually synced from admin panel (Variant: ${variantCode}, UID: ${uid})`,
          order_lines: items.map((item: any) => ({
            customer_order_line_ref: `${order.id}-${item.id}`,
            product_code: item.variantCode || item.productCode || "",
            card_uid: item.uid || undefined,
            product_uom_qty: item.quantity,
            lpa_string: item.lpaString || undefined
          })),
        };

        try {
          // 创建 Odoo 订单
          const response = await selectedOdooService.createOrder(variantOdooOrder);
          console.log(`Odoo order created for variant ${variantCode}:`, response);

          // 获取 Odoo 响应中的状态和消息
          const responseStatus = response.result?.status || "unknown";
          const responseMessage = response.result?.message || "No response message";

          // 设置 OdooOrderStatus 的状态 - Odoo返回"ok"表示成功
          const isSuccess = responseStatus === "ok" || responseStatus === "success";
          const odooStatus = isSuccess ? "processing" : "error";

          // 设置 OdooOrderStatus 的描述
          const odooDescription = isSuccess
            ? `Successful: ${responseMessage}`
            : `Failed: ${responseMessage}`;

          odooResponses.push({
            groupKey,
            variantCode,
            uid,
            response,
            status: odooStatus,
            description: odooDescription,
            success: isSuccess,
            odooOrderRef: response.result?.data?.order_name || null
          });
        } catch (variantError) {
          console.error(`Error creating Odoo order for group ${groupKey} (variant: ${variantCode}, uid: ${uid}):`, variantError);

          odooResponses.push({
            groupKey,
            variantCode,
            uid,
            response: null,
            status: "error",
            description: `Error: ${variantError instanceof Error ? variantError.message : 'Unknown error'}`,
            success: false,
            odooOrderRef: null
          });
        }
      }

      // 在创建真实的 Odoo 订单状态记录之前，先删除默认的记录
      console.log(`删除订单 ${params.orderId} 的默认 odooOrderStatus 记录`);
      const deletedDefaultRecords = await prisma.odooOrderStatus.deleteMany({
        where: {
          orderId: params.orderId,
          variantCode: "default"
        }
      });
      console.log(`已删除 ${deletedDefaultRecords.count} 个默认 odooOrderStatus 记录`);

      // 为每个响应创建单独的 OdooOrderStatus 记录
      console.log(`准备将 ${odooResponses.length} 个Odoo响应记录到数据库`);
      for (const response of odooResponses) {
        try {
          // 确保variantCode是UUID格式
          const formattedVariantCode = response.variantCode || "default";

          // 确保UID是数字字符串格式，排除'no-uid'情况
          const rawUid = response.uid === 'no-uid' ? null : response.uid;
          const formattedUid = rawUid ? rawUid.replace(/[^0-9,]/g, '') : null;

          const status = response.success ? "processing" : "error";

          console.log(`[ODOO_CREATE] Processing order ${params.orderId}, variant ${formattedVariantCode}, uid=${formattedUid || 'none'}`);

          // 查找是否已存在相同 orderId, variantCode, uid 的记录
          const existingStatus = await prisma.odooOrderStatus.findFirst({
            where: {
              orderId: params.orderId,
              variantCode: formattedVariantCode,
              uid: formattedUid
            }
          });

          if (existingStatus) {
            // 更新现有记录
            await prisma.odooOrderStatus.update({
              where: { id: existingStatus.id },
              data: {
                status: status,
                description: response.description,
                odooOrderRef: response.odooOrderRef,
                lastCheckedAt: new Date(),
              }
            });
          } else {
            // 创建新记录
            await prisma.odooOrderStatus.create({
              data: {
                orderId: params.orderId,
                variantCode: formattedVariantCode,
                uid: formattedUid,
                status: status,
                description: response.description,
                odooOrderRef: response.odooOrderRef,
                isDigital: false,
                deliveredQty: 0,
                lastCheckedAt: new Date(),
              }
            });
          }

          console.log(`OdooOrderStatus 成功记录: orderId=${params.orderId}, variant=${formattedVariantCode}, uid=${formattedUid || 'none'}, status=${status}`);
        } catch (statusError) {
          console.error(`记录 OdooOrderStatus 失败: orderId=${params.orderId}, variant=${formattedVariantCode}, uid=${formattedUid || 'none'}`);
          console.error(`错误详情:`, statusError);

          // 记录更详细的错误信息
          if (statusError instanceof Error) {
            console.error(`错误名称: ${statusError.name}`);
            console.error(`错误消息: ${statusError.message}`);
            console.error(`堆栈跟踪: ${statusError.stack}`);
          }

          // 尝试以不同的方式记录，以防是格式问题
          try {
            console.log(`尝试使用替代方法记录 OdooOrderStatus...`);
            await prisma.odooOrderStatus.create({
              data: {
                orderId: params.orderId,
                variantCode: response.variantCode || "unknown",
                uid: response.uid === 'no-uid' ? null : response.uid,
                status: response.success ? "processing" : "error",
                description: `${response.description} (备用记录)`,
                odooOrderRef: response.odooOrderRef,
                isDigital: false,
                deliveredQty: 0,
                lastCheckedAt: new Date(),
              }
            });
            console.log(`使用替代方法成功记录 OdooOrderStatus`);
          } catch (fallbackError) {
            console.error(`替代记录方法也失败:`, fallbackError);
          }
        }
      }

      // 记录总体状态
      const allSuccess = odooResponses.every(r => r.success);
      console.log(`Created ${odooResponses.length} Odoo orders for order ${params.orderId}, all success: ${allSuccess}`);

    } catch (error) {
      console.error("Failed to create Odoo orders:", error);
      throw error;
    }

    // 获取更新后的 OdooOrderStatus 记录
    const updatedStatuses = await prisma.odooOrderStatus.findMany({
      where: { orderId: params.orderId }
    })

    // 确定总体状态
    const overallStatus = odooResponses.every(r => r.success) ? "success" : "error";
    const overallMessage = overallStatus === "success"
      ? `All ${odooResponses.length} Odoo orders created successfully using ${serviceType}`
      : `Some Odoo orders failed to create using ${serviceType}`;

    return NextResponse.json({
      success: true,
      message: overallMessage,
      odooResponses: odooResponses.map(r => ({
        variantCode: r.variantCode,
        uid: r.uid === 'no-uid' ? null : r.uid,
        status: r.status,
        description: r.description,
        odooOrderRef: r.odooOrderRef
      })),
      odooStatuses: updatedStatuses,
      serviceType
    })
  } catch (error) {
    console.error("[ODOO_CREATE_ORDER]", error)
    return new NextResponse(`Error creating Odoo order: ${error instanceof Error ? error.message : 'Unknown error'}`, { status: 500 })
  }
}