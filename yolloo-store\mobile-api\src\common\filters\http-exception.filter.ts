import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse() as any;

    let errorCode = 'INTERNAL_ERROR';
    if (status === HttpStatus.UNAUTHORIZED) errorCode = 'UNAUTHORIZED';
    else if (status === HttpStatus.BAD_REQUEST) errorCode = 'VALIDATION_ERROR';
    else if (status === HttpStatus.NOT_FOUND) errorCode = 'RESOURCE_NOT_FOUND';
    else if (status === HttpStatus.CONFLICT) errorCode = 'ALREADY_EXISTS';

    const errorResponse = {
      error: {
        code: errorCode,
        message: exceptionResponse.message || exception.message,
        details: exceptionResponse.details || {},
      },
    };

    response.status(status).json(errorResponse);
  }
}
