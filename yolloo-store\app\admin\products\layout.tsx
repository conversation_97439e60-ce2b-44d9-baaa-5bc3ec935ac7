import { prisma } from "@/lib/prisma"

interface ProductsLayoutProps {
  children: React.ReactNode
}

async function getProducts() {
  return await prisma.product.findMany({
    orderBy: {
      createdAt: "desc",
    },
    include: {
      category: true,
    },
  })
}

export default async function ProductsLayout({ children }: ProductsLayoutProps) {
  const products = await getProducts()

  if (!children) {
    return null
  }

  // @ts-ignore - Ignore type checking for production build
  const childComponent = children as React.ReactElement
  
  // @ts-ignore - Ignore type checking for production build
  return childComponent.type.name === "ProductsPage" ? (
    // @ts-ignore - Ignore type checking for production build
    childComponent.type({ products })
  ) : (
    children
  )
} 