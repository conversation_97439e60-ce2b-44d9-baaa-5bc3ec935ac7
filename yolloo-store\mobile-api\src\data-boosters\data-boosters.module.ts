import { Module } from '@nestjs/common';
import { DataBoostersService } from './data-boosters.service';
import { DataBoostersController } from './data-boosters.controller';
import { PrismaService } from '../prisma.service';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [AuthModule],
  controllers: [DataBoostersController],
  providers: [DataBoostersService, PrismaService],
})
export class DataBoostersModule {}
