import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { headers } from "next/headers"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function PATCH(
  req: Request,
  { params }: { params: { productId: string } }
) {
  try {
    const headersList = headers();
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { productId } = params;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: {
        id: productId,
      },
      include: {
        variants: true,
      },
    });

    if (!existingProduct) {
      return new NextResponse("Product not found", { status: 404 });
    }

    // Update product
    const updatedProduct = await prisma.product.update({
      where: {
        id: productId,
      },
      data: {
        name: body.name === null ? undefined : body.name,
        description: body.description === null ? undefined : body.description,
        websiteDescription: body.websiteDescription === null ? undefined : body.websiteDescription,
        price: body.price !== undefined && body.price !== null ?
          isNaN(parseFloat(body.price)) ? undefined : parseFloat(body.price) : undefined,
        images: body.images === null ? undefined : body.images,
        category: body.categoryId ? {
          connect: { id: body.categoryId }
        } : undefined,
        stock: body.stock !== undefined && body.stock !== null ?
          isNaN(parseInt(body.stock, 10)) ? undefined : parseInt(body.stock, 10) : undefined,
        sku: body.sku === null ? undefined : body.sku,
        specifications: body.specifications === null ? undefined : body.specifications,
        mcc: body.mcc === null ? null : body.mcc,
        requiredUID: body.requiredUID === null ? undefined : body.requiredUID,
        off_shelve: body.off_shelve === null ? undefined : body.off_shelve,
        status: body.status === null ? undefined : body.status,
        dataSize: body.dataSize === null ? null : body.dataSize,
        planType: body.planType === null ? null : body.planType,
        country: body.country === null ? null : body.country,
        countryCode: body.countryCode === null ? null : body.countryCode,
      },
      include: {
        variants: {
          orderBy: [
            { durationType: 'asc' },
            { duration: 'asc' }
          ]
        },
        category: true,
      },
    });

    // 删除产品记录相关代码已移除

    // Handle variants
    if (body.variants && Array.isArray(body.variants)) {
      // Get existing variant IDs
      const existingVariantIds = existingProduct.variants.map(
        (variant: any) => variant.id
      );

      // Get new variant IDs (those that have an ID)
      const newVariantIds = body.variants
        .filter((v: any) => v && v.id)
        .map((v: any) => v.id);

      // Find variants to delete (those in existing but not in new)
      const variantsToDelete = existingVariantIds.filter(
        (id: any) => !newVariantIds.includes(id)
      );

      // Delete removed variants
      if (variantsToDelete.length > 0) {
        await prisma.productVariant.deleteMany({
          where: {
            id: {
              in: variantsToDelete,
            },
          },
        });
      }

      // Update or create variants
      for (const variant of body.variants) {
        if (!variant) continue; // Skip null or undefined variants

        if (variant.id) {
          // Update existing variant
          await prisma.productVariant.update({
            where: {
              id: variant.id,
            },
            data: {
              price: variant.price !== null && variant.price !== undefined ?
                isNaN(Number(variant.price)) ? 0 : Number(variant.price) : 0,
              currency: variant.currency === null ? undefined : (variant.currency || "USD"),
              attributes: variant.attributes === null ? {} : (variant.attributes || {}),
              duration: variant.duration === null ? null :
                (variant.duration !== undefined ?
                  (isNaN(Number(variant.duration)) ? null : Number(variant.duration)) : null),
              durationType: variant.durationType === null ? null : (variant.durationType || null),
              variantCode: variant.variantCode === null ? null : (variant.variantCode || null),
            },
          });
        } else {
          // Create new variant
          await prisma.productVariant.create({
            data: {
              price: variant.price !== null && variant.price !== undefined ?
                isNaN(Number(variant.price)) ? 0 : Number(variant.price) : 0,
              currency: variant.currency === null ? "USD" : (variant.currency || "USD"),
              attributes: variant.attributes === null ? {} : (variant.attributes || {}),
              productId: productId,
              duration: variant.duration === null ? null :
                (variant.duration !== undefined ?
                  (isNaN(Number(variant.duration)) ? null : Number(variant.duration)) : null),
              durationType: variant.durationType === null ? null : (variant.durationType || null),
              variantCode: variant.variantCode === null ? null : (variant.variantCode || null),
            },
          });
        }
      }
    }

    return NextResponse.json(updatedProduct);
  } catch (error) {
    console.error("[PRODUCT_PATCH]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { productId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // 验证商品存在
    const product = await prisma.product.findUnique({
      where: { id: params.productId },
      select: {
        id: true,
        name: true
      }
    });

    if (!product) {
      return new NextResponse("Product not found", { status: 404 })
    }

    console.log(`[PRODUCT_DELETE] Starting deletion process for product ID: ${params.productId} (${product.name})`);

    // 获取产品的sku
    const productDetails = await prisma.product.findUnique({
      where: { id: params.productId },
      select: { sku: true }
    });

    if (!productDetails) {
      return new NextResponse("Product details not found", { status: 404 });
    }

    // 检查是否有订单项使用此产品（通过productCode）
    const orderItemsCount = await prisma.orderItem.count({
      where: {
        productCode: productDetails.sku,
      },
    });

    if (orderItemsCount > 0) {
      return new NextResponse(
        `Cannot delete product: "${product.name}" because it is used in ${orderItemsCount} order(s). Please first delete those orders.`,
        { status: 400 }
      );
    }

    // 检查是否有Esim使用此产品
    const esimsCount = await prisma.esim.count({
      where: {
        productId: params.productId,
      },
    });

    if (esimsCount > 0) {
      return new NextResponse(
        `Cannot delete product: "${product.name}" because it is linked to ${esimsCount} eSIM(s). Please first update those eSIMs to use a different product.`,
        { status: 400 }
      );
    }

    // 使用事务来确保产品和相关数据的删除是原子的
    await prisma.$transaction(async (tx) => {
      try {
        // 1. 删除所有关联的CartItem
        console.log(`[PRODUCT_DELETE] Deleting cart items for product: ${params.productId}`);
        const cartItemsResult = await tx.cartItem.deleteMany({
          where: {
            productId: params.productId,
          },
        })
        console.log(`[PRODUCT_DELETE] Deleted ${cartItemsResult.count} cart items`);

        // 2. 删除所有关联的WishlistItem
        console.log(`[PRODUCT_DELETE] Deleting wishlist items for product: ${params.productId}`);
        const wishlistItemsResult = await tx.wishlistItem.deleteMany({
          where: {
            productId: params.productId,
          },
        })
        console.log(`[PRODUCT_DELETE] Deleted ${wishlistItemsResult.count} wishlist items`);

        // 3. 删除所有关联的Review
        console.log(`[PRODUCT_DELETE] Deleting reviews for product: ${params.productId}`);
        const reviewsResult = await tx.review.deleteMany({
          where: {
            productId: params.productId,
          },
        })
        console.log(`[PRODUCT_DELETE] Deleted ${reviewsResult.count} reviews`);

        // 4. 删除所有关联的ProductParameter
        console.log(`[PRODUCT_DELETE] Deleting product parameters for product: ${params.productId}`);
        const parametersResult = await tx.productParameter.deleteMany({
          where: {
            productId: params.productId,
          },
        })
        console.log(`[PRODUCT_DELETE] Deleted ${parametersResult.count} product parameters`);

        // 5. 删除所有关联的ProductVariant
        console.log(`[PRODUCT_DELETE] Deleting product variants for product: ${params.productId}`);
        const variantsResult = await tx.productVariant.deleteMany({
          where: {
            productId: params.productId,
          },
        })
        console.log(`[PRODUCT_DELETE] Deleted ${variantsResult.count} product variants`);

        // 6. 删除产品
        console.log(`[PRODUCT_DELETE] Deleting product: ${params.productId}`);
        await tx.product.delete({
          where: {
            id: params.productId,
          },
        })
        console.log(`[PRODUCT_DELETE] Successfully deleted product: ${params.productId}`);
      } catch (txError) {
        console.error(`[PRODUCT_DELETE] Transaction error:`, txError);
        throw txError; // Re-throw to trigger transaction rollback
      }
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[PRODUCT_DELETE]", error)

    if (error instanceof Error) {
      // Foreign key constraint errors often have specific error codes/messages
      if (error.message.includes("foreign key constraint")) {
        return new NextResponse("Cannot delete this product because it is referenced by other records in the database", { status: 400 })
      }

      return new NextResponse(`Error deleting product: ${error.message}`, { status: 500 })
    }

    return new NextResponse("Internal error", { status: 500 })
  }
}