"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>T<PERSON>le, DialogDescription } from "@/components/ui/dialog";
import { EsimBuyNowForm } from "@/components/esims/EsimBuyNowForm";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Icons } from "@/components/icons";
import { ShoppingCart } from "lucide-react";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  stock: number;
  status: string;
  country: string | null;
  countryCode?: string | null;
  planType?: string | null;
  dataSize?: number | null;
  sku: string;
  variants?: { id: string; price: number; duration: number; durationType: string }[];
}

export default function EsimProductCard({ product }: { product: Product }) {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Card className="group p-4 flex flex-col gap-3 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-200 bg-gradient-to-br from-white via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 border-0">
        <div className="relative w-full aspect-video rounded-xl overflow-hidden mb-2 bg-gradient-to-tr from-blue-100 to-violet-100 dark:from-gray-800 dark:to-gray-900 flex items-center justify-center">
          {product.images?.[0] ? (
            <img
              src={product.images[0]}
              alt={product.name}
              className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <Icons.image className="h-16 w-16 text-blue-200 dark:text-gray-700" />
          )}
          {/* 角标示例 */}
          {product.stock <= 10 && (
            <span className="absolute top-2 right-2 bg-gradient-to-r from-pink-500 to-yellow-400 text-white text-xs px-2 py-1 rounded-full shadow">
              Low Stock
            </span>
          )}
        </div>
        <div className="flex-1 flex flex-col gap-1">
          <h3 className="font-semibold text-lg truncate" title={product.name}>{product.name}</h3>
          <div className="flex flex-wrap gap-2 items-center mt-1">
            {product.country && (
              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-400 dark:border-blue-800">
                {product.country}
              </Badge>
            )}
            {product.planType && (
              <Badge variant="outline" className="text-xs bg-violet-50 text-violet-700 border-violet-200 dark:bg-violet-950 dark:text-violet-400 dark:border-violet-800">
                {product.planType}
              </Badge>
            )}
            {product.dataSize && (
              <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800">
                {product.dataSize >= 1024 ? `${(product.dataSize / 1024).toFixed(2)} GB` : `${product.dataSize} MB`}
              </Badge>
            )}
          </div>
          <div className="mt-2 text-2xl font-extrabold bg-gradient-to-r from-blue-500 to-violet-500 bg-clip-text text-transparent">
            ${product.price.toFixed(2)}
          </div>
        </div>
        <Button
          className="mt-2 w-full bg-gradient-to-r from-blue-500 to-violet-500 text-white font-bold shadow-lg hover:scale-105 transition-transform"
          onClick={() => setOpen(true)}
          variant="default"
        >
          <ShoppingCart className="mr-2 h-5 w-5" />
          View & Buy
        </Button>
      </Card>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>{product.name}</DialogTitle>
            <DialogDescription>{product.description}</DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4">
            {product.images?.[0] && (
              <img src={product.images[0]} alt={product.name} className="rounded-lg w-full object-cover max-h-48" />
            )}
            <EsimBuyNowForm
              product={{ id: product.id, stock: product.stock }}
              variants={Array.isArray(product.variants)
                ? product.variants.map(v => ({
                    id: v.id,
                    price: typeof v.price === 'number' ? v.price : Number(v.price),
                    duration: v.duration,
                    durationType: v.durationType,
                  }))
                : []}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
} 