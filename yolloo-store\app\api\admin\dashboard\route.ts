import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";
import { DateFormatter, DateUtils } from "@/lib/utils";

// 辅助函数：将对象中的所有BigInt转换为Number，并格式化日期
function convertBigIntToNumber(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'bigint') {
    return Number(obj);
  }

  // 处理日期对象 - 使用统一的日期格式化工具
  if (obj instanceof Date) {
    return DateFormatter.iso(obj);
  }

  // 处理可能是PostgreSQL日期的对象
  if (obj && typeof obj === 'object' && obj.hasOwnProperty('toISOString')) {
    return DateFormatter.iso(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(convertBigIntToNumber);
  }

  if (typeof obj === 'object') {
    const result: Record<string, any> = {};
    for (const key in obj) {
      result[key] = convertBigIntToNumber(obj[key]);
    }
    return result;
  }

  return obj;
}

// 添加动态路由配置
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// GET /api/admin/dashboard - 获取仪表板数据
export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const period = searchParams.get("period") || "30d"; // 7d, 30d, 90d, 1y

    // 计算日期范围
    const now = new Date();
    let startDate: Date;
    switch (period) {
      case "7d":
        startDate = DateUtils.addDays(now, -7);
        break;
      case "30d":
        startDate = DateUtils.addDays(now, -30);
        break;
      case "90d":
        startDate = DateUtils.addDays(now, -90);
        break;
      case "1y":
        startDate = DateUtils.addDays(now, -365);
        break;
      default:
        startDate = DateUtils.addDays(now, -30);
    }

    // 先处理 topProducts
    const topProductItems = await prisma.orderItem.groupBy({
      by: ["productCode"],
      where: {
        order: {
          status: "PAID",
          createdAt: {
            gte: startDate,
          },
        },
      },
      _sum: {
        quantity: true,
        price: true,
      },
      orderBy: {
        _sum: {
          quantity: "desc",
        },
      },
      take: 5,
    });
    const productCodes = topProductItems.map((item) => item.productCode).filter((code): code is string => !!code);
    const products = productCodes.length > 0
      ? await prisma.product.findMany({ where: { sku: { in: productCodes } } })
      : [];
    const topProducts = topProductItems.map((item) => ({
      ...item,
      product: products.find((p) => p.sku === item.productCode),
    }));

    // 由于我们移除了 OrderItem 和 Product 之间的关系，我们需要使用 JOIN 查询
    // 使用原始 SQL 查询来获取按类别分组的订单项
    const topCategoryItemsRaw = await prisma.$queryRaw`
      SELECT
        p."categoryId",
        SUM(oi.quantity) as quantity
      FROM "OrderItem" oi
      JOIN "Product" p ON oi."productCode" = p.sku
      JOIN "Order" o ON oi."orderId" = o.id
      WHERE
        o.status = 'PAID'
        AND o."createdAt" >= ${startDate}
      GROUP BY p."categoryId"
      ORDER BY quantity DESC
    `;

    // 转换查询结果为预期格式
    const groupedByCategory = Array.isArray(topCategoryItemsRaw)
      ? topCategoryItemsRaw.reduce((acc, item: any) => {
          const categoryId = item.categoryId;
          if (!categoryId) return acc;
          acc[categoryId] = { quantity: Number(item.quantity) };
          return acc;
        }, {} as Record<string, { quantity: number }>)
      : {};
    const topCategories = Object.entries(groupedByCategory).map(([categoryId, data]) => ({
      categoryId,
      _sum: { quantity: (data as { quantity: number }).quantity }
    }));

    // 其余统计并发
    const [
      totalRevenue,
      totalOrders,
      totalProducts,
      totalUsers,
      averageOrderValue,
      recentOrders,
      salesTrend,
      userGrowth,
    ] = await Promise.all([
      // 总收入
      prisma.order.aggregate({
        where: {
          status: "PAID",
          createdAt: {
            gte: startDate,
          },
        },
        _sum: {
          total: true,
        },
      }),
      // 订单总数
      prisma.order.count({
        where: {
          createdAt: {
            gte: startDate,
          },
        },
      }),
      // 产品总数
      prisma.product.count(),
      // 用户总数
      prisma.user.count(),
      // 平均订单金额
      prisma.order.aggregate({
        where: {
          status: "PAID",
          createdAt: {
            gte: startDate,
          },
        },
        _avg: {
          total: true,
        },
      }),
      // 最近订单
      prisma.order.findMany({
        where: {
          createdAt: {
            gte: startDate,
          },
        },
        include: {
          user: {
            select: {
              name: true,
              email: true,
            },
          },
          items: true,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 5,
      }),
      // 销售趋势
      prisma.$queryRaw`
        SELECT
          DATE_TRUNC('day', "createdAt") as date,
          COUNT(*) as orders,
          SUM(total) as revenue
        FROM "Order"
        WHERE
          status = 'PAID'
          AND "createdAt" >= ${startDate}
        GROUP BY DATE_TRUNC('day', "createdAt")
        ORDER BY date ASC
      `,
      // 用户增长
      prisma.$queryRaw`
        SELECT
          DATE_TRUNC('day', "createdAt") as date,
          COUNT(*) as users
        FROM "User"
        WHERE "createdAt" >= ${startDate}
        GROUP BY DATE_TRUNC('day', "createdAt")
        ORDER BY date ASC
      `,
    ]);

    // 处理销售趋势数据，确保日期格式正确 - 使用统一的日期格式化工具
    const formattedSalesTrend = Array.isArray(salesTrend)
      ? salesTrend.map(item => ({
          ...item,
          date: DateFormatter.iso(item.date),
          revenue: typeof item.revenue === 'bigint' ? Number(item.revenue) : item.revenue,
          orders: typeof item.orders === 'bigint' ? Number(item.orders) : item.orders
        }))
      : [];

    // 处理用户增长数据，确保日期格式正确 - 使用统一的日期格式化工具
    const formattedUserGrowth = Array.isArray(userGrowth)
      ? userGrowth.map(item => ({
          ...item,
          date: DateFormatter.iso(item.date),
          users: typeof item.users === 'bigint' ? Number(item.users) : item.users
        }))
      : [];

    // 在返回数据前转换所有BigInt
    const responseData = {
      overview: {
        totalRevenue: totalRevenue._sum.total || 0,
        totalOrders,
        totalProducts,
        totalUsers,
        averageOrderValue: averageOrderValue._avg.total || 0,
      },
      topProducts,
      topCategories,
      recentOrders,
      trends: {
        sales: formattedSalesTrend,
        users: formattedUserGrowth,
      },
    };

    // 转换所有BigInt为Number
    const safeResponseData = convertBigIntToNumber(responseData);

    return NextResponse.json(safeResponseData);
  } catch (error) {
    console.error("[ADMIN_DASHBOARD_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}