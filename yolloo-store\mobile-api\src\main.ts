import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Set global prefix for all routes
  app.setGlobalPrefix('api/mobile');

  // Serve static files
  app.useStaticAssets(join(__dirname, '..', 'public'), {
    prefix: '/api/mobile/static/',
  });

  // Enable CORS
  app.enableCors();

  // Set up global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // Strip properties that do not have any decorators
      transform: true, // Transform payloads to DTO instances
      forbidNonWhitelisted: true, // Throw errors if non-whitelisted properties are present
      transformOptions: {
        enableImplicitConversion: true, // Automatically transform primitive types
      },
    }),
  );

  // 使用环境变量中的端口，优先 MOBILE_API_PORT，其次 PORT，最后默认 4000
  const port = process.env.MOBILE_API_PORT || process.env.PORT || 4000;
  await app.listen(port, '0.0.0.0');
  console.log(`Mobile API is running on port ${port}, URL: ${await app.getUrl()}`);
}
bootstrap();
