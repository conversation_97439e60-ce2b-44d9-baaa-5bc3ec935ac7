"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

interface AutoUpdateOdooStatusProps {
  orderId: string
}

export function AutoUpdateOdooStatus({ orderId }: AutoUpdateOdooStatusProps) {
  const router = useRouter()
  const [isUpdating, setIsUpdating] = useState(true)

  useEffect(() => {
    // Function to update Odoo status
    async function updateOdooStatus() {
      try {
        const response = await fetch(`/api/admin/orders/${orderId}/update-status`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        })

        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.message || "Failed to update order status")
        }

        // 显示更新的状态记录数量
        const statusCount = data.odooStatuses?.length || 0;

        // Show success toast
        toast.success("Odoo status updated automatically", {
          description: `Updated ${statusCount} status records. Order information has been refreshed with the latest status from Odoo.`
        })

        // Refresh the page to show updated information
        router.refresh()
      } catch (error) {
        console.error("Auto update Odoo status error:", error)
        // Show error toast with less intrusive styling
        toast.error(`Failed to auto-update Odoo status: ${error instanceof Error ? error.message : 'Unknown error'}`, {
          duration: 3000
        })
      } finally {
        setIsUpdating(false)
      }
    }

    // Call the update function when component mounts
    updateOdooStatus()
  }, [orderId, router])

  // This component doesn't render anything visible
  return null
}
