import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


type MarketingCommissionStatus = "PENDING" | "APPROVED" | "REJECTED" | "PAID"

interface Commission {
  id: string
  userId: string
  amount: number
  status: MarketingCommissionStatus
  createdAt: Date
  updatedAt: Date
  description: string | null
  user: {
    id: string
    name: string | null
  }
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Fetch commissions with user information
    const commissions = await prisma.marketingCommission.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    // Transform the data to match the frontend interface
    const transformedCommissions = commissions.map((commission: Commission) => ({
      id: commission.id,
      userId: commission.userId,
      userName: commission.user.name || "Unknown User",
      amount: commission.amount,
      status: commission.status,
      createdAt: commission.createdAt,
      updatedAt: commission.updatedAt,
      description: commission.description || "",
    }))

    return NextResponse.json(transformedCommissions)
  } catch (error) {
    console.error("[COMMISSIONS_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 