"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { AddMemberButton } from "../add-member-button";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";

export default function InviteMemberPage({ params }: { params: { id: string } }) {
  const router = useRouter();

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6 flex items-center justify-between">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/affiliate/organization/${params.id}/members`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Members
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Invite New Member</CardTitle>
          <CardDescription>
            Send an invitation to a new member to join your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="max-w-md mx-auto">
            <AddMemberButton organizationId={params.id} showAsPage={true} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 