import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function PATCH(
  req: Request,
  { params }: { params: { orderId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await req.json()
    const { status, trackingNumber } = body

    if (!status) {
      return new NextResponse("Status is required", { status: 400 })
    }

    const order = await prisma.order.findUnique({
      where: {
        id: params.orderId,
      },
      select: {
        id: true,
        status: true,
        odooStatuses: {
          select: {
            id: true,
            orderId: true,
            variantCode: true,
            status: true,
            trackingNumber: true,
            lastCheckedAt: true
          }
        },
        shippingAddress: true,
        shippingAddressSnapshot: true,
      },
    })

    if (!order) {
      return new NextResponse("Order not found", { status: 404 })
    }

    // 如果是发货状态，需要提供运单号
    if (status === "SHIPPED" && !trackingNumber) {
      return new NextResponse("Tracking number is required for shipping", { status: 400 })
    }

    // 如果订单状态变更为CANCELLED，恢复库存
    if (status === "CANCELLED" && order.status !== "CANCELLED") {
      // 获取订单项
      const orderItems = await prisma.orderItem.findMany({
        where: {
          orderId: params.orderId
        },
        select: {
          productCode: true,
          quantity: true
        }
      });

      // 恢复每个产品的库存
      for (const item of orderItems) {
        if (item.productCode) {
          try {
            // 先检查产品是否存在
            const product = await prisma.product.findUnique({
              where: { sku: item.productCode },
              select: { id: true }
            });

            if (product) {
              await prisma.product.update({
                where: { sku: item.productCode },
                data: {
                  stock: {
                    increment: item.quantity
                  }
                }
              });
              console.log(`[ADMIN_ORDER_PATCH] Restored stock for product ${item.productCode} by ${item.quantity}`);
            } else {
              console.warn(`[ADMIN_ORDER_PATCH] Product with sku ${item.productCode} not found, skipping stock restoration`);
            }
          } catch (stockError) {
            console.error(`[ADMIN_ORDER_PATCH] Failed to restore stock for product ${item.productCode}:`, stockError);
            // 不中断订单取消流程，继续处理其他产品
          }
        }
      }
    }

    // 更新订单状态
    const updatedOrder = await prisma.order.update({
      where: {
        id: params.orderId,
      },
      data: {
        status,
      },
    })

    // 如果订单状态变更为PAID，同时更新关联的支付状态为COMPLETED
    if (status === "PAID") {
      await prisma.payment.updateMany({
        where: {
          orders: {
            some: {
              id: params.orderId,
            },
          },
        },
        data: {
          status: "COMPLETED",
        },
      });
    }

    // 如果订单状态为DELIVERED，更新关联的佣金状态
    if (status === "DELIVERED") {
      // 查找订单关联的推广记录
      const referral = await prisma.affiliateReferral.findFirst({
        where: {
          orderId: params.orderId,
          status: 'PENDING'
        },
        include: {
          organizationCommission: true
        }
      });

      // 如果找到推广记录，更新其状态为APPROVED
      if (referral) {
        console.log(`Updating affiliate referral status for order: ${params.orderId}`);

        // 更新AffiliateReferral状态
        await prisma.affiliateReferral.update({
          where: { id: referral.id },
          data: { status: 'APPROVED' }
        });

        // 如果有关联的组织佣金记录，同时更新其状态
        if (referral.organizationCommission) {
          console.log(`Updating organization commission status for order: ${params.orderId}`);
          await prisma.organizationCommission.update({
            where: { id: referral.organizationCommissionId! },
            data: { status: 'APPROVED' }
          });
        }
      }
    }

    // 如果提供了运单号，更新或创建odooStatus
    if (trackingNumber) {
      // 使用标准的UUID格式作为variantCode
      const defaultVariantCode = "default";

      // 查找是否已存在相同 orderId, variantCode 的记录
      const existingStatus = await prisma.odooOrderStatus.findFirst({
        where: {
          orderId: params.orderId,
          variantCode: defaultVariantCode
        }
      });

      if (existingStatus) {
        // 更新现有记录
        await prisma.odooOrderStatus.update({
          where: { id: existingStatus.id },
          data: {
            status: status,
            trackingNumber: trackingNumber,
            lastCheckedAt: new Date(),
          }
        });
      } else {
        // 创建新记录
        await prisma.odooOrderStatus.create({
          data: {
            orderId: params.orderId,
            variantCode: defaultVariantCode,
            status: status,
            trackingNumber: trackingNumber,
            lastCheckedAt: new Date(),
          }
        });
      }
    }

    return NextResponse.json(updatedOrder)
  } catch (error) {
    console.error("[ORDER_PATCH]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { orderId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // 验证订单存在
    const orderExists = await prisma.order.findUnique({
      where: { id: params.orderId },
      select: { id: true }
    });

    if (!orderExists) {
      return new NextResponse("Order not found", { status: 404 })
    }

    console.log(`[ORDER_DELETE] Starting deletion process for order ID: ${params.orderId}`);

    // 使用事务来确保订单和相关项目的删除是原子的
    await prisma.$transaction(async (tx) => {
      try {
        // 1. 找到关联的AffiliateReferral记录
        console.log(`[ORDER_DELETE] Looking for affiliate referral for order: ${params.orderId}`);
        const referral = await tx.affiliateReferral.findUnique({
          where: {
            orderId: params.orderId,
          },
          select: {
            id: true,
            organizationCommissionId: true,
          }
        });

        // 2. 如果存在引用关系，先处理OrganizationCommission
        if (referral) {
          console.log(`[ORDER_DELETE] Found referral: ${referral.id} for order: ${params.orderId}`);

          // 2.1 如果存在组织佣金记录，更新其状态为REJECTED
          if (referral.organizationCommissionId) {
            console.log(`[ORDER_DELETE] Updating organization commission status: ${referral.organizationCommissionId}`);
            await tx.organizationCommission.update({
              where: {
                id: referral.organizationCommissionId,
              },
              data: {
                status: "REJECTED",
              },
            });
          }

          // 2.2 删除AffiliateReferral记录 - 这是必须的，因为它有一个orderId唯一约束
          console.log(`[ORDER_DELETE] Deleting affiliate referral: ${referral.id}`);
          await tx.affiliateReferral.delete({
            where: {
              id: referral.id,
            },
          });
        } else {
          console.log(`[ORDER_DELETE] No referral found for order: ${params.orderId}`);
        }

        // 3. 获取订单项并恢复库存，然后删除订单项
        console.log(`[ORDER_DELETE] Getting order items for order: ${params.orderId}`);
        const orderItems = await tx.orderItem.findMany({
          where: {
            orderId: params.orderId,
          },
          select: {
            id: true,
            productCode: true,
            quantity: true
          }
        });

        // 恢复每个产品的库存
        for (const item of orderItems) {
          if (item.productCode) {
            try {
              // 先检查产品是否存在
              const product = await tx.product.findUnique({
                where: { sku: item.productCode },
                select: { id: true }
              });

              if (product) {
                await tx.product.update({
                  where: { sku: item.productCode },
                  data: {
                    stock: {
                      increment: item.quantity
                    }
                  }
                });
                console.log(`[ORDER_DELETE] Restored stock for product ${item.productCode} by ${item.quantity}`);
              } else {
                console.warn(`[ORDER_DELETE] Product with sku ${item.productCode} not found, skipping stock restoration`);
              }
            } catch (stockError) {
              console.error(`[ORDER_DELETE] Failed to restore stock for product ${item.productCode}:`, stockError);
              // 不中断订单删除流程，继续处理其他产品
            }
          }
        }

        // 删除订单项
        const orderItemsResult = await tx.orderItem.deleteMany({
          where: {
            orderId: params.orderId,
          },
        });
        console.log(`[ORDER_DELETE] Deleted ${orderItemsResult.count} order items`);

        // 4. 删除订单的物流状态（如果存在）
        console.log(`[ORDER_DELETE] Checking for odoo status of order: ${params.orderId}`);
        const odooStatusResult = await tx.odooOrderStatus.deleteMany({
          where: {
            orderId: params.orderId,
          },
        });
        console.log(`[ORDER_DELETE] Deleted ${odooStatusResult.count} odoo status records`);

        // 5. 删除订单
        console.log(`[ORDER_DELETE] Deleting order: ${params.orderId}`);
        await tx.order.delete({
          where: {
            id: params.orderId,
          },
        });
        console.log(`[ORDER_DELETE] Successfully deleted order: ${params.orderId}`);
      } catch (txError) {
        console.error(`[ORDER_DELETE] Transaction error:`, txError);
        throw txError; // Re-throw to trigger transaction rollback
      }
    });

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[ORDER_DELETE] Error:", error)
    return new NextResponse(`Internal error: ${error instanceof Error ? error.message : 'Unknown error'}`, { status: 500 })
  }
}