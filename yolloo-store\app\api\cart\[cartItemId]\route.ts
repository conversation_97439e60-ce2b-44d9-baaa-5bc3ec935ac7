import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


interface CartParams {
  cartItemId: string
}

// PATCH /api/cart/[cartItemId] - 更新购物车商品数量
export async function PATCH(
  req: Request,
  { params }: { params: CartParams }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await req.json()
    const { quantity } = body

    if (!quantity) {
      return new NextResponse("Quantity is required", { status: 400 })
    }

    // 检查购物车商品是否存在且属于当前用户
    const cartItem = await prisma.cartItem.findUnique({
      where: {
        id: params.cartItemId,
      },
      include: {
        product: true,
      },
    })

    if (!cartItem) {
      return new NextResponse("Cart item not found", { status: 404 })
    }

    if (cartItem.userId !== session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // 检查库存
    if (quantity > cartItem.product.stock) {
      return new NextResponse("Insufficient stock", { status: 400 })
    }

    // 更新数量
    const updatedCartItem = await prisma.cartItem.update({
      where: {
        id: params.cartItemId,
      },
      data: {
        quantity,
      },
    })

    return NextResponse.json(updatedCartItem)
  } catch (error) {
    console.error("[CART_PATCH]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

// DELETE /api/cart/[cartItemId] - 删除购物车商品
export async function DELETE(
  req: Request,
  { params }: { params: CartParams }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // 检查购物车商品是否存在且属于当前用户
    const cartItem = await prisma.cartItem.findUnique({
      where: {
        id: params.cartItemId,
      },
    })

    if (!cartItem) {
      return new NextResponse("Cart item not found", { status: 404 })
    }

    if (cartItem.userId !== session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // 删除购物车商品
    await prisma.cartItem.delete({
      where: {
        id: params.cartItemId,
      },
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[CART_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 