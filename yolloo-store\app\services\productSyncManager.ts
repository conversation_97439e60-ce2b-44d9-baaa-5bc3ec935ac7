import { OdooService } from './odooService';
import { Product, convertOdooToProduct } from '../types/product';
import { prisma } from '@/lib/prisma';
import {
    PRODUCT_TYPES,
    PRODUCT_STATUS,
    CATEGORY_DEFAULTS,
    PRODUCT_DEFAULTS
} from '../config/odoo';
import { formatProductName } from '@/lib/utils';
import { EventEmitter } from 'events';

// 辅助函数：标准化分隔符，将 ',' 和 '，' 转换为 ';'
function standardizeSeparators(value: string | null | undefined): string | null | undefined {
    if (!value) return value;
    return value.replace(/[,，]/g, ';');
}

// 同步选项接口
export interface SyncOptions {
    productTypes?: string[];  // 要同步的产品类型，默认为所有类型
    forceCategoryName?: string;  // 强制设置分类名称
    odooConfig?: any;  // 自定义Odoo配置
    batchSize?: number;  // 每批处理的产品数量
    skipEmptyVariantsAndZeroPrice?: boolean;  // 是否跳过无变体且价格为0的产品
    sourceType?: string;  // 产品来源类型，用于区分不同账号的产品，例如 'standard' 或 'qr'
    onlyUpdateOwnProducts?: boolean;  // 是否只更新自己账号的产品
}

// 同步统计信息接口
export interface SyncStats {
    totalProcessed: number;
    totalSkipped: number;
    totalUpdated: number;
    totalCreated: number;
    totalVariants: number;
    byType: Record<string, {
        processed: number;
        skipped: number;
        updated: number;
        created: number;
        variants: number;
    }>;
    startTime: Date;
    endTime?: Date;
    duration?: number;  // 毫秒
}

// 同步结果接口
export interface SyncResult {
    products: Product[];
    stats: SyncStats;
    errors: Error[];
}

// 同步事件类型
export enum SyncEventType {
    STARTED = 'sync:started',
    COMPLETED = 'sync:completed',
    FAILED = 'sync:failed',
    PROGRESS = 'sync:progress',
    PRODUCT_PROCESSED = 'sync:product:processed',
    PRODUCT_SKIPPED = 'sync:product:skipped',
    PRODUCT_ERROR = 'sync:product:error',
    TYPE_COMPLETED = 'sync:type:completed'
}

// 产品同步管理器
export class ProductSyncManager {
    private static instance: ProductSyncManager;
    private eventEmitter: EventEmitter;
    private activeSyncs: Map<string, boolean> = new Map();

    private constructor() {
        this.eventEmitter = new EventEmitter();
        // 增加最大监听器数量，避免内存泄漏警告
        this.eventEmitter.setMaxListeners(50);
    }

    // 单例模式获取实例
    public static getInstance(): ProductSyncManager {
        if (!ProductSyncManager.instance) {
            ProductSyncManager.instance = new ProductSyncManager();
        }
        return ProductSyncManager.instance;
    }

    // 订阅同步事件
    public on(event: SyncEventType, listener: (...args: any[]) => void): void {
        this.eventEmitter.on(event, listener);
    }

    // 取消订阅同步事件
    public off(event: SyncEventType, listener: (...args: any[]) => void): void {
        this.eventEmitter.off(event, listener);
    }

    // 同步产品
    public async syncProducts(options: SyncOptions = {}): Promise<SyncResult> {
        const syncId = `sync_${Date.now()}`;

        // 检查是否已有同步进行中
        if (this.activeSyncs.size > 0) {
            throw new Error('另一个同步进程正在运行中，请稍后再试');
        }

        this.activeSyncs.set(syncId, true);

        const {
            productTypes = PRODUCT_TYPES,
            forceCategoryName,
            odooConfig,
            skipEmptyVariantsAndZeroPrice = true,
            sourceType = 'standard',
            onlyUpdateOwnProducts = true
        } = options;

        // 创建Odoo服务实例
        const odooService = odooConfig ? new OdooService(odooConfig) : null;

        // 初始化统计信息
        const stats: SyncStats = {
            totalProcessed: 0,
            totalSkipped: 0,
            totalUpdated: 0,
            totalCreated: 0,
            totalVariants: 0,
            byType: {},
            startTime: new Date()
        };

        let allConvertedProducts: Product[] = [];
        const errors: Error[] = [];

        try {
            console.log(`[Sync:${syncId}] 开始同步产品，类型: ${productTypes.join(', ')}`);
            this.eventEmitter.emit(SyncEventType.STARTED, { syncId, options });

            // 第一阶段：收集所有产品数据，但不保存到数据库
            const productsToProcess: Array<{
                converted: Product;
                productType: string;
                category: any;
                baseProductData: any;
                existingProduct: any;
            }> = [];

            // 遍历所有产品类型
            for (const productType of productTypes) {
                // 初始化当前类型的统计信息
                stats.byType[productType] = {
                    processed: 0,
                    skipped: 0,
                    updated: 0,
                    created: 0,
                    variants: 0
                };

                try {
                    // 获取产品列表
                    const service = odooService || new OdooService({
                        address: process.env.ODOO_ADDRESS || '',
                        channelId: process.env.ODOO_CHANNEL_ID || '',
                        channelLanguage: process.env.ODOO_CHANNEL_LANGUAGE || 'en_US',
                        authSecret: process.env.ODOO_AUTH_SECRET || '',
                        signMethod: process.env.ODOO_SIGN_METHOD || 'md5',
                    });

                    const productsResponse = await service.pullProducts(productType);

                    // 验证响应数据结构
                    if (!productsResponse?.result?.data) {
                        console.error(`[Sync:${syncId}] 无效的响应结构，类型: ${productType}`);
                        continue;
                    }

                    const products = productsResponse.result.data;

                    // 处理每个产品
                    for (const product of products) {
                        try {
                            stats.totalProcessed++;
                            stats.byType[productType].processed++;

                            // 转换产品
                            const converted = convertOdooToProduct(product);

                            // 如果指定了强制分类名称，则覆盖
                            if (forceCategoryName) {
                                converted.category = forceCategoryName;
                            }

                            stats.totalVariants += converted.variants.length;
                            stats.byType[productType].variants += converted.variants.length;

                            // 跳过变体数量为0且价格为0的产品
                            if (skipEmptyVariantsAndZeroPrice && converted.variants.length === 0 && converted.price === 0) {
                                console.log(`[Sync:${syncId}] 跳过产品 ${converted.sku}，无变体且价格为0`);
                                stats.totalSkipped++;
                                stats.byType[productType].skipped++;
                                this.eventEmitter.emit(SyncEventType.PRODUCT_SKIPPED, {
                                    syncId,
                                    productType,
                                    sku: converted.sku,
                                    reason: 'no_variants_zero_price'
                                });
                                continue;
                            }

                            // 查找或创建分类
                            let category = await prisma.category.findFirst({
                                where: { name: converted.category }
                            });

                            if (!category) {
                                category = await prisma.category.create({
                                    data: {
                                        name: converted.category,
                                        description: CATEGORY_DEFAULTS.defaultDescription(converted.category)
                                    }
                                });
                            }

                            // 准备产品数据
                            const baseProductData = {
                                name: formatProductName(converted.name),
                                description: converted.description,
                                websiteDescription: converted.websiteDescription,
                                price: converted.price,
                                images: [], // 忽略Odoo的图片URL，使用空数组
                                stock: converted.stock,
                                status: converted.isActive ? PRODUCT_STATUS.ACTIVE : PRODUCT_STATUS.INACTIVE,
                                off_shelve: converted.off_shelve,
                                requiredUID: converted.requiredUID,
                                mcc: standardizeSeparators(converted.mcc),
                                dataSize: converted.dataSize,
                                planType: converted.planType,
                                country: standardizeSeparators(converted.country),
                                countryCode: standardizeSeparators(converted.countryCode),
                                odooLastSyncAt: new Date(),
                                specifications: {
                                    odooId: converted.metadata.odooId,
                                    odooProductCode: converted.metadata.odooProductCode,
                                    sourceType: sourceType, // 添加来源类型
                                },
                                category: {
                                    connect: {
                                        id: category.id
                                    }
                                }
                            };

                            // 查找现有产品
                            let existingProduct;

                            if (onlyUpdateOwnProducts) {
                                // 只查找和更新自己账号的产品
                                existingProduct = await prisma.product.findFirst({
                                    where: {
                                        sku: converted.sku,
                                        specifications: {
                                            path: ['sourceType'],
                                            equals: sourceType
                                        }
                                    }
                                });
                            } else {
                                // 查找任何匹配SKU的产品
                                existingProduct = await prisma.product.findUnique({
                                    where: { sku: converted.sku }
                                });
                            }

                            // 将产品添加到待处理列表，而不是立即保存
                            productsToProcess.push({
                                converted,
                                productType,
                                category,
                                baseProductData,
                                existingProduct
                            });

                            allConvertedProducts.push(converted);

                            // 发送产品收集事件
                            this.eventEmitter.emit(SyncEventType.PRODUCT_PROCESSED, {
                                syncId,
                                productType,
                                sku: converted.sku,
                                isNew: !existingProduct,
                                variantsCount: converted.variants.length
                            });

                        } catch (error) {
                            stats.totalSkipped++;
                            stats.byType[productType].skipped++;
                            errors.push(error as Error);

                            // 发送产品错误事件
                            this.eventEmitter.emit(SyncEventType.PRODUCT_ERROR, {
                                syncId,
                                productType,
                                sku: product.product_code,
                                error
                            });
                        }
                    }

                    // 每种类型同步完成后输出统计信息
                    const typeStats = stats.byType[productType];
                    console.log(`[Sync:${syncId}] 类型=${productType}: 处理=${typeStats.processed}, 创建=${typeStats.created}, 更新=${typeStats.updated}, 跳过=${typeStats.skipped}, 变体=${typeStats.variants}`);

                    // 发送类型完成事件
                    this.eventEmitter.emit(SyncEventType.TYPE_COMPLETED, {
                        syncId,
                        productType,
                        stats: typeStats
                    });

                } catch (error) {
                    console.error(`[Sync:${syncId}] 处理产品类型 ${productType} 时出错:`, error);
                    errors.push(error as Error);
                }
            }

            // 第二阶段：对收集的产品进行排序，然后按排序后的顺序保存到数据库
            console.log(`[Sync:${syncId}] 开始第二阶段：对 ${productsToProcess.length} 个产品进行排序并保存`);

            // 判断产品是否为多区域产品的辅助函数
            const isMultiRegionProduct = (product: Product) => {
                if (product.country) {
                    const countries = product.country.split(/[,;]/).map(c => c.trim()).filter(Boolean);
                    if (countries.length > 1) return true;
                }
                if (product.countryCode) {
                    const countryCodes = product.countryCode.split(/[,;]/).map(c => c.trim()).filter(Boolean);
                    if (countryCodes.length > 1) return true;
                }
                return false;
            };

            // 对产品进行排序：单一country的产品排在前面，然后按名称A-Z排序
            const sortedProducts = productsToProcess.sort((a, b) => {
                const aIsMultiRegion = isMultiRegionProduct(a.converted);
                const bIsMultiRegion = isMultiRegionProduct(b.converted);

                // 首先按区域类型排序：单一区域产品排在前面
                if (aIsMultiRegion !== bIsMultiRegion) {
                    return aIsMultiRegion ? 1 : -1; // 单一区域(false)排在前面
                }

                // 在同一区域类型内，按名称A-Z排序
                return a.converted.name.localeCompare(b.converted.name);
            });

            console.log(`[Sync:${syncId}] 产品排序完成，开始按顺序保存到数据库`);

            // 按排序后的顺序保存产品到数据库
            for (let i = 0; i < sortedProducts.length; i++) {
                const { converted, productType, baseProductData, existingProduct } = sortedProducts[i];

                try {
                    // 更新或创建产品
                    await prisma.product.upsert({
                        where: {
                            sku: converted.sku
                        },
                        update: {
                            ...baseProductData,
                            variants: {
                                deleteMany: {},
                                create: converted.variants.map(variant => ({
                                    price: Number(variant.price),
                                    currency: variant.currency || PRODUCT_DEFAULTS.currency,
                                    attributes: variant.attributes,
                                    variantCode: variant.variantCode,
                                    duration: variant.duration,
                                    durationType: variant.durationType
                                }))
                            },
                            parameters: {
                                deleteMany: {},
                                create: converted.parameters
                                    .filter(param => param.code !== 'mcc')  // 过滤掉MCC参数
                                    .map(param => ({
                                        code: param.code,
                                        name: param.name,
                                        value: param.value
                                    }))
                            }
                        },
                        create: {
                            ...baseProductData,
                            sku: converted.sku,
                            variants: {
                                create: converted.variants.map(variant => ({
                                    price: Number(variant.price),
                                    currency: variant.currency || PRODUCT_DEFAULTS.currency,
                                    attributes: variant.attributes,
                                    variantCode: variant.variantCode,
                                    duration: variant.duration,
                                    durationType: variant.durationType
                                }))
                            },
                            parameters: {
                                create: converted.parameters
                                    .filter(param => param.code !== 'mcc')  // 过滤掉MCC参数
                                    .map(param => ({
                                        code: param.code,
                                        name: param.name,
                                        value: param.value
                                    }))
                            }
                        },
                        include: {
                            variants: true,
                            parameters: true
                        }
                    });

                    // 更新统计信息
                    if (existingProduct) {
                        stats.totalUpdated++;
                        stats.byType[productType].updated++;
                    } else {
                        stats.totalCreated++;
                        stats.byType[productType].created++;
                    }

                    // 每保存100个产品输出一次进度
                    if ((i + 1) % 100 === 0 || i === sortedProducts.length - 1) {
                        console.log(`[Sync:${syncId}] 已保存 ${i + 1}/${sortedProducts.length} 个产品`);
                    }

                } catch (error) {
                    console.error(`[Sync:${syncId}] 保存产品 ${converted.sku} 时出错:`, error);
                    errors.push(error as Error);
                }
            }

            console.log(`[Sync:${syncId}] 产品保存完成，共保存 ${sortedProducts.length} 个产品`);

            // 验证同步结果
            const dbProducts = await prisma.product.findMany({
                include: {
                    variants: true,
                    parameters: true
                }
            });

            // 计算同步时间
            stats.endTime = new Date();
            stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

            // 输出总结统计信息
            console.log(`[Sync:${syncId}] 摘要: 处理=${stats.totalProcessed}, 创建=${stats.totalCreated}, 更新=${stats.totalUpdated}, 跳过=${stats.totalSkipped}, 变体=${stats.totalVariants}, 耗时=${stats.duration}ms`);
            console.log(`[Sync:${syncId}] 数据库中的产品总数: ${dbProducts.length}`);

            // 发送完成事件
            this.eventEmitter.emit(SyncEventType.COMPLETED, {
                syncId,
                stats,
                productsCount: allConvertedProducts.length,
                errorsCount: errors.length
            });

            return {
                products: allConvertedProducts,
                stats,
                errors
            };

        } catch (error) {
            console.error(`[Sync:${syncId}] 同步产品时出错:`, error);

            // 计算同步时间
            stats.endTime = new Date();
            stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

            // 发送失败事件
            this.eventEmitter.emit(SyncEventType.FAILED, {
                syncId,
                error,
                stats
            });

            throw error;
        } finally {
            // 清除活动同步标记
            this.activeSyncs.delete(syncId);
        }
    }
}
