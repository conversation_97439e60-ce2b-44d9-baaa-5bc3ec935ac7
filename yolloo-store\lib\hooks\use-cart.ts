import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface CartItem {
  id: string
  productId: string
  name: string
  price: number
  quantity: number
  image?: string
  stock: number
  variant?: {
    id: string
    price: number
    duration?: number | null
    durationType?: string | null
  }
  uid?: string
}

interface CartStore {
  items: CartItem[]
  addItem: (item: CartItem) => void
  removeItem: (productId: string, variantId?: string, uid?: string) => void
  updateQuantity: (productId: string, quantity: number, variantId?: string, uid?: string) => void
  clearCart: () => void
  initializeCart: (items: CartItem[]) => void
}

export const useCart = create<CartStore>()(
  persist(
    (set) => ({
      items: [],
      addItem: (item) => {
        set((state) => {
          const existingItem = state.items.find(
            (i) => 
              i.productId === item.productId && 
              i.variant?.id === item.variant?.id &&
              i.uid === item.uid
          )

          if (existingItem) {
            return {
              items: state.items.map((i) =>
                i.id === existingItem.id
                  ? { ...i, quantity: i.quantity + 1 }
                  : i
              ),
            }
          }

          return {
            items: [...state.items, item],
          }
        })
      },
      removeItem: (productId, variantId, uid) => {
        set((state) => {
          console.log('Removing item:', { productId, variantId, uid })
          console.log('Current items:', state.items)
          const newItems = state.items.filter((item) => {
            // 如果产品ID不匹配，保留该商品
            if (item.productId !== productId) {
              return true
            }

            // 处理变体和UID的组合情况
            const variantMatches = variantId 
              ? item.variant?.id === variantId  // 有variantId时必须完全匹配
              : !item.variant                   // 没有variantId时商品也不能有variant
            
            const uidMatches = uid
              ? item.uid === uid                // 有uid时必须完全匹配
              : !item.uid                       // 没有uid时商品也不能有uid

            // 只有当变体和UID都匹配时才删除商品
            return !(variantMatches && uidMatches)
          })
          console.log('New items:', newItems)
          return { items: newItems }
        })
      },
      updateQuantity: (productId, quantity, variantId, uid) => {
        set((state) => {
          console.log('Updating quantity:', { productId, quantity, variantId, uid })
          console.log('Current items:', state.items)
          const newItems = state.items.map((item) => {
            // 如果产品ID不匹配，保持原样
            if (item.productId !== productId) {
              return item
            }

            // 处理变体和UID的组合情况
            const variantMatches = variantId 
              ? item.variant?.id === variantId  // 有variantId时必须完全匹配
              : !item.variant                   // 没有variantId时商品也不能有variant
            
            const uidMatches = uid
              ? item.uid === uid                // 有uid时必须完全匹配
              : !item.uid                       // 没有uid时商品也不能有uid

            // 只有当变体和UID都匹配时才更新数量
            return (variantMatches && uidMatches) ? { ...item, quantity } : item
          })
          console.log('New items:', newItems)
          return { items: newItems }
        })
      },
      clearCart: () => {
        set({ items: [] })
      },
      initializeCart: (items) => {
        set({ items })
      },
    }),
    {
      name: 'cart-storage',
    }
  )
) 