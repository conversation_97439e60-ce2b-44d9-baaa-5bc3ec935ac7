'use client'

import { useEffect, useRef } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'

const userReviews = [
  {
    id: 1,
    name: "<PERSON>",
    rating: 5,
    review: "Amazing service! The eSIM worked perfectly during my trip to Europe. No more worrying about roaming charges or finding local SIM cards.",
    platform: "android",
    country: "United States"
  },
  {
    id: 2,
    name: "<PERSON>",
    rating: 5,
    review: "Best travel companion ever! Used it across 5 countries in Asia and the connection was always reliable. Highly recommend!",
    platform: "ios",
    country: "Canada"
  },
  {
    id: 3,
    name: "<PERSON>",
    rating: 4,
    review: "Great value for money. The setup was easy and customer support was very helpful when I had questions.",
    platform: "android",
    country: "Australia"
  },
  {
    id: 4,
    name: "<PERSON>",
    rating: 5,
    review: "Seamless experience from purchase to activation. The data speeds were excellent throughout my business trip.",
    platform: "ios",
    country: "Spain"
  },
  {
    id: 5,
    name: "Lisa Park",
    rating: 5,
    review: "Love how easy it is to switch between different country plans. Perfect for frequent travelers like me!",
    platform: "android",
    country: "South Korea"
  },
  {
    id: 6,
    name: "<PERSON>",
    rating: 4,
    review: "Reliable connection and fair pricing. The app interface is user-friendly and makes managing data plans simple.",
    platform: "ios",
    country: "United Kingdom"
  }
]

export default function NewUserReact() {
  const scrollRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<number>()
  const isHoveredRef = useRef(false)

  useEffect(() => {
    const scrollContainer = scrollRef.current
    if (!scrollContainer) return

    let scrollPosition = 0
    const scrollSpeed = 0.5 // pixels per frame

    const animate = () => {
      if (!isHoveredRef.current && scrollContainer) {
        scrollPosition += scrollSpeed

        // Get the width of one set of cards (half of total width)
        const totalWidth = scrollContainer.scrollWidth
        const oneSetWidth = totalWidth / 2

        // Reset position when we've scrolled one full set
        if (scrollPosition >= oneSetWidth) {
          scrollPosition = 0
        }

        scrollContainer.style.transform = `translateX(-${scrollPosition}px)`
      }

      animationRef.current = requestAnimationFrame(animate)
    }

    animationRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [])

  const handleMouseEnter = () => {
    isHoveredRef.current = true
  }

  const handleMouseLeave = () => {
    isHoveredRef.current = false
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span
        key={index}
        className={`text-lg ${
          index < rating ? 'text-yellow-400' : 'text-gray-300'
        }`}
      >
        ★
      </span>
    ))
  }

  const averageRating = userReviews.reduce((sum, review) => sum + review.rating, 0) / userReviews.length

  return (
    <section id="user-react" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4" style={{
            background: 'linear-gradient(90deg, #B82E4E 0%, #F799A6 50%, #B82E4E 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            Hear from Users Around the World
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Start your journey with confidence—just like they did
          </p>

          {/* Character Image */}
          <div className="flex justify-center mb-8">
            <div className="w-32 h-32 relative">
              <Image
                src="/Frame 196.svg"
                alt="User character"
                fill
                className="object-contain"
              />
            </div>
          </div>

          {/* Rating Summary */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <div className="flex items-center gap-2">
              <span className="text-3xl font-bold text-[#B82E4E]">
                4.9/5
              </span>
              <span className="text-2xl text-yellow-400">★</span>
            </div>
            <div className="text-gray-600">
              Real reviews from travelers in over 190 countries
            </div>
          </div>
        </motion.div>

        {/* Reviews Infinite Scroll */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="relative overflow-hidden"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {/* Gradient overlays */}
          <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-gray-50 to-transparent z-10 pointer-events-none"></div>
          <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-gray-50 to-transparent z-10 pointer-events-none"></div>

          {/* Scrolling container */}
          <div ref={scrollRef} className="flex gap-4">
            {/* Double set for seamless infinite scroll */}
            {[...Array(2)].map((_, setIndex) => (
              userReviews.map((review) => (
                <div
                  key={`set-${setIndex}-${review.id}`}
                  className="bg-white rounded-xl p-4 shadow-md border border-gray-100 hover:shadow-lg transition-shadow duration-300 flex-shrink-0"
                  style={{ width: '280px' }}
                >
                  {/* User Info */}
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold text-gray-600">
                        {review.name.charAt(0)}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-900 text-sm truncate">{review.name}</h4>
                      <div className="flex items-center gap-1">
                        {renderStars(review.rating)}
                      </div>
                    </div>
                  </div>

                  {/* Review Text */}
                  <p className="text-gray-600 leading-relaxed mb-3 text-xs line-clamp-3">
                    {review.review}
                  </p>

                  {/* Platform and Country */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                        review.platform === 'android'
                          ? 'bg-green-100 text-green-700'
                          : 'bg-gray-100 text-gray-700'
                      }`}>
                        <div className="flex items-center gap-1">
                          <Image
                            src={review.platform === 'android' ? '/android .svg' : '/apple.svg'}
                            alt={review.platform}
                            width={10}
                            height={10}
                            className="object-contain"
                          />
                          <span className="text-xs">{review.platform === 'android' ? 'Android' : 'iOS'}</span>
                        </div>
                      </div>
                    </div>
                    <span className="text-xs text-gray-500 truncate max-w-[80px]">{review.country}</span>
                  </div>
                </div>
              ))
            ))}
          </div>
        </motion.div>
      </div>

      {/* CSS for text truncation */}
      <style jsx>{`
        .line-clamp-3 {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </section>
  )
}
