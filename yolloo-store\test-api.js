const fetch = require('node-fetch');

async function testAPI() {
    try {
        console.log('Testing yolloo-smart API...');
        const response = await fetch('http://localhost:8000/api/yolloo-smart/products?page=1&limit=12&regionType=all&country=&sortBy=default&search=');
        
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers.raw());
        
        const text = await response.text();
        console.log('Response body (first 500 chars):', text.substring(0, 500));
        
        if (response.headers.get('content-type')?.includes('application/json')) {
            const data = JSON.parse(text);
            console.log('Pagination:', data.pagination);
        } else {
            console.log('Response is not JSON');
        }
    } catch (error) {
        console.error('Error:', error);
    }
}

testAPI();
