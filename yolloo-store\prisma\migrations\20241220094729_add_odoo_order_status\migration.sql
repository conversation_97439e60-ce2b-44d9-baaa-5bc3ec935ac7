-- CreateTable
CREATE TABLE "OdooOrderStatus" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "description" TEXT,
    "productName" TEXT,
    "isDigital" BOOLEAN NOT NULL DEFAULT false,
    "deliveredQty" INTEGER NOT NULL DEFAULT 0,
    "trackingNumber" TEXT,
    "planState" TEXT,
    "lastCheckedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OdooOrderStatus_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "OdooOrderStatus_orderId_key" ON "OdooOrderStatus"("orderId");

-- AddForeignKey
ALTER TABLE "OdooOrderStatus" ADD CONSTRAINT "OdooOrderStatus_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;
