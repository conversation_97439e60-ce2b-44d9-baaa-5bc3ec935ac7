-- 首先备份现有的 DeletedProductInfo 表中的数据
-- 创建一个临时表来存储必要的数据
CREATE TABLE "TempDeletedProductInfo" (
    "id" TEXT NOT NULL,
    "sku" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "images" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "TempDeletedProductInfo_pkey" PRIMARY KEY ("id")
);

-- 将现有数据复制到临时表中
INSERT INTO "TempDeletedProductInfo" ("id", "sku", "name", "price", "images", "createdAt")
SELECT "id", "sku", "name", "price", "images", "createdAt" FROM "DeletedProductInfo";

-- 删除 OrderItem 表中的外键约束
ALTER TABLE "OrderItem" DROP CONSTRAINT IF EXISTS "OrderItem_deletedProductId_fkey";

-- 删除原始表
DROP TABLE "DeletedProductInfo";

-- 创建新的简化表
CREATE TABLE "DeletedProductInfo" (
    "id" TEXT NOT NULL,
    "sku" TEXT NOT NULL UNIQUE,
    "name" TEXT NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "images" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "DeletedProductInfo_pkey" PRIMARY KEY ("id")
);

-- 将数据从临时表复制回新表
INSERT INTO "DeletedProductInfo" ("id", "sku", "name", "price", "images", "createdAt")
SELECT "id", "sku", "name", "price", "images", "createdAt" FROM "TempDeletedProductInfo";

-- 删除临时表
DROP TABLE "TempDeletedProductInfo";

-- 重新添加外键约束
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_deletedProductId_fkey" 
FOREIGN KEY ("deletedProductId") REFERENCES "DeletedProductInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;
