'use client'

import { useEffect } from 'react'

interface ErrorProps {
  error: Error
  reset: () => void
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Optionally log the error to an error reporting service
    console.error(error)
  }, [error])

  return (
    <div className="flex min-h-[400px] flex-col items-center justify-center">
      <h2 className="text-2xl font-bold text-red-600">Something went wrong!</h2>
      <p className="mt-4 text-gray-600">We apologize for the inconvenience.</p>
      <button
        onClick={reset}
        className="mt-6 rounded-md bg-black px-4 py-2 text-sm text-white transition-colors hover:bg-gray-800"
      >
        Try again
      </button>
    </div>
  )
} 