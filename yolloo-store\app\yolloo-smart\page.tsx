'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SearchIcon, ChevronDown, HelpCircle, ArrowUp, X } from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

// Tooltip Component
const CustomTooltip = ({ content, title }: { content: string; title: string }) => {
  const [isVisible, setIsVisible] = useState(false)

  return (
    <div className="relative inline-block">
      <div
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        className="cursor-help"
      >
        <HelpCircle className="w-3 h-3 text-gray-400 hover:text-gray-600 transition-colors" />
      </div>
      {isVisible && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-3">
          <div className="text-sm font-semibold text-gray-900 mb-1">{title}</div>
          <div className="text-xs text-gray-600 leading-relaxed">{content}</div>
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"></div>
        </div>
      )}
    </div>
  )
}

// 产品区域接口
interface ProductRegion {
  name: string;
  flagCode: string;
  dataSize: string;
  planType: string;
  price: string;
  originalProduct: {
    id: string;
    name: string;
    description: string;
    websiteDescription: string;
    price: number;
    minPrice: number;
  };
}

// 单排滚动组件
const ScrollingRow = ({ regions, direction = 'left', speed = 1 }: {
  regions: ProductRegion[],
  direction?: 'left' | 'right',
  speed?: number
}) => {
  const scrollRef = useRef<HTMLDivElement>(null)
  const [isPaused, setIsPaused] = useState(false)
  const animationRef = useRef<number>()
  const scrollPositionRef = useRef(0)
  const isInitializedRef = useRef(false)

  // 创建足够多的数据用于无缝滚动
  const extendedRegions = [...regions, ...regions, ...regions, ...regions]

  useEffect(() => {
    const scrollContainer = scrollRef.current
    if (!scrollContainer) return

    const cardWidth = 272 // w-64 + gap-4 = 256 + 16 = 272
    const totalWidth = cardWidth * regions.length
    const scrollSpeed = direction === 'left' ? -speed * 0.3 : speed * 0.3

    // 初始化位置 - 从中间开始，避免边界问题
    if (!isInitializedRef.current) {
      scrollPositionRef.current = -totalWidth // 从第二组数据开始显示
      scrollContainer.style.transform = `translateX(${scrollPositionRef.current}px)`
      isInitializedRef.current = true
    }

    const animate = () => {
      if (!isPaused && scrollContainer) {
        scrollPositionRef.current += scrollSpeed

        // 无缝循环逻辑 - 在边界处重置到安全位置
        if (scrollPositionRef.current <= -totalWidth * 2) {
          // 滚动到最左边时，重置到中间位置
          scrollPositionRef.current = -totalWidth
        } else if (scrollPositionRef.current >= 0) {
          // 滚动到最右边时，重置到中间位置
          scrollPositionRef.current = -totalWidth
        }

        scrollContainer.style.transform = `translateX(${scrollPositionRef.current}px)`
      }
      animationRef.current = requestAnimationFrame(animate)
    }

    animationRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isPaused, direction, speed, regions.length])

  return (
    <div
      className="overflow-hidden"
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
    >
      <div
        ref={scrollRef}
        className="flex gap-4"
        style={{ width: 'max-content' }}
      >
        {extendedRegions.map((region, index) => (
          <Link
            key={`scroll-${direction}-${index}`}
            href={`/products/${region.originalProduct.id}`}
            className="bg-white rounded-xl p-4 shadow-md hover:shadow-lg transition-all duration-300 border border-pink-100 hover:border-[#DF4362]/30 group flex-shrink-0 w-64"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-[#DF4362] to-[#B82E4E] rounded-full flex items-center justify-center flex-shrink-0">
                <img
                  src={`https://flagcdn.com/w20/${region.flagCode}.png`}
                  alt={`${region.name} flag`}
                  className="w-4 h-3 object-cover rounded-sm"
                  onError={(e) => {
                    // 如果国旗加载失败，显示默认图标
                    e.currentTarget.style.display = 'none';
                    const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                    if (nextElement) {
                      nextElement.classList.remove('hidden');
                    }
                  }}
                />
                <div className="w-4 h-3 bg-[#DF4362] rounded-sm hidden flex items-center justify-center">
                  <span className="text-white text-xs font-bold">{region.flagCode.toUpperCase()}</span>
                </div>
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-gray-900 text-sm truncate">{region.name}</h3>
                <p className="text-[#DF4362] font-medium text-xs">{region.price}</p>
                <p className="text-gray-500 text-xs">{region.dataSize} • {region.planType}</p>
              </div>
              <div className="text-gray-400 group-hover:text-[#DF4362] transition-colors flex-shrink-0">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  )
}

// 双排滚动组件
const AutoScrollingCountries = () => {
  const [products, setProducts] = useState<ProductRegion[]>([])
  const [loading, setLoading] = useState(true)

  // 获取真实产品数据
  useEffect(() => {
    async function fetchProducts() {
      try {
        const response = await fetch('/api/yolloo-smart/products?limit=50')
        const data = await response.json()
        if (data.products) {
          // 转换数据格式以匹配ProductRegion接口
          const convertedProducts = data.products.map((product: any) => {
            const formatDataSize = (size: number | null): string => {
              if (!size) return "1GB";
              if (size >= 1024) {
                const gbValue = size / 1024;
                return Number.isInteger(gbValue) ? `${gbValue}GB` : `${gbValue.toFixed(1)}GB`;
              } else {
                return `${size}MB`;
              }
            };

            const primaryCountry = product.country ? product.country.split(/[,;]/)[0].trim() : "Global";
            const countryCode = product.countryCode ? product.countryCode.split(/[,;]/)[0].trim().toLowerCase() : "global";

            return {
              name: primaryCountry,
              flagCode: countryCode,
              dataSize: formatDataSize(product.dataSize),
              planType: product.planType || "Daily",
              price: `From $${product.price.toFixed(1)}`,
              originalProduct: {
                id: product.id,
                name: product.name,
                description: product.description,
                websiteDescription: product.websiteDescription,
                price: product.price,
                minPrice: product.price
              }
            };
          });
          setProducts(convertedProducts)
        }
      } catch (error) {
        console.error('Error fetching products:', error)
      } finally {
        setLoading(false)
      }
    }
    fetchProducts()
  }, [])

  if (loading) {
    return (
      <div className="flex justify-center items-center py-16">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#DF4362]"></div>
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-16 text-gray-500">
        No products available
      </div>
    )
  }

  // 分割数据为两排
  const firstRowRegions = products.slice(0, Math.ceil(products.length / 2))
  const secondRowRegions = products.slice(Math.ceil(products.length / 2))

  return (
    <div className="space-y-4">
      {/* 第一排 - 向左滚动 */}
      <ScrollingRow regions={firstRowRegions} direction="left" speed={1} />

      {/* 第二排 - 向右滚动 */}
      <ScrollingRow regions={secondRowRegions} direction="right" speed={1} />
    </div>
  )
}

// 回到顶部按钮组件
const BackToTopButton = () => {
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  return (
    <button
      onClick={scrollToTop}
      className="fixed bottom-8 right-24 w-12 h-12 bg-[#DF4362] hover:bg-[#C73A56] text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-50 group"
      aria-label="回到顶部"
    >
      <ArrowUp className="w-5 h-5 group-hover:scale-110 transition-transform duration-200" />
    </button>
  )
}

export default function YollooSmartPage() {
  const [products, setProducts] = useState<any[]>([])
  const [pagination, setPagination] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [countries, setCountries] = useState<any[]>([])
  
  // 筛选状态 - 与products页面保持一致
  const [currentPage, setCurrentPage] = useState(1)
  const [regionType, setRegionType] = useState("all")
  const [countryFilter, setCountryFilter] = useState("")
  const [sortBy, setSortBy] = useState("default")
  const [searchQuery, setSearchQuery] = useState("")
  const [searchInput, setSearchInput] = useState("")
  
  const itemsPerPage = 12

  // 当筛选条件改变时重置分页
  useEffect(() => {
    setCurrentPage(1)
  }, [regionType, countryFilter, sortBy, searchQuery])

  // 保存产品列表区域的滚动位置
  const productSectionRef = useRef<HTMLElement>(null)

  // 处理分页点击，滚动到产品区域
  const handlePageChange = (e: React.MouseEvent, page: number) => {
    e.preventDefault()
    setCurrentPage(page)
    // 延迟滚动，确保页面更新后再滚动
    setTimeout(() => {
      if (productSectionRef.current) {
        productSectionRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }
    }, 100)
  }

  // 获取产品数据 - 与products页面保持一致
  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
      })

      if (searchQuery) params.append('search', searchQuery)
      if (regionType !== 'all') params.append('regionType', regionType)
      if (countryFilter) params.append('country', countryFilter)
      if (sortBy !== 'default') params.append('sort', sortBy)

      const response = await fetch(`/api/yolloo-smart/products?${params.toString()}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      setProducts(data.products)
      setPagination(data.pagination)

    } catch (error) {
      console.error('Error fetching products:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch products')
      setProducts([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [currentPage, searchQuery, regionType, countryFilter, sortBy, itemsPerPage])

  // 获取国家列表
  const fetchCountries = useCallback(async () => {
    try {
      const response = await fetch('/api/products/countries')
      if (response.ok) {
        const data = await response.json()
        setCountries(data.countries)
      }
    } catch (error) {
      console.error('Error fetching countries:', error)
    }
  }, [])

  // 当参数变化时获取数据
  useEffect(() => {
    fetchProducts()
  }, [fetchProducts])

  // 初始化时获取国家列表
  useEffect(() => {
    fetchCountries()
  }, [fetchCountries])

  // 获取国家产品数量
  const getCountryCount = (country: string) => {
    const countryData = countries.find(c => c.name === country)
    return countryData ? countryData.count : 0
  }

  // 获取唯一国家列表
  const getUniqueCountries = () => {
    return countries.map(c => c.name)
  }

  return (
    <div className="min-h-screen relative">
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden min-h-[600px]" style={{
        backgroundImage: "url('/yolloo smart hero.svg')",
        backgroundSize: 'auto 100%',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}>
        <div className="container mx-auto relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="text-white">
              {/* Badge */}
              <div className="inline-flex items-center px-6 py-3 rounded-full font-medium mb-8 backdrop-blur-sm bg-[#FFF0F0] text-[#DF4362] text-2xl whitespace-nowrap">
                Stay Connected Anywhere with eSIM
              </div>

              {/* Description */}
              <div className="space-y-2">
                <p className="font-semibold bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] bg-clip-text text-transparent text-sm lg:text-base whitespace-nowrap">
                  Instant activation, flexible plans, and global coverage — starting at just $9.9.
                </p>
                <p className="text-white/90 text-sm lg:text-base whitespace-nowrap">
                  Choose from 100+ destinations with 5G-ready data and hotspot sharing.
                </p>
              </div>
            </div>

            {/* Right Content - 3D eSIM Card */}
            <div className="relative flex justify-center lg:justify-end">
              <img
                src="/image 6.png"
                alt="eSIM Card"
                className="w-80 h-auto object-contain drop-shadow-2xl"
              />
            </div>
          </div>
        </div>

        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-20 right-20 w-24 h-24 border border-white/20 rounded-full"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 border border-white/20 rounded-full"></div>
        </div>
      </section>

      {/* Search Section */}
      <section className="relative -mt-8 pb-8 bg-gradient-to-b from-transparent to-[#FFF0F0]">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <div className="absolute left-6 top-1/2 transform -translate-y-1/2 text-[#DF4362]">
                <SearchIcon className="w-5 h-5" />
              </div>
              <Input
                placeholder="Where do you want to go"
                className="pl-14 pr-20 py-4 rounded-full border-0 bg-white text-gray-900 placeholder-gray-400 text-base shadow-xl focus:ring-0 focus:outline-none h-14"
              />
              <Button
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-[#DF4362] hover:bg-[#C73A56] text-white rounded-full w-10 h-10 p-0 flex items-center justify-center shadow-md"
              >
                <SearchIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Countries Section */}
      <section className="py-16 relative overflow-hidden bg-[#FFF0F0]">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] bg-clip-text text-transparent">Popular Countries</h2>

          {/* Auto Scrolling Countries */}
          <div className="relative">
            {/* Left gradient overlay */}
            <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-[#FFF0F0] to-transparent z-10 pointer-events-none"></div>

            {/* Right gradient overlay */}
            <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-[#FFF0F0] to-transparent z-10 pointer-events-none"></div>

            <AutoScrollingCountries />
          </div>
        </div>
      </section>

      {/* All Destination Countries/Regions Section */}
      <section ref={productSectionRef} className="py-16 relative min-h-[600px] bg-gray-50 bg-[url('/swag.svg')] bg-contain bg-center bg-no-repeat">
        {/* Overlay to maintain content readability */}
        <div className="absolute inset-0 bg-gray-50/90"></div>
        <div className="container mx-auto px-4 relative z-10">
          <h2 className="text-3xl font-bold text-center mb-4 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] bg-clip-text text-transparent">All destination countries/regions</h2>
          <p className="text-center text-gray-600 mb-12 max-w-3xl mx-auto">
            The best data plan for every destination — seamless and secure connectivity in over 200 countries and regions.
          </p>

          {/* Filters - 与products页面保持一致 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <Select value={regionType} onValueChange={setRegionType}>
              <SelectTrigger>
                <SelectValue placeholder="Region Type" />
              </SelectTrigger>
              <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
                <SelectItem value="all">All Region Types</SelectItem>
                <SelectItem value="single">Local</SelectItem>
                <SelectItem value="multi">Regional</SelectItem>
              </SelectContent>
            </Select>

            <Select value={countryFilter || "all"} onValueChange={value => setCountryFilter(value === "all" ? "" : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Country" />
              </SelectTrigger>
              <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
                <SelectItem value="all">All Destinations</SelectItem>
                {getUniqueCountries().map(country => (
                  <SelectItem key={country} value={country}>
                    {country} ({getCountryCount(country)})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort By">
                  {sortBy === "default" ? "Sort By" :
                    sortBy === "price-asc" ? "Price: Low to High" :
                    sortBy === "price-desc" ? "Price: High to Low" :
                    sortBy === "name-asc" ? "Name: A to Z" :
                    "Name: Z to A"
                  }
                </SelectValue>
              </SelectTrigger>
              <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
                <SelectItem value="default">Sort By</SelectItem>
                <SelectItem value="price-asc">Price: Low to High</SelectItem>
                <SelectItem value="price-desc">Price: High to Low</SelectItem>
                <SelectItem value="name-asc">Name: A to Z</SelectItem>
                <SelectItem value="name-desc">Name: Z to A</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => {
                setRegionType("all")
                setCountryFilter("")
                setSortBy("default")
                setCurrentPage(1)
                setSearchInput("")
                setSearchQuery("")
              }}
            >
              Reset Filters
            </Button>
          </div>

          {/* Products Grid */}
          <div className="max-w-6xl mx-auto">
            {loading ? (
              <div className="flex items-center justify-center min-h-[60vh]">
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#DF4362]"></div>
                  <p className="mt-4 text-gray-600">Loading products...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
                <div className="text-red-500 mb-4">
                  <HelpCircle className="h-12 w-12" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Error Loading Products</h3>
                <p className="text-gray-600 mb-4">{error}</p>
                <Button onClick={() => fetchProducts()}>
                  Try Again
                </Button>
              </div>
            ) : products.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-12 text-center bg-white shadow-md rounded-lg">
                <HelpCircle className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Products Found</h3>
                <p className="text-gray-600 mb-4">Try adjusting your filters to find more products.</p>
                <Button
                  onClick={() => {
                    setRegionType("all")
                    setCountryFilter("")
                    setSortBy("default")
                    setCurrentPage(1)
                    setSearchInput("")
                    setSearchQuery("")
                  }}
                >
                  Clear Filters
                </Button>
              </div>
            ) : (
              <div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                  {products.map((product) => {
                    // 格式化数据大小
                    const formatDataSize = (size: number | null): string => {
                      if (!size) return "1GB";
                      if (size >= 1024) {
                        const gbValue = size / 1024;
                        return Number.isInteger(gbValue) ? `${gbValue}GB` : `${gbValue.toFixed(1)}GB`;
                      } else {
                        return `${size}MB`;
                      }
                    };

                    // 获取主要国家（第一个国家）
                    const primaryCountry = product.country ? product.country.split(/[,;]/)[0].trim() : "Global";

                    return (
                      <div key={product.id} className="bg-white rounded-xl border border-gray-200 p-4 hover:shadow-lg transition-all duration-200 hover:border-gray-300 flex flex-col h-full">
                        {/* Country Header */}
                        <div className="flex items-center mb-4">
                          <div className="w-3 h-3 bg-[#DF4362] rounded-full mr-3 flex-shrink-0"></div>
                          <span className="text-base font-semibold text-gray-900">{primaryCountry}</span>
                        </div>

                        {/* Data Plan */}
                        <div className="flex-1 mb-4">
                          <div className="flex items-center gap-2 mb-3">
                            <span className="text-2xl font-bold text-gray-900">{formatDataSize(product.dataSize)}</span>
                            <span className="text-gray-500">/</span>
                            <div className="flex items-center gap-1">
                              <span className="text-gray-500 text-sm">Natural Day</span>
                              <CustomTooltip
                                title="Natural Day"
                                content="Refers to a calendar day, from 00:00 to 23:59, typically based on a specific time zone (UTC+8)."
                              />
                            </div>
                          </div>

                          {/* Price */}
                          <div className="mb-3">
                            <span className="text-lg font-semibold text-[#DF4362]">From ${product.price.toFixed(1)}</span>
                          </div>

                          {/* Tags */}
                          <div className="flex gap-2 mb-4">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-pink-100 text-pink-700 border border-pink-200">
                              5G
                            </span>
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200">
                              {product.country && product.country.split(/[,;]/).length > 1 ? 'Regional' : 'Local'}
                            </span>
                          </div>
                        </div>

                        {/* Action Button */}
                        <Link href={`/products/${product.id}`} className="mt-auto">
                          <Button
                            className="w-full bg-[#DF4362] hover:bg-[#C73A56] text-white font-semibold py-3 rounded-lg transition-all duration-200"
                          >
                            View Details
                          </Button>
                        </Link>
                      </div>
                    );
                  })}
                </div>

                {/* Pagination */}
                {pagination && pagination.totalPages > 1 && (
                  <div className="w-full flex justify-center items-center my-8">
                    <Pagination className="justify-center">
                      <PaginationContent className="justify-center">
                        <PaginationItem>
                          <PaginationPrevious
                            href="#"
                            onClick={(e) => handlePageChange(e, Math.max(1, currentPage - 1))}
                            aria-disabled={!pagination?.hasPrev}
                          />
                        </PaginationItem>

                        {/* First page */}
                        {pagination.totalPages > 5 && currentPage > 3 && (
                          <PaginationItem>
                            <PaginationLink
                              href="#"
                              onClick={(e) => handlePageChange(e, 1)}
                            >
                              1
                            </PaginationLink>
                          </PaginationItem>
                        )}

                        {/* Ellipsis at the beginning */}
                        {pagination.totalPages > 5 && currentPage > 3 && (
                          <PaginationItem>
                            <PaginationEllipsis />
                          </PaginationItem>
                        )}

                        {/* Page numbers */}
                        {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                          .filter(page => {
                            if (pagination.totalPages <= 5) {
                              // Show all pages if 5 or fewer
                              return true;
                            } else {
                              // Show pages around current page
                              if (currentPage <= 3) {
                                // Near the beginning
                                return page <= 5;
                              } else if (currentPage >= pagination.totalPages - 2) {
                                // Near the end
                                return page > pagination.totalPages - 5;
                              } else {
                                // In the middle
                                return Math.abs(page - currentPage) <= 1;
                              }
                            }
                          })
                          .map((page) => (
                            <PaginationItem key={page}>
                              <PaginationLink
                                href="#"
                                onClick={(e) => handlePageChange(e, page)}
                                isActive={currentPage === page}
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          ))}

                        {/* Ellipsis at the end */}
                        {pagination.totalPages > 5 && currentPage < pagination.totalPages - 2 && (
                          <PaginationItem>
                            <PaginationEllipsis />
                          </PaginationItem>
                        )}

                        {/* Last page */}
                        {pagination.totalPages > 5 && currentPage < pagination.totalPages - 2 && (
                          <PaginationItem>
                            <PaginationLink
                              href="#"
                              onClick={(e) => handlePageChange(e, pagination.totalPages)}
                            >
                              {pagination.totalPages}
                            </PaginationLink>
                          </PaginationItem>
                        )}

                        <PaginationItem>
                          <PaginationNext
                            href="#"
                            onClick={(e) => handlePageChange(e, Math.min(pagination.totalPages, currentPage + 1))}
                            aria-disabled={!pagination?.hasNext}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* 回到顶部按钮 */}
      <BackToTopButton />
    </div>
  )
}
