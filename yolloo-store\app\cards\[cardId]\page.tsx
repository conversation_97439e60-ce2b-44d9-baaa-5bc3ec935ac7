"use client"

import { useEffect, useState } from "react"
import '../dialog-fix.css'
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { ChevronLeft, Signal, Globe, RefreshCw, CreditCard, AlertTriangle, Edit, Check, Package, BarChart, FileText, PowerOff, Clock, ChevronDown, ChevronUp, ArrowUp } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { AddEsimDialog } from "@/components/esims/add-esim-dialog"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { PackageUsage } from "@/components/boss/package-usage"
import { PackageDetailsView } from "@/components/boss/package-details-view"
import { packageStatusMap, orderSourceMap, formatDataUsage } from "@/lib/boss-utils"
import { DateFormatter } from "@/lib/utils"

// 用户前端专用的日期时间格式化 (使用用户浏览器时区，避免SSR问题)
const formatDateTime = (dateTimeStr: string | null) => {
  if (!dateTimeStr) return '-';
  return DateFormatter.forUserSafe(dateTimeStr, '-');
};

// 生成用户友好的eSIM包标题
const generatePackageTitle = (pkg: any) => {
  // 如果有packageName，优先使用
  if (pkg.packageName && pkg.packageName.trim()) {
    return pkg.packageName;
  }

  // 根据orderSource生成基础类型名称
  const typeMap: Record<string, string> = {
    'withuid_localload_order': 'Yolloo Package',
    'withuid_rpm_order': 'QR eSIM',
    'withoutuid_package_order': 'Pure eSIM',
    'withuid_renew_order': 'Renewed Package',
    // Legacy mappings
    'rpm_order': 'QR eSIM',
    'product_order': 'Yolloo Package'
  };

  const baseType = typeMap[pkg.orderSource] || 'eSIM Package';

  // 如果有时间信息，添加到标题中
  if (pkg.planStartTime) {
    const startDate = new Date(pkg.planStartTime);
    const month = startDate.toLocaleDateString('en-US', { month: 'short' });
    const day = startDate.getDate();
    return `${baseType} (${month} ${day})`;
  }

  // 如果没有时间信息，只返回类型
  return baseType;
};

interface EsimProfile {
  id: string
  imsi: string
  carrierName: string
  networkType: string
  serviceArea: string
  roamingEnabled: boolean
  status: string
  activationDate?: string
  expiryDate?: string
}

interface EsimPlan {
  id: string
  name: string
  description: string
  dataLimit: number
  duration: number
  price: number
  currency: string
  features: any
  roamingRegions: string[]
  status: string
}

interface Esim {
  id: string
  iccid: string
  status: string
  activationDate?: string
  expiryDate?: string
  product: {
    id: string
    name: string
    description: string
  }
  profile?: EsimProfile
  plan?: EsimPlan
}

interface YollooCard {
  id: string
  number: string
  customName?: string
  status: "Active" | "Inactive" | "Expired"
  type: string
  activationDate?: string
  expiryDate?: string
  createdAt: string
  esims: Esim[]
  dataUsage?: {
    used: number
    total: number
  } | null
  networkStatus?: {
    signal: number
    lastConnected?: string
  }
  location?: {
    country: string
    region: string
  }
}

export default function CardDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session, status } = useSession()
  const [card, setCard] = useState<YollooCard | null>(null)
  const [loading, setLoading] = useState(true)
  const [isUnbinding, setIsUnbinding] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)
  const [apiErrors, setApiErrors] = useState<string[]>([])
  const [isEditingName, setIsEditingName] = useState(false)
  const [customName, setCustomName] = useState("")

  // Boss service packages state
  const [bossPackages, setBossPackages] = useState<any[]>([])
  const [loadingPackages, setLoadingPackages] = useState(false)
  const [packageError, setPackageError] = useState<string | null>(null)
  const [selectedPackage, setSelectedPackage] = useState<any>(null)

  // Package details and usage state
  const [packageUsage, setPackageUsage] = useState<any>(null)
  const [packageDetails, setPackageDetails] = useState<any>(null)
  const [loadingUsage, setLoadingUsage] = useState(false)
  const [loadingDetails, setLoadingDetails] = useState(false)
  const [usageError, setUsageError] = useState<string | null>(null)
  const [detailsError, setDetailsError] = useState<string | null>(null)

  // Dialog states
  const [usageDialogOpen, setUsageDialogOpen] = useState(false)
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [isPerformingAction, setIsPerformingAction] = useState(false)
  const [showEndedPackages, setShowEndedPackages] = useState(false)

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
    } else if (status === "authenticated") {
      fetchCardDetails()
    }
  }, [status, params.cardId, refreshKey])

  useEffect(() => {
    if (card?.customName) {
      setCustomName(card.customName)
    }

    // Fetch Boss service packages when card is loaded
    if (card) {
      fetchBossPackages()
    }
  }, [card])

  // 添加全局清理函数，确保在组件卸载时清理所有遮罩层
  useEffect(() => {
    // 组件挂载时
    document.body.style.pointerEvents = '';

    // 组件卸载时
    return () => {
      // 确保所有对话框都被关闭
      setUsageDialogOpen(false);
      setDetailsDialogOpen(false);

      // 重置body样式
      document.body.style.pointerEvents = '';

      // 移除可能存在的遮罩层
      const overlays = document.querySelectorAll('[data-radix-portal]');
      overlays.forEach(overlay => {
        overlay.remove();
      });
    };
  }, [])

  const fetchCardDetails = async () => {
    // 重置错误状态
    setApiErrors([]);

    try {
      // 获取卡片基本信息
      const response = await fetch(`/api/cards/${params.cardId}`);
      if (!response.ok) {
        toast.error("Failed to load card details");
        router.push("/cards");
        return;
      }
      const cardData = await response.json();

      // 初始化合并数据，确保即使后续API调用失败也有基本数据
      let mergedData = {
        ...cardData,
        profiles: []
      };

      setCard(mergedData); // 先设置基本卡片数据，确保页面可以显示

      try {
        // 获取Profile列表
        const profilesResponse = await fetch(`/api/esims/profiles?yollooCardId=${params.cardId}`);

        if (profilesResponse.ok) {
          const profilesData = await profilesResponse.json();
          mergedData.profiles = profilesData?.profiles;
        } else {
          console.error("Failed to fetch profiles:", await profilesResponse.text());
          setApiErrors(prev => [...prev, "Profile information failed to load, showing limited data"]);
          toast.error("Could not load profile information, showing limited data");
        }
      } catch (profileError) {
        console.error("Error fetching profiles:", profileError);
        setApiErrors(prev => [...prev, "Profile information failed to load, showing limited data"]);
        toast.error("Could not load profile information, showing limited data");
      }

      // 更新卡片数据，包含所有可用信息
      setCard(mergedData);
    } catch (error) {
      console.error("Error in fetchCardDetails:", error);
      setApiErrors(prev => [...prev, "Card details incomplete, showing limited data"]);
      toast.error("Error loading complete card details, showing limited data");
      // 不再跳转回卡片列表页面，而是继续显示已加载的数据
    } finally {
      setLoading(false);
    }
  };

  const handleActivate = async () => {
    if (!card) return

    try {
      const response = await fetch(`/api/cards/${card.id}/activate`, {
        method: "POST"
      })
      if (response.ok) {
        toast.success("Card activated successfully")
        fetchCardDetails()
      } else {
        toast.error("Failed to activate card")
      }
    } catch (error) {
      toast.error("Error activating card")
    }
  }

  const handleUnbind = async () => {
    if (!card) return

    try {
      setIsUnbinding(true)
      const response = await fetch(`/api/cards/${card.id}`, {
        method: "DELETE"
      })

      if (response.ok) {
        toast.success("Card unbound successfully")
        router.push("/cards")
      } else {
        const error = await response.text()
        throw new Error(error)
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to unbind card")
    } finally {
      setIsUnbinding(false)
    }
  }

  const handleEsimAdded = () => {
    setRefreshKey(prev => prev + 1)
  }

  const handleUpdateCardName = async () => {
    if (!card) return

    try {
      const response = await fetch(`/api/cards/${card.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          customName: customName.trim() || null
        })
      })

      if (response.ok) {
        // Update local state
        setCard(prev => prev ? {...prev, customName: customName.trim() || null} : null)
        toast.success("Card name updated successfully")
        setIsEditingName(false)
      } else {
        toast.error("Failed to update card name")
      }
    } catch (error) {
      toast.error("Error updating card name")
    }
  }

  // Fetch Boss service packages
  const fetchBossPackages = async () => {
    if (!card) return

    setLoadingPackages(true)
    setPackageError(null)

    try {
      const response = await fetch(`/api/boss/packages?cardId=${card.id}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch packages: ${response.status}`)
      }

      const data = await response.json()

      // 检查是否有数据可用（即使有错误）
      if (data.resultCode === '10000' && data.data) {
        // 检查是否有错误信息
        if (data.data.hasError && data.data.errorDetails) {
          // 记录错误，但继续处理可能存在的部分数据
          console.warn('Boss service returned an error:', data.data.errorDetails);
          setPackageError(`Some package data may be incomplete: ${data.data.errorDetails.message}`);
        }

        // 处理列表数据（即使是空列表或部分数据）
        const packagesList = data.data.list || [];

        if (packagesList.length === 0) {
          // 如果列表为空，但没有明确的错误，设置一个友好的消息
          if (!data.data.hasError) {
            setPackageError('No packages found for this card');
          }
          setBossPackages([]);
          return;
        }

        // 将数据分为三组：当前激活的、非终止状态的、已终止状态的
        const terminatedPackages = packagesList.filter(pkg => pkg.packageStatus === 800);
        const currentActivePackages = packagesList.filter(pkg => pkg.whetherCurrent === true && pkg.packageStatus !== 800);
        const otherActivePackages = packagesList.filter(pkg => pkg.whetherCurrent !== true && pkg.packageStatus !== 800);

        // 对当前激活的套餐按照uidSort从大到小排序，相同uidSort按照开始时间排序
        const sortedCurrentActivePackages = [...currentActivePackages].sort((a, b) => {
          const aSort = a.uidSort !== undefined ? a.uidSort : 0;
          const bSort = b.uidSort !== undefined ? b.uidSort : 0;

          // 如果优先级相同，按照开始时间排序（新的在前）
          if (aSort === bSort) {
            const aTime = a.planStartTime ? new Date(a.planStartTime).getTime() : 0;
            const bTime = b.planStartTime ? new Date(b.planStartTime).getTime() : 0;
            return bTime - aTime; // 降序排列，新的在前
          }

          return bSort - aSort; // 优先级降序
        });

        // 对其他非终止状态的套餐按照uidSort从大到小排序，相同uidSort按照开始时间排序
        const sortedOtherActivePackages = [...otherActivePackages].sort((a, b) => {
          const aSort = a.uidSort !== undefined ? a.uidSort : 0;
          const bSort = b.uidSort !== undefined ? b.uidSort : 0;

          // 如果优先级相同，按照开始时间排序（新的在前）
          if (aSort === bSort) {
            const aTime = a.planStartTime ? new Date(a.planStartTime).getTime() : 0;
            const bTime = b.planStartTime ? new Date(b.planStartTime).getTime() : 0;
            return bTime - aTime; // 降序排列，新的在前
          }

          return bSort - aSort; // 优先级降序
        });

        // 对已终止状态的套餐也按照uidSort从大到小排序，相同uidSort按照开始时间排序
        const sortedTerminatedPackages = [...terminatedPackages].sort((a, b) => {
          const aSort = a.uidSort !== undefined ? a.uidSort : 0;
          const bSort = b.uidSort !== undefined ? b.uidSort : 0;

          // 如果优先级相同，按照开始时间排序（新的在前）
          if (aSort === bSort) {
            const aTime = a.planStartTime ? new Date(a.planStartTime).getTime() : 0;
            const bTime = b.planStartTime ? new Date(b.planStartTime).getTime() : 0;
            return bTime - aTime; // 降序排列，新的在前
          }

          return bSort - aSort; // 优先级降序
        });

        // 合并三组数据：先是当前激活的套餐，然后是其他非终止状态的套餐，最后是已终止套餐
        const sortedPackages = [
          ...sortedCurrentActivePackages,
          ...sortedOtherActivePackages,
          ...sortedTerminatedPackages
        ];

        setBossPackages(sortedPackages);

        // 选择第一个套餐作为默认选中（优先选择当前激活的套餐）
        if (sortedCurrentActivePackages.length > 0 && !selectedPackage) {
          setSelectedPackage(sortedCurrentActivePackages[0]);
        } else if (sortedOtherActivePackages.length > 0 && !selectedPackage) {
          setSelectedPackage(sortedOtherActivePackages[0]);
        } else if (sortedPackages.length > 0 && !selectedPackage) {
          setSelectedPackage(sortedPackages[0]);
        }
      } else {
        // 处理API返回的错误
        setPackageError(data.resultMsg || 'Failed to fetch packages');
        setBossPackages([]);
      }
    } catch (error) {
      console.error('Error fetching Boss packages:', error);
      setPackageError(error instanceof Error ? error.message : 'Unknown error occurred');
      setBossPackages([]);
    } finally {
      setLoadingPackages(false);
    }
  }

  // Fetch package usage
  const fetchPackageUsage = async (orderSn: string) => {
    setLoadingUsage(true)
    setUsageError(null)
    setUsageDialogOpen(true)

    try {
      const response = await fetch(`/api/boss/packages?orderSn=${orderSn}&action=queryUsage`)

      if (!response.ok) {
        throw new Error(`Failed to fetch usage: ${response.status}`)
      }

      const data = await response.json()

      if (data.resultCode === '10000' && data.data) {
        // 即使数据为空也显示空的使用情况
        setPackageUsage(data.data)

        // 如果数据为空或格式不正确，显示友好的提示
        if (!data.data.usageList || !Array.isArray(data.data.usageList) || data.data.usageList.length === 0) {
          setUsageError('No usage data available for this package. This may be normal for new packages.')
        }
      } else {
        // 处理错误但不阻止用户查看其他内容
        const errorMsg = data.resultMsg || 'Failed to fetch usage data';
        console.warn('Boss service usage data error:', errorMsg);
        setUsageError(`${errorMsg}. You can still view other package information.`);
        setPackageUsage(null);
      }
    } catch (error) {
      console.error('Error fetching package usage:', error);
      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred';
      setUsageError(`${errorMsg}. You can still view other package information.`);
      setPackageUsage(null);
    } finally {
      setLoadingUsage(false);
    }
  }

  // Fetch package details
  const fetchPackageDetails = async (orderSn: string) => {
    setLoadingDetails(true)
    setDetailsError(null)
    setDetailsDialogOpen(true)

    try {
      const response = await fetch(`/api/boss/packages?orderSn=${orderSn}&action=queryDetails`)

      if (!response.ok) {
        throw new Error(`Failed to fetch details: ${response.status}`)
      }

      const data = await response.json()

      if (data.resultCode === '10000') {
        if (data.data) {
          setPackageDetails(data.data)
        } else {
          // 处理空数据情况
          setDetailsError('No detailed information available for this package.')
          // 仍然显示我们已有的基本信息
          setPackageDetails(selectedPackage || null)
        }
      } else {
        // 处理错误但不阻止用户查看其他内容
        const errorMsg = data.resultMsg || 'Failed to fetch package details';
        console.warn('Boss service details error:', errorMsg);
        setDetailsError(`${errorMsg}. Basic information is still available.`);
        // 使用已有的套餐基本信息
        setPackageDetails(selectedPackage || null);
      }
    } catch (error) {
      console.error('Error fetching package details:', error);
      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred';
      setDetailsError(`${errorMsg}. Basic information is still available.`);
      // 使用已有的套餐基本信息
      setPackageDetails(selectedPackage || null);
    } finally {
      setLoadingDetails(false);
    }
  }

  // Set package as top
  const handleSetTop = async (orderSn: string, uid: string) => {
    setIsPerformingAction(true)

    try {
      const response = await fetch('/api/boss/packages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'toppingOrderPlan',
          orderSn,
          uid,
          cardId: card?.id
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to set package as top: ${response.status}`)
      }

      const data = await response.json()

      if (data.resultCode === '10000') {
        toast.success('Package set as top successfully')
        fetchBossPackages() // Refresh packages list
      } else {
        toast.error(data.resultMsg || 'Failed to set package as top')
      }
    } catch (error) {
      console.error('Error setting package as top:', error)
      toast.error(error instanceof Error ? error.message : 'Unknown error occurred')
    } finally {
      setIsPerformingAction(false)
    }
  }

  // Deactivate package
  const handleDeactivate = async (orderSn: string) => {
    setIsPerformingAction(true)

    try {
      const response = await fetch('/api/boss/packages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'closeOrderPlan',
          orderSn,
          cardId: card?.id
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to deactivate package: ${response.status}`)
      }

      const data = await response.json()

      if (data.resultCode === '10000') {
        toast.success('Package deactivated successfully')
        fetchBossPackages() // Refresh packages list
      } else {
        toast.error(data.resultMsg || 'Failed to deactivate package')
      }
    } catch (error) {
      console.error('Error deactivating package:', error)
      toast.error(error instanceof Error ? error.message : 'Unknown error occurred')
    } finally {
      setIsPerformingAction(false)
    }
  }

  if (loading || !card) {
    return (
      <div className="container py-10">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container max-w-6xl py-6 sm:py-12 px-4 sm:px-6">
      {/* Header with back button */}
      <div className="flex flex-wrap items-center justify-between gap-3 mb-6 sm:mb-8">
        <Button
          variant="ghost"
          onClick={() => router.push("/cards")}
          className="group flex items-center text-muted-foreground hover:text-foreground"
        >
          <ChevronLeft className="mr-2 h-4 w-4 transition-transform group-hover:-translate-x-1" />
          Back to Cards
        </Button>

        <div className="flex items-center gap-2">
          <AddEsimDialog cardId={card.id} cardNumber={card.number} onSuccess={handleEsimAdded} />

        </div>
      </div>

      {/* Error notifications */}
      {apiErrors.length > 0 && (
        <div className="mb-6 sm:mb-8 rounded-lg border-l-4 border-yellow-400 bg-yellow-50 p-3 sm:p-4">
          <div className="flex flex-col sm:flex-row sm:items-start">
            <AlertTriangle className="h-6 w-6 text-yellow-600 mb-2 sm:mb-0 sm:mr-3" />
            <div>
              <h3 className="font-semibold text-yellow-800">Some data couldn't be loaded</h3>
              <ul className="mt-2 text-sm text-yellow-700 space-y-1">
                {apiErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
              <Button
                variant="outline"
                size="sm"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100"
                onClick={() => fetchCardDetails()}
              >
                <RefreshCw className="mr-2 h-3 w-3" />
                Reload Data
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="grid gap-6 sm:gap-8 grid-cols-1 lg:grid-cols-3">
        {/* Main Card Info - Takes 1/3 of the screen on large displays */}
        <div className="lg:col-span-1">
          <Card className="shadow-md border-0 overflow-hidden w-full">
            <div className="bg-gradient-to-r from-primary/80 to-primary/60 p-4 sm:p-6">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-white mb-1">Yolloo Card</h2>
                  <p className="text-sm text-white/80 break-all">#{card.number}</p>
                </div>
                <CreditCard className="h-8 w-8 sm:h-10 sm:w-10 text-white/80" />
              </div>
            </div>

            <CardContent className="p-4 sm:p-6 space-y-4 sm:space-y-6">
              {/* Card Name Section */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Card Name</h3>
                {isEditingName ? (
                  <div className="flex flex-col sm:flex-row gap-2 sm:items-center sm:space-x-2">
                    <Input
                      value={customName}
                      onChange={(e) => setCustomName(e.target.value)}
                      placeholder="Enter custom card name"
                      className="flex-1"
                      autoFocus
                    />
                    <div className="flex space-x-1 self-end sm:self-auto">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={handleUpdateCardName}
                        className="h-9 w-9 border-green-200 text-green-600 hover:bg-green-50 hover:text-green-700"
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => {
                          setIsEditingName(false);
                          setCustomName(card?.customName || "");
                        }}
                        className="h-9 w-9"
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                    <p className="font-medium text-base sm:text-lg break-words">{card?.customName || `Card ${card.number}`}</p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsEditingName(true)}
                      className="text-muted-foreground hover:text-foreground self-start"
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Rename
                    </Button>
                  </div>
                )}
              </div>

              <div className="pt-2 border-t border-border">
                <div className="grid grid-cols-2 gap-3 sm:gap-4">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Status</p>
                    <div className="font-medium flex items-center">
                      <span className={`h-2 w-2 rounded-full mr-2 ${
                        card.status === 'Active' ? 'bg-green-500' :
                        card.status === 'Inactive' ? 'bg-amber-500' :
                        'bg-red-500'
                      }`}></span>
                      {card.status}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Type</p>
                    <p className="font-medium break-words">{card.type}</p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3 sm:gap-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Activation</p>
                  <p className="font-medium text-xs sm:text-sm">
                    {card.activationDate
                      ? DateFormatter.forUserSafe(card.activationDate)
                      : 'N/A'}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Bound Date</p>
                  <p className="font-medium text-xs sm:text-sm">
                    {DateFormatter.forUserSafe(card.createdAt)}
                  </p>
                </div>
              </div>

              {/* Card Actions */}
              <div className="flex flex-col gap-2 pt-4">
                {card.status === "Inactive" && (
                  <Button
                    onClick={handleActivate}
                    className="w-full bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white text-xs sm:text-sm"
                    disabled={apiErrors.length > 0}
                    title={apiErrors.length > 0 ? "Cannot activate: API connection failed" : "Activate card"}
                  >
                    Activate Card
                  </Button>
                )}
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 text-xs sm:text-sm"
                      disabled={isUnbinding}
                    >
                      {isUnbinding ? "Unbinding..." : "Unbind Card"}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="sm:max-w-md max-w-[calc(100%-2rem)]">
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action will unbind the Yolloo card from your account. You will no longer have access to this card after unbinding.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter className="flex-col sm:flex-row gap-2">
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleUnbind}
                        className="bg-red-600 text-white hover:bg-red-700"
                      >
                        Unbind
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>

          {/* Usage Stats - Optional */}
          {card.dataUsage && (
            <Card className="mt-4 sm:mt-6 shadow-md border-0 w-full">
              <CardHeader className="pb-2 p-4 sm:p-6">
                <CardTitle className="text-base sm:text-lg">Data Usage</CardTitle>
              </CardHeader>
              <CardContent className="pt-0 px-4 sm:px-6 pb-4 sm:pb-6">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Used</span>
                    <span className="font-medium">N/A</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* eSIM Section - Takes 2/3 of the screen on large displays */}
        <div className="lg:col-span-2">


          {/* Dialog components for package details and usage */}
          <Dialog open={usageDialogOpen} onOpenChange={(open) => {
            setUsageDialogOpen(open);
            // 确保在关闭对话框时清理DOM
            if (!open) {
              // 给DOM一点时间来完成动画和清理
              setTimeout(() => {
                document.body.style.pointerEvents = '';
              }, 100);
            }
          }}>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle className="flex items-center">
                  <BarChart className="mr-2 h-5 w-5" />
                  Package Usage
                </DialogTitle>
              </DialogHeader>
              {loadingUsage ? (
                <div className="flex items-center justify-center h-40">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : usageError ? (
                <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                  <div className="flex flex-col items-center text-center">
                    <AlertTriangle className="h-6 w-6 text-amber-500 mb-2" />
                    <h4 className="font-medium text-amber-800 mb-1">Unable to Load Usage Data</h4>
                    <p className="text-amber-700 text-sm mb-3">{usageError}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => selectedPackage && fetchPackageUsage(selectedPackage.orderSn)}
                      className="border-amber-300 text-amber-700 hover:bg-amber-100 hover:text-amber-800"
                    >
                      <RefreshCw className="mr-2 h-3.5 w-3.5" />
                      Try Again
                    </Button>
                  </div>
                </div>
              ) : (
                <PackageUsage usageData={packageUsage} />
              )}
            </DialogContent>
          </Dialog>

          {/* 使用标准的Dialog组件模式 */}
          <Dialog open={detailsDialogOpen} onOpenChange={(open) => {
            setDetailsDialogOpen(open);
            // 确保在关闭对话框时清理DOM
            if (!open) {
              // 给DOM一点时间来完成动画和清理
              setTimeout(() => {
                document.body.style.pointerEvents = '';
              }, 100);
            }
          }}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5" />
                  Package Details
                </DialogTitle>
              </DialogHeader>
              {loadingDetails ? (
                <div className="flex items-center justify-center h-40">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : detailsError ? (
                <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                  <div className="flex flex-col items-center text-center">
                    <AlertTriangle className="h-6 w-6 text-amber-500 mb-2" />
                    <h4 className="font-medium text-amber-800 mb-1">Unable to Load Package Details</h4>
                    <p className="text-amber-700 text-sm mb-3">{detailsError}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => selectedPackage && fetchPackageDetails(selectedPackage.orderSn)}
                      className="border-amber-300 text-amber-700 hover:bg-amber-100 hover:text-amber-800"
                    >
                      <RefreshCw className="mr-2 h-3.5 w-3.5" />
                      Try Again
                    </Button>
                  </div>
                </div>
              ) : (
                <PackageDetailsView detailsData={packageDetails} />
              )}
            </DialogContent>
          </Dialog>

          {/* eSIM Section */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 mb-4">
            <div>
              <h2 className="text-xl sm:text-2xl font-bold">eSIMs</h2>
              {loadingPackages && (
                <p className="text-xs text-muted-foreground">
                  Loading eSIM packages, this may take a moment...
                </p>
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchBossPackages}
              disabled={loadingPackages}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${loadingPackages ? 'animate-spin' : ''}`} />
              {loadingPackages ? 'Loading...' : 'Refresh'}
            </Button>
          </div>

          {loadingPackages ? (
            <div className="flex items-center justify-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : packageError ? (
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg mb-6">
              <div className="flex flex-col sm:flex-row items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5" />
                <div className="flex-1">
                  <h4 className="font-medium text-amber-800 mb-1">Package Information Notice</h4>
                  <p className="text-amber-700 text-sm mb-3">{packageError}</p>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={fetchBossPackages}
                      className="border-amber-300 text-amber-700 hover:bg-amber-100 hover:text-amber-800"
                    >
                      <RefreshCw className="mr-2 h-3.5 w-3.5" />
                      Refresh Data
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-amber-600 hover:text-amber-800 hover:bg-amber-100"
                      onClick={() => setPackageError(null)}
                    >
                      Dismiss
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <>
              {/* Display active eSIMs (both from card.esims and bossPackages) */}
              {(() => {
                // Filter active packages (not End status)
                const activePackages = bossPackages.filter(pkg => pkg.packageStatus !== 800);
                const endedPackages = bossPackages.filter(pkg => pkg.packageStatus === 800);

                // Sort active packages: Currently Active (whetherCurrent=true) first, then others
                const sortedActivePackages = [...activePackages].sort((a, b) => {
                  // First priority: Currently Active packages (whetherCurrent=true) come first
                  if (a.whetherCurrent !== b.whetherCurrent) {
                    return b.whetherCurrent ? 1 : -1; // true comes before false
                  }

                  // Second priority: uidSort (higher values first)
                  const aSort = a.uidSort !== undefined ? a.uidSort : 0;
                  const bSort = b.uidSort !== undefined ? b.uidSort : 0;
                  if (aSort !== bSort) {
                    return bSort - aSort; // Descending order
                  }

                  // Third priority: Start time (newer first)
                  const aTime = a.planStartTime ? new Date(a.planStartTime).getTime() : 0;
                  const bTime = b.planStartTime ? new Date(b.planStartTime).getTime() : 0;
                  return bTime - aTime; // Descending order
                });

                return (
                  <>
                    {/* Active eSIMs and Packages */}
                    {(card.esims.length > 0 || sortedActivePackages.length > 0) ? (
                      <div className="grid gap-4 sm:gap-6 grid-cols-1 md:grid-cols-2">
                        {/* Regular eSIMs */}
                        {card.esims.map((esim) => (
                          <Card key={esim.id} className="shadow-sm hover:shadow-md transition-shadow duration-200 border-0 w-full">
                            <CardHeader className="pb-2 border-b">
                              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                                <div>
                                  <CardTitle className="text-lg mb-1 break-words">{esim.product.name}</CardTitle>
                                  <CardDescription className="break-all">
                                    ICCID: <span className="font-mono text-xs sm:text-sm">{esim.iccid}</span>
                                  </CardDescription>
                                </div>
                                <Badge
                                  variant={
                                    esim.status === 'Active' ? 'success' :
                                    esim.status === 'Inactive' ? 'secondary' :
                                    'destructive'
                                  }
                                  className="self-start sm:self-auto"
                                >
                                  {esim.status}
                                </Badge>
                              </div>
                            </CardHeader>

                            <CardContent className="pt-4 space-y-4">
                              {/* Profile Information */}
                              {esim.profile && (
                                <div className="bg-slate-50 p-2 sm:p-3 rounded-md">
                                  <h4 className="font-semibold mb-2 sm:mb-3 flex items-center text-slate-700">
                                    <Signal className="mr-2 h-4 w-4" />
                                    Network Profile
                                  </h4>
                                  <div className="grid grid-cols-1 xs:grid-cols-2 gap-2 sm:gap-3 text-sm">
                                    <div>
                                      <p className="text-muted-foreground mb-1">Carrier</p>
                                      <p className="font-medium break-words">{esim.profile.carrierName}</p>
                                    </div>
                                    <div>
                                      <p className="text-muted-foreground mb-1">Network</p>
                                      <p className="font-medium break-words">{esim.profile.networkType}</p>
                                    </div>
                                    <div>
                                      <p className="text-muted-foreground mb-1">Service Area</p>
                                      <p className="font-medium break-words">{esim.profile.serviceArea}</p>
                                    </div>
                                    <div>
                                      <p className="text-muted-foreground mb-1">IMSI</p>
                                      <p className="font-mono text-xs break-all">{esim.profile.imsi}</p>
                                    </div>
                                  </div>
                                </div>
                              )}

                              {/* Plan Information */}
                              {esim.plan && (
                                <div className="bg-blue-50 p-2 sm:p-3 rounded-md">
                                  <h4 className="font-semibold mb-2 sm:mb-3 flex items-center text-blue-700">
                                    <Globe className="mr-2 h-4 w-4" />
                                    Data Plan
                                  </h4>
                                  <div className="grid grid-cols-1 xs:grid-cols-2 gap-2 sm:gap-3 text-sm">
                                    <div>
                                      <p className="text-muted-foreground mb-1">Plan Name</p>
                                      <p className="font-medium break-words">{esim.plan.name}</p>
                                    </div>
                                    <div>
                                      <p className="text-muted-foreground mb-1">Data Limit</p>
                                      <p className="font-medium">{esim.plan.dataLimit}GB</p>
                                    </div>
                                    <div>
                                      <p className="text-muted-foreground mb-1">Duration</p>
                                      <p className="font-medium">{esim.plan.duration} days</p>
                                    </div>
                                    <div>
                                      <p className="text-muted-foreground mb-1">Price</p>
                                      <p className="font-medium">
                                        {esim.plan.price} {esim.plan.currency}
                                      </p>
                                    </div>
                                  </div>
                                  {esim.plan.roamingRegions.length > 0 && (
                                    <div className="mt-3">
                                      <p className="text-sm text-muted-foreground mb-1">Roaming Regions</p>
                                      <div className="flex flex-wrap gap-1">
                                        {esim.plan.roamingRegions.map((region) => (
                                          <Badge key={region} variant="outline" className="bg-white text-blue-800 border-blue-200">
                                            {region}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* Action Buttons */}
                              <div className="flex gap-2 pt-2">
                                <Button className="flex-1 text-xs sm:text-sm" variant="outline">
                                  Manage
                                </Button>
                                {esim.status === "Inactive" && (
                                  <Button
                                    className="flex-1 bg-green-600 hover:bg-green-700 text-white text-xs sm:text-sm"
                                    disabled={apiErrors.length > 0}
                                    title={apiErrors.length > 0 ? "Cannot activate: API connection failed" : "Activate eSIM"}
                                  >
                                    Activate
                                  </Button>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        ))}

                        {/* Boss Service Packages as eSIMs */}
                        {sortedActivePackages.map((pkg) => (
                          <Card key={pkg.id} className={`shadow-sm hover:shadow-md transition-shadow duration-200 border-0 w-full ${pkg.whetherCurrent ? 'ring-2 ring-green-500 ring-opacity-50' : ''}`}>
                            <CardHeader className="pb-4">
                              {/* Title and Status Row */}
                              <div className="flex items-start justify-between gap-3 mb-3">
                                <CardTitle className="text-lg font-semibold break-words flex-1">
                                  {generatePackageTitle(pkg)}
                                  {pkg.whetherCurrent && (
                                    <span className="ml-2 text-sm text-green-600 font-medium">(Currently Active)</span>
                                  )}
                                </CardTitle>
                                <div className="flex flex-col gap-2 shrink-0">
                                  <Badge
                                    variant={packageStatusMap[pkg.packageStatus]?.variant || 'default'}
                                  >
                                    {packageStatusMap[pkg.packageStatus]?.label || `Status: ${pkg.packageStatus}`}
                                  </Badge>
                                  {pkg.whetherCurrent && (
                                    <Badge variant="success" className="text-xs">
                                      In Use
                                    </Badge>
                                  )}
                                </div>
                              </div>

                              {/* Key Information Grid */}
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 text-sm">
                                <div className="flex items-center gap-3">
                                  <div className="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full">
                                    <Globe className="h-3 w-3 text-blue-600" />
                                  </div>
                                  <div className="min-w-0 flex-1">
                                    <p className="text-xs text-muted-foreground">Data Used</p>
                                    <p className="font-medium">{formatDataUsage(pkg.usageBytes || 0)}</p>
                                  </div>
                                </div>

                                <div className="flex items-center gap-3">
                                  <div className="flex items-center justify-center w-6 h-6 bg-purple-100 rounded-full">
                                    <Signal className="h-3 w-3 text-purple-600" />
                                  </div>
                                  <div className="min-w-0 flex-1">
                                    <p className="text-xs text-muted-foreground">Type</p>
                                    <p className="font-medium">{pkg.orderSource ? (orderSourceMap[pkg.orderSource] || pkg.orderSource) : 'Package'}</p>
                                  </div>
                                </div>

                                {pkg.planEndTime && (
                                  <div className="flex items-center gap-3 sm:col-span-2">
                                    <div className="flex items-center justify-center w-6 h-6 bg-green-100 rounded-full">
                                      <Clock className="h-3 w-3 text-green-600" />
                                    </div>
                                    <div className="min-w-0 flex-1">
                                      <p className="text-xs text-muted-foreground">Valid Until</p>
                                      <p className="font-medium text-xs">{formatDateTime(pkg.planEndTime) || 'N/A'}</p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </CardHeader>

                            <CardContent className="pt-0">
                              {/* Action Buttons */}
                              <div className="grid grid-cols-2 gap-2 mb-4">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    if (!loadingDetails) {
                                      setSelectedPackage(pkg);
                                      setDetailsDialogOpen(true);
                                      setPackageDetails(pkg);
                                    }
                                  }}
                                  disabled={loadingDetails}
                                  className="w-full"
                                >
                                  <FileText className="mr-1.5 h-3.5 w-3.5" />
                                  Details
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    if (!loadingUsage && pkg.orderSn) {
                                      fetchPackageUsage(pkg.orderSn);
                                    }
                                  }}
                                  disabled={loadingUsage || !pkg.orderSn}
                                  className="w-full"
                                >
                                  <BarChart className="mr-1.5 h-3.5 w-3.5" />
                                  Usage
                                </Button>
                                {!pkg.whetherCurrent && pkg.packageStatus !== 800 && (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleSetTop(pkg.orderSn, card?.number || '')}
                                    disabled={isPerformingAction}
                                    className="col-span-2 w-full text-blue-600 border-blue-200 hover:bg-blue-50 hover:text-blue-700"
                                  >
                                    <ArrowUp className="mr-1.5 h-3.5 w-3.5" />
                                    Set as Top
                                  </Button>
                                )}
                                {pkg.packageStatus !== 800 && (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleDeactivate(pkg.orderSn)}
                                    disabled={isPerformingAction}
                                    className="col-span-2 w-full text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700"
                                  >
                                    <PowerOff className="mr-1.5 h-3.5 w-3.5" />
                                    Deactivate Package
                                  </Button>
                                )}
                              </div>


                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <Card className="border-dashed bg-transparent shadow-none">
                        <div className="p-6 sm:p-10 text-center">
                          <div className="rounded-full bg-slate-100 w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center mx-auto mb-3 sm:mb-4">
                            <CreditCard className="h-6 w-6 sm:h-8 sm:w-8 text-slate-400" />
                          </div>
                          <h3 className="text-lg sm:text-xl font-semibold mb-2">No eSIMs Added Yet</h3>
                          <p className="text-sm sm:text-base text-muted-foreground mb-4 sm:mb-6 max-w-md mx-auto">
                            You haven't added any eSIMs to this card yet. Buy a package to start using mobile data.
                          </p>
                        </div>
                      </Card>
                    )}

                    {/* Ended Packages Section with Toggle */}
                    {endedPackages.length > 0 && (
                      <>
                        <div className="flex items-center my-8">
                          <div className="flex-grow h-px bg-gray-200"></div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setShowEndedPackages(!showEndedPackages)}
                            className="mx-4 text-gray-500 hover:text-gray-700 hover:bg-gray-50 transition-colors px-3 py-2 rounded-md"
                          >
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium whitespace-nowrap">
                                {showEndedPackages ? 'Hide' : 'Show'} Ended eSIMs ({endedPackages.length})
                              </span>
                              <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${showEndedPackages ? 'rotate-180' : ''}`} />
                            </div>
                          </Button>
                          <div className="flex-grow h-px bg-gray-200"></div>
                        </div>

                        <div className={`transition-all duration-300 ease-in-out overflow-hidden ${showEndedPackages ? 'max-h-none opacity-100' : 'max-h-0 opacity-0'}`}>
                          <div className="grid gap-4 sm:gap-6 grid-cols-1 md:grid-cols-2 pt-4">
                            {endedPackages.map((pkg) => (
                            <Card key={pkg.id} className="shadow-sm hover:shadow-md transition-shadow duration-200 border-0 w-full opacity-75">
                              <CardHeader className="pb-4">
                                {/* Title and Status Row */}
                                <div className="flex items-start justify-between gap-3 mb-3">
                                  <CardTitle className="text-lg font-semibold break-words flex-1">
                                    {generatePackageTitle(pkg)}
                                  </CardTitle>
                                  <Badge variant="secondary" className="shrink-0">
                                    Ended
                                  </Badge>
                                </div>

                                {/* Key Information Grid */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 text-sm">
                                  <div className="flex items-center gap-3">
                                    <div className="flex items-center justify-center w-6 h-6 bg-gray-100 rounded-full">
                                      <Globe className="h-3 w-3 text-gray-600" />
                                    </div>
                                    <div className="min-w-0 flex-1">
                                      <p className="text-xs text-muted-foreground">Data Used</p>
                                      <p className="font-medium">{formatDataUsage(pkg.usageBytes || 0)}</p>
                                    </div>
                                  </div>

                                  <div className="flex items-center gap-3">
                                    <div className="flex items-center justify-center w-6 h-6 bg-red-100 rounded-full">
                                      <Clock className="h-3 w-3 text-red-600" />
                                    </div>
                                    <div className="min-w-0 flex-1">
                                      <p className="text-xs text-muted-foreground">Ended</p>
                                      <p className="font-medium text-xs">{formatDateTime(pkg.planEndTime) || 'N/A'}</p>
                                    </div>
                                  </div>
                                </div>
                              </CardHeader>

                              <CardContent className="pt-0">
                                {/* Action Buttons */}
                                <div className="grid grid-cols-2 gap-2 mb-4">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      if (!loadingDetails) {
                                        setSelectedPackage(pkg);
                                        setDetailsDialogOpen(true);
                                        setPackageDetails(pkg);
                                      }
                                    }}
                                    disabled={loadingDetails}
                                    className="w-full"
                                  >
                                    <FileText className="mr-1.5 h-3.5 w-3.5" />
                                    Details
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      if (!loadingUsage && pkg.orderSn) {
                                        fetchPackageUsage(pkg.orderSn);
                                      }
                                    }}
                                    disabled={loadingUsage || !pkg.orderSn}
                                    className="w-full"
                                  >
                                    <BarChart className="mr-1.5 h-3.5 w-3.5" />
                                    Usage
                                  </Button>
                                </div>


                              </CardContent>
                            </Card>
                            ))}
                          </div>
                        </div>
                      </>
                    )}
                  </>
                );
              })()}
            </>
          )}
        </div>
      </div>
    </div>
  )
}