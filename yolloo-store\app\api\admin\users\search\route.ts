import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// GET - Search users (admin only)
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }
    
    // Get query parameter
    const { searchParams } = new URL(req.url);
    const query = searchParams.get("query") || "";
    
    if (!query || query.length < 1) {
      return NextResponse.json([]);
    }
    
    // Search users - optimize query for better performance
    const users = await prisma.user.findMany({
      where: {
        OR: [
          { name: { contains: query, mode: "insensitive" } },
          { email: { contains: query, mode: "insensitive" } },
        ],
        // Exclude users who already have an organization
        NOT: {
          affiliate: {
            organizationId: { not: null }
          }
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        affiliate: {
          select: {
            id: true,
            organizationId: true,
          },
        },
      },
      take: 10,
      orderBy: {
        name: 'asc'
      }
    });
    
    // Add cache headers to improve performance
    const response = NextResponse.json(users);
    response.headers.set('Cache-Control', 'max-age=10'); // Cache for 10 seconds
    
    return response;
  } catch (error) {
    console.error("Error searching users:", error);
    return NextResponse.json(
      { error: "Failed to search users" },
      { status: 500 }
    );
  }
} 