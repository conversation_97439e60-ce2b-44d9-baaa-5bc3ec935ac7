import { redirect, notFound } from "next/navigation"
import { prisma } from "@/lib/prisma"
import { ProductActions } from '@/components/products/product-actions'
import type {
  Order,
  OrderItem,
  Product as PrismaProduct,
  ProductVariant as PrismaProductVariant,
  Address,
  OdooOrderStatus
} from "@prisma/client"
import { cn } from '@/lib/utils'
import { formatProductName, parseProductBadge } from "@/lib/utils"
import { FormattedDescription } from "@/components/formatted-description"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface ProductPageProps {
  params: {
    productId: string
  }
}

// 组件期望的类型
interface ProductVariant {
  id: string
  name: string
  price: number
  attributes?: Record<string, string>
  duration: number
  durationType: string
}

interface Product {
  id: string
  name: string
  price: number
  images?: string[]
  stock: number
  variants?: ProductVariant[]
  requiredUID?: boolean
  category?: { name: string }
  reviews: any[]
}

// 类型适配器函数
function adaptProduct(prismaProduct: PrismaProduct & {
  variants: PrismaProductVariant[],
  category: { id: string, name: string },
  reviews: any[],
  parameters?: { code: string, name: string, value: string }[]
}): Product {
  return {
    id: prismaProduct.id,
    name: prismaProduct.name,
    price: prismaProduct.price,
    images: prismaProduct.images,
    stock: prismaProduct.stock,
    category: prismaProduct.category,
    requiredUID: prismaProduct.requiredUID,
    variants: prismaProduct.variants.map(variant => {
      let variantName = `Variant ${variant.id.substring(0, 5)}`;

      // Use duration and durationType if available
      if (variant.duration && variant.durationType) {
        variantName = `${prismaProduct.name} ${variant.duration} ${variant.durationType}${variant.duration > 1 ? 's' : ''}`;
      }

      return {
        id: variant.id,
        name: variantName,
        price: Number(variant.price),
        duration: variant.duration,
        durationType: variant.durationType
      };
    }),
    reviews: prismaProduct.reviews || [],
  }
}

type OrderWithRelations = Order & {
  items: (OrderItem & {
    product: PrismaProduct;
    variant: PrismaProductVariant | null;
  })[];
  shippingAddress?: Address | null;
  shippingAddressSnapshot?: {
    name: string;
    phone: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  } | null;
  odooStatuses: OdooOrderStatus[];
  odooStatus: OdooOrderStatus | null; // 为了兼容性保留
}

export default async function ProductPage({ params }: ProductPageProps) {
  const product = await prisma.product.findFirst({
    where: {
      id: params.productId,
      status: "ACTIVE",
      off_shelve: false
    },
    include: {
      category: true,
      variants: true,
      parameters: true,
    },
  })

  if (!product) {
    notFound()
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="grid gap-8 lg:grid-cols-2">
        <div className="space-y-4">
          <div className={cn(
            "relative aspect-square max-h-[600px] overflow-hidden rounded-xl shadow-md bg-gradient-to-tr from-gray-50 to-white dark:from-gray-900 dark:to-gray-950",
            product.off_shelve && "opacity-60"
          )}>
            {product.images?.[0] ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                src={product.images[0]}
                alt={formatProductName(product.name)}
                className={cn(
                  "h-full w-full rounded-xl object-contain p-4 transition-all duration-300 hover:scale-105",
                  product.off_shelve && "grayscale"
                )}
              />
            ) : (
              <div className="flex aspect-square w-full items-center justify-center rounded-xl bg-secondary">
                <span className="text-muted-foreground">No image available</span>
              </div>
            )}
            {product.off_shelve && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-xl">
                <span className="bg-black/70 text-white px-4 py-2 rounded-full font-medium">
                  Off Shelf
                </span>
              </div>
            )}
          </div>
          <div className="grid grid-cols-4 gap-2">
            {product.images?.slice(1).map((image, index) => (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                key={index}
                src={image}
                alt={`${formatProductName(product.name)} ${index + 2}`}
                className={cn(
                  "aspect-square h-24 w-24 rounded-lg object-cover shadow-sm border border-gray-100 dark:border-gray-800 transition-all duration-200 hover:shadow-md hover:scale-105",
                  product.off_shelve && "opacity-60 grayscale"
                )}
              />
            ))}
          </div>
        </div>
        <div className="space-y-6">
          <div>
            {(() => {
              const { badge, name } = parseProductBadge(product.name);
              return (
                <h1
                  className={cn(
                    "text-2xl font-bold flex items-center gap-3 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent",
                    product.off_shelve && "from-muted-foreground to-muted-foreground/70"
                  )}
                >
                  {badge && (
                    <span
                      className="inline-block rounded-full bg-gradient-to-r from-primary to-primary/70 text-white font-bold px-3 py-1 text-xs shadow-sm"
                    >
                      {badge}
                    </span>
                  )}
                  {formatProductName(name)}
                </h1>
              );
            })()}
          </div>



          <div className="prose max-w-none text-sm text-muted-foreground">
            <FormattedDescription
              text={product.websiteDescription || ''}
              maxLines={10}
              plain={false}
            />
          </div>

          <ProductActions product={adaptProduct(product)} />
        </div>
      </div>

      <div className="mt-12">
        <Tabs defaultValue="description" className="w-full">
          <TabsList className="mb-6 grid w-full grid-cols-2">
            <TabsTrigger value="description">Description</TabsTrigger>
            <TabsTrigger value="specifications">Specifications</TabsTrigger>
          </TabsList>

          <TabsContent value="description" className="space-y-4">
            <div className="prose max-w-none">
              <FormattedDescription text={product.websiteDescription || ''} />
            </div>
          </TabsContent>

          <TabsContent value="specifications" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-muted/50 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Product Details</h3>
                <ul className="space-y-2">
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Category</span>
                    <span className="font-medium">{product.category?.name || 'N/A'}</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Stock</span>
                    <span className="font-medium">{product.stock} units</span>
                  </li>
                </ul>
              </div>
              {Array.isArray(product.parameters) && product.parameters.length > 0 && (
                <div className="bg-muted/50 p-4 rounded-lg">
                  <h3 className="font-medium mb-2">Technical Specifications</h3>
                  <ul className="space-y-2">
                    {product.parameters.map((param, index) => (
                      <li key={index} className="flex justify-between">
                        <span className="text-muted-foreground">{param.name || param.code || 'Parameter'}</span>
                        <span className="font-medium">{param.value || 'N/A'}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

