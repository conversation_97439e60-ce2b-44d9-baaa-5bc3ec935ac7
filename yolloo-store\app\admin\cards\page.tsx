import { Metadata } from "next"
import { getServerSession } from "next-auth"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { DataTable } from "./data-table"
import { columns } from "./columns"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { Icons } from "@/components/icons"

export const metadata: Metadata = {
  title: "Cards Management",
  description: "Manage Yolloo Cards",
}

export default async function CardsPage() {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== "ADMIN") {
    redirect("/auth/signin")
  }

  const cards = await prisma.yollooCard.findMany({
    include: {
      user: true,
    },
    orderBy: {
      createdAt: "asc",
    },
  })

  return (
    <div className="flex flex-col gap-4 p-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Cards Management</h2>
          <p className="text-muted-foreground">
            Manage and monitor all Yolloo Cards
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button asChild>
            <Link href="/admin/cards/import">
              <Icons.download className="mr-2 h-4 w-4" />
              Import Cards
            </Link>
          </Button>
          <Button asChild>
            <Link href="/admin/cards/new">
              <Icons.add className="mr-2 h-4 w-4" />
              Add Card
            </Link>
          </Button>
        </div>
      </div>
      <DataTable data={cards} columns={columns} />
    </div>
  )
} 