"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { toast } from "sonner"

interface UpdateOdooStatusButtonProps {
  orderId: string
}

export function UpdateOdooStatusButton({ orderId }: UpdateOdooStatusButtonProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  async function handleUpdateStatus() {
    setIsLoading(true)

    try {
      const response = await fetch(`/api/admin/orders/${orderId}/update-status`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || "Failed to update order status")
      }

      // 显示更新的状态记录数量
      const statusCount = data.odooStatuses?.length || 0;

      toast.success(`Odoo status updated successfully`, {
        description: `Updated ${statusCount} status records. Order information has been refreshed with the latest status from Odoo.`
      })

      // 刷新页面以显示更新的信息
      router.refresh()
    } catch (error) {
      console.error(error)
      toast.error(`Failed to update order status: ${error instanceof Error ? error.message : 'Unknown error'}`, {
        duration: 5000
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button
      onClick={handleUpdateStatus}
      disabled={isLoading}
      variant="outline"
      size="sm"
    >
      {isLoading ? (
        <>
          <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          Updating...
        </>
      ) : (
        <>
          <Icons.refresh className="mr-2 h-4 w-4" />
          Refresh Status
        </>
      )}
    </Button>
  )
}