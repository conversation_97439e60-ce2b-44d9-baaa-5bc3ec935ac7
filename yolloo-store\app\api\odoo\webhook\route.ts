import { NextRequest, NextResponse } from 'next/server';
import { createOdooService } from '../../../services/odooService';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


const odooService = createOdooService({
    address: process.env.ODOO_ADDRESS || '',
    channelId: process.env.ODOO_CHANNEL_ID || '',
    channelLanguage: process.env.ODOO_CHANNEL_LANGUAGE || 'zh_CN',
    authSecret: process.env.ODOO_AUTH_SECRET || '',
    signMethod: process.env.ODOO_SIGN_METHOD || 'MD5',
});

export async function POST(request: NextRequest) {
    try {
        const rawBody = await request.text();
        console.log('Received Odoo webhook raw body:', rawBody);

        // 验证签名
        const signature = request.headers.get('X-Sign-Value');
        if (!odooService.verifySignature(rawBody, signature)) {
            console.error('Invalid signature');
            return NextResponse.json(
                {
                    code: 403,
                    msg: 'Invalid signature',
                    data: null
                },
                { status: 403 }
            );
        }

        const jsonData = JSON.parse(rawBody);
        console.log('Parsed Odoo webhook data:', jsonData);

        const action = jsonData.action;
        const data = jsonData.data;

        switch (action) {
            case 'accept_product_info_from_internal':
                // 处理产品信息推送
                await odooService.handleProductPush(data);
                break;
            case 'accept_order_status_from_internal':
                // 处理订单状态推送
                await odooService.handleOrderStatusPush(data);
                break;
            default:
                console.warn('Unknown action:', action);
                return NextResponse.json(
                    {
                        code: 400,
                        msg: 'Unknown action',
                        data: null
                    },
                    { status: 400 }
                );
        }

        return NextResponse.json({
            code: 200,
            msg: 'Success',
            data: null
        });
    } catch (error) {
        console.error('Error processing Odoo webhook:', error);
        return NextResponse.json(
            {
                code: 500,
                msg: 'Internal server error',
                data: null
            },
            { status: 500 }
        );
    }
} 