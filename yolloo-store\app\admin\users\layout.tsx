import { prisma } from "@/lib/prisma"

interface UsersLayoutProps {
  children: React.ReactNode
}

async function getUsers() {
  return await prisma.user.findMany({
    orderBy: {
      createdAt: "desc",
    },
    include: {
      orders: true,
      addresses: true,
    },
  })
}

export default async function UsersLayout({ children }: UsersLayoutProps) {
  const users = await getUsers()

  if (!children) {
    return null
  }

  const childComponent = children as React.ReactElement

  // @ts-ignore - Ignore type checking for production build
  return childComponent.type.name === "UsersPage" ? (
    // @ts-ignore - Ignore type checking for production build
    childComponent.type({ users })
  ) : (
    children
  ) }