"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Icons } from "@/components/icons"
import { toast } from "sonner"
import { DateFormatter } from "@/lib/utils"

type CommissionStatus = "PENDING" | "APPROVED" | "REJECTED" | "PAID"

const COMMISSION_STATUS_MAP = {
  PENDING: { label: "Pending", variant: "warning" },
  APPROVED: { label: "Approved", variant: "success" },
  REJECTED: { label: "Rejected", variant: "destructive" },
  PAID: { label: "Paid", variant: "default" },
} as const

interface Commission {
  id: string
  userId: string
  userName: string
  amount: number
  status: CommissionStatus
  createdAt: string
  updatedAt: string
  description: string
}

export default function CommissionsPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [commissions, setCommissions] = useState<Commission[]>([])
  const [searchTerm, setSearchTerm] = useState("")

  // Fetch commissions data
  async function fetchCommissions() {
    setIsLoading(true)
    try {
      const response = await fetch("/api/admin/commissions")
      if (!response.ok) {
        throw new Error("Failed to fetch commissions")
      }
      const data = await response.json()
      setCommissions(data)
    } catch (error) {
      console.error(error)
      toast.error("Failed to load commissions")
    } finally {
      setIsLoading(false)
    }
  }

  // Update commission status
  async function updateCommissionStatus(id: string, status: CommissionStatus) {
    try {
      const response = await fetch(`/api/admin/commissions/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      })

      if (!response.ok) {
        throw new Error("Failed to update commission status")
      }

      toast.success("Commission status updated successfully")
      fetchCommissions()
    } catch (error) {
      console.error(error)
      toast.error("Failed to update commission status")
    }
  }

  // Filter commissions based on search term
  const filteredCommissions = commissions.filter(
    (commission) =>
      commission.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      commission.id.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Commission Management</h1>
        <Button onClick={fetchCommissions} disabled={isLoading}>
          {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
          Refresh
        </Button>
      </div>

      <div className="flex items-center gap-4">
        <div className="flex-1">
          <Label htmlFor="search">Search</Label>
          <Input
            id="search"
            placeholder="Search by user name or commission ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCommissions.map((commission) => (
              <TableRow key={commission.id}>
                <TableCell className="font-mono">{commission.id}</TableCell>
                <TableCell>{commission.userName}</TableCell>
                <TableCell>${commission.amount.toFixed(2)}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      COMMISSION_STATUS_MAP[commission.status]?.variant ||
                      "secondary"
                    }
                  >
                    {COMMISSION_STATUS_MAP[commission.status]?.label ||
                      commission.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  {DateFormatter.withTimezone(commission.createdAt)}
                </TableCell>
                <TableCell>{commission.description}</TableCell>
                <TableCell>
                  <Select
                    defaultValue={commission.status}
                    onValueChange={(value) =>
                      updateCommissionStatus(commission.id, value as CommissionStatus)
                    }
                  >
                    <SelectTrigger className="w-[130px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
                      {Object.entries(COMMISSION_STATUS_MAP).map(
                        ([value, { label }]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                </TableCell>
              </TableRow>
            ))}
            {filteredCommissions.length === 0 && (
              <TableRow>
                <TableCell colSpan={7} className="text-center">
                  No commissions found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}