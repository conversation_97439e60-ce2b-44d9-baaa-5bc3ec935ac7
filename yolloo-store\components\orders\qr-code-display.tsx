"use client"

import { useState, useEffect, useRef } from "react"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"

import { Icons } from "@/components/icons"
import { QrC<PERSON>, Copy, Check } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import QRCodeLib from "qrcode"

interface QRCodeData {
  uid: string
  qrCodeContent: string
}

interface QRCodeResponse {
  customer_order_ref: string
  qrcode_url?: string
  qrcode: QRCodeData[]
}

interface QRCodeDisplayProps {
  orderId: string
  orderRef: string
  status: string
  isQrCodeProduct: boolean
}

export function QRCodeDisplay({ orderId, orderRef, status, isQrCodeProduct }: QRCodeDisplayProps) {
  const [qrCodes, setQrCodes] = useState<QRCodeData[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null)
  const [qrCodeImages, setQrCodeImages] = useState<Record<number, string>>({})
  const { toast } = useToast()

  // 只有当订单状态为已发货且是QR code商品时才显示
  const shouldShowQRCode = isQrCodeProduct && (status === "delivered" || status === "processing")

  // 验证LPA字符串格式
  const isValidLPA = (lpaString: string): boolean => {
    return lpaString && lpaString.trim() !== '' && lpaString.startsWith('LPA:')
  }

  // 生成QR码图片
  const generateQRCodeImage = async (lpaString: string, index: number) => {
    try {
      const qrCodeDataURL = await QRCodeLib.toDataURL(lpaString, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      })
      setQrCodeImages(prev => ({ ...prev, [index]: qrCodeDataURL }))
    } catch (error) {
      console.error('Error generating QR code:', error)
    }
  }

  const fetchQRCodes = async () => {
    if (!shouldShowQRCode) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/odoo/orders/qrcode', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customer_order_ref: orderRef
        })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch QR codes')
      }

      const data = await response.json()

      if (data?.result?.data?.[0]?.qrcode) {
        const qrCodeData = data.result.data[0].qrcode
        setQrCodes(qrCodeData)

        // 为每个有效的LPA字符串生成QR码图片
        qrCodeData.forEach((qrCode: QRCodeData, index: number) => {
          if (isValidLPA(qrCode.qrCodeContent)) {
            generateQRCodeImage(qrCode.qrCodeContent, index)
          }
        })
      } else {
        setError('No QR codes available yet')
      }
    } catch (err) {
      console.error('Error fetching QR codes:', err)
      setError('Failed to load QR codes')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchQRCodes()
  }, [shouldShowQRCode, orderRef])

  const copyToClipboard = async (content: string, index: number) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopiedIndex(index)
      toast({
        title: "Copied!",
        description: "LPA string copied to clipboard",
      })
      setTimeout(() => setCopiedIndex(null), 2000)
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Please copy the text manually",
        variant: "destructive"
      })
    }
  }

  const downloadQRCodeImage = (index: number) => {
    if (qrCodeImages[index]) {
      // 下载QR码图片
      const link = document.createElement('a')
      link.href = qrCodeImages[index]
      link.download = `esim-qrcode-${index + 1}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  if (!shouldShowQRCode) {
    return null
  }

  return (
    <Card>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <QrCode className="h-5 w-5 text-blue-600" />
            <h2 className="font-semibold">eSIM QR Codes</h2>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchQRCodes}
            disabled={loading}
          >
            {loading ? (
              <Icons.spinner className="h-4 w-4 animate-spin" />
            ) : (
              <Icons.refresh className="h-4 w-4" />
            )}
            Refresh
          </Button>
        </div>

        {loading && qrCodes.length === 0 && (
          <div className="flex items-center justify-center py-8">
            <Icons.spinner className="h-6 w-6 animate-spin mr-2" />
            <span>Loading QR codes...</span>
          </div>
        )}

        {error && (
          <div className="text-center py-8">
            <p className="text-muted-foreground">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchQRCodes}
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        )}

        {qrCodes.length > 0 && (
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Your eSIM activation codes are ready. Use it to activate your eSIM.
            </div>

            {qrCodes.map((qrCode, index) => {
              const hasValidLPA = isValidLPA(qrCode.qrCodeContent)
              const qrCodeImage = qrCodeImages[index]

              return (
                <div key={index} className="border rounded-lg p-4 space-y-4">
                  {/* QR码图片显示 */}
                  {hasValidLPA && qrCodeImage && (
                    <div className="flex justify-center">
                      <div className="bg-white p-4 rounded-lg border">
                        <img
                          src={qrCodeImage}
                          alt={`eSIM QR Code ${index + 1}`}
                          className="w-48 h-48"
                        />
                      </div>
                    </div>
                  )}

                  {/* 操作按钮 */}
                  <div className="flex justify-center gap-2 flex-wrap">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(qrCode.qrCodeContent, index)}
                      className="flex items-center gap-1"
                    >
                      {copiedIndex === index ? (
                        <Check className="h-4 w-4 text-green-600" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                      {copiedIndex === index ? 'Copied!' : 'Copy LPA'}
                    </Button>

                    {hasValidLPA && qrCodeImage && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadQRCodeImage(index)}
                      >
                        <QrCode className="h-4 w-4 mr-1" />
                        Download QR
                      </Button>
                    )}
                  </div>

                  {/* LPA字符串显示 */}
                  <div className="bg-gray-50 rounded p-3">
                    <p className="text-xs text-muted-foreground mb-1">LPA String:</p>
                    <p className="font-mono text-sm break-all select-all">
                      {qrCode.qrCodeContent}
                    </p>
                  </div>

                  {/* 激活说明 */}
                  <div className="text-xs text-muted-foreground">
                    <p>📱 To activate: Go to Settings → Cellular → Add eSIM → Use QR Code</p>
                  </div>

                  {/* 如果LPA格式无效，显示警告 */}
                  {!hasValidLPA && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
                      <p className="text-sm text-yellow-800">
                        ⚠️ Invalid LPA format. QR code cannot be generated.
                      </p>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        )}
      </div>
    </Card>
  )
}
