import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await req.json()
    const { items } = body

    if (!items || !Array.isArray(items)) {
      return new NextResponse("Invalid request body", { status: 400 })
    }

    // 获取所有商品的当前库存
    const products = await prisma.product.findMany({
      where: {
        id: {
          in: items.map(item => item.productId)
        }
      },
      select: {
        id: true,
        name: true,
        stock: true,
      }
    })

    // 检查每个商品的库存是否充足
    const stockErrors: string[] = []
    for (const item of items) {
      const product = products.find(p => p.id === item.productId)
      if (!product) {
        stockErrors.push(`Product ${item.productId} not found`)
        continue
      }
      if (product.stock < item.quantity) {
        stockErrors.push(`Insufficient stock for ${product.name}`)
      }
    }

    if (stockErrors.length > 0) {
      return new NextResponse(JSON.stringify({
        message: "Stock check failed",
        errors: stockErrors
      }), { 
        status: 400,
        headers: { "Content-Type": "application/json" }
      })
    }

    return new NextResponse(JSON.stringify({ success: true }), {
      headers: { "Content-Type": "application/json" }
    })
  } catch (error) {
    console.error("[STOCK_CHECK]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 