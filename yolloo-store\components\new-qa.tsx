'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'

const faqs = [
  {
    id: 1,
    question: "What is Yolloo eSIM?",
    answer: "Yolloo eSIM is a digital SIM card that allows you to connect to mobile networks without a physical SIM card. It's embedded directly into your device and can be activated instantly through a QR code."
  },
  {
    id: 2,
    question: "How does Yolloo eSIM work?",
    answer: "Simply purchase a data plan, receive a QR code via email, scan it with your device's camera, and you're connected! No need to visit stores or wait for physical SIM cards to arrive."
  },
  {
    id: 3,
    question: "Which devices support Yolloo eSIM?",
    answer: "Most modern smartphones and tablets support eSIM technology, including iPhone XS and newer, Google Pixel 3 and newer, Samsung Galaxy S20 and newer, and many other devices. Check your device compatibility on our website."
  },
  {
    id: 4,
    question: "Can I use Yolloo eSIM while traveling?",
    answer: "Absolutely! Yolloo eSIM is perfect for travelers. You can purchase local data plans for over 190 countries and regions, avoiding expensive roaming charges and staying connected wherever you go."
  }
]

export default function NewQA() {
  const [openItems, setOpenItems] = useState<number[]>([])

  const toggleItem = (id: number) => {
    setOpenItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  return (
    <section id="qa" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6" style={{
            background: 'linear-gradient(90deg, #B82E4E 0%, #F799A6 50%, #B82E4E 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            Got Questions? We've Got Answers
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Let's clear up the confusion—quick and easy.
          </p>
        </motion.div>

        {/* FAQ List - Vertical Layout */}
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {faqs.map((faq, index) => {
              const isOpen = openItems.includes(faq.id)
              return (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl border border-pink-200 hover:shadow-lg transition-shadow duration-300"
                >
                  <button
                    onClick={() => toggleItem(faq.id)}
                    className="w-full p-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                  >
                    <h3 className="text-lg font-semibold text-gray-900 pr-4">
                      {faq.question}
                    </h3>
                    <div className={`flex-shrink-0 w-8 h-8 flex items-center justify-center transition-transform duration-200 ${
                      isOpen ? 'rotate-180' : ''
                    }`}>
                      <span className="text-pink-600 text-2xl">
                        ▼
                      </span>
                    </div>
                  </button>

                  {isOpen && (
                    <div className="px-6 pb-6 animate-in slide-in-from-top-2 duration-300">
                      <div className="pt-2 border-t border-gray-100">
                        <p className="text-gray-600 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  )}
                </motion.div>
              )
            })}
          </div>
        </div>

        {/* Contact Link */}
        <div className="text-center mt-12">
          <p className="text-gray-600 mb-4">Still have questions?</p>
          <button className="text-pink-600 font-semibold hover:text-pink-700 transition-colors duration-200">
            Let us help you →
          </button>
        </div>
      </div>
    </section>
  )
}
