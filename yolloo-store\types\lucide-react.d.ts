declare module 'lucide-react' {
  import React from 'react';
  
  export interface LucideProps extends React.SVGAttributes<SVGElement> {
    color?: string;
    size?: string | number;
    strokeWidth?: string | number;
  }
  
  export type LucideIcon = React.FC<LucideProps>;
  
  // 导出所有图标组件
  export const AlertTriangle: LucideIcon;
  export const AlertCircle: LucideIcon;
  export const ArrowRight: LucideIcon;
  export const ArrowUp: LucideIcon;
  export const Check: LucideIcon;
  export const PowerOff: LucideIcon;
  export const Command: LucideIcon;
  export const CreditCard: LucideIcon;
  export const Copy: LucideIcon;
  export const File: LucideIcon;
  export const FileText: LucideIcon;
  export const Globe: LucideIcon;
  export const HelpCircle: LucideIcon;
  export const Image: LucideIcon;
  export const Laptop: LucideIcon;
  export const Loader2: LucideIcon;
  export const LogOut: LucideIcon;
  export const Mail: LucideIcon;
  export const Moon: LucideIcon;
  export const MoreVertical: LucideIcon;
  export const Network: LucideIcon;
  export const Package: LucideIcon;
  export const Pizza: LucideIcon;
  export const Plus: LucideIcon;
  export const RefreshCw: LucideIcon;
  export const Settings: LucideIcon;
  export const Shield: LucideIcon;
  export const ShoppingCart: LucideIcon;
  export const Signal: LucideIcon;
  export const SunMedium: LucideIcon;
  export const BarChart: LucideIcon;
  export const Wifi: LucideIcon;
  export const Calendar: LucideIcon;
  export const Trash: LucideIcon;
  export const User: LucideIcon;
  export const X: LucideIcon;
  export const Zap: LucideIcon;
  export const Minus: LucideIcon;
  export const Pencil: LucideIcon;
  export const Download: LucideIcon;
  export const RotateCwIcon: LucideIcon;
  export const Truck: LucideIcon;
  export const Eye: LucideIcon;
  export const EyeOff: LucideIcon;
  export const Edit: LucideIcon;
  export const RotateCcw: LucideIcon;
  export const HeadphonesIcon: LucideIcon;
  export const ShieldCheck: LucideIcon;
  export const ExternalLink: LucideIcon;
  export const Upload: LucideIcon;
  export const Plane: LucideIcon;
  export const QrCode: LucideIcon;
  export const MessageCircle: LucideIcon;
  export const Menu: LucideIcon;
  export const Star: LucideIcon;
  export const ArrowUp: LucideIcon;
  export const Clock: LucideIcon;
  export const Wallet: LucideIcon;
  export const Smartphone: LucideIcon;
  export const Circle: LucideIcon;
  export const MoreHorizontal: LucideIcon;
  export const ShoppingCartIcon: LucideIcon;
  export const SearchIcon: LucideIcon;
  export const Tag: LucideIcon;
  export const Layers: LucideIcon;
  export const Store: LucideIcon;
  export const ChevronDown: LucideIcon;
  export const ChevronUp: LucideIcon;
  export const ChevronLeft: LucideIcon;
  export const ChevronRight: LucideIcon;
  export const ChevronsLeft: LucideIcon;
  export const ChevronsRight: LucideIcon;
  export const CheckCircle: LucideIcon;
  export const XCircle: LucideIcon;
  export const UserPlus: LucideIcon;
  export const HomeIcon: LucideIcon;
  export const Folder: LucideIcon;
} 