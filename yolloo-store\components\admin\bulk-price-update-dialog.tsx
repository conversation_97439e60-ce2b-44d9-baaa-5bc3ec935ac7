"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Icons } from "@/components/icons"
import { toast } from "sonner"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"

interface Category {
  id: string
  name: string
}

interface BulkPriceUpdateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  categories: Category[]
  onSuccess: () => void
}

export function BulkPriceUpdateDialog({
  open,
  onOpenChange,
  categories,
  onSuccess
}: BulkPriceUpdateDialogProps) {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [adjustmentType, setAdjustmentType] = useState<'increase' | 'decrease'>('increase')
  const [adjustmentPercentage, setAdjustmentPercentage] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async () => {
    // 验证输入
    if (selectedCategories.length === 0) {
      toast.error("Please select at least one category")
      return
    }

    const percentage = parseFloat(adjustmentPercentage)
    if (isNaN(percentage) || percentage <= 0 || percentage > 100) {
      toast.error("Please enter a valid percentage between 1 and 100")
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/admin/products/bulk-price-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          categoryIds: selectedCategories,
          adjustmentType,
          adjustmentPercentage: percentage,
        }),
      })

      if (!response.ok) {
        const errorData = await response.text()
        throw new Error(errorData || 'Failed to update prices')
      }

      const result = await response.json()
      
      toast.success(
        `Successfully updated ${result.updatedCount} products. ` +
        `Prices ${adjustmentType === 'increase' ? 'increased' : 'decreased'} by ${percentage}%.`
      )

      // 重置表单
      setSelectedCategories([])
      setAdjustmentType('increase')
      setAdjustmentPercentage('')
      
      // 关闭对话框并刷新数据
      onOpenChange(false)
      onSuccess()

    } catch (error) {
      console.error('Error updating prices:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update prices')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev => 
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    )
  }

  const handleSelectAll = () => {
    if (selectedCategories.length === categories.length) {
      setSelectedCategories([])
    } else {
      setSelectedCategories(categories.map(cat => cat.id))
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Icons.dollarSign className="mr-2 h-5 w-5" />
            Bulk Price Update
          </DialogTitle>
          <DialogDescription>
            Adjust prices for products in selected categories by a percentage.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* 分类选择 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Select Categories</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSelectAll}
                className="text-xs"
              >
                {selectedCategories.length === categories.length ? 'Deselect All' : 'Select All'}
              </Button>
            </div>
            
            <div className="max-h-40 overflow-y-auto border rounded-md p-3 space-y-2">
              {categories.map((category) => (
                <div key={category.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={category.id}
                    checked={selectedCategories.includes(category.id)}
                    onCheckedChange={() => handleCategoryToggle(category.id)}
                  />
                  <Label
                    htmlFor={category.id}
                    className="text-sm font-normal cursor-pointer flex-1"
                  >
                    {category.name}
                  </Label>
                </div>
              ))}
            </div>

            {selectedCategories.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {selectedCategories.map(categoryId => {
                  const category = categories.find(cat => cat.id === categoryId)
                  return category ? (
                    <Badge key={categoryId} variant="secondary" className="text-xs">
                      {category.name}
                    </Badge>
                  ) : null
                })}
              </div>
            )}
          </div>

          {/* 调价类型 */}
          <div className="space-y-2">
            <Label htmlFor="adjustmentType">Adjustment Type</Label>
            <Select
              value={adjustmentType}
              onValueChange={(value: 'increase' | 'decrease') => setAdjustmentType(value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="increase">
                  <div className="flex items-center">
                    <Icons.trendingUp className="mr-2 h-4 w-4 text-green-600" />
                    Increase Price
                  </div>
                </SelectItem>
                <SelectItem value="decrease">
                  <div className="flex items-center">
                    <Icons.trendingDown className="mr-2 h-4 w-4 text-red-600" />
                    Decrease Price
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 调价百分比 */}
          <div className="space-y-2">
            <Label htmlFor="percentage">Adjustment Percentage</Label>
            <div className="relative">
              <Input
                id="percentage"
                type="number"
                min="1"
                max="100"
                step="0.1"
                placeholder="Enter percentage (1-100)"
                value={adjustmentPercentage}
                onChange={(e) => setAdjustmentPercentage(e.target.value)}
                className="pr-8"
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                %
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              Example: Enter 30 to {adjustmentType === 'increase' ? 'increase' : 'decrease'} prices by 30%
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading || selectedCategories.length === 0 || !adjustmentPercentage}
            className={adjustmentType === 'increase' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
          >
            {isLoading ? (
              <>
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <Icons.dollarSign className="mr-2 h-4 w-4" />
                Update Prices
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
