#!/bin/bash

echo "🚀 Starting Yolloo Store in TESTING environment..."

# 停止可能运行的其他环境
echo "📦 Stopping other environments..."
docker compose -f docker-compose.development.yml down 2>/dev/null || true
docker compose -f docker-compose.production.yml down 2>/dev/null || true

# 启动测试环境
echo "🔧 Starting testing environment..."
docker compose -f docker-compose.testing.yml up -d

echo "✅ Testing environment started!"
echo "📊 App: http://localhost:8000"
echo "📱 Mobile API: http://localhost:4000"
echo ""
echo "📋 To view logs: docker compose -f docker-compose.testing.yml logs -f"
echo "🛑 To stop: docker compose -f docker-compose.testing.yml down"
