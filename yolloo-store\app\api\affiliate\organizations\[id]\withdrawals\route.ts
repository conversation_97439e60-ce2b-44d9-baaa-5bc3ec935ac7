import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for creating a withdrawal request
const createWithdrawalSchema = z.object({
  amount: z.number().positive(),
  paymentMethod: z.string(),
  paymentDetails: z.record(z.any()).optional(),
});

// Helper function to check if user has admin access to the organization
async function hasAdminAccess(userId: string, organizationId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { 
      role: true,
      affiliate: {
        select: {
          id: true,
          organizationId: true,
          isAdmin: true,
        },
      },
    },
  });

  if (!user) return false;
  
  // System admin has access to all organizations
  if (user.role === "ADMIN") return true;
  
  // Organization admin has access to their organization
  if (user.affiliate?.organizationId === organizationId && user.affiliate.isAdmin) {
    return true;
  }
  
  return false;
}

// GET - List organization withdrawals
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const organizationId = params.id;
    
    // Check if user has admin access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);
    
    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    // Get organization withdrawals
    const withdrawals = await prisma.organizationWithdrawal.findMany({
      where: { organizationId },
      orderBy: { createdAt: "desc" },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });
    
    return NextResponse.json(withdrawals);
  } catch (error) {
    console.error("Error fetching organization withdrawals:", error);
    return NextResponse.json(
      { error: "Failed to fetch organization withdrawals" },
      { status: 500 }
    );
  }
}

// POST - Create a withdrawal request
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const organizationId = params.id;
    
    // Check if user has admin access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);
    
    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validationResult = createWithdrawalSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { amount, paymentMethod, paymentDetails } = validationResult.data;
    
    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Check if organization has enough earnings
    if (organization.totalEarnings < amount) {
      return NextResponse.json(
        { error: "Insufficient earnings for withdrawal" },
        { status: 400 }
      );
    }
    
    // Create the withdrawal request
    const withdrawal = await prisma.organizationWithdrawal.create({
      data: {
        organizationId,
        amount,
        paymentMethod,
        paymentDetails,
      },
    });
    
    // Update organization's total earnings
    await prisma.affiliateOrganization.update({
      where: { id: organizationId },
      data: {
        totalEarnings: {
          decrement: amount,
        },
      },
    });
    
    return NextResponse.json(withdrawal);
  } catch (error) {
    console.error("Error creating withdrawal request:", error);
    return NextResponse.json(
      { error: "Failed to create withdrawal request" },
      { status: 500 }
    );
  }
} 