-- 删除旧的唯一约束
DROP INDEX IF EXISTS "OdooOrderStatus_orderId_key";
ALTER TABLE "OdooOrderStatus" DROP CONSTRAINT IF EXISTS "OdooOrderStatus_orderId_variantCode_key";

-- 添加列（如果不存在）
ALTER TABLE "OdooOrderStatus" ADD COLUMN IF NOT EXISTS "variantCode" TEXT DEFAULT 'default';
ALTER TABLE "OdooOrderStatus" ADD COLUMN IF NOT EXISTS "odooOrderRef" TEXT;

-- 更新所有现有记录，确保没有 NULL 值
UPDATE "OdooOrderStatus" SET "variantCode" = 'default' WHERE "variantCode" IS NULL;

-- 现在可以安全地将列设置为 NOT NULL
ALTER TABLE "OdooOrderStatus" ALTER COLUMN "variantCode" SET NOT NULL;

-- 添加复合索引以提高查询性能
DROP INDEX IF EXISTS "OdooOrderStatus_orderId_variantCode_uid_idx";
CREATE INDEX "OdooOrderStatus_orderId_variantCode_uid_idx" ON "OdooOrderStatus"("orderId", "variantCode", "uid");

-- 添加单独的索引以提高查询性能
CREATE INDEX IF NOT EXISTS "OdooOrderStatus_variantCode_idx" ON "OdooOrderStatus"("variantCode");
CREATE INDEX IF NOT EXISTS "OdooOrderStatus_uid_idx" ON "OdooOrderStatus"("uid");

-- 添加注释到 OdooOrderStatus 表的 variantCode 和 uid 字段
COMMENT ON COLUMN "OdooOrderStatus"."variantCode" IS 'UUID format like "d5855c2f-0340-4744-95f2-63b10faa2b09"';
COMMENT ON COLUMN "OdooOrderStatus"."uid" IS 'Numeric string format like "11111111111111111111"';
