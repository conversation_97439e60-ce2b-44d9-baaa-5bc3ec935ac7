'use client';

import { Badge } from "@/components/ui/badge";
import { packageStatusMap, orderSourceMap, formatDateTime, formatDataUsage } from "@/lib/boss-utils";

interface PackageDetailProps {
  packageData: any;
}

export function PackageDetail({ packageData }: PackageDetailProps) {
  if (!packageData) {
    return <div className="py-4 text-center text-gray-500">No package selected</div>;
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h3 className="text-sm font-medium">Order Number</h3>
          <p className="text-sm">{packageData.orderSn || '-'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Package Status</h3>
          <div>
            {packageData.packageStatus !== null && packageData.packageStatus !== undefined ? (
              <Badge variant={packageStatusMap[packageData.packageStatus]?.variant || 'default'}>
                {packageStatusMap[packageData.packageStatus]?.label || `Status: ${packageData.packageStatus}`}
              </Badge>
            ) : '-'}
          </div>
        </div>
        <div>
          <h3 className="text-sm font-medium">User UID</h3>
          <p className="text-sm">{packageData.uid || '-'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Currently Active</h3>
          <p className="text-sm">{packageData.whetherCurrent ? 'Yes' : 'No'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">eSIM Profile Status</h3>
          <p className="text-sm">{packageData.esimProfileStatus || '-'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Order Source</h3>
          <p className="text-sm">
            {packageData.orderSource ? orderSourceMap[packageData.orderSource] || packageData.orderSource : '-'}
          </p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Data Usage</h3>
          <p className="text-sm">{formatDataUsage(packageData.usageBytes)}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Priority</h3>
          <p className="text-sm">{packageData.uidSort || '-'}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Start Time</h3>
          <p className="text-sm">{formatDateTime(packageData.planStartTime)}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">End Time</h3>
          <p className="text-sm">{formatDateTime(packageData.planEndTime)}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Expiry Time</h3>
          <p className="text-sm">{formatDateTime(packageData.planExpireTime)}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">ID</h3>
          <p className="text-sm text-xs">{packageData.id || '-'}</p>
        </div>
      </div>
    </div>
  );
}
