import { NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import Redis from 'ioredis';
import { sendWelcomeSubscriptionEmail } from "@/app/services/emailService";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Create Redis client using URL
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// Cache key prefix
const CACHE_PREFIX = process.env.CACHE_KEY || 'yolloo-cache';

// Rate limit implementation
async function checkRateLimit(ip: string): Promise<boolean> {
  const key = `${CACHE_PREFIX}:ratelimit:${ip}`;
  const limit = 5; // 5 requests
  const window = 60; // 60 seconds

  try {
    // Get current count
    const current = await redis.get(key);
    const count = current ? parseInt(current) : 0;

    if (count >= limit) {
      return false;
    }

    // Increment count and set expiry
    if (count === 0) {
      await redis.setex(key, window, '1');
    } else {
      await redis.incr(key);
    }

    return true;
  } catch (error) {
    console.error('Rate limit error:', error);
    return true; // Allow request if Redis fails
  }
}

// Input validation schema
const subscribeSchema = z.object({
  email: z.string().email('Invalid email address'),
  referralCode: z.string().optional(),
});

export async function POST(req: Request) {
  try {
    // Get IP for rate limiting
    const ip = req.headers.get('x-forwarded-for') || 'anonymous';
    
    // Check rate limit
    const allowed = await checkRateLimit(ip);
    if (!allowed) {
      return NextResponse.json(
        { message: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const result = subscribeSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { message: 'Invalid input data' },
        { status: 400 }
      );
    }

    const { email, referralCode } = result.data;

    // Check if email already exists
    const existingSubscription = await prisma.preSaleSubscription.findUnique({
      where: { email },
    });

    if (existingSubscription) {
      return NextResponse.json(
        { message: 'This email is already subscribed' },
        { status: 400 }
      );
    }

    // Generate unique discount code
    const discountCode = generateDiscountCode();

    // Create subscription record
    const subscription = await prisma.preSaleSubscription.create({
      data: {
        email,
        referralCode,
        discountCode,
        status: 'PENDING',
        ipAddress: ip,
        userAgent: req.headers.get('user-agent') || undefined,
      },
    });

    // Send welcome email with discount code
    await sendWelcomeSubscriptionEmail(email, discountCode);

    return NextResponse.json(
      { message: 'Subscription successful', discountCode },
      { status: 201 }
    );
  } catch (error) {
    console.error('Subscription error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

function generateDiscountCode(): string {
  const prefix = 'YOLLOO';
  const randomPart = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `${prefix}${randomPart}`;
} 