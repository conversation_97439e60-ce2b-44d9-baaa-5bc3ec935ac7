"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import axios from "axios";
import { Loader2, MoreHorizontal, X, <PERSON>fresh<PERSON><PERSON>, <PERSON>r, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Icons } from "@/components/icons";
import { OrganizationIcons } from "@/components/organization-icons";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { format } from "date-fns"
import { DateFormatter } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Clock } from "lucide-react";
import { InviteLinkButton } from "./invite-link-button";

// Define the form schema for updating an organization
const updateOrgSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters").optional(),
  description: z.string().optional(),
  logo: z.string().optional(),
  commissionRate: z.number().min(0).max(1).optional(),
  discountRate: z.number().min(0).max(1).optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED"]).optional(),
});

type UpdateOrgFormValues = z.infer<typeof updateOrgSchema>;

// Define types for the organization data
interface Organization {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  code: string;
  commissionRate: number;
  discountRate: number;
  totalEarnings: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  _count: {
    members: number;
    invites: number;
    withdrawals: number;
  };
  monthlyEarnings?: {
    month: string;
    year: number;
    amount: number;
    totalCommissions: number;
    memberCommissions: number;
  }[];
}

interface Member {
  id: string;
  code: string;
  commissionRate: number;
  totalEarnings: number;
  isAdmin: boolean;
  user: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
}

interface Invite {
  id: string;
  email: string;
  status: string;
  inviteCode: string;
  expiresAt: string;
  createdAt: string;
  affiliate?: {
    id: string;
    user: {
      id: string;
      name: string;
      email: string;
      image: string;
    };
  };
}

interface Stats {
  totalVisits: number;
  totalConversions: number;
  conversionRate: number;
  totalCommissions: number;
  memberCommissions?: number;
  organizationActualEarnings?: number;
}

export default function AdminOrganizationDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [members, setMembers] = useState<Member[]>([]);
  const [invites, setInvites] = useState<Invite[]>([]);
  const [stats, setStats] = useState<Stats | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // 添加成员管理相关状态
  const [isAddMemberDialogOpen, setIsAddMemberDialogOpen] = useState(false);
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [editMember, setEditMember] = useState<Member | null>(null);
  const [isEditMemberDialogOpen, setIsEditMemberDialogOpen] = useState(false);
  const [isEditingMember, setIsEditingMember] = useState(false);
  const [isDeletingMember, setIsDeletingMember] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<Member | null>(null);
  const [memberEmail, setMemberEmail] = useState("");
  const [memberCommissionRate, setMemberCommissionRate] = useState(0.1);
  const [memberIsAdmin, setMemberIsAdmin] = useState(false);
  const [searchUsers, setSearchUsers] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [selectedUser, setSelectedUser] = useState<any | null>(null);
  const [isSearching, setIsSearching] = useState(false);

  // Batch operations state
  const [isBatchUserDialogOpen, setIsBatchUserDialogOpen] = useState(false);
  const [isBatchMemberDialogOpen, setIsBatchMemberDialogOpen] = useState(false);
  const [batchUsersCsv, setBatchUsersCsv] = useState("");
  const [batchMembersCsv, setBatchMembersCsv] = useState("");
  const [isProcessingBatchUsers, setIsProcessingBatchUsers] = useState(false);
  const [isProcessingBatchMembers, setIsProcessingBatchMembers] = useState(false);
  const [batchUsersResult, setBatchUsersResult] = useState<any>(null);
  const [batchMembersResult, setBatchMembersResult] = useState<any>(null);
  const [passwordResetLinks, setPasswordResetLinks] = useState<{ userId: string; email: string; resetLink: string; token: string }[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<{ id: string; name: string; email: string; image?: string }[]>([]);
  // 添加下拉菜单打开状态控制
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // 添加分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 在状态定义部分添加新的状态变量
  const [selectedMembersToDelete, setSelectedMembersToDelete] = useState<Member[]>([]);
  const [isDeleteMembersDialogOpen, setIsDeleteMembersDialogOpen] = useState(false);
  const [isDeletingBatchMembers, setIsDeletingBatchMembers] = useState(false);

  // 添加新的状态变量
  const [viewMember, setViewMember] = useState<Member | null>(null);
  const [isViewMemberDialogOpen, setIsViewMemberDialogOpen] = useState(false);

  // 添加邀请相关状态
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteByEmail, setInviteByEmail] = useState(false);
  const [inviteLink, setInviteLink] = useState<string | null>(null);

  // 在组件状态定义部分添加新的状态
  const [memberOrders, setMemberOrders] = useState<any[]>([]);

  // 在组件状态部分添加
  const [isRefreshingStats, setIsRefreshingStats] = useState(false);

  const { register, handleSubmit, formState: { errors }, reset, setValue } = useForm<UpdateOrgFormValues>({
    resolver: zodResolver(updateOrgSchema),
  });

  // Fetch organization details on component mount
  useEffect(() => {
    const fetchOrganizationDetails = async () => {
      try {
        setLoading(true);

        // Fetch organization details
        const orgResponse = await axios.get(`/api/admin/organizations/${params.id}`);
        console.log("Organization API response:", orgResponse.data);

        // Set organization data from API response
        setOrganization(orgResponse.data);

        // If API returns additionalStats, use them
        if (orgResponse.data.additionalStats) {
          setStats({
            totalVisits: orgResponse.data._count?.visits || 0,
            totalConversions: 0,
            conversionRate: 0,
            totalCommissions: orgResponse.data.additionalStats.totalCommissions || 0,
            memberCommissions: orgResponse.data.additionalStats.memberCommissions || 0,
            organizationActualEarnings: orgResponse.data.additionalStats.organizationActualEarnings || 0
          });
        }

        // Fetch members
        try {
        const membersResponse = await axios.get(`/api/admin/organizations/${params.id}/members`);
          setMembers(membersResponse.data || []);
        } catch (memberError) {
          console.error("Error fetching members:", memberError);
          setMembers([]);
        }

        // Fetch invites - this endpoint might not exist yet
        try {
        const invitesResponse = await axios.get(`/api/admin/organizations/${params.id}/invites`);
          setInvites(invitesResponse.data || []);
        } catch (inviteError) {
          console.error("Error fetching invites:", inviteError);
          setInvites([]);
        }

        // Always fetch analytics and member orders to ensure we have up-to-date data
        try {
          const analyticsResponse = await axios.get(`/api/admin/organizations/${params.id}/analytics`);
          if (analyticsResponse.data) {
            // 更新统计数据
            if (analyticsResponse.data.stats) {
              setStats(analyticsResponse.data.stats);
            }

            // 更新组织数据（包括月度收益）
            if (analyticsResponse.data.organization) {
              // 确保保留原有组织数据，但更新从API获取的新数据
              setOrganization(prev => ({
                ...prev,
                ...analyticsResponse.data.organization
              }));
            }

            // 更新成员订单记录
            if (analyticsResponse.data.memberOrders) {
              setMemberOrders(analyticsResponse.data.memberOrders);
            }

            // Check if we need to refresh the stats to get all data
            if (analyticsResponse.data.needsRefresh) {
              console.log("Data needs refresh - automatically refreshing stats");
              await handleRefreshStats();
            }
          } else {
            // If no stats available yet, do an initial refresh to ensure we have data
            await handleRefreshStats();
          }
        } catch (analyticsError) {
          console.error("Error fetching analytics:", analyticsError);
          setStats({
            totalVisits: 0,
            totalConversions: 0,
            conversionRate: 0,
            totalCommissions: 0,
            memberCommissions: 0,
            organizationActualEarnings: 0
          });

          // If analytics fetch fails, try refreshing stats to build up the data
          try {
            await handleRefreshStats();
          } catch (refreshError) {
            console.error("Failed to refresh stats after analytics error:", refreshError);
          }
        }

        // Set form defaults
        if (orgResponse.data) {
        reset({
            name: orgResponse.data.name || "",
          description: orgResponse.data.description || "",
          logo: orgResponse.data.logo || "",
            commissionRate: orgResponse.data.commissionRate || 0.1,
            discountRate: orgResponse.data.discountRate || 0.05,
            status: orgResponse.data.status || "ACTIVE",
          });
        }
      } catch (error) {
        console.error("Error fetching organization details:", error);
        toast.error("Failed to fetch organization details");
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizationDetails();
  }, [params.id, reset]);

  // Handle organization update
  const onUpdateOrganization = async (data: UpdateOrgFormValues) => {
    try {
      setIsUpdating(true);
      const response = await axios.patch(`/api/admin/organizations/${params.id}`, data);
      setOrganization(response.data);
      toast.success("Organization updated successfully");
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error("Error updating organization:", error);
      toast.error("Failed to update organization");
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle organization deletion
  const onDeleteOrganization = async () => {
    try {
      setIsDeleting(true);
      await axios.delete(`/api/admin/organizations/${params.id}`);
      toast.success("Organization deleted successfully");
      router.push("/admin/organizations");
    } catch (error) {
      console.error("Error deleting organization:", error);
      toast.error("Failed to delete organization");
      setIsDeleting(false);
    }
  };

  // Get status badge for organization
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Active</Badge>;
      case "INACTIVE":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Inactive</Badge>;
      case "SUSPENDED":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // 添加获取成员列表的函数
  const fetchMembers = async () => {
    try {
      const membersResponse = await axios.get(`/api/admin/organizations/${params.id}/members`);
      setMembers(membersResponse.data || []);
    } catch (error) {
      console.error("Error fetching members:", error);
      toast.error("Failed to fetch members");
    }
  };

  // 修改添加成员的函数
  const handleAddMember = async () => {
    try {
      setIsAddingMember(true);

      let response;

      if (inviteByEmail) {
        if (!inviteEmail) {
          toast.error("Please enter an email address");
          setIsAddingMember(false);
          return;
        }

        // Send invitation by email
        response = await axios.post(`/api/admin/organizations/${params.id}/members`, {
          email: inviteEmail,
          commissionRate: memberCommissionRate,
          isAdmin: memberIsAdmin
        });

        // Get the base URL for the invite link
        const baseUrl = window.location.origin;
        const inviteUrl = `${baseUrl}/invite/${response.data.invite.inviteCode}`;
        setInviteLink(inviteUrl);

        toast.success("Invitation sent successfully");
      } else {
        if (!selectedUser) {
          toast.error("Please select a user");
          setIsAddingMember(false);
          return;
        }

        // Add existing user directly
        response = await axios.post(`/api/admin/organizations/${params.id}/members`, {
          userId: selectedUser.id,
          commissionRate: memberCommissionRate,
          isAdmin: memberIsAdmin
        });

        // 添加成功后重新获取成员列表，而不是直接更新状态
        await fetchMembers();
        toast.success("Member added successfully");

        // 重置表单和关闭对话框
        setSelectedUser(null);
        setMemberCommissionRate(0.1);
        setMemberIsAdmin(false);
        setSearchUsers("");
        setSearchResults([]);
        setIsAddMemberDialogOpen(false);
      }
    } catch (error) {
      console.error("Error adding member:", error);
      toast.error("Failed to add member");
    } finally {
      setIsAddingMember(false);
    }
  };

  const handleEditMember = async () => {
    if (!editMember) return;

    try {
      setIsEditingMember(true);
      const response = await axios.patch(`/api/admin/organizations/${params.id}/members/${editMember.id}`, {
        commissionRate: memberCommissionRate,
        isAdmin: memberIsAdmin
      });

      // 更新成员后重新获取成员列表
      await fetchMembers();
      toast.success("Member updated successfully");

      // 关闭对话框
      setEditMember(null);
      setIsEditMemberDialogOpen(false);
    } catch (error) {
      console.error("Error updating member:", error);
      toast.error("Failed to update member");
    } finally {
      setIsEditingMember(false);
    }
  };

  const handleDeleteMember = async () => {
    if (!memberToDelete) return;

    try {
      setIsDeletingMember(true);
      await axios.delete(`/api/admin/organizations/${params.id}/members/${memberToDelete.id}`);

      // 删除成员后重新获取成员列表
      await fetchMembers();
      toast.success("Member removed successfully");

      // 关闭对话框
      setMemberToDelete(null);
    } catch (error) {
      console.error("Error removing member:", error);
      toast.error("Failed to remove member");
    } finally {
      setIsDeletingMember(false);
    }
  };

  const searchForUsers = async (query: string) => {
    if (!query || query.length < 1) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      const response = await axios.get(`/api/admin/users/search?query=${query}`);
      setSearchResults(response.data || []);
    } catch (error) {
      console.error("Error searching users:", error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const openEditMemberDialog = (member: Member) => {
    setEditMember(member);
    setMemberCommissionRate(member.commissionRate);
    setMemberIsAdmin(member.isAdmin);
    setIsEditMemberDialogOpen(true);
  };

  // 处理用户搜索的防抖
  useEffect(() => {
    const timer = setTimeout(() => {
      searchForUsers(searchUsers);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchUsers]);

  // Batch user creation
  const handleBatchUserCreation = async () => {
    if (!batchUsersCsv.trim()) {
      toast.error("Please enter user data");
      return;
    }

    try {
      setIsProcessingBatchUsers(true);
      setBatchUsersResult(null);

      // Parse CSV data
      const lines = batchUsersCsv.trim().split('\n');
      const headers = lines[0].split(',').map(h => h.trim().toLowerCase());

      const nameIndex = headers.indexOf('name');
      const emailIndex = headers.indexOf('email');
      const roleIndex = headers.indexOf('role');

      if (nameIndex === -1 || emailIndex === -1) {
        toast.error("CSV must include 'name' and 'email' columns");
        return;
      }

      const users = lines.slice(1).map(line => {
        const values = line.split(',').map(v => v.trim());
        return {
          name: values[nameIndex],
          email: values[emailIndex],
          role: roleIndex !== -1 ? values[roleIndex] : "CUSTOMER", // Default to CUSTOMER if role not provided
        };
      });

      // Filter out invalid entries
      const validUsers = users.filter(user => user.name && user.email);

      if (validUsers.length === 0) {
        toast.error("No valid users found in CSV");
        return;
      }

      // Send to API
      const response = await axios.post('/api/admin/users/batch', validUsers);
      setBatchUsersResult(response.data);

      // Store the reset links
      setPasswordResetLinks(response.data.resetLinks || []);

      toast.success(`Successfully created ${response.data.users.length} users with password reset links`);

      // Now add these users as members to the current organization
      if (response.data.users.length > 0) {
        try {
          // Prepare member data for batch addition
          const membersToAdd = response.data.users.map(user => ({
            userId: user.id,
            commissionRate: memberCommissionRate || 0.1, // Use the current commission rate or default to 0.1
            isAdmin: false // Default to regular member, not admin
          }));

          // Add the newly created users as members
          const memberResponse = await axios.post(`/api/admin/organizations/${params.id}/members/batch`, membersToAdd);

          // 批量添加成员后重新获取成员列表
          await fetchMembers();

          toast.success(`Added ${memberResponse.data.totalAdded} users as members to the organization`);

          if (memberResponse.data.totalFailed > 0) {
            toast.error(`Failed to add ${memberResponse.data.totalFailed} users as members`);
          }
        } catch (memberError) {
          console.error("Error adding batch members:", memberError);
          toast.error("Users were created but could not be added as members");
        }
      }

      // Don't clear form or close dialog so admin can see the reset links

    } catch (error: any) {
      console.error("Error creating batch users:", error);
      if (error.response?.data?.existingEmails) {
        toast.error(`Some emails already exist: ${error.response.data.existingEmails.join(', ')}`);
      } else {
        toast.error("Failed to create users");
      }
    } finally {
      setIsProcessingBatchUsers(false);
    }
  };

  // Batch member addition
  const handleBatchMemberAddition = async () => {
    if (selectedUsers.length === 0) {
      toast.error("Please select users to add");
      return;
    }

    try {
      setIsProcessingBatchMembers(true);
      setBatchMembersResult(null);

      // Prepare data
      const membersToAdd = selectedUsers.map(user => ({
        userId: user.id,
        commissionRate: memberCommissionRate,
        isAdmin: memberIsAdmin
      }));

      // Send to API
      const response = await axios.post(`/api/admin/organizations/${params.id}/members/batch`, membersToAdd);

      // 批量添加成员后重新获取成员列表，而不是直接更新状态
      await fetchMembers();

      setBatchMembersResult(response.data);
      toast.success(`Successfully added ${response.data.totalAdded} members`);

      // Clear selection
      setSelectedUsers([]);
      setIsBatchMemberDialogOpen(false);
    } catch (error) {
      console.error("Error adding batch members:", error);
      toast.error("Failed to add members");
    } finally {
      setIsProcessingBatchMembers(false);
    }
  };

  // Toggle user selection for batch member addition
  const toggleUserSelection = (user: any) => {
    if (selectedUsers.some(u => u.id === user.id)) {
      setSelectedUsers(prev => prev.filter(u => u.id !== user.id));
    } else {
      setSelectedUsers(prev => [...prev, user]);
    }
  };

  // 处理批量用户对话框打开
  const handleOpenBatchUserDialog = () => {
    setIsBatchUserDialogOpen(true);
    setIsDropdownOpen(false); // 关闭下拉菜单
  };

  // 处理批量成员对话框打开
  const handleOpenBatchMemberDialog = () => {
    setIsBatchMemberDialogOpen(true);
    setIsDropdownOpen(false); // 关闭下拉菜单
  };

  // 计算总页数
  const totalPages = Math.ceil(members.length / pageSize);

  // 获取当前页的成员
  const getCurrentPageMembers = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return members.slice(startIndex, endIndex);
  };

  // 页码变更处理
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 添加批量删除成员的函数
  const handleBatchDeleteMembers = async () => {
    if (selectedMembersToDelete.length === 0) {
      toast.error("Please select members to delete");
      return;
    }

    try {
      setIsDeletingBatchMembers(true);

      // 遍历删除所选成员
      const deletePromises = selectedMembersToDelete.map(member =>
        axios.delete(`/api/admin/organizations/${params.id}/members/${member.id}`)
      );

      await Promise.all(deletePromises);

      // 删除成功后重新获取成员列表
      await fetchMembers();
      toast.success(`Successfully removed ${selectedMembersToDelete.length} members`);

      // 重置选择状态并关闭对话框
      setSelectedMembersToDelete([]);
      setIsDeleteMembersDialogOpen(false);
    } catch (error) {
      console.error("Error removing members:", error);
      toast.error("Failed to remove members");
    } finally {
      setIsDeletingBatchMembers(false);
    }
  };

  // 切换选择成员的函数
  const toggleMemberSelection = (member: Member) => {
    if (selectedMembersToDelete.some(m => m.id === member.id)) {
      setSelectedMembersToDelete(prev => prev.filter(m => m.id !== member.id));
    } else {
      setSelectedMembersToDelete(prev => [...prev, member]);
    }
  };

  // 全选/取消全选函数
  const toggleAllMembers = (checked: boolean) => {
    if (checked) {
      setSelectedMembersToDelete(getCurrentPageMembers());
    } else {
      setSelectedMembersToDelete([]);
    }
  };

  // 处理批量删除对话框打开
  const handleOpenBatchDeleteDialog = () => {
    if (selectedMembersToDelete.length === 0) {
      toast.error("Please select members to delete");
      return;
    }
    setIsDeleteMembersDialogOpen(true);
    setIsDropdownOpen(false); // 关闭下拉菜单
  };

  // 添加查看成员详情的函数
  const handleViewMember = (member: Member) => {
    setViewMember(member);
    setIsViewMemberDialogOpen(true);
  };

  // Copy invite link to clipboard
  const copyInviteLink = () => {
    if (inviteLink) {
      navigator.clipboard.writeText(inviteLink);
      toast.success("Invite link copied to clipboard");
    }
  };

  // Reset the form when dialog closes
  const handleAddMemberDialogChange = (open: boolean) => {
    if (!open) {
      setSelectedUser(null);
      setMemberCommissionRate(0.1);
      setMemberIsAdmin(false);
      setSearchUsers("");
      setSearchResults([]);
      setInviteByEmail(false);
      setInviteEmail("");
      setInviteLink(null);
    }
    setIsAddMemberDialogOpen(open);
  };

  // 添加刷新数据的函数
  const handleRefreshStats = async () => {
    try {
      setIsRefreshingStats(true);

      // 先执行刷新统计请求
      const refreshResponse = await axios.post(`/api/admin/organizations/${params.id}/refresh-stats`);

      if (!refreshResponse.data.success) {
        toast.error("Failed to refresh statistics");
        return;
      }

      // 刷新成功后，重新获取页面所需的全部数据
      try {
        // 获取最新的统计数据
        const analyticsResponse = await axios.get(`/api/admin/organizations/${params.id}/analytics`);

        // 更新统计数据
        if (analyticsResponse.data.stats) {
          setStats(analyticsResponse.data.stats);
        }

        // 更新组织数据
        if (analyticsResponse.data.organization) {
          setOrganization(prev => ({
            ...prev,
            ...analyticsResponse.data.organization
          }));
        }

        // 更新成员订单记录
        if (analyticsResponse.data.memberOrders) {
          setMemberOrders(analyticsResponse.data.memberOrders);
        }

        toast.success("Statistics refreshed successfully");
      } catch (analyticsError) {
        console.error("Error fetching updated data:", analyticsError);
        // 即使获取详细数据失败，至少更新刷新返回的基本统计
        if (refreshResponse.data.stats) {
          setStats(refreshResponse.data.stats);
        }
        toast.error("Partially refreshed statistics");
      }
    } catch (error) {
      console.error("Error refreshing statistics:", error);
      toast.error("Failed to refresh statistics");
    } finally {
      setIsRefreshingStats(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <h3 className="text-lg font-medium">Organization not found</h3>
        <p className="text-sm text-muted-foreground">
          The organization you are looking for does not exist.
        </p>
        <Button asChild className="mt-4">
          <Link href="/admin/organizations">
            <OrganizationIcons.arrowLeft className="mr-2 h-4 w-4" />
            Back to Organizations
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <Button asChild variant="outline" size="sm" className="mb-4">
          <Link href="/admin/organizations">
            <OrganizationIcons.arrowLeft className="mr-2 h-4 w-4" />
            Back to Organizations
          </Link>
        </Button>

        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center gap-3">
            {organization.logo ? (
              <Avatar className="h-12 w-12">
                <AvatarImage src={organization.logo} alt={organization.name} />
                <AvatarFallback>{organization.name?.substring(0, 2).toUpperCase() || 'OR'}</AvatarFallback>
              </Avatar>
            ) : (
              <Avatar className="h-12 w-12">
                <AvatarFallback>{organization.name?.substring(0, 2).toUpperCase() || 'OR'}</AvatarFallback>
              </Avatar>
            )}
            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-3xl font-bold">{organization.name}</h1>
                {getStatusBadge(organization.status)}
              </div>
              <p className="text-sm text-muted-foreground">
                Code: <span className="font-mono font-medium">{organization.code || (organization as any)?.organization?.code || "未设置"}</span>
              </p>
            </div>
          </div>

          <div className="flex gap-2">
            <InviteLinkButton organizationId={params.id} />
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Icons.edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              </DialogTrigger>
              <DialogContent>
                <form onSubmit={handleSubmit(onUpdateOrganization)}>
                  <DialogHeader>
                    <DialogTitle>Edit Organization</DialogTitle>
                    <DialogDescription>
                      Update organization details and settings.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="name">Organization Name</Label>
                      <Input
                        id="name"
                        placeholder="Enter organization name"
                        {...register("name")}
                      />
                      {errors.name && (
                        <p className="text-sm text-red-500">{errors.name.message}</p>
                      )}
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        placeholder="Enter organization description"
                        {...register("description")}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="logo">Logo URL</Label>
                      <Input
                        id="logo"
                        placeholder="Enter logo URL (optional)"
                        {...register("logo")}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="commissionRate">Commission Rate</Label>
                        <Input
                          id="commissionRate"
                          type="number"
                          step="0.01"
                          min="0"
                          max="1"
                          placeholder="0.12"
                          {...register("commissionRate", { valueAsNumber: true })}
                        />
                        {errors.commissionRate && (
                          <p className="text-sm text-red-500">{errors.commissionRate.message}</p>
                        )}
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="discountRate">Discount Rate</Label>
                        <Input
                          id="discountRate"
                          type="number"
                          step="0.01"
                          min="0"
                          max="1"
                          placeholder="0.05"
                          {...register("discountRate", { valueAsNumber: true })}
                        />
                        {errors.discountRate && (
                          <p className="text-sm text-red-500">{errors.discountRate.message}</p>
                        )}
                      </div>
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="status">Status</Label>
                      <Select
                        onValueChange={(value) => setValue("status", value as "ACTIVE" | "INACTIVE" | "SUSPENDED")}
                        defaultValue={organization.status}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ACTIVE">Active</SelectItem>
                          <SelectItem value="INACTIVE">Inactive</SelectItem>
                          <SelectItem value="SUSPENDED">Suspended</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isUpdating}>
                      {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Save Changes
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive">
                  <Icons.trash className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Organization</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete this organization? This action cannot be undone and will remove all associated data including members, invites, and commissions.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={onDeleteOrganization}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    disabled={isDeleting}
                  >
                    {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        {organization?.description && (
          <p className="mt-4 text-muted-foreground">{organization.description}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Members</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{organization._count?.members || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Earnings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${organization.totalEarnings?.toFixed(2) || '0.00'}</div>
          </CardContent>
        </Card>

        {stats && (
          <>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Visits</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalVisits}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Conversion Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.conversionRate?.toFixed(2) || '0.00'}%</div>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      <Tabs defaultValue="members" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="invites">Invites</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="members">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Organization Members</CardTitle>
                <CardDescription>
                  Manage members and their roles in this organization.
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Dialog open={isAddMemberDialogOpen} onOpenChange={handleAddMemberDialogChange}>
                  <DialogTrigger asChild>
                    <Button>
                      <OrganizationIcons.userPlus className="mr-2 h-4 w-4" />
                      Add Member
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add Organization Member</DialogTitle>
                      <DialogDescription>
                        Add a new member to this organization.
                      </DialogDescription>
                    </DialogHeader>

                    {inviteLink ? (
                      <div className="space-y-4">
                        <Alert className="bg-green-50 border-green-200">
                          <AlertDescription className="text-green-800">
                            Invitation created successfully! Share the link below with the invited member.
                          </AlertDescription>
                        </Alert>

                        <div className="flex items-center space-x-2">
                          <Input value={inviteLink} readOnly className="flex-1" />
                          <Button size="sm" onClick={copyInviteLink}>Copy</Button>
                        </div>

                        <div className="text-sm text-muted-foreground space-y-2">
                          <p className="flex items-start">
                            <AlertTriangle className="h-4 w-4 mr-2 mt-0.5 text-blue-500" />
                            The invited user will be able to set their own password when they follow this link.
                          </p>
                          <p className="flex items-start">
                            <Clock className="h-4 w-4 mr-2 mt-0.5 text-blue-500" />
                            This invitation link will expire in 7 days.
                          </p>
                        </div>

                        <DialogFooter>
                          <Button onClick={() => {
                            // Reset form and reopen dialog to create another invitation
                            setInviteLink(null);
                            setInviteEmail("");
                            setInviteByEmail(true);
                          }}>
                            Create Another Invitation
                          </Button>
                          <Button variant="outline" onClick={() => setIsAddMemberDialogOpen(false)}>
                            Done
                          </Button>
                        </DialogFooter>
                      </div>
                    ) : (
                      <>
                        <div className="flex items-center space-x-4 mb-4">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="existingUserOption"
                              checked={!inviteByEmail}
                              onCheckedChange={(checked) => setInviteByEmail(!checked)}
                            />
                            <Label htmlFor="existingUserOption">Existing User</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="inviteOption"
                              checked={inviteByEmail}
                              onCheckedChange={(checked) => setInviteByEmail(!!checked)}
                            />
                            <Label htmlFor="inviteOption">Send Invitation</Label>
                          </div>
                        </div>

                        <div className="grid gap-4 py-4">
                          {inviteByEmail ? (
                            <div className="grid gap-2">
                              <Label htmlFor="inviteEmail">Email Address</Label>
                              <Input
                                id="inviteEmail"
                                type="email"
                                placeholder="<EMAIL>"
                                value={inviteEmail}
                                onChange={(e) => setInviteEmail(e.target.value)}
                              />
                              <p className="text-xs text-muted-foreground">
                                An invitation link will be generated for this email.
                              </p>
                            </div>
                          ) : (
                            <div className="grid gap-2">
                              <Label htmlFor="userSearch">Search User</Label>
                              <div className="relative">
                                <Input
                                  id="userSearch"
                                  placeholder="Search by name or email (min 1 char)"
                                  value={searchUsers}
                                  onChange={(e) => setSearchUsers(e.target.value)}
                                />
                                {isSearching && (
                                  <div className="absolute right-3 top-1/2 -translate-y-1/2">
                                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                                  </div>
                                )}
                              </div>
                              {searchResults.length > 0 && (
                                <div className="border rounded-md max-h-40 overflow-y-auto">
                                  {searchResults.map(user => (
                                    <div
                                      key={user.id}
                                      className={`p-2 flex items-center gap-2 cursor-pointer hover:bg-muted ${
                                        selectedUser?.id === user.id ? 'bg-muted' : ''
                                      }`}
                                      onClick={() => setSelectedUser(user)}
                                    >
                                      <Checkbox
                                        checked={selectedUser?.id === user.id}
                                        className="mr-1"
                                      />
                                      <Avatar className="h-6 w-6">
                                        <AvatarImage src={user.image} alt={user.name} />
                                        <AvatarFallback>{user.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                                      </Avatar>
                                      <div>
                                        <div className="text-sm font-medium">{user.name}</div>
                                        <div className="text-xs text-muted-foreground">{user.email}</div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          )}

                          {(selectedUser || inviteByEmail) && (
                            <>
                              {selectedUser && !inviteByEmail && (
                                <div className="bg-muted p-3 rounded-md">
                                  <div className="text-sm font-medium">Selected User</div>
                                  <div className="flex items-center gap-2 mt-1">
                                    <Avatar className="h-8 w-8">
                                      <AvatarImage src={selectedUser.image} alt={selectedUser.name} />
                                      <AvatarFallback>{selectedUser.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                                    </Avatar>
                                    <div>
                                      <div className="font-medium">{selectedUser.name}</div>
                                      <div className="text-xs text-muted-foreground">{selectedUser.email}</div>
                                    </div>
                                  </div>
                                </div>
                              )}

                              <div className="grid gap-2">
                                <Label htmlFor="memberCommissionRate">Commission Rate</Label>
                                <div className="flex items-center gap-2">
                                  <Input
                                    id="memberCommissionRate"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    max="1"
                                    value={memberCommissionRate}
                                    onChange={(e) => setMemberCommissionRate(parseFloat(e.target.value))}
                                  />
                                  <span className="text-sm font-mono">({(memberCommissionRate * 100).toFixed(0)}%)</span>
                                </div>
                                <p className="text-xs text-muted-foreground">
                                  This is the percentage of the product price the member will earn. This comes from the organization's commission (e.g., if organization earns 30% and member rate is 20%, member gets 20% and organization keeps 10%).
                                </p>
                              </div>

                              <div className="flex items-center gap-2">
                                <Checkbox
                                  id="memberIsAdmin"
                                  checked={memberIsAdmin}
                                  onCheckedChange={(checked) => setMemberIsAdmin(!!checked)}
                                />
                                <label htmlFor="memberIsAdmin" className="text-sm cursor-pointer">
                                  Make this member an organization admin
                                </label>
                              </div>
                            </>
                          )}
                        </div>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => setIsAddMemberDialogOpen(false)}>
                            Cancel
                          </Button>
                          <Button
                            onClick={handleAddMember}
                            disabled={isAddingMember || (!inviteByEmail && !selectedUser) || (inviteByEmail && !inviteEmail)}
                          >
                            {isAddingMember && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                            {inviteByEmail ? "Send Invitation" : "Add Member"}
                          </Button>
                        </DialogFooter>
                      </>
                    )}
                  </DialogContent>
                </Dialog>

                {/* Batch Operations Dropdown */}
                <div className="relative">
                  <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline">
                        <MoreHorizontal className="h-4 w-4 mr-2" />
                        Batch Operations
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={handleOpenBatchUserDialog}>
                        <Icons.user className="h-4 w-4 mr-2" />
                        Batch Create Users
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleOpenBatchMemberDialog}>
                        <OrganizationIcons.users className="h-4 w-4 mr-2" />
                        Batch Add Members
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={handleOpenBatchDeleteDialog}
                        className="text-destructive"
                        disabled={selectedMembersToDelete.length === 0}
                      >
                        <Icons.trash className="h-4 w-4 mr-2" />
                        Delete Selected Members
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {members.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed">
                  <p className="text-sm text-muted-foreground">No members in this organization yet.</p>
                  <Button onClick={() => setIsAddMemberDialogOpen(true)} className="mt-2" variant="outline" size="sm">
                    <OrganizationIcons.userPlus className="mr-2 h-4 w-4" />
                    Add First Member
                  </Button>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-10">
                          <Checkbox
                            checked={getCurrentPageMembers().length > 0 &&
                              selectedMembersToDelete.length === getCurrentPageMembers().length}
                            onCheckedChange={toggleAllMembers}
                            aria-label="Select all members"
                          />
                        </TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Commission</TableHead>
                        <TableHead>Earnings</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {getCurrentPageMembers().map((member) => (
                        <TableRow key={member.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedMembersToDelete.some(m => m.id === member.id)}
                              onCheckedChange={() => toggleMemberSelection(member)}
                              aria-label={`Select ${member.user?.name || 'member'}`}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={member.user?.image || ''} alt={member.user?.name || ''} />
                                <AvatarFallback>{member.user?.name?.substring(0, 2).toUpperCase() || ''}</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="text-sm font-medium">{member.user?.name || 'Unknown User'}</div>
                                <div className="text-xs text-muted-foreground">{member.user?.email || 'No email'}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {member.isAdmin ? (
                              <Badge variant="outline" className="bg-primary/10 text-primary">Admin</Badge>
                            ) : (
                              <Badge variant="outline">Member</Badge>
                            )}
                          </TableCell>
                          <TableCell>{(member.commissionRate * 100)?.toFixed(0) || '0'}%</TableCell>
                          <TableCell>${member.totalEarnings?.toFixed(2) || '0.00'}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="ghost" size="sm" onClick={() => openEditMemberDialog(member)}>
                                <Icons.edit className="h-4 w-4" />
                                <span className="sr-only">Edit</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-destructive hover:text-destructive/90"
                                onClick={() => setMemberToDelete(member)}
                              >
                                <Icons.trash className="h-4 w-4" />
                                <span className="sr-only">Delete</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewMember(member)}
                              >
                                View
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {totalPages > 1 && (
                    <div className="flex items-center justify-center py-4 space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                        disabled={currentPage <= 1}
                      >
                        <Icons.chevronLeft className="h-4 w-4" />
                      </Button>
                      <div className="flex items-center space-x-1">
                        {[...Array(totalPages)].map((_, i) => (
                          <Button
                            key={i}
                            variant={currentPage === i + 1 ? "default" : "outline"}
                            size="sm"
                            className="w-8 h-8 p-0"
                            onClick={() => handlePageChange(i + 1)}
                          >
                            {i + 1}
                          </Button>
                        ))}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage >= totalPages}
                      >
                        <Icons.chevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invites">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Pending Invitations</CardTitle>
                <CardDescription>
                  Manage organization invitations.
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              {invites.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed">
                  <p className="text-sm text-muted-foreground">No pending invitations.</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Email</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Expires</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {invites.slice(0, 5).map((invite) => (
                        <TableRow key={invite.id}>
                          <TableCell>
                            <div className="font-medium">{invite.email}</div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className={
                              invite.status === "PENDING"
                                ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                                : invite.status === "ACCEPTED"
                                ? "bg-green-50 text-green-700 border-green-200"
                                : "bg-red-50 text-red-700 border-red-200"
                            }>
                              {invite.status.charAt(0) + invite.status.slice(1).toLowerCase()}
                            </Badge>
                          </TableCell>
                          <TableCell>{DateFormatter.custom(invite.expiresAt, "MMM d, yyyy")}</TableCell>
                          <TableCell>{DateFormatter.custom(invite.createdAt, "MMM d, yyyy")}</TableCell>
                          <TableCell className="text-right">
                            <Button asChild variant="ghost" size="sm">
                              <Link href={`/admin/organizations/${params.id}/invites/${invite.id}`}>
                                View
                              </Link>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Organization Analytics</CardTitle>
                  <CardDescription>
                    View performance metrics for this organization.
                  </CardDescription>
                </div>
                <Button
                  onClick={() => handleRefreshStats()}
                  size="sm"
                  disabled={isRefreshingStats}
                  variant="outline"
                >
                  {isRefreshingStats ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Refreshing...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Refresh Stats
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {!stats ? (
                <div className="flex justify-center items-center h-32">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-muted-foreground">Traffic</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="p-4 rounded-lg border">
                          <div className="text-sm text-muted-foreground">Total Visits</div>
                          <div className="text-2xl font-bold">{stats.totalVisits}</div>
                        </div>
                        <div className="p-4 rounded-lg border">
                          <div className="text-sm text-muted-foreground">Conversions</div>
                          <div className="text-2xl font-bold">{stats.totalConversions}</div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-muted-foreground">Performance</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="p-4 rounded-lg border">
                          <div className="text-sm text-muted-foreground">Conversion Rate</div>
                          <div className="text-2xl font-bold">{stats.conversionRate?.toFixed(2) || '0.00'}%</div>
                        </div>
                        <div className="p-4 rounded-lg border">
                          <div className="text-sm text-muted-foreground">Total Orders</div>
                          <div className="text-2xl font-bold">{stats.totalConversions}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-2">Organization Settings</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 rounded-lg border">
                        <div className="text-sm text-muted-foreground">Commission Rate</div>
                        <div className="text-xl font-bold">{(organization.commissionRate * 100)?.toFixed(0) || '0'}%</div>
                      </div>
                      <div className="p-4 rounded-lg border">
                        <div className="text-sm text-muted-foreground">Discount Rate</div>
                        <div className="text-xl font-bold">{(organization.discountRate * 100)?.toFixed(0) || '0'}%</div>
                      </div>
                      <div className="p-4 rounded-lg border">
                        <div className="text-sm text-muted-foreground">Affiliate Code</div>
                        <div className="text-xl font-bold font-mono">{organization.code}</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 添加新的佣金分配统计部分 */}
          <div className="space-y-2 mt-4">
            <h3 className="text-sm font-medium text-muted-foreground">Commission Distribution</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 rounded-lg border">
                <div className="text-sm text-muted-foreground">Organization Gross Earnings</div>
                <div className="text-2xl font-bold">${stats.totalCommissions?.toFixed(2) || '0.00'}</div>
                <div className="text-xs text-muted-foreground mt-1">Total commission generated</div>
              </div>
              <div className="p-4 rounded-lg border">
                <div className="text-sm text-muted-foreground">Member Commissions</div>
                <div className="text-2xl font-bold">${stats.memberCommissions?.toFixed(2) || '0.00'}</div>
                <div className="text-xs text-muted-foreground mt-1">Distributed to members</div>
              </div>
              <div className="p-4 rounded-lg border bg-green-50/40">
                <div className="text-sm text-muted-foreground">Organization Net Earnings</div>
                <div className="text-2xl font-bold">${stats.organizationActualEarnings?.toFixed(2) || '0.00'}</div>
                <div className="text-xs text-muted-foreground mt-1">Actual organization profit</div>
              </div>
            </div>
          </div>

          {/* Performance Overview Card */}
          <div className="grid gap-6 mt-8">
            <Card>
              <CardHeader>
                <CardTitle>Performance Overview</CardTitle>
                <CardDescription>
                  Summary of organization performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <User className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Members</p>
                      <h3 className="text-2xl font-bold">{members.length}</h3>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <BarChart className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Referrals</p>
                      <h3 className="text-2xl font-bold">{stats.totalConversions}</h3>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <BarChart className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Earnings</p>
                      <h3 className="text-2xl font-bold">${stats.organizationActualEarnings?.toFixed(2) || '0.00'}</h3>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Monthly Earnings Card */}
          {organization && (
            <div className="grid gap-6 mt-8">
              <Card>
                <CardHeader>
                  <CardTitle>Monthly Earnings</CardTitle>
                  <CardDescription>
                    Revenue trends over the last 6 months
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {organization.monthlyEarnings?.length > 0 ? (
                    <div className="relative">
                      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mt-2">
                        {organization.monthlyEarnings.map((month, index) => (
                          <Card key={index} className="p-2">
                            <CardContent className="p-2">
                              <div className="flex justify-between items-center mb-1">
                                <p className="text-sm font-medium">
                                  {month.month} {month.year}
                                </p>
                              </div>
                              <p className="text-xl font-bold text-green-600">
                                ${month.amount.toFixed(2)}
                              </p>
                              <div className="mt-2 space-y-1">
                                <div className="flex justify-between items-center text-xs">
                                  <span className="text-muted-foreground">Total:</span>
                                  <span className="font-medium">${month.totalCommissions.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between items-center text-xs">
                                  <span className="text-muted-foreground">Members:</span>
                                  <span className="font-medium">${month.memberCommissions.toFixed(2)}</span>
                                </div>
                              </div>
                              {month.amount > 0 && month.totalCommissions > 0 && (
                                <div className="w-full bg-green-100 rounded-full h-1.5 mt-2">
                                  <div
                                    className="bg-green-600 h-1.5 rounded-full"
                                    style={{
                                      width: `${Math.min(100, (month.amount / month.totalCommissions) * 100)}%`
                                    }}
                                  ></div>
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed">
                      <p className="text-sm text-muted-foreground">No monthly earnings data available</p>
                      <p className="text-sm text-muted-foreground">Click the refresh button above to load data</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Top Performing Members Card */}
          {members.length > 0 && (
            <div className="grid gap-6 mt-8">
              <Card>
                <CardHeader>
                  <CardTitle>Top Performing Members</CardTitle>
                  <CardDescription>
                    Members with the highest referrals and earnings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {members
                      .sort((a, b) => b.totalEarnings - a.totalEarnings)
                      .slice(0, 6)
                      .map((member, index) => (
                        <Card key={member.id} className="overflow-hidden">
                          <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-4">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-10 w-10 border-2 border-background">
                                <AvatarImage src={member.user?.image} alt={member.user?.name} />
                                <AvatarFallback>{member.user?.name?.substring(0, 2).toUpperCase() || 'UN'}</AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium">{member.user?.name}</p>
                                <div className="flex items-center text-sm text-muted-foreground">
                                  <BarChart className="h-3 w-3 mr-1" />
                                  <span>Top {index + 1} Performer</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <CardContent className="p-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <h4 className="text-sm font-medium text-muted-foreground">Code</h4>
                                <p className="text-sm font-mono">{member.code}</p>
                              </div>
                              <div>
                                <h4 className="text-sm font-medium text-muted-foreground">Earnings</h4>
                                <p className="text-xl font-bold">${member.totalEarnings?.toFixed(2) || '0.00'}</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 在Analytics标签内容的底部添加成员订单记录表格 */}
          <div className="mt-8">
            <h3 className="text-sm font-medium text-muted-foreground mb-4">Member Orders</h3>
            {memberOrders.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed">
                <p className="text-sm text-muted-foreground">No orders found for this organization</p>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Member</TableHead>
                      <TableHead>Order ID</TableHead>
                      <TableHead>Order Amount</TableHead>
                      <TableHead>Member Commission</TableHead>
                      <TableHead>Organization Commission</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {memberOrders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-7 w-7">
                              <AvatarImage src={order.affiliate?.user?.image} alt={order.affiliate?.user?.name} />
                              <AvatarFallback>{order.affiliate?.user?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="text-sm font-medium">{order.affiliate?.user?.name}</div>
                              <div className="text-xs text-muted-foreground">{order.affiliate?.code}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="font-mono text-xs">{order.orderId.substring(0, 8)}...</TableCell>
                        <TableCell>${order.order?.total?.toFixed(2) || '0.00'}</TableCell>
                        <TableCell>${order.commissionAmount?.toFixed(2) || '0.00'}</TableCell>
                        <TableCell>
                          {order.organizationCommission?.commissionAmount
                            ? `$${(order.organizationCommission.commissionAmount - order.commissionAmount).toFixed(2)}`
                            : '-'}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className={
                            order.status === "APPROVED"
                              ? "bg-green-50 text-green-700 border-green-200"
                              : order.status === "PENDING"
                              ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                              : order.status === "PAID"
                              ? "bg-blue-50 text-blue-700 border-blue-200"
                              : "bg-red-50 text-red-700 border-red-200"
                          }>
                            {order.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{DateFormatter.custom(order.createdAt, "MMM d, yyyy")}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* 添加成员编辑对话框 */}
      <Dialog open={isEditMemberDialogOpen} onOpenChange={setIsEditMemberDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Member</DialogTitle>
            <DialogDescription>
              Update member role and commission rate.
            </DialogDescription>
          </DialogHeader>
          {editMember && (
            <div className="grid gap-4 py-4">
              <div className="flex items-center gap-3 p-3 bg-muted rounded-md">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={editMember.user.image} alt={editMember.user.name} />
                  <AvatarFallback>{editMember.user.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{editMember.user.name}</div>
                  <div className="text-sm text-muted-foreground">{editMember.user.email}</div>
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="editCommissionRate">Commission Rate</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="editCommissionRate"
                    type="number"
                    step="0.01"
                    min="0"
                    max="1"
                    value={memberCommissionRate}
                    onChange={(e) => setMemberCommissionRate(parseFloat(e.target.value))}
                  />
                  <span className="text-sm font-mono">({(memberCommissionRate * 100).toFixed(0)}%)</span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Checkbox
                  id="editMemberIsAdmin"
                  checked={memberIsAdmin}
                  onCheckedChange={(checked) => setMemberIsAdmin(!!checked)}
                />
                <label htmlFor="editMemberIsAdmin" className="text-sm cursor-pointer">
                  Organization admin privileges
                </label>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditMemberDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditMember} disabled={isEditingMember}>
              {isEditingMember && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 成员删除确认对话框 */}
      <AlertDialog open={!!memberToDelete} onOpenChange={(open) => !open && setMemberToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Member</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove this member from the organization? This action cannot be undone.
              {memberToDelete && (
                <div className="flex items-center gap-2 mt-2 p-2 bg-muted rounded-md">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={memberToDelete.user.image} alt={memberToDelete.user.name} />
                    <AvatarFallback>{memberToDelete.user.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{memberToDelete.user.name}</div>
                    <div className="text-xs text-muted-foreground">{memberToDelete.user.email}</div>
                  </div>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteMember}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeletingMember}
            >
              {isDeletingMember && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Remove Member
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Batch User Creation Dialog */}
      <Dialog
        open={isBatchUserDialogOpen}
        onOpenChange={(open) => {
          setIsBatchUserDialogOpen(open);
          if (!open) {
            setIsDropdownOpen(false); // 确保对话框关闭时下拉菜单也关闭
            setBatchUsersCsv("");
            setPasswordResetLinks([]);
          }
        }}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Batch Create Users</DialogTitle>
            <DialogDescription>
              Create multiple users at once by uploading a CSV file or pasting CSV data.
            </DialogDescription>
          </DialogHeader>

          {passwordResetLinks.length > 0 ? (
            <div className="space-y-4">
              <Alert className="bg-green-50 border-green-200">
                <AlertDescription className="text-green-800">
                  Users created successfully! Password reset links have been generated for each user.
                </AlertDescription>
              </Alert>

              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Email</TableHead>
                      <TableHead className="text-right">Action</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {passwordResetLinks.map((link) => (
                      <TableRow key={link.userId}>
                        <TableCell>{link.email}</TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              navigator.clipboard.writeText(link.resetLink);
                              toast.success(`Reset link copied for ${link.email}`);
                            }}
                          >
                            Copy Link
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              <div className="text-sm text-muted-foreground space-y-2">
                <p className="flex items-start">
                  <AlertTriangle className="h-4 w-4 mr-2 mt-0.5 text-blue-500" />
                  These links allow users to set their own passwords. Send them to the respective users.
                </p>
                <p className="flex items-start">
                  <Clock className="h-4 w-4 mr-2 mt-0.5 text-blue-500" />
                  Links will expire in 7 days.
                </p>
              </div>

              <DialogFooter className="space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    // Copy all links as a formatted text
                    const text = passwordResetLinks.map(link =>
                      `${link.email}: ${link.resetLink}`
                    ).join('\n');
                    navigator.clipboard.writeText(text);
                    toast.success("All reset links copied to clipboard");
                  }}
                >
                  Copy All Links
                </Button>
                <Button
                  onClick={() => {
                    setBatchUsersCsv("");
                    setPasswordResetLinks([]);
                  }}
                >
                  Create More Users
                </Button>
                <Button
                  variant="secondary"
                  onClick={() => setIsBatchUserDialogOpen(false)}
                >
                  Done
                </Button>
              </DialogFooter>
            </div>
          ) : (
            <>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="csvData">CSV Data</Label>
                  <Textarea
                    id="csvData"
                    placeholder="name,email,role
John Doe,<EMAIL>,CUSTOMER
Jane Smith,<EMAIL>,ADMIN"
                    rows={10}
                    value={batchUsersCsv}
                    onChange={(e) => setBatchUsersCsv(e.target.value)}
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-muted-foreground">
                    CSV must include 'name' and 'email' columns. 'role' column is optional (defaults to CUSTOMER). Valid roles: ADMIN, CUSTOMER, STAFF. First row should be headers.
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsBatchUserDialogOpen(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleBatchUserCreation}
                  disabled={isProcessingBatchUsers || !batchUsersCsv.trim()}
                >
                  {isProcessingBatchUsers && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Create Users
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Batch Member Addition Dialog */}
      <Dialog
        open={isBatchMemberDialogOpen}
        onOpenChange={(open) => {
          setIsBatchMemberDialogOpen(open);
          if (!open) setIsDropdownOpen(false); // 确保对话框关闭时下拉菜单也关闭
        }}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Batch Add Members</DialogTitle>
            <DialogDescription>
              Add multiple members to this organization at once.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label>Search and Select Users</Label>
              <div className="relative">
                <Input
                  placeholder="Search by name or email (min 1 char)"
                  value={searchUsers}
                  onChange={(e) => setSearchUsers(e.target.value)}
                />
                {isSearching && (
                  <div className="absolute right-3 top-1/2 -translate-y-1/2">
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                  </div>
                )}
              </div>
            </div>

            {searchResults.length > 0 && (
              <div className="border rounded-md max-h-40 overflow-y-auto">
                {searchResults.map(user => (
                  <div
                    key={user.id}
                    className={`p-2 flex items-center gap-2 cursor-pointer hover:bg-muted ${
                      selectedUsers.some(u => u.id === user.id) ? 'bg-muted' : ''
                    }`}
                    onClick={() => toggleUserSelection(user)}
                  >
                    <Checkbox
                      checked={selectedUsers.some(u => u.id === user.id)}
                      className="mr-1"
                      // 移除onCheckedChange，避免重复触发选择
                    />
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={user.image} alt={user.name} />
                      <AvatarFallback>{user.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="text-sm font-medium">{user.name}</div>
                      <div className="text-xs text-muted-foreground">{user.email}</div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {selectedUsers.length > 0 && (
              <div className="bg-muted p-3 rounded-md">
                <div className="flex justify-between items-center mb-2">
                  <div className="text-sm font-medium">Selected Users ({selectedUsers.length})</div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedUsers([])}
                    className="h-7 text-xs"
                  >
                    Clear All
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {selectedUsers.map(user => (
                    <div
                      key={user.id}
                      className="bg-background rounded-full px-2 py-1 text-xs flex items-center gap-1"
                    >
                      <span>{user.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleUserSelection(user)}
                        className="h-4 w-4 p-0 rounded-full"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="grid gap-2">
              <Label htmlFor="batchCommissionRate">Commission Rate (for all selected users)</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="batchCommissionRate"
                  type="number"
                  step="0.01"
                  min="0"
                  max="1"
                  value={memberCommissionRate}
                  onChange={(e) => setMemberCommissionRate(parseFloat(e.target.value))}
                />
                <span className="text-sm font-mono">({(memberCommissionRate * 100).toFixed(0)}%)</span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Checkbox
                id="batchIsAdmin"
                checked={memberIsAdmin}
                onCheckedChange={(checked) => setMemberIsAdmin(!!checked)}
              />
              <label htmlFor="batchIsAdmin" className="text-sm cursor-pointer">
                Make all selected users organization admins
              </label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBatchMemberDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleBatchMemberAddition}
              disabled={isProcessingBatchMembers || selectedUsers.length === 0}
            >
              {isProcessingBatchMembers && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Add {selectedUsers.length} Members
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 批量删除成员确认对话框 */}
      <AlertDialog open={isDeleteMembersDialogOpen} onOpenChange={setIsDeleteMembersDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Members</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedMembersToDelete.length} members from this organization? This action cannot be undone.

              {selectedMembersToDelete.length > 0 && (
                <div className="mt-4 max-h-40 overflow-y-auto border rounded-md p-2">
                  {selectedMembersToDelete.map(member => (
                    <div key={member.id} className="flex items-center gap-2 py-1 border-b last:border-0">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={member.user?.image || ''} alt={member.user?.name || ''} />
                        <AvatarFallback>{member.user?.name?.substring(0, 2).toUpperCase() || ''}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{member.user?.name || 'Unknown User'}</span>
                    </div>
                  ))}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBatchDeleteMembers}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeletingBatchMembers}
            >
              {isDeletingBatchMembers && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete Members
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 成员详情查看对话框 */}
      <Dialog open={isViewMemberDialogOpen} onOpenChange={setIsViewMemberDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Member Details</DialogTitle>
            <DialogDescription>
              View detailed information about this member.
            </DialogDescription>
          </DialogHeader>

          {viewMember && (
            <div className="space-y-4 py-4">
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={viewMember.user?.image || ''} alt={viewMember.user?.name || ''} />
                  <AvatarFallback>{viewMember.user?.name?.substring(0, 2).toUpperCase() || ''}</AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="text-lg font-semibold">{viewMember.user?.name || 'Unknown User'}</h3>
                  <p className="text-sm text-muted-foreground">{viewMember.user?.email || 'No email'}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Role</p>
                  <div>
                    {viewMember.isAdmin ? (
                      <Badge variant="outline" className="bg-primary/10 text-primary">Admin</Badge>
                    ) : (
                      <Badge variant="outline">Member</Badge>
                    )}
                  </div>
                </div>

                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Affiliate Code</p>
                  <p className="font-mono text-sm">{viewMember.code || 'Not set'}</p>
                </div>

                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Commission Rate</p>
                  <p className="font-medium">{(viewMember.commissionRate * 100).toFixed(0)}%</p>
                </div>

                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Total Earnings</p>
                  <p className="font-medium">${viewMember.totalEarnings?.toFixed(2) || '0.00'}</p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewMemberDialogOpen(false)}>
              Close
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                setIsViewMemberDialogOpen(false);
                openEditMemberDialog(viewMember!);
              }}
              disabled={!viewMember}
            >
              Edit Member
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}