import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import React from 'react';

interface PasswordResetEmailProps {
  name: string;
  resetLink: string;
}

export const PasswordResetEmail = ({ name, resetLink }: PasswordResetEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>Reset your Yolloo Store password</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logo}>
            <Img
              src="https://i.postimg.cc/F15k4vk9/logo.png"
              width="300"
              height="auto"
              alt="Yolloo Store"
            />
          </Section>
          <Section style={content}>
            <Heading style={heading}>Password Reset</Heading>
            <Text style={paragraph}>Hello {name},</Text>
            <Text style={paragraph}>
              You recently requested to reset your password for your Yolloo Store account. 
              Click the button below to set a new password:
            </Text>
            <Section style={buttonContainer}>
              <Link style={button} href={resetLink}>
                Reset Password
              </Link>
            </Section>
            <Text style={paragraph}>
              If you did not request a password reset, please ignore this email or contact
              support if you have questions.
            </Text>
            <Text style={paragraph}>
              This password reset link is only valid for 7 days.
            </Text>
          </Section>
          <Section style={footer}>
            <Text style={footerText}>
              © {new Date().getFullYear()} Yolloo Store. All rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '580px',
};

const logo = {
  marginTop: '32px',
};

const content = {
  padding: '40px 32px',
  backgroundColor: '#ffffff',
  borderRadius: '5px',
};

const heading = {
  fontSize: '24px',
  letterSpacing: '-0.5px',
  lineHeight: '1.3',
  fontWeight: '400',
  color: '#484848',
  padding: '17px 0 0',
};

const paragraph = {
  margin: '0 0 15px',
  fontSize: '15px',
  lineHeight: '1.4',
  color: '#3c4149',
};

const buttonContainer = {
  padding: '27px 0 27px',
};

const button = {
  backgroundColor: '#5e6ad2',
  borderRadius: '5px',
  fontWeight: '600',
  color: '#fff',
  fontSize: '15px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '14px 26px',
};

const footer = {
  padding: '0 32px',
};

const footerText = {
  fontSize: '13px',
  lineHeight: '24px',
  color: '#8898aa',
  textAlign: 'center' as const,
};

export default PasswordResetEmail; 