"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import axios from "axios";
import { <PERSON>ader2, <PERSON>r, <PERSON><PERSON><PERSON>, ChevronLeft, Wallet, CreditCard } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { formatDistanceToNow } from "date-fns";
import { DateFormatter } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { Icons } from "@/components/icons";

// Define types for the organization data
interface Organization {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  code: string;
  commissionRate: number;
  discountRate: number;
  status: string;
  createdAt: string;
  _count: {
    members: number;
  };
  administrators?: Array<{
    id: string;
    name: string;
    email: string;
    image?: string;
  }>;
  currentUserEmail?: string;
}

export default function OrganizationDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [isMember, setIsMember] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [currentUserEmail, setCurrentUserEmail] = useState<string | null>(null);

  // Fetch organization details on component mount
  useEffect(() => {
    const fetchOrganizationDetails = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/affiliate/organizations/${params.id}`);
        setOrganization(response.data.organization);
        setIsMember(response.data.isMember);
        setIsAdmin(response.data.isAdmin);

        // 保存当前用户的邮箱以便用于辨识
        if (response.data.currentUser) {
          setCurrentUserEmail(response.data.currentUser.email);
        }

        // If user is not a member, redirect back to organizations page
        if (!response.data.isMember) {
          toast.error("You do not have access to this organization");
          router.push("/affiliate/organization");
          return;
        }
      } catch (error) {
        console.error("Error fetching organization details:", error);
        toast.error("Failed to fetch organization details");
        router.push("/affiliate/organization");
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizationDetails();
  }, [params.id, router]);

  // Handle organization edit
  const handleEditOrganization = () => {
    router.push(`/affiliate/organization/${params.id}/edit`);
  };

  // Handle members management
  const handleManageMembers = () => {
    router.push(`/affiliate/organization/${params.id}/members`);
  };

  // Handle withdrawals management
  const handleManageWithdrawals = () => {
    router.push(`/affiliate/organization/${params.id}/withdrawals`);
  };

  // Handle analytics view
  const handleViewAnalytics = () => {
    router.push(`/affiliate/organization/${params.id}/analytics`);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!organization || !isMember) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6 flex items-center justify-between">
        <Button variant="outline" size="sm" asChild>
          <Link href="/affiliate">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Affiliate Dashboard
          </Link>
        </Button>

        {/* Admin Actions */}
        {isAdmin && (
          <div className="flex gap-2">
            <Button size="sm" onClick={handleEditOrganization}>
              Edit Organization
            </Button>
          </div>
        )}
      </div>

      <div className="grid gap-6">
        {/* Organization Details */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {organization?.logo ? (
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={organization.logo} alt={organization.name} />
                    <AvatarFallback>{organization?.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                  </Avatar>
                ) : (
                  <Avatar className="h-12 w-12">
                    <AvatarFallback>{organization?.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                  </Avatar>
                )}
                <div>
                  <CardTitle className="text-xl">{organization?.name}</CardTitle>
                  <CardDescription>
                    Organization Code: {organization?.code}
                  </CardDescription>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Description */}
              {organization?.description && (
                <>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-2">About</h3>
                    <p>{organization.description}</p>
                  </div>
                  <Separator />
                </>
              )}

              {/* Admin Management Section */}
              {isAdmin && (
                <>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-3">Management</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleManageMembers}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium">Manage Members</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>View and manage members</span>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleManageWithdrawals}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium">Withdrawals</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center">
                            <Wallet className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>Process withdrawals</span>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleViewAnalytics}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium">Analytics</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center">
                            <BarChart className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>View performance data</span>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                  <Separator />
                </>
              )}

              {/* Basic Statistics */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3">Organization Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Members</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-2xl font-bold">{organization?._count.members}</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Commission Rate</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <BarChart className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-2xl font-bold">
                          {(organization?.commissionRate * 100).toFixed(0)}%
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Discount Rate</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-2xl font-bold">
                          {(organization?.discountRate * 100).toFixed(0)}%
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        Discount for customers using organization referral code.
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 mt-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Created</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {DateFormatter.relative(organization?.createdAt)}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              <Separator />

              {/* Administrators */}
              {organization?.administrators && organization.administrators.length > 0 && (
                <>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-3">Organization Administrators</h3>
                    <div className="space-y-4">
                      {organization.administrators.map((admin) => {
                        // 检查当前管理员是否是当前用户
                        const isCurrentUser = currentUserEmail && admin.email === currentUserEmail;
                        return (
                          <div key={admin.id} className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={admin.image} alt={admin.name} />
                              <AvatarFallback>{admin.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium flex items-center">
                                {admin.name}
                                {isCurrentUser && (
                                  <span className="ml-2 bg-primary/10 text-primary text-xs px-2 py-0.5 rounded-full">
                                    You
                                  </span>
                                )}
                              </p>
                              <p className="text-sm text-muted-foreground">{admin.email}</p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                  <Separator />
                </>
              )}

              {/* Info - 只对非管理员显示 */}
              {!isAdmin && (
                <div className="rounded-lg bg-muted p-4">
                  <h3 className="font-medium mb-2">About Your Membership</h3>
                  <p className="text-sm text-muted-foreground">
                    You are a member of this organization. Your commission rate is applied directly to the product price. The organization earns the difference between their commission rate and your commission rate. For example, if the organization's rate is 30% and your rate is 20%, on a $100 order, you earn $20 and the organization keeps $10.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}