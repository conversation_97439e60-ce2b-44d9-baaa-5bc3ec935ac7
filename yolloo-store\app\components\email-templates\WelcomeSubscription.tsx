import * as React from 'react';
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

export interface WelcomeSubscriptionEmailProps {
  discountCode: string;
  previewText?: string;
}

export const WelcomeSubscriptionEmail = ({
  discountCode,
  previewText = 'Welcome to Yolloo Store! Your presale subscription is confirmed.',
}: WelcomeSubscriptionEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src="https://i.postimg.cc/F15k4vk9/logo.png"
            width="300"
            height="auto"
            alt="Yolloo Store"
            style={logo}
          />
          <Heading style={heading}>Welcome to Yolloo Store!</Heading>
          <Text style={paragraph}>
            Thank you for subscribing to our presale notifications! We're excited to have you join our community.
          </Text>
          <Text style={paragraph}>
            As a special thank you, here's your exclusive discount code that you can use on your first purchase:
          </Text>
          <Section style={codeContainer}>
            <Text style={discountCodeText}>{discountCode}</Text>
          </Section>
          <Text style={paragraph}>
            This discount code is valid for your first purchase on the Yolloo Store. Visit our store to explore our products!
          </Text>
          <Section style={btnContainer}>
            <Link style={button} href="https://yolloo.com">
              Visit Our Store
            </Link>
          </Section>
          <Text style={footer}>
            If you have any questions, feel free to{' '}
            <Link href="mailto:<EMAIL>" style={link}>
              contact our support team
            </Link>
            .
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default WelcomeSubscriptionEmail;

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px',
  maxWidth: '600px',
  borderRadius: '5px',
  boxShadow: '0 2px 5px rgba(0, 0, 0, 0.05)',
};

const logo = {
  margin: '0 auto',
  marginBottom: '20px',
  display: 'block',
};

const heading = {
  fontSize: '24px',
  letterSpacing: '-0.5px',
  lineHeight: '1.3',
  fontWeight: '400',
  textAlign: 'center' as const,
  color: '#111827',
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#4b5563',
  marginTop: '16px',
};

const codeContainer = {
  background: '#f9fafb',
  border: '1px solid #e5e7eb',
  borderRadius: '5px',
  padding: '12px',
  margin: '24px 0',
  textAlign: 'center' as const,
};

const discountCodeText = {
  fontSize: '24px',
  fontWeight: 'bold',
  letterSpacing: '2px',
  color: '#111827',
  margin: '0',
};

const btnContainer = {
  textAlign: 'center' as const,
  marginTop: '24px',
};

const button = {
  backgroundColor: '#2563eb',
  borderRadius: '5px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
};

const link = {
  color: '#2563eb',
  textDecoration: 'underline',
};

const footer = {
  fontSize: '14px',
  color: '#6b7280',
  marginTop: '32px',
  textAlign: 'center' as const,
}; 