"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"
import { Icons } from "@/components/icons"

interface NavItem {
  title: string
  href: string
  icon: keyof typeof Icons
  disabled?: boolean
}

export const navItems: NavItem[] = [
  {
    title: "Dashboard",
    href: "/admin",
    icon: "post",
  },
  {
    title: "Products",
    href: "/admin/products",
    icon: "media",
  },
  {
    title: "Product Sync",
    href: "/admin/products/sync",
    icon: "refresh",
  },
  {
    title: "Orders",
    href: "/admin/orders",
    icon: "billing",
  },
  {
    title: "Users",
    href: "/admin/users",
    icon: "user",
  },
  {
    title: "Cards",
    href: "/admin/cards",
    icon: "billing",
  },
  {
    title: "Subscribers",
    href: "/admin/subscribers",
    icon: "shield",
  },
  {
    title: "Commissions",
    href: "/admin/commissions",
    icon: "billing",
  },
  {
    title: "Affiliates",
    href: "/admin/affiliates",
    icon: "zap",
  },
  {
    title: "Organizations",
    href: "/admin/organizations",
    icon: "users",
  },
  {
    title: "Settings",
    href: "/admin/settings",
    icon: "settings",
  },
]

export function AdminNav() {
  const path = usePathname()

  if (!navItems?.length) {
    return null
  }

  return (
    <nav className="grid items-start gap-2">
      {navItems.map((item, index) => {
        const Icon = Icons[item.icon]
        return (
          item.href && (
            <Link
              key={index}
              href={item.disabled ? "/" : item.href}
              className={cn(
                buttonVariants({ variant: "ghost" }),
                path === item.href || (path?.startsWith(item.href) && item.href !== "/admin")
                  ? "bg-muted hover:bg-muted"
                  : "hover:bg-transparent hover:underline",
                "justify-start",
                item.disabled && "cursor-not-allowed opacity-60"
              )}
            >
              <Icon className="mr-2 h-4 w-4" />
              {item.title}
            </Link>
          )
        )
      })}
    </nav>
  )
}