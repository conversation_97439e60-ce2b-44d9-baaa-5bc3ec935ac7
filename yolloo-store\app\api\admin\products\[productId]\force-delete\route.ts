import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

/**
 * 强制删除产品的API端点
 * 即使产品有关联的订单项或eSIM，也会强制删除
 * 步骤:
 * 1. 对于关联的订单项，保留productCode和variantCode用于历史记录
 * 2. 对于关联的eSIM，直接删除
 * 3. 删除所有其他关联数据（购物车、愿望清单、评论、参数、变体）
 * 4. 删除产品本身
 */
export async function DELETE(
  _req: Request,
  { params }: { params: { productId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // 验证商品存在
    const product = await prisma.product.findUnique({
      where: { id: params.productId },
      include: {
        category: true,
        variants: true,
      }
    });

    if (!product) {
      return new NextResponse("Product not found", { status: 404 });
    }

    console.log(`[FORCE_DELETE] Starting force deletion process for product ID: ${params.productId} (${product.name})`);

    // 使用事务来确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 1. 检查产品是否有关联订单（通过productCode）
      const orderItems = await tx.orderItem.findMany({
        where: {
          productCode: product.sku
        },
        include: {
          order: true
        }
      });

      if (orderItems.length > 0) {
        console.log(`[FORCE_DELETE] Product has ${orderItems.length} associated order items`);

        // 保留OrderItem中的productCode和variantCode，不设置为null
        // 这样可以保持订单与原始商品的历史关联
        console.log(`[FORCE_DELETE] Preserving productCode and variantCode in ${orderItems.length} order items for historical reference`);
      }

      // 2. 检查是否有关联的Esim，直接删除
      const esims = await tx.esim.findMany({
        where: {
          productId: params.productId
        }
      });

      if (esims.length > 0) {
        console.log(`[FORCE_DELETE] Product has ${esims.length} associated eSIMs, deleting them`);

        // 直接删除所有关联的eSIM
        await tx.esim.deleteMany({
          where: {
            productId: params.productId
          }
        });

        console.log(`[FORCE_DELETE] Deleted ${esims.length} eSIMs`);
      }

      // 3. 删除所有关联的CartItem
      const cartItemsResult = await tx.cartItem.deleteMany({
        where: {
          productId: params.productId,
        },
      });
      console.log(`[FORCE_DELETE] Deleted ${cartItemsResult.count} cart items`);

      // 4. 删除所有关联的WishlistItem
      const wishlistItemsResult = await tx.wishlistItem.deleteMany({
        where: {
          productId: params.productId,
        },
      });
      console.log(`[FORCE_DELETE] Deleted ${wishlistItemsResult.count} wishlist items`);

      // 5. 删除所有关联的Review
      const reviewsResult = await tx.review.deleteMany({
        where: {
          productId: params.productId,
        },
      });
      console.log(`[FORCE_DELETE] Deleted ${reviewsResult.count} reviews`);

      // 6. 删除所有关联的ProductParameter
      const parametersResult = await tx.productParameter.deleteMany({
        where: {
          productId: params.productId,
        },
      });
      console.log(`[FORCE_DELETE] Deleted ${parametersResult.count} product parameters`);

      // 7. 删除所有关联的ProductVariant
      const variantsResult = await tx.productVariant.deleteMany({
        where: {
          productId: params.productId,
        },
      });
      console.log(`[FORCE_DELETE] Deleted ${variantsResult.count} product variants`);

      // 8. 删除产品
      await tx.product.delete({
        where: {
          id: params.productId,
        },
      });
      console.log(`[FORCE_DELETE] Successfully deleted product: ${params.productId}`);
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("[FORCE_DELETE]", error);

    if (error instanceof Error) {
      return new NextResponse(`Error force deleting product: ${error.message}`, { status: 500 });
    }

    return new NextResponse("Internal error", { status: 500 });
  }
}
