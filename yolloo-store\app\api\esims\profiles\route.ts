import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// Get profiles for a Yolloo card
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const yollooCardId = searchParams.get('yollooCardId');

    if (!yollooCardId) {
      return new NextResponse("Missing yollooCardId", { status: 400 });
    }

    // 验证Yolloo卡属于当前用户，同时获取关联的esims数据
    const card = await prisma.yollooCard.findFirst({
      where: {
        id: yollooCardId,
        userId: session.user.id
      },
      include: {
        esims: {
          include: {
            product: true,
            profile: true
          }
        }
      }
    });

    if (!card) {
      return new NextResponse("Card not found or unauthorized", { status: 404 });
    }

    const mergedData = {
      profiles: [], 
      localEsims: card.esims
    };

    return NextResponse.json(mergedData);
  } catch (error) {
    return new NextResponse("Internal error", { status: 500 });
  }
}

// Enable/Disable profile
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const json = await request.json();
    const { yollooCardId, profileId, action } = json;

    if (!yollooCardId || !profileId || !action) {
      return new NextResponse("Missing required fields", { status: 400 });
    }

    // 验证Yolloo卡属于当前用户
    const card = await prisma.yollooCard.findFirst({
      where: {
        id: yollooCardId,
        userId: session.user.id
      }
    });

    if (!card) {
      return new NextResponse("Card not found or unauthorized", { status: 404 });
    }

    // IOT Device service has been removed
    // Just update the local eSIM status

    if (action !== 'enable' && action !== 'disable') {
      return new NextResponse("Invalid action", { status: 400 });
    }

    console.log(`[ESIMS_PROFILES] ${action} profile ${profileId} for card ${yollooCardId}`);

    // Update local esim status directly
    await prisma.esim.updateMany({
      where: {
        yollooCardId: card.id,
        profileId: String(profileId)
      },
      data: {
        status: action === 'enable' ? 'Active' : 'Inactive'
      }
    });

    // Return a success response
    return NextResponse.json({
      success: true,
      message: `Profile ${action === 'enable' ? 'enabled' : 'disabled'} successfully`
    });
  } catch (error) {
    return new NextResponse("Internal error", { status: 500 });
  }
}

// Delete profile
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const yollooCardId = searchParams.get('yollooCardId');
    const profileId = searchParams.get('profileId');

    if (!yollooCardId || !profileId) {
      return new NextResponse("Missing required parameters", { status: 400 });
    }

    // 验证Yolloo卡属于当前用户
    const card = await prisma.yollooCard.findFirst({
      where: {
        id: yollooCardId,
        userId: session.user.id
      }
    });

    if (!card) {
      return new NextResponse("Card not found or unauthorized", { status: 404 });
    }

    // IOT Device service has been removed
    // Just update the local eSIM record
    console.log(`[ESIMS_PROFILES] Delete profile ${profileId} for card ${yollooCardId}`);

    // Update local esim record directly
    await prisma.esim.updateMany({
      where: {
        yollooCardId: card.id,
        profileId: String(profileId)
      },
      data: {
        profileId: null,
        status: 'Inactive'
      }
    });

    // Return a success response
    return NextResponse.json({
      success: true,
      message: 'Profile deleted successfully'
    });
  } catch (error) {
    return new NextResponse("Internal error", { status: 500 });
  }
}