{"openapi": "3.0.3", "info": {"title": "Mobile API - 首页9宫格接口", "description": "移动应用首页9宫格导航相关的完整API接口文档，包括首页数据、原生模块和HTML页面接口", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://127.0.0.1:4000/api/mobile", "description": "本地开发服务器"}], "paths": {"/home": {"get": {"tags": ["首页核心接口"], "summary": "获取首页完整数据", "description": "获取首页数据，包括9宫格按键配置、轮播图、分类、推荐套餐等", "operationId": "getHomeData", "parameters": [{"name": "Accept-Language", "in": "header", "description": "语言设置", "required": false, "schema": {"type": "string", "enum": ["zh-CN", "en-US"], "default": "zh-CN"}}, {"name": "X-Theme", "in": "header", "description": "主题设置", "required": false, "schema": {"type": "string", "enum": ["light", "dark"], "default": "light"}}, {"name": "X-<PERSON><PERSON><PERSON><PERSON>", "in": "header", "description": "货币设置", "required": false, "schema": {"type": "string", "enum": ["CNY", "USD"], "default": "CNY"}}, {"name": "city", "in": "query", "description": "城市名称", "required": false, "schema": {"type": "string", "example": "北京"}}, {"name": "lat", "in": "query", "description": "纬度", "required": false, "schema": {"type": "number", "format": "double", "example": 39.9042}}, {"name": "lng", "in": "query", "description": "经度", "required": false, "schema": {"type": "number", "format": "double", "example": 116.4074}}], "responses": {"200": {"description": "成功获取首页数据", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HomeDataResponse"}, "example": {"banners": [{"id": "1", "imageUrl": "https://example.com/banner1.jpg", "title": "5G全球套餐促销", "link": "/products/5g-package", "priority": 1}], "categories": [{"id": "1", "name": "旅行", "icon": "https://example.com/travel-icon.png", "link": "/categories/travel"}, {"id": "2", "name": "ESIM卡", "icon": "https://example.com/esim-icon.png", "link": "/categories/esim"}], "gridButtons": [{"id": "number-retention", "title": "保号套餐", "icon": "shield", "type": "native", "action": "/number-retention", "position": 1, "color": "#007AFF"}, {"id": "mobile-recharge", "title": "手机充值", "icon": "smartphone", "type": "native", "action": "/mobile-recharge", "position": 2, "color": "#34C759"}, {"id": "travel-packages", "title": "旅游套餐", "icon": "map", "type": "native", "action": "/travel-packages", "position": 3, "color": "#FF9500"}, {"id": "local-packages", "title": "本地套餐", "icon": "map-pin", "type": "native", "action": "/local-packages", "position": 4, "color": "#5856D6"}, {"id": "data-boosters", "title": "加油流量包", "icon": "zap", "type": "native", "action": "/data-boosters", "position": 5, "color": "#FF3B30"}, {"id": "data-cards", "title": "流量卡", "icon": "credit-card", "type": "html", "action": "/pages/content?pageId=data-cards", "position": 6, "color": "#5856D6"}, {"id": "travel-data", "title": "出行流量", "icon": "car", "type": "html", "action": "/pages/content?pageId=travel-data", "position": 7, "color": "#007AFF"}, {"id": "5g-packages", "title": "5G套餐", "icon": "wifi", "type": "html", "action": "/pages/content?pageId=5g-packages", "position": 8, "color": "#34C759"}, {"id": "international-roaming", "title": "国际漫游", "icon": "globe", "type": "html", "action": "/pages/content?pageId=international-roaming", "position": 9, "color": "#8E8E93"}], "recommendedPackages": [{"id": "1", "name": "欧洲旅行套餐", "description": "欧洲通用30天数据", "price": 29.99, "currency": "CNY", "imageUrl": "https://example.com/europe-package.jpg", "dataSize": 10240, "planType": "Total", "countries": ["法国", "德国", "意大利"]}], "travelTips": [{"id": "1", "title": "亚洲最佳数据套餐", "imageUrl": "https://example.com/asia-tips.jpg", "link": "/tips/asia-data-plans"}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/home/<USER>": {"get": {"tags": ["首页核心接口"], "summary": "获取推荐内容", "description": "获取个性化推荐内容", "operationId": "getRecommendations", "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}, {"name": "type", "in": "query", "description": "推荐类型", "required": false, "schema": {"type": "string", "enum": ["tips", "promotions", "packages"], "example": "packages"}}, {"name": "limit", "in": "query", "description": "返回数量限制", "required": false, "schema": {"type": "integer", "minimum": 1, "example": 5}}], "responses": {"200": {"description": "成功获取推荐内容", "content": {"application/json": {"schema": {"type": "object", "properties": {"recommendations": {"type": "array", "items": {"$ref": "#/components/schemas/Recommendation"}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/guides/nearby": {"get": {"tags": ["首页核心接口"], "summary": "获取附近指南", "description": "获取基于位置的附近指南信息", "operationId": "getNearbyGuides", "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}, {"name": "lat", "in": "query", "description": "纬度", "required": true, "schema": {"type": "number", "format": "double", "example": 39.9042}}, {"name": "lng", "in": "query", "description": "经度", "required": true, "schema": {"type": "number", "format": "double", "example": 116.4074}}, {"name": "radius", "in": "query", "description": "搜索半径(公里)", "required": false, "schema": {"type": "number", "minimum": 0.1, "example": 5.0}}, {"name": "limit", "in": "query", "description": "返回数量限制", "required": false, "schema": {"type": "integer", "minimum": 1, "example": 10}}], "responses": {"200": {"description": "成功获取附近指南", "content": {"application/json": {"schema": {"type": "object", "properties": {"guides": {"type": "array", "items": {"$ref": "#/components/schemas/NearbyGuide"}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/number-retention": {"get": {"tags": ["9宫格原生模块"], "summary": "获取保号套餐列表", "description": "获取保号套餐列表，支持分类、套餐类型等筛选", "operationId": "getNumberRetentionPackages", "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}, {"name": "category", "in": "query", "description": "套餐分类", "required": false, "schema": {"type": "string", "example": "basic"}}, {"name": "planType", "in": "query", "description": "套餐类型", "required": false, "schema": {"type": "string", "enum": ["monthly", "yearly", "unlimited"], "example": "monthly"}}, {"$ref": "#/components/parameters/Page"}, {"$ref": "#/components/parameters/PageSize"}, {"$ref": "#/components/parameters/SortBy"}, {"$ref": "#/components/parameters/SortOrder"}], "responses": {"200": {"description": "成功获取保号套餐列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NumberRetentionPackagesResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/number-retention/{packageId}": {"get": {"tags": ["9宫格原生模块"], "summary": "获取保号套餐详情", "description": "根据套餐ID获取保号套餐详细信息", "operationId": "getNumberRetentionPackageById", "parameters": [{"name": "packageId", "in": "path", "description": "套餐ID", "required": true, "schema": {"type": "string", "example": "nr-001"}}, {"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}], "responses": {"200": {"description": "成功获取套餐详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NumberRetentionPackageDetail"}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/mobile-recharge/options": {"get": {"tags": ["9宫格原生模块"], "summary": "获取手机充值选项", "description": "获取可用的手机充值选项，支持运营商和账户类型筛选", "operationId": "getRechargeOptions", "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}, {"name": "operator", "in": "query", "description": "运营商", "required": false, "schema": {"type": "string", "example": "china-mobile"}}, {"name": "accountType", "in": "query", "description": "账户类型", "required": false, "schema": {"type": "string", "enum": ["prepaid", "postpaid"], "example": "prepaid"}}, {"$ref": "#/components/parameters/Page"}, {"$ref": "#/components/parameters/PageSize"}, {"name": "sortBy", "in": "query", "description": "排序字段", "required": false, "schema": {"type": "string", "default": "amount", "example": "amount"}}, {"$ref": "#/components/parameters/SortOrder"}], "responses": {"200": {"description": "成功获取充值选项", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RechargeOptionsResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/mobile-recharge/order": {"post": {"tags": ["9宫格原生模块"], "summary": "创建手机充值订单", "description": "创建手机充值订单", "operationId": "createRechargeOrder", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RechargeOrderRequest"}}}}, "responses": {"201": {"description": "充值订单创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/travel-packages": {"get": {"tags": ["9宫格原生模块"], "summary": "获取旅游套餐列表", "description": "获取旅游套餐列表，支持目的地、地区、时长等筛选", "operationId": "getTravelPackages", "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}, {"name": "destination", "in": "query", "description": "目的地", "required": false, "schema": {"type": "string", "example": "欧洲"}}, {"name": "region", "in": "query", "description": "地区", "required": false, "schema": {"type": "string", "example": "亚洲"}}, {"name": "duration", "in": "query", "description": "天数", "required": false, "schema": {"type": "string", "enum": ["7", "15", "30", "90"], "example": "30"}}, {"name": "dataSize", "in": "query", "description": "数据大小", "required": false, "schema": {"type": "string", "enum": ["1GB", "3GB", "5GB", "10GB", "20GB", "unlimited"], "example": "5GB"}}, {"$ref": "#/components/parameters/Page"}, {"$ref": "#/components/parameters/PageSize"}, {"$ref": "#/components/parameters/SortBy"}, {"$ref": "#/components/parameters/SortOrder"}], "responses": {"200": {"description": "成功获取旅游套餐列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TravelPackagesResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/travel-packages/{packageId}": {"get": {"tags": ["9宫格原生模块"], "summary": "获取旅游套餐详情", "description": "根据套餐ID获取旅游套餐详细信息", "operationId": "getTravelPackageById", "parameters": [{"name": "packageId", "in": "path", "description": "套餐ID", "required": true, "schema": {"type": "string", "example": "tp-001"}}, {"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}], "responses": {"200": {"description": "成功获取套餐详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TravelPackageDetail"}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/travel-packages/order": {"post": {"tags": ["9宫格原生模块"], "summary": "创建旅游套餐订单", "description": "创建旅游套餐订单", "operationId": "createTravelOrder", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TravelPackageOrderRequest"}}}}, "responses": {"201": {"description": "旅游订单创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/local-packages": {"get": {"tags": ["9宫格原生模块"], "summary": "获取本地套餐列表", "description": "获取本地套餐列表，支持城市、省份、套餐类型等筛选", "operationId": "getLocalPackages", "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}, {"name": "city", "in": "query", "description": "城市", "required": false, "schema": {"type": "string", "example": "北京"}}, {"name": "province", "in": "query", "description": "省份", "required": false, "schema": {"type": "string", "example": "北京市"}}, {"name": "planType", "in": "query", "description": "套餐类型", "required": false, "schema": {"type": "string", "enum": ["daily", "weekly", "monthly"], "example": "monthly"}}, {"name": "dataSize", "in": "query", "description": "数据大小", "required": false, "schema": {"type": "string", "enum": ["1GB", "3GB", "5GB", "10GB", "20GB", "unlimited"], "example": "5GB"}}, {"$ref": "#/components/parameters/Page"}, {"$ref": "#/components/parameters/PageSize"}, {"$ref": "#/components/parameters/SortBy"}, {"$ref": "#/components/parameters/SortOrder"}], "responses": {"200": {"description": "成功获取本地套餐列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalPackagesResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/local-packages/{packageId}": {"get": {"tags": ["9宫格原生模块"], "summary": "获取本地套餐详情", "description": "根据套餐ID获取本地套餐详细信息", "operationId": "getLocalPackageById", "parameters": [{"name": "packageId", "in": "path", "description": "套餐ID", "required": true, "schema": {"type": "string", "example": "lp-001"}}, {"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}], "responses": {"200": {"description": "成功获取套餐详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalPackageDetail"}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/local-packages/order": {"post": {"tags": ["9宫格原生模块"], "summary": "创建本地套餐订单", "description": "创建本地套餐订单", "operationId": "createLocalOrder", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalPackageOrderRequest"}}}}, "responses": {"201": {"description": "本地订单创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/data-boosters": {"get": {"tags": ["9宫格原生模块"], "summary": "获取加油流量包列表", "description": "获取加油流量包列表，支持运营商、数据大小、有效期等筛选", "operationId": "getDataBoosters", "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}, {"name": "boosterType", "in": "query", "description": "流量包类型", "required": false, "schema": {"type": "string", "enum": ["emergency", "daily", "weekly", "monthly"], "example": "emergency"}}, {"name": "dataSize", "in": "query", "description": "数据大小", "required": false, "schema": {"type": "string", "enum": ["100MB", "500MB", "1GB", "3GB", "5GB", "10GB"], "example": "1GB"}}, {"name": "activationType", "in": "query", "description": "激活类型", "required": false, "schema": {"type": "string", "enum": ["instant", "scheduled"], "example": "instant"}}, {"$ref": "#/components/parameters/Page"}, {"$ref": "#/components/parameters/PageSize"}, {"$ref": "#/components/parameters/SortBy"}, {"$ref": "#/components/parameters/SortOrder"}], "responses": {"200": {"description": "成功获取流量包列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataBoostersResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/data-boosters/{boosterId}": {"get": {"tags": ["9宫格原生模块"], "summary": "获取流量包详情", "description": "根据流量包ID获取详细信息", "operationId": "getDataBoosterById", "parameters": [{"name": "boosterId", "in": "path", "description": "流量包ID", "required": true, "schema": {"type": "string", "example": "db-001"}}, {"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}], "responses": {"200": {"description": "成功获取流量包详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataBoosterDetail"}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/data-boosters/order": {"post": {"tags": ["9宫格原生模块"], "summary": "创建流量包订单", "description": "创建流量包订单", "operationId": "createDataBoosterOrder", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataBoosterOrderRequest"}}}}, "responses": {"201": {"description": "流量包订单创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/pages/configs": {"get": {"tags": ["HTML页面接口"], "summary": "获取页面配置", "description": "获取HTML页面的配置信息，包括布局、样式、组件等", "operationId": "getPageConfigs", "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}, {"name": "category", "in": "query", "description": "页面分类", "required": false, "schema": {"type": "string", "example": "products"}}, {"name": "layout", "in": "query", "description": "布局类型", "required": false, "schema": {"type": "string", "enum": ["grid", "list"], "example": "grid"}}, {"name": "version", "in": "query", "description": "页面版本", "required": false, "schema": {"type": "string", "example": "v1.0"}}], "responses": {"200": {"description": "成功获取页面配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageConfigsResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/pages/content": {"get": {"tags": ["HTML页面接口"], "summary": "获取页面内容", "description": "获取HTML页面的具体内容数据，支持4个页面类型", "operationId": "getPageContent", "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}, {"name": "pageId", "in": "query", "description": "页面ID", "required": true, "schema": {"type": "string", "enum": ["data-cards", "travel-data", "5g-packages", "international-roaming"], "example": "data-cards"}}, {"name": "version", "in": "query", "description": "页面版本", "required": false, "schema": {"type": "string", "example": "v1.0"}}, {"name": "includeAssets", "in": "query", "description": "是否包含资源文件", "required": false, "schema": {"type": "boolean", "example": false}}], "responses": {"200": {"description": "成功获取页面内容", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageContentResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/location/reverse-geocode": {"get": {"tags": ["支持功能接口"], "summary": "反向地理编码", "description": "将经纬度坐标转换为地理位置信息（国家、城市等）", "operationId": "reverseGeocode", "parameters": [{"$ref": "#/components/parameters/AcceptLanguage"}, {"$ref": "#/components/parameters/XTheme"}, {"$ref": "#/components/parameters/XCurrency"}, {"name": "lat", "in": "query", "description": "纬度", "required": true, "schema": {"type": "number", "format": "double", "minimum": -90, "maximum": 90, "example": 39.9042}}, {"name": "lng", "in": "query", "description": "经度", "required": true, "schema": {"type": "number", "format": "double", "minimum": -180, "maximum": 180, "example": 116.4074}}, {"name": "language", "in": "query", "description": "返回语言", "required": false, "schema": {"type": "string", "enum": ["zh", "en"], "default": "zh", "example": "zh"}}], "responses": {"200": {"description": "成功获取地理位置信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReverseGeocodeResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/health": {"get": {"tags": ["支持功能接口"], "summary": "健康检查", "description": "检查服务健康状态和数据库连接", "operationId": "checkHealth", "responses": {"200": {"description": "服务正常", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}, "500": {"description": "服务异常", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}}}}, "components": {"schemas": {"HomeDataResponse": {"type": "object", "properties": {"banners": {"type": "array", "items": {"$ref": "#/components/schemas/Banner"}}, "categories": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "gridButtons": {"type": "array", "items": {"$ref": "#/components/schemas/GridButton"}}, "recommendedPackages": {"type": "array", "items": {"$ref": "#/components/schemas/RecommendedPackage"}}, "travelTips": {"type": "array", "items": {"$ref": "#/components/schemas/TravelTip"}}}}, "Banner": {"type": "object", "properties": {"id": {"type": "string", "example": "1"}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/banner1.jpg"}, "title": {"type": "string", "example": "5G全球套餐促销"}, "link": {"type": "string", "example": "/products/5g-package"}, "priority": {"type": "integer", "example": 1}}}, "Category": {"type": "object", "properties": {"id": {"type": "string", "example": "1"}, "name": {"type": "string", "example": "旅行"}, "icon": {"type": "string", "format": "uri", "example": "https://example.com/travel-icon.png"}, "link": {"type": "string", "example": "/categories/travel"}}}, "GridButton": {"type": "object", "properties": {"id": {"type": "string", "example": "number-retention"}, "title": {"type": "string", "example": "保号套餐"}, "icon": {"type": "string", "example": "shield"}, "type": {"type": "string", "enum": ["native", "html"], "example": "native"}, "action": {"type": "string", "example": "/number-retention"}, "position": {"type": "integer", "minimum": 1, "maximum": 9, "example": 1}, "color": {"type": "string", "pattern": "^#[0-9A-Fa-f]{6}$", "example": "#007AFF"}}, "required": ["id", "title", "icon", "type", "action", "position", "color"]}, "RecommendedPackage": {"type": "object", "properties": {"id": {"type": "string", "example": "1"}, "name": {"type": "string", "example": "欧洲旅行套餐"}, "description": {"type": "string", "example": "欧洲通用30天数据"}, "price": {"type": "number", "format": "float", "example": 29.99}, "currency": {"type": "string", "example": "CNY"}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/europe-package.jpg"}, "dataSize": {"type": "integer", "description": "数据大小(MB)", "example": 10240}, "planType": {"type": "string", "example": "Total"}, "countries": {"type": "array", "items": {"type": "string"}, "example": ["法国", "德国", "意大利"]}}}, "TravelTip": {"type": "object", "properties": {"id": {"type": "string", "example": "1"}, "title": {"type": "string", "example": "亚洲最佳数据套餐"}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/asia-tips.jpg"}, "link": {"type": "string", "example": "/tips/asia-data-plans"}}}, "DetailedBanner": {"type": "object", "properties": {"id": {"type": "string", "example": "1"}, "type": {"type": "string", "example": "carousel"}, "title": {"type": "string", "example": "5G流量包全球促销"}, "subtitle": {"type": "string", "example": "限时8.5折优惠"}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/banner1.jpg"}, "link": {"type": "string", "example": "/products/5g-global"}, "position": {"type": "string", "example": "home"}, "priority": {"type": "integer", "example": 1}, "startDate": {"type": "string", "format": "date-time", "example": "2023-09-01T00:00:00Z"}, "endDate": {"type": "string", "format": "date-time", "example": "2023-10-01T00:00:00Z"}}}, "Recommendation": {"type": "object", "properties": {"id": {"type": "string", "example": "rec-001"}, "type": {"type": "string", "enum": ["tips", "promotions", "packages"], "example": "packages"}, "title": {"type": "string", "example": "推荐套餐"}, "description": {"type": "string", "example": "为您推荐的优质套餐"}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/recommendation.jpg"}, "link": {"type": "string", "example": "/packages/recommended"}}}, "NearbyGuide": {"type": "object", "properties": {"id": {"type": "string", "example": "guide-001"}, "title": {"type": "string", "example": "北京旅游指南"}, "description": {"type": "string", "example": "北京地区旅游和通信指南"}, "distance": {"type": "number", "format": "float", "description": "距离(公里)", "example": 2.5}, "location": {"type": "object", "properties": {"lat": {"type": "number", "format": "double", "example": 39.9042}, "lng": {"type": "number", "format": "double", "example": 116.4074}}}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/beijing-guide.jpg"}, "link": {"type": "string", "example": "/guides/beijing"}}}, "NumberRetentionPackagesResponse": {"type": "object", "properties": {"packages": {"type": "array", "items": {"$ref": "#/components/schemas/NumberRetentionPackage"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "categories": {"type": "array", "items": {"$ref": "#/components/schemas/PackageCategory"}}, "context": {"$ref": "#/components/schemas/RequestContext"}}}, "NumberRetentionPackage": {"type": "object", "properties": {"id": {"type": "string", "example": "nr-001"}, "name": {"type": "string", "example": "基础保号套餐"}, "description": {"type": "string", "example": "适合轻度使用的保号套餐"}, "price": {"type": "number", "format": "float", "example": 19.99}, "originalPrice": {"type": "number", "format": "float", "example": 29.99}, "currency": {"type": "string", "example": "CNY"}, "planType": {"type": "string", "enum": ["monthly", "yearly", "unlimited"], "example": "monthly"}, "features": {"type": "array", "items": {"type": "string"}, "example": ["保号功能", "基础通话", "短信服务"]}, "validity": {"type": "string", "example": "30天"}, "category": {"type": "string", "example": "basic"}, "isPopular": {"type": "boolean", "example": false}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/nr-package.jpg"}}}, "NumberRetentionPackageDetail": {"allOf": [{"$ref": "#/components/schemas/NumberRetentionPackage"}, {"type": "object", "properties": {"detailedFeatures": {"type": "object", "properties": {"calls": {"type": "object", "properties": {"minutes": {"type": "integer", "example": 100}, "description": {"type": "string", "example": "国内通话100分钟"}}}, "sms": {"type": "object", "properties": {"count": {"type": "integer", "example": 50}, "description": {"type": "string", "example": "国内短信50条"}}}, "data": {"type": "object", "properties": {"amount": {"type": "string", "example": "1GB"}, "description": {"type": "string", "example": "国内流量1GB"}}}, "validity": {"type": "object", "properties": {"days": {"type": "integer", "example": 30}, "description": {"type": "string", "example": "有效期30天"}}}}}, "terms": {"type": "array", "items": {"type": "string"}, "example": ["仅限国内使用", "不可转让", "到期自动失效"]}}}]}, "RechargeOptionsResponse": {"type": "object", "properties": {"rechargeOptions": {"type": "array", "items": {"$ref": "#/components/schemas/RechargeOption"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "operators": {"type": "array", "items": {"$ref": "#/components/schemas/Operator"}}, "context": {"$ref": "#/components/schemas/RequestContext"}}}, "RechargeOption": {"type": "object", "properties": {"id": {"type": "string", "example": "recharge-001"}, "operator": {"type": "string", "example": "china-mobile"}, "operatorName": {"type": "string", "example": "中国移动"}, "amount": {"type": "number", "format": "float", "example": 20.0}, "currency": {"type": "string", "example": "CNY"}, "discount": {"type": "number", "format": "float", "example": 0.95}, "finalAmount": {"type": "number", "format": "float", "example": 19.0}, "accountType": {"type": "string", "enum": ["prepaid", "postpaid"], "example": "prepaid"}, "description": {"type": "string", "example": "中国移动20元充值"}, "processingTime": {"type": "string", "example": "即时到账"}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/china-mobile.png"}, "isPopular": {"type": "boolean", "example": true}}}, "RechargeOrderRequest": {"type": "object", "required": ["phoneNumber", "operator", "amount"], "properties": {"phoneNumber": {"type": "string", "pattern": "^1[3-9]\\d{9}$", "example": "***********"}, "operator": {"type": "string", "example": "china-mobile"}, "amount": {"type": "number", "format": "float", "minimum": 1, "example": 20.0}, "accountType": {"type": "string", "enum": ["prepaid", "postpaid"], "example": "prepaid"}}}, "OrderResponse": {"type": "object", "properties": {"orderId": {"type": "string", "example": "order-123456"}, "status": {"type": "string", "enum": ["pending", "processing", "completed", "failed"], "example": "pending"}, "amount": {"type": "number", "format": "float", "example": 20.0}, "currency": {"type": "string", "example": "CNY"}, "createdAt": {"type": "string", "format": "date-time", "example": "2023-09-01T10:00:00Z"}, "paymentUrl": {"type": "string", "format": "uri", "example": "https://payment.example.com/pay/order-123456"}}}, "Pagination": {"type": "object", "properties": {"total": {"type": "integer", "example": 100}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 10}, "hasMore": {"type": "boolean", "example": true}}}, "PackageCategory": {"type": "object", "properties": {"id": {"type": "string", "example": "basic"}, "name": {"type": "string", "example": "基础套餐"}}}, "Operator": {"type": "object", "properties": {"id": {"type": "string", "example": "china-mobile"}, "name": {"type": "string", "example": "中国移动"}, "logo": {"type": "string", "format": "uri", "example": "https://example.com/china-mobile-logo.png"}}}, "RequestContext": {"type": "object", "properties": {"language": {"type": "string", "example": "zh-CN"}, "theme": {"type": "string", "example": "light"}, "currency": {"type": "string", "example": "CNY"}}}, "TravelPackagesResponse": {"type": "object", "properties": {"packages": {"type": "array", "items": {"$ref": "#/components/schemas/TravelPackage"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "destinations": {"type": "array", "items": {"$ref": "#/components/schemas/Destination"}}, "context": {"$ref": "#/components/schemas/RequestContext"}}}, "TravelPackage": {"type": "object", "properties": {"id": {"type": "string", "example": "tp-001"}, "name": {"type": "string", "example": "欧洲旅行套餐"}, "description": {"type": "string", "example": "欧洲多国通用30天数据套餐"}, "price": {"type": "number", "format": "float", "example": 89.99}, "originalPrice": {"type": "number", "format": "float", "example": 99.99}, "currency": {"type": "string", "example": "CNY"}, "destination": {"type": "string", "example": "欧洲"}, "countries": {"type": "array", "items": {"type": "string"}, "example": ["法国", "德国", "意大利", "西班牙"]}, "dataSize": {"type": "string", "example": "10GB"}, "duration": {"type": "string", "example": "30天"}, "features": {"type": "array", "items": {"type": "string"}, "example": ["高速4G/5G", "热点分享", "无限制速"]}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/europe-travel.jpg"}, "isPopular": {"type": "boolean", "example": true}}}, "TravelPackageDetail": {"allOf": [{"$ref": "#/components/schemas/TravelPackage"}, {"type": "object", "properties": {"detailedFeatures": {"type": "object", "properties": {"coverage": {"type": "object", "properties": {"countries": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "example": "法国"}, "code": {"type": "string", "example": "FR"}, "operators": {"type": "array", "items": {"type": "string"}, "example": ["Orange", "SFR"]}}}}}}, "usage": {"type": "object", "properties": {"dataLimit": {"type": "string", "example": "10GB总量"}, "speedLimit": {"type": "string", "example": "无限制速"}, "hotspot": {"type": "boolean", "example": true}}}}}, "terms": {"type": "array", "items": {"type": "string"}, "example": ["仅限旅游使用", "激活后30天有效", "不可转让"]}}}]}, "TravelPackageOrderRequest": {"type": "object", "required": ["packageId", "startDate"], "properties": {"packageId": {"type": "string", "example": "tp-001"}, "startDate": {"type": "string", "format": "date", "example": "2023-10-01"}, "endDate": {"type": "string", "format": "date", "example": "2023-10-31"}, "travelerInfo": {"type": "object", "properties": {"name": {"type": "string", "example": "张三"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "***********"}}}}}, "LocalPackagesResponse": {"type": "object", "properties": {"packages": {"type": "array", "items": {"$ref": "#/components/schemas/LocalPackage"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "cities": {"type": "array", "items": {"$ref": "#/components/schemas/City"}}, "context": {"$ref": "#/components/schemas/RequestContext"}}}, "LocalPackage": {"type": "object", "properties": {"id": {"type": "string", "example": "lp-001"}, "name": {"type": "string", "example": "北京本地套餐"}, "description": {"type": "string", "example": "北京地区专用月度套餐"}, "price": {"type": "number", "format": "float", "example": 39.99}, "currency": {"type": "string", "example": "CNY"}, "city": {"type": "string", "example": "北京"}, "province": {"type": "string", "example": "北京市"}, "planType": {"type": "string", "enum": ["daily", "weekly", "monthly"], "example": "monthly"}, "dataSize": {"type": "string", "example": "5GB"}, "features": {"type": "array", "items": {"type": "string"}, "example": ["本地高速流量", "免费通话", "短信服务"]}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/beijing-local.jpg"}}}, "LocalPackageDetail": {"allOf": [{"$ref": "#/components/schemas/LocalPackage"}, {"type": "object", "properties": {"coverage": {"type": "object", "properties": {"areas": {"type": "array", "items": {"type": "string"}, "example": ["朝阳区", "海淀区", "西城区"]}, "operators": {"type": "array", "items": {"type": "string"}, "example": ["中国移动", "中国联通"]}}}}}]}, "LocalPackageOrderRequest": {"type": "object", "required": ["packageId", "phoneNumber"], "properties": {"packageId": {"type": "string", "example": "lp-001"}, "phoneNumber": {"type": "string", "pattern": "^1[3-9]\\d{9}$", "example": "***********"}, "activationDate": {"type": "string", "format": "date", "example": "2023-10-01"}}}, "DataBoostersResponse": {"type": "object", "properties": {"boosters": {"type": "array", "items": {"$ref": "#/components/schemas/DataBooster"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "operators": {"type": "array", "items": {"$ref": "#/components/schemas/Operator"}}, "context": {"$ref": "#/components/schemas/RequestContext"}}}, "DataBooster": {"type": "object", "properties": {"id": {"type": "string", "example": "db-001"}, "name": {"type": "string", "example": "1GB流量包"}, "description": {"type": "string", "example": "通用1GB流量包，30天有效"}, "price": {"type": "number", "format": "float", "example": 15.99}, "currency": {"type": "string", "example": "CNY"}, "operator": {"type": "string", "example": "china-mobile"}, "operatorName": {"type": "string", "example": "中国移动"}, "dataSize": {"type": "string", "example": "1GB"}, "validity": {"type": "string", "example": "30天"}, "boosterType": {"type": "string", "enum": ["general", "video", "social", "gaming"], "example": "general"}, "features": {"type": "array", "items": {"type": "string"}, "example": ["高速流量", "全国通用", "即时生效"]}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/data-booster.jpg"}, "isPopular": {"type": "boolean", "example": false}}}, "DataBoosterDetail": {"allOf": [{"$ref": "#/components/schemas/DataBooster"}, {"type": "object", "properties": {"usage": {"type": "object", "properties": {"restrictions": {"type": "array", "items": {"type": "string"}, "example": ["仅限4G/5G网络", "不可用于热点分享"]}, "speedLimit": {"type": "string", "example": "无限制速"}}}, "activation": {"type": "object", "properties": {"method": {"type": "string", "example": "短信激活"}, "timeframe": {"type": "string", "example": "购买后24小时内"}}}}}]}, "DataBoosterOrderRequest": {"type": "object", "required": ["boosterId", "phoneNumber"], "properties": {"boosterId": {"type": "string", "example": "db-001"}, "phoneNumber": {"type": "string", "pattern": "^1[3-9]\\d{9}$", "example": "***********"}, "operator": {"type": "string", "example": "china-mobile"}, "activationTime": {"type": "string", "enum": ["immediate", "scheduled"], "default": "immediate", "example": "immediate"}, "scheduledDate": {"type": "string", "format": "date-time", "example": "2023-10-01T09:00:00Z"}}}, "PageConfigsResponse": {"type": "object", "properties": {"configs": {"type": "array", "items": {"$ref": "#/components/schemas/PageConfig"}}, "context": {"$ref": "#/components/schemas/RequestContext"}}}, "PageConfig": {"type": "object", "properties": {"pageId": {"type": "string", "enum": ["data-cards", "travel-data", "5g-packages", "international-roaming"], "example": "data-cards"}, "title": {"type": "string", "example": "流量卡"}, "version": {"type": "string", "example": "v1.0"}, "layout": {"type": "object", "properties": {"type": {"type": "string", "example": "grid"}, "columns": {"type": "integer", "example": 2}, "sections": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "hero"}, "type": {"type": "string", "example": "banner"}, "order": {"type": "integer", "example": 1}}}}}}, "theme": {"type": "object", "properties": {"primaryColor": {"type": "string", "example": "#007AFF"}, "backgroundColor": {"type": "string", "example": "#FFFFFF"}}}}}, "PageContentResponse": {"type": "object", "properties": {"pageId": {"type": "string", "example": "data-cards"}, "content": {"type": "object", "properties": {"hero": {"$ref": "#/components/schemas/HeroSection"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/ProductCard"}}, "promotions": {"type": "array", "items": {"$ref": "#/components/schemas/Promotion"}}}}, "context": {"$ref": "#/components/schemas/RequestContext"}}}, "HeroSection": {"type": "object", "properties": {"title": {"type": "string", "example": "全球流量卡"}, "subtitle": {"type": "string", "example": "随时随地，畅享网络"}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/hero-image.jpg"}, "ctaButton": {"type": "object", "properties": {"text": {"type": "string", "example": "立即购买"}, "action": {"type": "string", "example": "/products/data-cards"}}}}}, "ProductCard": {"type": "object", "properties": {"id": {"type": "string", "example": "pc-001"}, "name": {"type": "string", "example": "全球通用流量卡"}, "price": {"type": "number", "format": "float", "example": 59.99}, "currency": {"type": "string", "example": "CNY"}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/product-card.jpg"}, "features": {"type": "array", "items": {"type": "string"}, "example": ["全球覆盖", "高速网络", "即插即用"]}, "link": {"type": "string", "example": "/products/pc-001"}}}, "Promotion": {"type": "object", "properties": {"id": {"type": "string", "example": "promo-001"}, "title": {"type": "string", "example": "限时优惠"}, "description": {"type": "string", "example": "全场8.5折优惠"}, "discount": {"type": "number", "format": "float", "example": 0.85}, "validUntil": {"type": "string", "format": "date-time", "example": "2023-10-31T23:59:59Z"}, "imageUrl": {"type": "string", "format": "uri", "example": "https://example.com/promotion.jpg"}}}, "ReverseGeocodeResponse": {"type": "object", "properties": {"location": {"type": "object", "properties": {"lat": {"type": "number", "format": "double", "example": 39.9042}, "lng": {"type": "number", "format": "double", "example": 116.4074}}}, "address": {"type": "object", "properties": {"country": {"type": "string", "example": "中国"}, "countryCode": {"type": "string", "example": "CN"}, "province": {"type": "string", "example": "北京市"}, "city": {"type": "string", "example": "北京"}, "district": {"type": "string", "example": "朝阳区"}, "street": {"type": "string", "example": "建国门外大街"}, "formattedAddress": {"type": "string", "example": "中国北京市朝阳区建国门外大街"}}}, "timezone": {"type": "string", "example": "Asia/Shanghai"}}}, "HealthResponse": {"type": "object", "properties": {"status": {"type": "string", "enum": ["ok", "error"], "example": "ok"}, "timestamp": {"type": "string", "format": "date-time", "example": "2023-09-01T10:00:00Z"}, "database": {"type": "string", "enum": ["connected", "disconnected"], "example": "connected"}, "error": {"type": "string", "example": null}}}, "Destination": {"type": "object", "properties": {"id": {"type": "string", "example": "europe"}, "name": {"type": "string", "example": "欧洲"}, "countries": {"type": "array", "items": {"type": "string"}, "example": ["法国", "德国", "意大利"]}}}, "City": {"type": "object", "properties": {"id": {"type": "string", "example": "beijing"}, "name": {"type": "string", "example": "北京"}, "province": {"type": "string", "example": "北京市"}}}, "ErrorResponse": {"type": "object", "properties": {"statusCode": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "请求参数错误"}, "error": {"type": "string", "example": "Bad Request"}, "timestamp": {"type": "string", "format": "date-time", "example": "2023-09-01T10:00:00Z"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"statusCode": 400, "message": "请求参数错误", "error": "Bad Request", "timestamp": "2023-09-01T10:00:00Z"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"statusCode": 401, "message": "未授权访问", "error": "Unauthorized", "timestamp": "2023-09-01T10:00:00Z"}}}}, "NotFound": {"description": "资源未找到", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"statusCode": 404, "message": "资源未找到", "error": "Not Found", "timestamp": "2023-09-01T10:00:00Z"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"statusCode": 500, "message": "服务器内部错误", "error": "Internal Server Error", "timestamp": "2023-09-01T10:00:00Z"}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "parameters": {"AcceptLanguage": {"name": "Accept-Language", "in": "header", "description": "语言设置", "required": false, "schema": {"type": "string", "enum": ["zh-CN", "en-US"], "default": "zh-CN"}}, "XTheme": {"name": "X-Theme", "in": "header", "description": "主题设置", "required": false, "schema": {"type": "string", "enum": ["light", "dark"], "default": "light"}}, "XCurrency": {"name": "X-<PERSON><PERSON><PERSON><PERSON>", "in": "header", "description": "货币设置", "required": false, "schema": {"type": "string", "enum": ["CNY", "USD"], "default": "CNY"}}, "Page": {"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 1, "example": 1}}, "PageSize": {"name": "pageSize", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10, "example": 10}}, "SortBy": {"name": "sortBy", "in": "query", "description": "排序字段", "required": false, "schema": {"type": "string", "default": "price", "example": "price"}}, "SortOrder": {"name": "sortOrder", "in": "query", "description": "排序方向", "required": false, "schema": {"type": "string", "enum": ["asc", "desc"], "default": "asc", "example": "asc"}}}}}