import { NextRequest, NextResponse } from 'next/server';
import { bossService } from '@/lib/boss';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

/**
 * GET 请求 - 获取订单分页数据
 */
export async function GET(request: NextRequest) {
  try {
    // 从URL参数中获取分页信息
    const searchParams = request.nextUrl.searchParams;
    const pageNum = parseInt(searchParams.get('pageNum') || '0');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const uid = searchParams.get('uid');
    const orderSn = searchParams.get('orderSn');
    const packageStatus = searchParams.get('packageStatus') ? 
      parseInt(searchParams.get('packageStatus') as string) : undefined;
    const esimProfileStatus = searchParams.get('esimProfileStatus');
    
    // 构建查询参数
    const queryParams = {
      pageNum,
      pageSize,
      uid,
      queryParam: {
        ...(uid && { uid }),
        ...(orderSn && { orderSn }),
        ...(packageStatus !== undefined && { packageStatus }),
        ...(esimProfileStatus && { esimProfileStatus })
      }
    };
    
    const response = await bossService.getDeviceOrderPage(queryParams);
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching order data from Boss service:', error);
    return NextResponse.json(
      {
        resultCode: '500',
        resultMsg: 'Failed to fetch order data',
        data: null
      },
      { status: 500 }
    );
  }
}

/**
 * POST 请求 - 创建订单或执行订单操作
 */
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const { action, ...params } = data;
    
    let response;
    
    // 根据请求的action调用不同的接口
    switch (action) {
      case 'createProductOrder':
        response = await bossService.createProductOrder(params);
        break;
      case 'createRpmOrder':
        response = await bossService.createRpmOrder(params);
        break;
      case 'cancelOrderPlan':
        response = await bossService.cancelOrderPlan(params.orderSn);
        break;
      case 'closeOrderPlan':
        response = await bossService.closeOrderPlan(params.orderSn);
        break;
      case 'queryUsageOrderPlan':
        response = await bossService.queryUsageOrderPlan(params.orderSn);
        break;
      case 'queryOrderPlan':
        response = await bossService.queryOrderPlan(params.orderSn);
        break;
      case 'toppingOrderPlan':
        response = await bossService.toppingOrderPlan(params.orderSn, params.uid);
        break;
      default:
        return NextResponse.json(
          {
            resultCode: '400',
            resultMsg: 'Invalid action',
            data: null
          },
          { status: 400 }
        );
    }
    
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error processing order operation:', error);
    return NextResponse.json(
      {
        resultCode: '500',
        resultMsg: 'Failed to process order operation',
        data: null
      },
      { status: 500 }
    );
  }
}
