/**
 * ContextMiddleware 统一解析 Accept-Language、X-Theme、X-Currency header
 * 并挂载到 req.context，供后续业务逻辑使用。
 * 默认值：language=en-US, theme=light, currency=USD
 */
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { RequestContext } from '../interfaces/context.interface';

@Injectable()
export class ContextMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    const language = req.header('accept-language') || 'en-US';
    const theme = req.header('x-theme') || 'light';
    const currency = req.header('x-currency') || 'USD';

    const context: RequestContext = {
      language,
      theme,
      currency,
    };
    // 挂载到 req 对象
    (req as any).context = context;
    next();
  }
}
