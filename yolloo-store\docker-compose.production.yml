services:

  # 主应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yolloo-app-production
    ports:
      - "8000:8000"
    env_file:
      - .env.production
    restart: always
    extra_hosts:
      - "openapi.vcs3-distribution.testing:***********"
      - "boss-server.vcs3-distribution.testing:***********"
      - "postgresql.postgresql:*************"
      - "redis-master.redis:*************"
      - "openapi.simmesh.com:***********"
      - "bs2-openmsg.simmesh.com:***********"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - mobile-api

  # 移动 API 服务
  mobile-api:
    build:
      context: .
      dockerfile: mobile-api/Dockerfile
    container_name: yolloo-mobile-api-production
    ports:
      - "4000:4000"
    env_file:
      - .env.production
    restart: always
    extra_hosts:
      - "openapi.vcs3-distribution.testing:***********"
      - "boss-server.vcs3-distribution.testing:***********"
      - "postgresql.postgresql:*************"
      - "redis-master.redis:*************"
      - "openapi.simmesh.com:***********"
      - "bs2-openmsg.simmesh.com:***********"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
