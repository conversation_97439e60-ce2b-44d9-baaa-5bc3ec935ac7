import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { AddCartItemDto } from './dto/add-cart-item.dto';
import { UpdateCartItemDto } from './dto/update-cart-item.dto';

@Injectable()
export class CartService {
  constructor(private prisma: PrismaService) {}

  async getCart(userId: string) {
    // 查询用户的购物车项目
    const cartItems = await this.prisma.cartItem.findMany({
      where: { userId },
      include: {
        product: true,
        variant: true,
      },
    });

    // 如果购物车为空，返回空购物车
    if (cartItems.length === 0) {
      return {
        items: [],
        summary: {
          subtotal: 0,
          shipping: 0,
          tax: 0,
          total: 0,
          currency: 'USD',
        },
      };
    }

    // 格式化购物车项目
    const items = cartItems.map(item => {
      const price = item.variant ? Number(item.variant.price) : item.product.price;
      const currency = item.variant ? item.variant.currency : 'USD';

      // 从规格中获取国家信息
      const specs = typeof item.product.specifications === 'string'
        ? JSON.parse(item.product.specifications)
        : item.product.specifications;
      const countries = specs?.countries || [];

      return {
        id: item.id,
        productId: item.productId,
        variantId: item.variantId,
        name: item.product.name,
        price: price, // 确保价格是数字类型
        currency: currency,
        quantity: item.quantity,
        imageUrl: item.product.images && item.product.images.length > 0 ?
          item.product.images[0] : 'https://example.com/default-product.jpg',
        attributes: {
          dataSize: item.product.dataSize || 0,
          duration: item.variant && item.variant.duration ? item.variant.duration : 30,
          countries: countries,
        },
      };
    });

    // 计算总价
    const subtotal = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const shipping = 0; // 假设免运费
    const tax = 0; // 假设免税
    const total = subtotal + shipping + tax;
    const currency = items[0].currency; // 使用第一个项目的货币

    return {
      items,
      summary: {
        subtotal,
        shipping,
        tax,
        total,
        currency,
      },
    };
  }

  async addCartItem(userId: string, addCartItemDto: AddCartItemDto) {
    const { productId, variantId, quantity } = addCartItemDto;

    // 检查产品是否存在
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // 如果指定了变体，检查变体是否存在
    if (variantId) {
      const variant = await this.prisma.productVariant.findUnique({
        where: { id: variantId },
      });

      if (!variant || variant.productId !== productId) {
        throw new NotFoundException('Product variant not found');
      }
    }

    // 检查购物车中是否已有相同产品和变体
    const existingCartItem = await this.prisma.cartItem.findFirst({
      where: {
        userId,
        productId,
        variantId: variantId || null,
      },
    });

    let cartItem;

    if (existingCartItem) {
      // 如果已存在，更新数量
      cartItem = await this.prisma.cartItem.update({
        where: { id: existingCartItem.id },
        data: { quantity: existingCartItem.quantity + quantity },
      });
    } else {
      // 如果不存在，创建新项目
      cartItem = await this.prisma.cartItem.create({
        data: {
          userId,
          productId,
          variantId,
          quantity,
        },
      });
    }

    return {
      id: cartItem.id,
      productId: cartItem.productId,
      variantId: cartItem.variantId,
      quantity: cartItem.quantity,
      message: '商品已添加到购物车',
    };
  }

  async updateCartItem(
    userId: string,
    itemId: string,
    updateCartItemDto: UpdateCartItemDto,
  ) {
    // 检查购物车项目是否存在且属于当前用户
    const cartItem = await this.prisma.cartItem.findFirst({
      where: {
        id: itemId,
        userId,
      },
    });

    if (!cartItem) {
      throw new NotFoundException('Cart item not found');
    }

    // 更新购物车项目数量
    const updatedCartItem = await this.prisma.cartItem.update({
      where: { id: itemId },
      data: { quantity: updateCartItemDto.quantity },
    });

    return {
      id: updatedCartItem.id,
      quantity: updatedCartItem.quantity,
      message: '购物车已更新',
    };
  }

  async removeCartItem(userId: string, itemId: string) {
    // 检查购物车项目是否存在且属于当前用户
    const cartItem = await this.prisma.cartItem.findFirst({
      where: {
        id: itemId,
        userId,
      },
    });

    if (!cartItem) {
      throw new NotFoundException('Cart item not found');
    }

    // 删除购物车项目
    await this.prisma.cartItem.delete({
      where: { id: itemId },
    });

    return {
      message: '商品已从购物车中移除',
    };
  }
}
