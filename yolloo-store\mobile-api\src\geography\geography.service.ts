import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { RatingService } from '../rating/rating.service';
import { RequestContext } from '../common/interfaces/context.interface';
import { GeographyQueryDto, ProductsByCountryDto } from './dto/geography-query.dto';
import {
  CONTINENTS,
  CONTINENT_COUNTRIES,
  CONTINENT_NAMES
} from '../common/constants/app.constants';

@Injectable()
export class GeographyService {
  private readonly logger = new Logger(GeographyService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly ratingService: RatingService
  ) {}

  /**
   * 获取所有大洲列表
   */
  async getContinents(ctx: RequestContext) {
    const isZh = ctx.language.startsWith('zh');

    try {
      // 首先尝试从数据库获取大洲数据
      const continents = await this.prisma.continent.findMany({
        where: {
          isActive: true,
        },
        orderBy: {
          code: 'asc',
        },
      });

      if (continents.length > 0) {
        const formattedContinents = continents.map(continent => ({
          id: continent.code,
          name: isZh ? continent.nameZh : continent.nameEn,
          nameEn: continent.nameEn,
          nameZh: continent.nameZh,
        }));

        return {
          continents: formattedContinents,
          context: {
            language: ctx.language,
            theme: ctx.theme,
            currency: ctx.currency,
          },
        };
      }

      // 如果数据库中没有数据，使用fallback
      const fallbackContinents = Object.keys(CONTINENT_NAMES).map(continentKey => ({
        id: continentKey,
        name: isZh ? CONTINENT_NAMES[continentKey].zh : CONTINENT_NAMES[continentKey].en,
        nameEn: CONTINENT_NAMES[continentKey].en,
        nameZh: CONTINENT_NAMES[continentKey].zh,
      }));

      return {
        continents: fallbackContinents,
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };

    } catch (error) {
      this.logger.error('Error fetching continents:', error);

      // 错误时使用fallback数据
      const fallbackContinents = Object.keys(CONTINENT_NAMES).map(continentKey => ({
        id: continentKey,
        name: isZh ? CONTINENT_NAMES[continentKey].zh : CONTINENT_NAMES[continentKey].en,
        nameEn: CONTINENT_NAMES[continentKey].en,
        nameZh: CONTINENT_NAMES[continentKey].zh,
      }));

      return {
        continents: fallbackContinents,
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };
    }
  }

  /**
   * 根据大洲获取国家列表
   */
  async getCountriesByContinent(continent: string, ctx: RequestContext) {
    const isZh = ctx.language.startsWith('zh');

    try {
      // 首先尝试从数据库获取国家数据
      const continentRecord = await this.prisma.continent.findFirst({
        where: {
          code: continent,
          isActive: true,
        },
        include: {
          countries: {
            where: {
              isActive: true,
            },
            orderBy: {
              nameEn: 'asc',
            },
          },
        },
      });

      if (continentRecord && continentRecord.countries.length > 0) {
        const formattedCountries = continentRecord.countries.map(country => ({
          code: country.code,
          name: isZh ? country.nameZh : country.nameEn,
          nameEn: country.nameEn,
          nameZh: country.nameZh,
          flagUrl: country.flagUrl,
          currency: country.currency,
          timezone: country.timezone,
        }));

        return {
          continent: {
            id: continentRecord.code,
            name: isZh ? continentRecord.nameZh : continentRecord.nameEn,
          },
          countries: formattedCountries,
          context: {
            language: ctx.language,
            theme: ctx.theme,
            currency: ctx.currency,
          },
        };
      }

      // 如果数据库中没有数据，使用fallback
      const countries = CONTINENT_COUNTRIES[continent] || [];
      const formattedCountries = countries.map(country => ({
        code: country.code,
        name: isZh ? country.name : country.nameEn,
        nameEn: country.nameEn,
        nameZh: country.name,
      }));

      return {
        continent: {
          id: continent,
          name: isZh ? CONTINENT_NAMES[continent]?.zh : CONTINENT_NAMES[continent]?.en,
        },
        countries: formattedCountries,
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };

    } catch (error) {
      this.logger.error('Error fetching countries by continent:', error);

      // 错误时使用fallback数据
      const countries = CONTINENT_COUNTRIES[continent] || [];
      const formattedCountries = countries.map(country => ({
        code: country.code,
        name: isZh ? country.name : country.nameEn,
        nameEn: country.nameEn,
        nameZh: country.name,
      }));

      return {
        continent: {
          id: continent,
          name: isZh ? CONTINENT_NAMES[continent]?.zh : CONTINENT_NAMES[continent]?.en,
        },
        countries: formattedCountries,
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };
    }
  }

  /**
   * 根据国家获取商品列表
   */
  async getProductsByCountry(dto: ProductsByCountryDto, ctx: RequestContext) {
    const { countryCode, planType, page = 1, pageSize = 10 } = dto;
    const isZh = ctx.language.startsWith('zh');

    try {
      // 构建查询条件
      const whereConditions: any = {
        status: 'ACTIVE',
        off_shelve: false,
      };

      // 如果指定了国家代码，添加国家过滤条件
      if (countryCode) {
        whereConditions.OR = [
          { countryCode: { contains: countryCode, mode: 'insensitive' } },
          { country: { contains: countryCode, mode: 'insensitive' } },
        ];
      }

      // 如果指定了计划类型，添加计划类型过滤条件
      if (planType) {
        whereConditions.planType = { equals: planType, mode: 'insensitive' };
      }

      // 查询产品总数
      const total = await this.prisma.product.count({
        where: whereConditions,
      });

      // 查询产品列表
      const skip = (page - 1) * pageSize;
      const products = await this.prisma.product.findMany({
        where: whereConditions,
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          variants: {
            select: {
              id: true,
              price: true,
              currency: true,
            },
            orderBy: {
              price: 'asc',
            },
          },
          reviews: {
            select: {
              rating: true,
            },
          },
        },
        skip,
        take: pageSize,
        orderBy: [
          { createdAt: 'desc' },
          { price: 'asc' },
        ],
      });

      // 格式化产品数据
      const formattedProducts = await Promise.all(products.map(async product => {
        // 使用RatingService计算真实评分
        const ratingData = await this.ratingService.calculateProductRating(product.id);

        // 获取最低价格（从变体中）
        const lowestPrice = product.variants.length > 0
          ? Math.min(...product.variants.map(v => Number(v.price)))
          : Number(product.price);

        // 获取货币（优先使用变体的货币，否则使用上下文货币）
        const currency = product.variants.length > 0
          ? product.variants[0].currency || ctx.currency
          : ctx.currency;

        // 解析规格中的国家信息
        let countries: string[] = [];
        try {
          const specs = typeof product.specifications === 'string'
            ? JSON.parse(product.specifications)
            : product.specifications;
          countries = specs?.countries || [];
        } catch (error) {
          this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
        }

        // 如果规格中没有国家信息，尝试从country字段解析
        if (countries.length === 0 && product.country) {
          countries = product.country.split(/[,;]/).map(c => c.trim()).filter(c => c);
        }

        return {
          id: product.id,
          name: product.name,
          description: product.description,
          price: lowestPrice,
          currency: currency,
          imageUrl: product.images && product.images.length > 0
            ? product.images[0]
            : '/images/defaults/product-placeholder.jpg',
          dataSize: product.dataSize || 0,
          planType: product.planType || 'Total',
          duration: 30, // 默认30天，可以从规格中获取
          countries: countries,
          countryCode: product.countryCode || countryCode,
          rating: ratingData.averageRating,
          reviewCount: ratingData.totalReviews,
          ratingDistribution: ratingData.ratingDistribution,
          category: {
            id: product.category.id,
            name: product.category.name,
          },
          variants: product.variants.map(variant => ({
            id: variant.id,
            price: Number(variant.price),
            currency: variant.currency,
          })),
        };
      }));

      return {
        products: formattedProducts,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
        filters: {
          countryCode,
          planType,
        },
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };

    } catch (error) {
      this.logger.error('Error fetching products by country:', error);

      // 如果数据库查询失败，返回fallback数据
      const mockProducts = this.generateMockProducts(countryCode || '', planType || '', isZh, ctx);
      const skip = (page - 1) * pageSize;
      const paginatedProducts = mockProducts.slice(skip, skip + pageSize);

      return {
        products: paginatedProducts,
        pagination: {
          page,
          pageSize,
          total: mockProducts.length,
          totalPages: Math.ceil(mockProducts.length / pageSize),
        },
        filters: {
          countryCode,
          planType,
        },
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
        error: 'Database query failed, showing fallback data',
      };
    }
  }

  /**
   * 生成Mock商品数据
   */
  private generateMockProducts(countryCode: string, planType: string, isZh: boolean, ctx: RequestContext) {
    const products: any[] = [];

    // 根据国家和计划类型生成不同的商品
    if (countryCode === 'JP') {
      // 日本商品
      if (planType === 'Total' || !planType) {
        products.push(
          {
            id: 'jp-total-500mb',
            name: isZh ? '500MB 流量卡' : '500MB Data Card',
            description: isZh ? '流量 | 全日本' : 'Data | All Japan',
            price: 39.9,
            currency: ctx.currency,
            imageUrl: '/images/products/jp-500mb.jpg',
            dataSize: 500,
            planType: 'Total',
            duration: 30,
            countries: ['JP'],
            countryCode: 'JP',
            rating: 4.8,
            reviewCount: 1250,
          },
          {
            id: 'jp-total-1gb',
            name: isZh ? '1GB 流量卡' : '1GB Data Card',
            description: isZh ? '流量 | 全日本' : 'Data | All Japan',
            price: 39.9,
            currency: ctx.currency,
            imageUrl: '/images/products/jp-1gb.jpg',
            dataSize: 1024,
            planType: 'Total',
            duration: 30,
            countries: ['JP'],
            countryCode: 'JP',
            rating: 4.7,
            reviewCount: 980,
          },
          {
            id: 'jp-total-3gb',
            name: isZh ? '3GB 流量卡' : '3GB Data Card',
            description: isZh ? '流量 | 全日本' : 'Data | All Japan',
            price: 39.9,
            currency: ctx.currency,
            imageUrl: '/images/products/jp-3gb.jpg',
            dataSize: 3072,
            planType: 'Total',
            duration: 30,
            countries: ['JP'],
            countryCode: 'JP',
            rating: 4.9,
            reviewCount: 1580,
          },
          {
            id: 'jp-total-10gb',
            name: isZh ? '10GB 流量卡' : '10GB Data Card',
            description: isZh ? '流量 | 全日本' : 'Data | All Japan',
            price: 39.9,
            currency: ctx.currency,
            imageUrl: '/images/products/jp-10gb.jpg',
            dataSize: 10240,
            planType: 'Total',
            duration: 30,
            countries: ['JP'],
            countryCode: 'JP',
            rating: 4.6,
            reviewCount: 750,
          }
        );
      }

      if (planType === 'Daily' || !planType) {
        products.push(
          {
            id: 'jp-daily-1gb',
            name: isZh ? '1GB 天卡' : '1GB Daily Card',
            description: isZh ? '流量 | 全日本' : 'Data | All Japan',
            price: 15.9,
            currency: ctx.currency,
            imageUrl: '/images/products/jp-daily-1gb.jpg',
            dataSize: 1024,
            planType: 'Daily',
            duration: 1,
            countries: ['JP'],
            countryCode: 'JP',
            rating: 4.5,
            reviewCount: 420,
          }
        );
      }
    }

    // 可以为其他国家添加类似的Mock数据
    if (countryCode === 'KR') {
      // 韩国商品
      if (planType === 'Total' || !planType) {
        products.push(
          {
            id: 'kr-total-1gb',
            name: isZh ? '1GB 流量卡' : '1GB Data Card',
            description: isZh ? '流量 | 全韩国' : 'Data | All Korea',
            price: 29.9,
            currency: ctx.currency,
            imageUrl: '/images/products/kr-1gb.jpg',
            dataSize: 1024,
            planType: 'Total',
            duration: 30,
            countries: ['KR'],
            countryCode: 'KR',
            rating: 4.7,
            reviewCount: 680,
          }
        );
      }
    }

    // 如果没有指定国家，返回所有国家的商品
    if (!countryCode) {
      return this.generateAllCountriesProducts(planType, isZh, ctx);
    }

    return products;
  }

  /**
   * 生成所有国家的商品数据
   */
  private generateAllCountriesProducts(planType: string, isZh: boolean, ctx: RequestContext) {
    const allProducts: any[] = [];

    // 为每个大洲的主要国家生成商品
    Object.keys(CONTINENT_COUNTRIES).forEach(continent => {
      const countries = CONTINENT_COUNTRIES[continent];
      countries.slice(0, 3).forEach(country => { // 只取前3个国家作为示例
        const products = this.generateMockProducts(country.code, planType, isZh, ctx);
        allProducts.push(...products);
      });
    });

    return allProducts;
  }

  /**
   * 获取商品筛选选项
   */
  async getProductFilters(ctx: RequestContext) {
    const isZh = ctx.language.startsWith('zh');

    try {
      // 从数据库获取实际的筛选选项
      const [planTypes, priceRange, dataSizeOptions] = await Promise.all([
        // 获取所有可用的计划类型
        this.prisma.product.findMany({
          where: {
            status: 'ACTIVE',
            off_shelve: false,
            planType: { not: null },
          },
          select: { planType: true },
          distinct: ['planType'],
        }),

        // 获取价格范围
        this.prisma.product.aggregate({
          where: {
            status: 'ACTIVE',
            off_shelve: false,
          },
          _min: { price: true },
          _max: { price: true },
        }),

        // 获取实际的数据大小选项
        this.prisma.product.findMany({
          where: {
            status: 'ACTIVE',
            off_shelve: false,
            dataSize: { not: null },
          },
          select: { dataSize: true },
          distinct: ['dataSize'],
          orderBy: { dataSize: 'asc' },
        }),
      ]);

      // 格式化计划类型
      const formattedPlanTypes = planTypes
        .filter(p => p.planType)
        .map(p => ({
          value: p.planType!,
          label: isZh
            ? (p.planType === 'Total' ? '总量卡' : p.planType === 'Daily' ? '天卡' : p.planType!)
            : (p.planType === 'Total' ? 'Total Data' : p.planType === 'Daily' ? 'Daily Data' : p.planType!),
          description: isZh
            ? (p.planType === 'Total' ? '一次性流量包，用完为止' : p.planType === 'Daily' ? '每日固定流量，按天计费' : '')
            : (p.planType === 'Total' ? 'One-time data package' : p.planType === 'Daily' ? 'Fixed daily data allowance' : ''),
        }));

      // 格式化数据大小选项
      const formattedDataSizes = dataSizeOptions
        .filter(d => d.dataSize && d.dataSize > 0)
        .map(d => {
          const sizeInMB = d.dataSize!;
          let label: string;

          if (sizeInMB >= 1024) {
            const sizeInGB = sizeInMB / 1024;
            label = sizeInGB % 1 === 0 ? `${sizeInGB}GB` : `${sizeInGB.toFixed(1)}GB`;
          } else {
            label = `${sizeInMB}MB`;
          }

          return {
            value: sizeInMB.toString(),
            label: label,
          };
        });

      return {
        planTypes: formattedPlanTypes.length > 0 ? formattedPlanTypes : [
          {
            value: 'Total',
            label: isZh ? '总量卡' : 'Total Data',
            description: isZh ? '一次性流量包，用完为止' : 'One-time data package'
          },
          {
            value: 'Daily',
            label: isZh ? '天卡' : 'Daily Data',
            description: isZh ? '每日固定流量，按天计费' : 'Fixed daily data allowance'
          },
        ],
        dataSizes: formattedDataSizes.length > 0 ? formattedDataSizes : [
          { value: '500', label: '500MB' },
          { value: '1024', label: '1GB' },
          { value: '3072', label: '3GB' },
          { value: '10240', label: '10GB' },
        ],
        priceRange: {
          min: priceRange._min.price || 0,
          max: priceRange._max.price || 100,
        },
        sortOptions: [
          { value: 'price', label: isZh ? '价格' : 'Price' },
          { value: 'dataSize', label: isZh ? '流量大小' : 'Data Size' },
          { value: 'rating', label: isZh ? '评分' : 'Rating' },
          { value: 'createdAt', label: isZh ? '最新' : 'Latest' },
        ],
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };

    } catch (error) {
      this.logger.error('Error fetching product filters:', error);

      // 返回默认的筛选选项
      return {
        planTypes: [
          {
            value: 'Total',
            label: isZh ? '总量卡' : 'Total Data',
            description: isZh ? '一次性流量包，用完为止' : 'One-time data package'
          },
          {
            value: 'Daily',
            label: isZh ? '天卡' : 'Daily Data',
            description: isZh ? '每日固定流量，按天计费' : 'Fixed daily data allowance'
          },
        ],
        dataSizes: [
          { value: '500', label: '500MB' },
          { value: '1024', label: '1GB' },
          { value: '3072', label: '3GB' },
          { value: '10240', label: '10GB' },
        ],
        priceRange: {
          min: 0,
          max: 100,
        },
        sortOptions: [
          { value: 'price', label: isZh ? '价格' : 'Price' },
          { value: 'dataSize', label: isZh ? '流量大小' : 'Data Size' },
          { value: 'rating', label: isZh ? '评分' : 'Rating' },
        ],
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
        error: 'Database query failed, showing default filters',
      };
    }
  }
}
