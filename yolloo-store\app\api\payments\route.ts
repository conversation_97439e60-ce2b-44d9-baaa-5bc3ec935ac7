import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";
import Stripe from "stripe";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  // @ts-ignore
  apiVersion: "2024-06-20",
});

// POST /api/payments - 创建支付
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { orderId } = body;

    if (!orderId) {
      return new NextResponse("Order ID is required", { status: 400 });
    }

    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        items: true,
      },
    });

    if (!order) {
      return new NextResponse("Order not found", { status: 404 });
    }

    if (order.userId !== session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (order.status !== "PENDING") {
      return new NextResponse("Order is not pending", { status: 400 });
    }

    // 创建Stripe支付会话
    const lineItems = order.items.map((item) => ({
      price_data: {
        currency: "usd",
        product_data: {
          name: item.variantText || "Product",
          images: [], // 由于我们移除了 product 关系，无法获取图片
        },
        unit_amount: Math.round(item.price * 100), // Stripe使用最小货币单位（分）
      },
      quantity: item.quantity,
    }));

    const stripeSession = await stripe.checkout.sessions.create({
      customer_email: session.user.email!,
      line_items: lineItems,
      mode: "payment",
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/orders/${order.id}?success=true`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/orders/${order.id}?canceled=true`,
      metadata: {
        orderId: order.id,
      },
    });

    // 创建支付记录
    const payment = await prisma.payment.create({
      data: {
        amount: order.total,
        status: "PENDING",
        provider: "stripe",
        paymentMethod: "card",
        orders: {
          connect: {
            id: order.id,
          },
        },
      },
    });

    // 更新订单状态
    await prisma.order.update({
      where: { id: order.id },
      data: {
        paymentId: payment.id,
      },
    });

    return NextResponse.json({ url: stripeSession.url });
  } catch (error) {
    console.error("[PAYMENTS_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}