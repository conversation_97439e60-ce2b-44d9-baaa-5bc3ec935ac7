import { NextRequest, NextResponse } from 'next/server';
import { createOdooService } from '../../../../services/odooService';
import { ODOO_CONFIG } from '../../../../config/odoo';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


const odooService = createOdooService(ODOO_CONFIG);

export async function POST(request: NextRequest) {
    try {
        const params = await request.json();
        const response = await odooService.getProductPrices(params);
        return NextResponse.json(response);
    } catch (error) {
        console.error('Error fetching product prices from Odoo:', error);
        return NextResponse.json(
            { 
                code: 500,
                msg: 'Failed to fetch product prices',
                data: null
            },
            { status: 500 }
        );
    }
} 