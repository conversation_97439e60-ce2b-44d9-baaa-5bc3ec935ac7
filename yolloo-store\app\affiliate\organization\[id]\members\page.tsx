"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import axios from "axios";
import { ChevronLeft, Loader2, Plus, User, Trash } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { formatDistanceToNow } from "date-fns";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { BulkOperations } from "./bulk-operations";
import { InviteLinkButton } from "./invite-link-button";

interface Member {
  id: string;
  createdAt: string;
  isAdmin: boolean;
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
}

export default function MembersPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [members, setMembers] = useState<Member[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAdmin, setIsAdmin] = useState(false);
  const [isError, setIsError] = useState(false);

  // Fetch members on component mount
  useEffect(() => {
    const fetchMembers = async () => {
      try {
        setLoading(true);
        // 首先验证用户是否是组织的管理员
        const orgResponse = await axios.get(`/api/affiliate/organizations/${params.id}`);
        
        if (!orgResponse.data.isAdmin) {
          toast.error("You do not have permission to manage members");
          router.push(`/affiliate/organization/${params.id}`);
          setIsError(true);
          return;
        }
        
        // 用户是管理员，继续获取成员列表
        setIsAdmin(true);
        const membersResponse = await axios.get(`/api/affiliate/organizations/${params.id}/members`);
        
        // API直接返回成员数组，不是包含在members属性中
        if (Array.isArray(membersResponse.data)) {
          setMembers(membersResponse.data);
        } else if (membersResponse.data.members) {
          // 兼容性处理，以防API格式变化
          setMembers(membersResponse.data.members);
        } else {
          // 如果没有成员数据，至少设置为空数组
          setMembers([]);
        }
      } catch (error) {
        console.error("Error fetching organization members:", error);
        toast.error("Failed to fetch members");
        setIsError(true);
        router.push(`/affiliate/organization/${params.id}`);
      } finally {
        setLoading(false);
      }
    };

    fetchMembers();
  }, [params.id, router]);

  // Filter members based on search query
  const filteredMembers = searchQuery.trim() === "" 
    ? members 
    : members.filter(member => 
        member.user.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
        member.user.email.toLowerCase().includes(searchQuery.toLowerCase())
      );

  // Handle adding new member via invite
  const handleInviteMember = () => {
    router.push(`/affiliate/organization/${params.id}/members/invite`);
  };

  // Handle setting admin status
  const handleToggleAdmin = async (memberId: string, isAdmin: boolean) => {
    try {
      await axios.patch(`/api/affiliate/organizations/${params.id}/members/${memberId}`, {
        isAdmin: !isAdmin
      });
      
      // Update local state to reflect the change
      setMembers(members.map(member => 
        member.id === memberId 
          ? { ...member, isAdmin: !isAdmin } 
          : member
      ));
      
      toast.success(`Member ${isAdmin ? 'removed from' : 'set as'} admin`);
    } catch (error) {
      console.error("Error updating member admin status:", error);
      if (axios.isAxiosError(error) && error.response?.status === 403) {
        toast.error(error.response.data.error || "Permission denied for this operation");
      } else {
        toast.error("Failed to update member status");
      }
    }
  };

  // Handle removing a member
  const handleRemoveMember = async (memberId: string) => {
    if (!confirm("Are you sure you want to remove this member?")) {
      return;
    }
    
    try {
      await axios.delete(`/api/affiliate/organizations/${params.id}/members/${memberId}`);
      
      // Update local state to remove the member
      setMembers(members.filter(member => member.id !== memberId));
      
      toast.success("Member removed successfully");
    } catch (error) {
      console.error("Error removing member:", error);
      if (axios.isAxiosError(error) && error.response?.status === 403) {
        toast.error(error.response.data.error || "Permission denied for this operation");
      } else {
        toast.error("Failed to remove member");
      }
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (isError) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6 flex items-center justify-between">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/affiliate/organization/${params.id}`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Organization
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Organization Members</CardTitle>
            <CardDescription>
              Manage the members of your organization
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {isAdmin && <InviteLinkButton organizationId={params.id} />}
            {isAdmin && <BulkOperations organizationId={params.id} />}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search members..."
                className="max-w-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {members.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No members found. Invite members to join your organization.
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Member</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Joined</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMembers.map((member) => (
                    <TableRow key={member.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={member.user.image} alt={member.user.name} />
                            <AvatarFallback>{member.user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <span>{member.user.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{member.user.email}</TableCell>
                      <TableCell>
                        {member.isAdmin ? (
                          <Badge variant="outline" className="bg-primary/10 text-primary">
                            Admin
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-muted text-muted-foreground">
                            Member
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {formatDistanceToNow(new Date(member.createdAt), { addSuffix: true })}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          {/* For non-admin members, show "Make Admin" button */}
                          {!member.isAdmin && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleToggleAdmin(member.id, member.isAdmin)}
                            >
                              Make Admin
                            </Button>
                          )}
                          {/* Only show "Remove" button for non-admins */}
                          {!member.isAdmin && (
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleRemoveMember(member.id)}
                            >
                              <Trash className="h-4 w-4 mr-1" />
                              Remove
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 