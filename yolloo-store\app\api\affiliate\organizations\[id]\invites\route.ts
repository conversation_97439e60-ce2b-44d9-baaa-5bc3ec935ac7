import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { sendOrganizationInviteEmail } from "@/app/services/emailService";
import { DateUtils } from "@/lib/utils";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for creating an invite
const createInviteSchema = z.object({
  email: z.string().email(),
  affiliateId: z.string().optional(),
});

// Helper function to check if user has admin access to the organization
async function hasAdminAccess(userId: string, organizationId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      role: true,
      affiliate: {
        select: {
          id: true,
          organizationId: true,
          isAdmin: true,
        },
      },
    },
  });

  if (!user) return false;

  // System admin has access to all organizations
  if (user.role === "ADMIN") return true;

  // Organization admin has access to their organization
  if (user.affiliate?.organizationId === organizationId && user.affiliate.isAdmin) {
    return true;
  }

  return false;
}

// GET - List organization invites
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const organizationId = params.id;

    // Check if user has admin access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);

    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Get organization invites
    const invites = await prisma.organizationInvite.findMany({
      where: { organizationId },
      include: {
        affiliate: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    return NextResponse.json(invites);
  } catch (error) {
    console.error("Error fetching organization invites:", error);
    return NextResponse.json(
      { error: "Failed to fetch organization invites" },
      { status: 500 }
    );
  }
}

// POST - Create a new invite
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const organizationId = params.id;

    // Check if user has admin access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);

    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    const body = await req.json();

    // Validate input
    const validationResult = createInviteSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { email, affiliateId } = validationResult.data;

    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Check if there's already an active invite for this email
    const existingInvite = await prisma.organizationInvite.findFirst({
      where: {
        organizationId,
        email,
        status: "PENDING",
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (existingInvite) {
      return NextResponse.json(
        { error: "An active invite already exists for this email" },
        { status: 400 }
      );
    }

    // If affiliateId is provided, check if affiliate exists
    if (affiliateId) {
      const affiliate = await prisma.affiliateProfile.findUnique({
        where: { id: affiliateId },
      });

      if (!affiliate) {
        return NextResponse.json({ error: "Affiliate not found" }, { status: 404 });
      }

      // Check if affiliate is already in an organization
      if (affiliate.organizationId) {
        return NextResponse.json(
          { error: "Affiliate is already a member of an organization" },
          { status: 400 }
        );
      }
    }

    // Generate a unique invite code
    const inviteCode = `INV-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

    // Create the invite
    const invite = await prisma.organizationInvite.create({
      data: {
        organizationId,
        email,
        inviteCode,
        ...(affiliateId && { affiliateId }),
        // Set expiration date to 7 days from now
        expiresAt: DateUtils.addDays(new Date(), 7),
      },
    });

    // Send email invitation
    const inviteUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invite/${inviteCode}`;
    await sendOrganizationInviteEmail(
      email,
      organization.name,
      inviteUrl,
      invite.expiresAt,
      session.user.name || undefined
    );

    return NextResponse.json(invite);
  } catch (error) {
    console.error("Error creating invite:", error);
    return NextResponse.json(
      { error: "Failed to create invite" },
      { status: 500 }
    );
  }
}