import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { cleanCardNumber } from "@/lib/utils"
import { Prisma } from "@prisma/client"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


interface RouteParams {
  params: {
    cardId: string
  }
}

export async function PATCH(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 403 })
    }

    const json = await req.json()
    const { number, type, status, activationDate, expiryDate } = json

    // 清理卡号，只保留数字
    const cleanedNumber = cleanCardNumber(number)

    if (!cleanedNumber) {
      return new NextResponse("Card number is required and must contain digits", { status: 400 })
    }

    const card = await prisma.yollooCard.update({
      where: {
        id: params.cardId,
      },
      data: {
        number: cleanedNumber,
        type,
        status,
        activationDate,
        expiryDate,
      },
    })

    return NextResponse.json(card)
  } catch (error) {
    console.error("[CARD_PATCH]", error)

    // 检查是否是唯一约束错误（卡片号码已存在）
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
      return new NextResponse("Card number already exists", { status: 400 })
    }

    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function DELETE(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 403 })
    }

    const card = await prisma.yollooCard.delete({
      where: {
        id: params.cardId,
      },
    })

    return NextResponse.json(card)
  } catch (error) {
    console.error("[CARD_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 