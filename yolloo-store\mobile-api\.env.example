# Database
DATABASE_URL="postgresql://username:password@localhost:5432/yolloo_mobile_api"

# JWT
MOBILE_API_JWT_SECRET="your-super-secret-jwt-key-here"

# Payment Gateways
# Stripe
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Alipay
ALIPAY_APP_ID="your_alipay_app_id"
ALIPAY_PRIVATE_KEY="your_alipay_private_key"
ALIPAY_PUBLIC_KEY="alipay_public_key"

# WeChat Pay
WECHAT_PAY_MCH_ID="your_wechat_mch_id"
WECHAT_PAY_API_KEY="your_wechat_api_key"
WECHAT_PAY_CERT_PATH="/path/to/wechat/cert.pem"
WECHAT_PAY_KEY_PATH="/path/to/wechat/key.pem"

# Social Authentication
# Google OAuth
GOOGLE_CLIENT_ID="your_google_client_id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your_google_client_secret"

# Facebook OAuth
FACEBOOK_APP_ID="your_facebook_app_id"
FACEBOOK_APP_SECRET="your_facebook_app_secret"

# Apple Sign-In
APPLE_TEAM_ID="your_apple_team_id"
APPLE_CLIENT_ID="your_apple_client_id"
APPLE_KEY_ID="your_apple_key_id"
APPLE_PRIVATE_KEY_PATH="/path/to/apple/private_key.p8"

# WeChat OAuth
WECHAT_APP_ID="your_wechat_app_id"
WECHAT_APP_SECRET="your_wechat_app_secret"

# File Upload
UPLOAD_MAX_SIZE="5242880" # 5MB in bytes
UPLOAD_ALLOWED_TYPES="image/jpeg,image/jpg,image/png,image/webp,image/gif"

# Email Service (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your_app_password"

# Redis (for caching and sessions)
REDIS_URL="redis://localhost:6379"

# Application
NODE_ENV="development"
PORT="3000"
API_BASE_URL="http://localhost:3000"
FRONTEND_URL="http://localhost:3001"

# Logging
LOG_LEVEL="debug"

# Rate Limiting
RATE_LIMIT_WINDOW_MS="900000" # 15 minutes
RATE_LIMIT_MAX_REQUESTS="100"

# CORS
CORS_ORIGIN="http://localhost:3001,http://localhost:3000"

# Security
BCRYPT_ROUNDS="12"
SESSION_SECRET="your-session-secret-key"

# Third-party APIs
# SMS Service (for verification codes)
SMS_PROVIDER="twilio" # or "aliyun", "tencent"
TWILIO_ACCOUNT_SID="your_twilio_account_sid"
TWILIO_AUTH_TOKEN="your_twilio_auth_token"
TWILIO_PHONE_NUMBER="+**********"

# Cloud Storage (optional, for file uploads)
AWS_ACCESS_KEY_ID="your_aws_access_key"
AWS_SECRET_ACCESS_KEY="your_aws_secret_key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="your-s3-bucket-name"

# Monitoring and Analytics
SENTRY_DSN="your_sentry_dsn"
GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"

# Feature Flags
ENABLE_SOCIAL_LOGIN="true"
ENABLE_PAYMENT_GATEWAY="true"
ENABLE_FILE_UPLOAD="true"
ENABLE_EMAIL_NOTIFICATIONS="true"
ENABLE_SMS_VERIFICATION="true"
