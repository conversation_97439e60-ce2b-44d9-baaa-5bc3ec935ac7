import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 添加动态路由配置
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// GET /api/orders/[orderId] - 获取订单详情
export async function GET(
  req: Request,
  { params }: { params: { orderId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const order = await prisma.order.findUnique({
      where: {
        id: params.orderId,
      },
      select: {
        id: true,
        userId: true,
        total: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        items: {
          select: {
            id: true,
            quantity: true,
            price: true,
            // 移除 product 关系查询，因为我们已经移除了外键关系
            productCode: true,
            variantCode: true,
            variantText: true,
          },
        },
        shippingAddress: true,
        shippingAddressSnapshot: true,
        payment: true,
      },
    })

    if (!order) {
      return new NextResponse("Order not found", { status: 404 })
    }

    if (order.userId !== session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    return NextResponse.json(order)
  } catch (error) {
    console.error("[ORDER_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

// PATCH /api/orders/[orderId] - 更新订单状态
export async function PATCH(
  req: Request,
  { params }: { params: { orderId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await req.json()
    const { status } = body

    if (!status) {
      return new NextResponse("Status is required", { status: 400 })
    }

    const order = await prisma.order.findUnique({
      where: {
        id: params.orderId,
      },
    })

    if (!order) {
      return new NextResponse("Order not found", { status: 404 })
    }

    if (order.userId !== session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // 只允许取消待支付的订单
    if (status === "CANCELLED" && order.status !== "PENDING") {
      return new NextResponse("Cannot cancel non-pending order", { status: 400 })
    }

    // 如果订单被取消，恢复库存
    if (status === "CANCELLED") {
      // 获取订单项
      const orderItems = await prisma.orderItem.findMany({
        where: {
          orderId: params.orderId
        },
        select: {
          productCode: true,
          quantity: true
        }
      });

      // 恢复每个产品的库存
      for (const item of orderItems) {
        if (item.productCode) {
          try {
            await prisma.product.update({
              where: { sku: item.productCode },
              data: {
                stock: {
                  increment: item.quantity
                }
              }
            });
            console.log(`[ORDER_PATCH] Restored stock for product ${item.productCode} by ${item.quantity}`);
          } catch (stockError) {
            console.error(`[ORDER_PATCH] Failed to restore stock for product ${item.productCode}:`, stockError);
            // 不中断订单取消流程，继续处理其他产品
          }
        }
      }
    }

    const updatedOrder = await prisma.order.update({
      where: {
        id: params.orderId,
      },
      data: {
        status,
      },
    })

    return NextResponse.json(updatedOrder)
  } catch (error) {
    console.error("[ORDER_PATCH]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}