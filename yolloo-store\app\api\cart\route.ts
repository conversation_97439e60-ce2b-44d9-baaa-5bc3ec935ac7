import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// GET /api/cart - 获取购物车
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const cartItems = await prisma.cartItem.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        product: true,
        variant: true,
      },
    });

    return NextResponse.json(cartItems);
  } catch (error) {
    console.error("[CART_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/cart - 添加到购物车
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { productId, quantity = 1, variantId } = await request.json();

    if (!productId) {
      return new NextResponse("Product ID is required", { status: 400 });
    }

    // 获取产品和变体信息
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        variants: true,
      },
    });

    if (!product) {
      return new NextResponse("Product not found", { status: 404 });
    }

    // 如果产品有变体但没有选择变体
    if (product.variants.length > 0 && !variantId) {
      return new NextResponse("Variant selection is required", { status: 400 });
    }

    // 如果选择了变体，验证变体是否存在
    if (variantId) {
      const variant = await prisma.productVariant.findUnique({
        where: { id: variantId },
      });

      if (!variant) {
        return new NextResponse("Selected variant not found", { status: 404 });
      }
    }

    // 查找现有的购物车项
    const existingCartItem = await prisma.cartItem.findFirst({
      where: {
        userId: session.user.id,
        productId: product.id,
        variantId: variantId || null,
      },
    });

    let cartItem;

    if (existingCartItem) {
      // 更新现有购物车项
      cartItem = await prisma.cartItem.update({
        where: { id: existingCartItem.id },
        data: {
          quantity: existingCartItem.quantity + quantity,
        },
        include: {
          product: true,
          variant: true,
        },
      });
    } else {
      // 创建新的购物车项
      cartItem = await prisma.cartItem.create({
        data: {
          userId: session.user.id,
          productId: product.id,
          variantId: variantId || null,
          quantity,
        },
        include: {
          product: true,
          variant: true,
        },
      });
    }

    return NextResponse.json(cartItem);
  } catch (error) {
    console.error("[CART_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// DELETE /api/cart - 删除购物车项
export async function DELETE(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const itemId = searchParams.get("itemId");

    if (!itemId) {
      return new NextResponse("Item ID is required", { status: 400 });
    }

    const cartItem = await prisma.cartItem.findUnique({
      where: { id: itemId },
    });

    if (!cartItem || cartItem.userId !== session.user.id) {
      return new NextResponse("Cart item not found", { status: 404 });
    }

    const deletedItem = await prisma.cartItem.delete({
      where: { id: itemId },
    });

    return NextResponse.json(deletedItem);
  } catch (error) {
    console.error("[CART_DELETE]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
} 