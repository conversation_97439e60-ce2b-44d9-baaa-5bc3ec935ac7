import React from 'react'

export default function HelpCenterPage() {
  const helpCategories = [
    {
      title: "Getting Started",
      items: [
        {
          question: "What is an eSIM?",
          answer: "An eSIM (embedded SIM) is a digital SIM that allows you to activate a cellular plan without using a physical SIM card. It's built directly into your device."
        },
        {
          question: "How do I install my eSIM?",
          answer: "Installation is simple: After purchase, you'll receive a QR code. Go to your device settings, select 'Add eSIM' or 'Add Cellular Plan', and scan the QR code. Follow the on-screen instructions to complete setup."
        }
      ]
    },
    {
      title: "Account & Billing",
      items: [
        {
          question: "How do I manage my subscription?",
          answer: "You can manage your subscription through your account dashboard. Log in to view, modify, or cancel your plans."
        },
        {
          question: "What payment methods do you accept?",
          answer: "We accept all major credit cards, PayPal, and other popular payment methods. All transactions are secure and encrypted."
        }
      ]
    },
    {
      title: "Technical Support",
      items: [
        {
          question: "My eSIM isn't working",
          answer: "First, ensure your device is eSIM compatible and that you've followed the installation steps correctly. If issues persist, contact our support team."
        },
        {
          question: "How do I check my data usage?",
          answer: "You can check your data usage in your device settings under the cellular or mobile data section, or log into your account dashboard."
        }
      ]
    }
  ]

  return (
      <div className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold heading-gradient mb-8">Help Center</h1>
            
            <div className="space-y-12">
              {helpCategories.map((category, index) => (
                <div key={index} className="space-y-6">
                  <h2 className="text-2xl font-semibold text-foreground">{category.title}</h2>
                  
                  <div className="space-y-4">
                    {category.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="bg-card rounded-lg p-6">
                        <h3 className="text-lg font-medium text-foreground mb-2">{item.question}</h3>
                        <p className="text-muted-foreground">{item.answer}</p>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-12 p-6 bg-card/50 rounded-lg">
              <h2 className="text-xl font-semibold text-foreground mb-4">Still Need Help?</h2>
              <p className="text-muted-foreground mb-4">
                Can't find what you're looking for? Our support team is here to help.
              </p>
              <div className="space-y-2">
                <p className="text-muted-foreground">
                  Email: <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>
                </p>
                <p className="text-muted-foreground">
                  Response time: Within 24 hours
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
  )
} 