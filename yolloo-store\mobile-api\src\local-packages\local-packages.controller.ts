import { Controller, Get, Post, Body, Query, Param, UseGuards } from '@nestjs/common';
import { LocalPackagesService } from './local-packages.service';
import { LocalPackagesQueryDto, LocalPackageOrderDto } from './dto/local-packages-query.dto';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { Public } from '../common/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller('local-packages')
export class LocalPackagesController {
  constructor(private readonly localPackagesService: LocalPackagesService) {}

  @Public()
  @Get()
  getLocalPackages(
    @Query() query: LocalPackagesQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.localPackagesService.getLocalPackages(query, ctx);
  }

  @Public()
  @Get(':packageId')
  getPackageById(
    @Param('packageId') packageId: string,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.localPackagesService.getPackageById(packageId, ctx);
  }

  @Post('order')
  createLocalOrder(
    @Body() orderData: LocalPackageOrderDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.localPackagesService.createLocalOrder(orderData, ctx);
  }
}
