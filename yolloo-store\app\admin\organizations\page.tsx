"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import axios from "axios";
import { Loader2, Plus, SearchIcon, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";

// Define the form schema for creating an organization
const createOrgSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().optional(),
  logo: z.string().optional(),
  commissionRate: z.number().min(0).max(1),
  discountRate: z.number().min(0).max(1),
  adminEmail: z.string().email("Invalid email address"),
});

type CreateOrgFormValues = z.infer<typeof createOrgSchema>;

// Define types for the organization data
interface Organization {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  code: string;
  commissionRate: number;
  discountRate: number;
  totalEarnings: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  _count: {
    members: number;
  };
}

// Define user interface for search results
interface UserSearchResult {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
}

export default function AdminOrganizationsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const itemsPerPage = 10;

  // For user search when creating organization
  const [adminSearchQuery, setAdminSearchQuery] = useState("");
  const [searchedUsers, setSearchedUsers] = useState<UserSearchResult[]>([]);
  const [isSearchingUsers, setIsSearchingUsers] = useState(false);
  const [selectedAdminEmail, setSelectedAdminEmail] = useState("");

  const { register, handleSubmit, formState: { errors }, reset, setValue, watch } = useForm<CreateOrgFormValues>({
    resolver: zodResolver(createOrgSchema),
    defaultValues: {
      commissionRate: 0.1,
      discountRate: 0.05,
    }
  });

  // Watch adminEmail to sync with the selected admin
  const adminEmail = watch("adminEmail");

  // User search debounce
  useEffect(() => {
    if (!adminSearchQuery || adminSearchQuery.length < 1) {
      setSearchedUsers([]);
      return;
    }

    const timer = setTimeout(() => {
      searchUsers(adminSearchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [adminSearchQuery]);

  // Function to search users by email/name
  const searchUsers = async (query: string) => {
    if (!query || query.length < 1) return;
    
    try {
      setIsSearchingUsers(true);
      const response = await axios.get(`/api/admin/users/search?query=${query}`);
      setSearchedUsers(response.data || []);
    } catch (error) {
      console.error("Error searching users:", error);
      toast.error("Failed to search users");
      setSearchedUsers([]);
    } finally {
      setIsSearchingUsers(false);
    }
  };

  // Select a user as admin
  const selectAdmin = (user: UserSearchResult) => {
    setValue("adminEmail", user.email);
    setSelectedAdminEmail(user.email);
    setAdminSearchQuery("");
    setSearchedUsers([]);
  };

  // Clear selected admin
  const clearSelectedAdmin = () => {
    setValue("adminEmail", "");
    setSelectedAdminEmail("");
    setAdminSearchQuery("");
  };

  // Fetch organizations on component mount and when filters change
  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        setLoading(true);
        
        // Build query parameters
        const params = new URLSearchParams();
        params.append("page", currentPage.toString());
        params.append("limit", itemsPerPage.toString());
        
        if (searchQuery) {
          params.append("search", searchQuery);
        }
        
        if (statusFilter && statusFilter !== "all") {
          params.append("status", statusFilter);
        }
        
        const response = await axios.get(`/api/admin/organizations?${params.toString()}`);
        setOrganizations(response.data.organizations || []);
        setTotalPages(response.data.pagination?.totalPages || 1);
        setTotalCount(response.data.pagination?.totalCount || 0);
      } catch (error) {
        console.error("Error fetching organizations:", error);
        toast.error("Failed to fetch organizations");
        setOrganizations([]);
        setTotalPages(1);
        setTotalCount(0);
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizations();
  }, [currentPage, searchQuery, statusFilter]);

  // Handle organization creation
  const onCreateOrganization = async (data: CreateOrgFormValues) => {
    try {
      setIsCreating(true);
      const response = await axios.post("/api/admin/organizations", data);
      setOrganizations((prev) => [response.data, ...prev]);
      toast.success("Organization created successfully");
      setIsCreateDialogOpen(false);
      reset();
    } catch (error) {
      console.error("Error creating organization:", error);
      toast.error("Failed to create organization");
    } finally {
      setIsCreating(false);
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
  };

  // Get status badge for organization
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Active</Badge>;
      case "INACTIVE":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Inactive</Badge>;
      case "SUSPENDED":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold">Organizations</h1>
          <p className="text-sm text-muted-foreground">
            Manage affiliate organizations and their members
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Organization
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[550px]">
            <form onSubmit={handleSubmit(onCreateOrganization)}>
              <DialogHeader>
                <DialogTitle>Create New Organization</DialogTitle>
                <DialogDescription>
                  Create a new affiliate organization and assign an admin.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Organization Name</Label>
                  <Input
                    id="name"
                    placeholder="Enter organization name"
                    {...register("name")}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name.message}</p>
                  )}
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Enter organization description (optional)"
                    {...register("description")}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="logo">Logo URL</Label>
                  <Input
                    id="logo"
                    placeholder="Enter logo URL (optional)"
                    {...register("logo")}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="commissionRate">Commission Rate</Label>
                    <Input
                      id="commissionRate"
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      placeholder="0.10"
                      {...register("commissionRate", { valueAsNumber: true })}
                    />
                    {errors.commissionRate && (
                      <p className="text-sm text-red-500">{errors.commissionRate.message}</p>
                    )}
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="discountRate">Discount Rate</Label>
                    <Input
                      id="discountRate"
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      placeholder="0.05"
                      {...register("discountRate", { valueAsNumber: true })}
                    />
                    {errors.discountRate && (
                      <p className="text-sm text-red-500">{errors.discountRate.message}</p>
                    )}
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="adminEmail">Admin Email</Label>
                  <div className="relative">
                    {selectedAdminEmail ? (
                      <div className="flex items-center border rounded-md p-2">
                        <span className="flex-1">{selectedAdminEmail}</span>
                        <Button 
                          type="button"
                          variant="ghost" 
                          size="sm" 
                          onClick={clearSelectedAdmin}
                          className="h-5 w-5 p-0"
                        >
                          &times;
                        </Button>
                      </div>
                    ) : (
                      <>
                        <Input
                          type="text"
                          placeholder="Search for users by email or name"
                          value={adminSearchQuery}
                          onChange={(e) => setAdminSearchQuery(e.target.value)}
                        />
                        <Input
                          type="hidden"
                          {...register("adminEmail")}
                        />
                        {searchedUsers.length > 0 && (
                          <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-auto">
                            {searchedUsers.map((user) => (
                              <div
                                key={user.id}
                                className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                                onClick={() => selectAdmin(user)}
                              >
                                <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-xs mr-2 overflow-hidden">
                                  {user.image ? (
                                    <img src={user.image} alt={user.name || user.email} className="w-full h-full object-cover" />
                                  ) : (
                                    user.name?.substring(0, 2) || user.email.substring(0, 2)
                                  )}
                                </div>
                                <div>
                                  <div className="font-medium">{user.name || "Unnamed User"}</div>
                                  <div className="text-xs text-gray-500">{user.email}</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                        {isSearchingUsers && (
                          <div className="absolute right-3 top-2.5">
                            <Loader2 className="h-4 w-4 animate-spin" />
                          </div>
                        )}
                      </>
                    )}
                  </div>
                  {errors.adminEmail && (
                    <p className="text-sm text-red-500">{errors.adminEmail.message}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    This user will be assigned as the organization admin. They must have an existing account.
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isCreating}>
                  {isCreating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Create Organization
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
      
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find organizations by name, code, or status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name or code..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="w-full md:w-48">
              <Select
                value={statusFilter}
                onValueChange={(value) => {
                  setStatusFilter(value);
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                  <SelectItem value="SUSPENDED">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button type="submit" className="md:w-auto">
              <SearchIcon className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => {
                setSearchQuery("");
                setStatusFilter("all");
                setCurrentPage(1);
              }}
              className="md:w-auto"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Reset
            </Button>
          </form>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Organizations</CardTitle>
          <CardDescription>
            Showing {organizations?.length || 0} of {totalCount} organizations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : !organizations || organizations.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 bg-muted/50 rounded-lg border border-dashed">
              <p className="text-sm text-muted-foreground">No organizations found</p>
              <Button onClick={() => setIsCreateDialogOpen(true)} className="mt-2" variant="outline" size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Create Organization
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Organization</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Members</TableHead>
                    <TableHead>Commission</TableHead>
                    <TableHead>Earnings</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {(organizations || []).map((org) => (
                    <TableRow key={org.id}>
                      <TableCell className="font-medium">
                        <Link href={`/admin/organizations/${org.id}`} className="hover:underline">
                          {org.name}
                        </Link>
                      </TableCell>
                      <TableCell>{org.code}</TableCell>
                      <TableCell>{org._count?.members || 0}</TableCell>
                      <TableCell>{(org.commissionRate * 100).toFixed(0)}%</TableCell>
                      <TableCell>${org.totalEarnings.toFixed(2)}</TableCell>
                      <TableCell>{getStatusBadge(org.status)}</TableCell>
                      <TableCell>{format(new Date(org.createdAt), "MMM d, yyyy")}</TableCell>
                      <TableCell className="text-right">
                        <Button asChild variant="ghost" size="sm">
                          <Link href={`/admin/organizations/${org.id}`}>
                            View
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {Math.min((currentPage - 1) * itemsPerPage + 1, totalCount)} to {Math.min(currentPage * itemsPerPage, totalCount)} of {totalCount} organizations
          </div>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setCurrentPage(prev => Math.max(prev - 1, 1));
                  }}
                  className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
              
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNumber = i + 1;
                const isVisible = 
                  pageNumber === 1 || 
                  pageNumber === totalPages || 
                  (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1);
                
                if (!isVisible && pageNumber === 2) {
                  return (
                    <PaginationItem key="ellipsis-start">
                      <PaginationEllipsis />
                    </PaginationItem>
                  );
                }
                
                if (!isVisible && pageNumber === totalPages - 1) {
                  return (
                    <PaginationItem key="ellipsis-end">
                      <PaginationEllipsis />
                    </PaginationItem>
                  );
                }
                
                if (isVisible) {
                  return (
                    <PaginationItem key={pageNumber}>
                      <PaginationLink
                        href="#"
                        isActive={currentPage === pageNumber}
                        onClick={(e) => {
                          e.preventDefault();
                          setCurrentPage(pageNumber);
                        }}
                      >
                        {pageNumber}
                      </PaginationLink>
                    </PaginationItem>
                  );
                }
                
                return null;
              })}
              
              <PaginationItem>
                <PaginationNext 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setCurrentPage(prev => Math.min(prev + 1, totalPages));
                  }}
                  className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </CardFooter>
      </Card>
    </div>
  );
} 