import { NextResponse } from "next/server"
import { hash } from "bcryptjs"
import { prisma } from "@/lib/prisma"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function POST(req: Request) {
  try {
    const { name, email, password } = await req.json()

    if (!name || !email || !password) {
      return new NextResponse("Missing required fields", { status: 400 })
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return new NextResponse("Invalid email format", { status: 400 })
    }

    // 检查邮箱是否已被使用
    const existingUser = await prisma.user.findUnique({
      where: {
        email,
      },
    })

    if (existingUser) {
      return new NextResponse("Email already exists", { status: 400 })
    }

    // 密码加密
    const hashedPassword = await hash(password, 10)

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name,
        email,
        hashedPassword,
        role: "CUSTOMER",
      },
    })

    // 更新预售订阅状态
    await prisma.preSaleSubscription.updateMany({
      where: {
        email: email.toLowerCase(),
        convertedToUser: false,
      },
      data: {
        convertedToUser: true,
        userId: user.id,
        status: "CONFIRMED",
      },
    })

    return NextResponse.json({
      user: {
        name: user.name,
        email: user.email,
      },
    })
  } catch (error) {
    console.error("[SIGNUP_ERROR]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 