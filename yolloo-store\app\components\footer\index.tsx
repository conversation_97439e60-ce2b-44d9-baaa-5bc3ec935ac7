import Link from "next/link"
import Image from "next/image"
import { Icons } from "@/components/icons"

const popularCountries = [
  { name: "Japan", href: "/destinations/japan" },
  { name: "China", href: "/destinations/china" },
  { name: "Malaysia", href: "/destinations/malaysia" },
  { name: "Germany", href: "/destinations/germany" },
  { name: "The United States", href: "/destinations/usa" },
  { name: "France", href: "/destinations/france" },
  { name: "Britain", href: "/destinations/britain" },
  { name: "Thailand", href: "/destinations/thailand" },
  { name: "Italy", href: "/destinations/italy" },
]

const companyLinks = [
  { name: "About Us", href: "/about" },
  { name: "Blog", href: "https://x.com/yollooesim" },
  { name: "Contact", href: "/contact" },
]

const socialLinks = [
  { name: "Facebook", icon: Icons.facebook, href: "#" },
  { name: "X", icon: Icons.twitter, href: "https://x.com/yollooesim" },
  { name: "Instagram", icon: Icons.instagram, href: "#" }
]

export default function Footer() {
  return (
    <footer className="bg-white relative">
      {/* Background Vector */}
      <div className="absolute left-0 top-0 w-full h-full opacity-50 z-0">
        <Image
          src="/Vector 5.svg"
          alt=""
          fill
          className="object-contain object-left-top"
        />
      </div>

      <div className="container mx-auto px-6 py-12 relative z-10">
        {/* Top Section with Logo and App Store Buttons */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-8">
          {/* Logo */}
          <div className="mb-6 md:mb-0">
            <Link href="/" className="inline-block">
              <Image
                src="/yollooIcon.svg"
                alt="Yolloo"
                width={140}
                height={48}
                className="h-12 w-auto"
              />
            </Link>
          </div>

          {/* App Store Buttons */}
          <div className="flex gap-4">
            <Link href="#" className="inline-block">
              <Image
                src="/googleplay.svg"
                alt="Google Play"
                width={140}
                height={42}
                className="h-10 w-auto"
              />
            </Link>
            <Link href="#" className="inline-block">
              <Image
                src="/iosplay.svg"
                alt="App Store"
                width={140}
                height={42}
                className="h-10 w-auto"
              />
            </Link>
          </div>
        </div>

        {/* Divider Line */}
        <div className="w-full h-px bg-gray-300 mb-12"></div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-12">
          {/* Popular Countries */}
          <div>
            <h3 className="font-semibold mb-6 text-[#E91E63] text-lg">
              Popular Countries
            </h3>
            <ul className="space-y-4">
              {popularCountries.map((country) => (
                <li key={country.name}>
                  <Link
                    href={country.href}
                    className="text-gray-600 hover:text-[#E91E63] transition-colors duration-200"
                  >
                    {country.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-semibold mb-6 text-[#E91E63] text-lg">
              Company
            </h3>
            <ul className="space-y-4">
              {companyLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-600 hover:text-[#E91E63] transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Follow Us */}
          <div>
            <h3 className="font-semibold mb-6 text-[#E91E63] text-lg">
              Follow Us
            </h3>
            <ul className="space-y-4">
              {socialLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-600 hover:text-[#E91E63] transition-colors duration-200 flex items-center"
                  >
                    <link.icon className="h-4 w-4 mr-2" />
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Information */}
          <div>
            <h3 className="font-semibold mb-6 text-[#E91E63] text-lg">
              Information
            </h3>
            <div className="flex items-center text-gray-600">
              <Icons.mail className="h-4 w-4 mr-2" />
              <span><EMAIL></span>
            </div>
          </div>
        </div>

        {/* Bottom Copyright */}
        <div className="pt-6 border-t border-gray-300">
          <p className="text-right text-gray-500 text-sm">
            © {new Date().getFullYear()} Yolloo. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}