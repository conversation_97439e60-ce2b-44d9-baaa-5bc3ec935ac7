import HowItWorks from "@/components/how-it-works"
import { Wifi, Globe, CreditCard, HeadphonesIcon, ShieldCheck, Zap } from "lucide-react"

const Feature = ({ icon: Icon, title, description }: {
  icon: any,
  title: string,
  description: string
}) => (
  <div className="flex gap-4 p-6 rounded-2xl bg-white/50 border border-gray-200 hover:border-pink-200 
  transition-all duration-300 hover:shadow-lg hover:shadow-pink-500/5">
    <div className="flex-shrink-0">
      <div className="w-12 h-12 flex items-center justify-center rounded-xl bg-gradient-to-br 
      from-[#F799A6]/20 to-[#B82E4E]/20">
        <Icon className="w-6 h-6 text-[#B82E4E]" />
      </div>
    </div>
    <div>
      <h3 className="font-semibold text-lg mb-2 text-gray-900">{title}</h3>
      <p className="text-gray-600 leading-relaxed">{description}</p>
    </div>
  </div>
)

export default function HowItWorksPage() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-white to-pink-50/50">
      {/* Hero Section */}
      <div className="pt-24 pb-16 text-center">
        <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] 
        to-[#B82E4E] text-transparent bg-clip-text">
          How Yolloo eSIM Works
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto px-4 leading-relaxed">
          Experience seamless global connectivity with our digital eSIM technology. 
          Stay connected anywhere in the world with just a few simple steps.
        </p>
      </div>

      {/* Process Section */}
      <HowItWorks />

      {/* Features Grid */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 text-gray-900">
              Why Choose Our eSIM Service?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover the advantages of using our eSIM technology for your global connectivity needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Feature
              icon={Globe}
              title="Global Coverage"
              description="Connect to reliable networks in over 190+ countries and regions worldwide"
            />
            <Feature
              icon={Wifi}
              title="Instant Activation"
              description="Get connected immediately after purchase with our instant digital delivery"
            />
            <Feature
              icon={CreditCard}
              title="Flexible Plans"
              description="Choose from a variety of data plans that suit your travel needs and budget"
            />
            <Feature
              icon={HeadphonesIcon}
              title="24/7 Support"
              description="Our dedicated support team is always ready to assist you with any questions"
            />
            <Feature
              icon={ShieldCheck}
              title="Secure Connection"
              description="Enterprise-grade security ensures your data and privacy are protected"
            />
            <Feature
              icon={Zap}
              title="High-Speed Data"
              description="Enjoy fast and reliable 4G/5G connections wherever you go"
            />
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 text-gray-900">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600">
              Everything you need to know about our eSIM service
            </p>
          </div>

          <div className="space-y-6">
            <div className="p-6 rounded-2xl bg-gradient-to-br from-pink-50/50 to-white border border-pink-100">
              <h3 className="font-semibold text-lg mb-2 text-gray-900">What is an eSIM?</h3>
              <p className="text-gray-600">
                An eSIM (embedded SIM) is a digital SIM card that allows you to activate a cellular plan 
                without using a physical SIM card. It's built into your device and can be programmed 
                remotely.
              </p>
            </div>

            <div className="p-6 rounded-2xl bg-gradient-to-br from-pink-50/50 to-white border border-pink-100">
              <h3 className="font-semibold text-lg mb-2 text-gray-900">Is my device eSIM compatible?</h3>
              <p className="text-gray-600">
                Most recent smartphones support eSIM technology, including iPhone XS and newer models, 
                Google Pixel 3 and newer, and many recent Samsung devices. Check your device settings 
                or contact us to confirm compatibility.
              </p>
            </div>

            <div className="p-6 rounded-2xl bg-gradient-to-br from-pink-50/50 to-white border border-pink-100">
              <h3 className="font-semibold text-lg mb-2 text-gray-900">How do I activate my eSIM?</h3>
              <p className="text-gray-600">
                After purchase, you'll receive a QR code. Simply scan this code with your phone's camera, 
                follow the on-screen instructions to download the eSIM profile, and activate it in your 
                device settings.
              </p>
            </div>

            <div className="p-6 rounded-2xl bg-gradient-to-br from-pink-50/50 to-white border border-pink-100">
              <h3 className="font-semibold text-lg mb-2 text-gray-900">Can I use multiple eSIMs?</h3>
              <p className="text-gray-600">
                Yes, most eSIM-compatible devices can store multiple eSIM profiles, though usually only 
                one can be active at a time. This makes it easy to switch between different plans or 
                providers.
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
} 