'use client'

import { TypeAnimation } from 'react-type-animation'
import Link from 'next/link'

export function HeroTitle() {
  return (
    <div className="max-w-6xl mx-auto px-4 sm:block flex flex-col justify-between h-[calc(100vh-200px)] sm:h-auto">
      <div className="text-4xl sm:text-4xl lg:text-6xl xl:text-7xl font-bold tracking-tight mt-0 sm:mt-2">
        <div className="text-white mb-2 sm:mb-4 leading-[1.15]">
          Stay Connected
          <span className="bg-gradient-to-r from-[#FAC4CC] to-[#F799A6] text-transparent bg-clip-text"> Worldwide</span>
        </div>
        <div className="h-[1.3em] sm:h-[1.2em] leading-[1.2] mb-4 sm:mb-8">
          <TypeAnimation
            sequence={[
              'with Digital eSIM',
              3000,
              'with Global Coverage',
              3000,
              'with Mobile Freedom',
              3000,
            ]}
            wrapper="span"
            speed={50}
            repeat={Infinity}
            className="bg-gradient-to-r from-[#FAC4CC] to-[#F799A6] text-transparent bg-clip-text inline-block"
          />
          <span className="inline-block w-[3px] h-[1em] bg-[#F799A6] ml-1 align-middle animate-cursor" />
        </div>
      </div>

      <div className="sm:space-y-14 sm:mt-24 lg:mt-24 flex flex-col justify-end h-full sm:block sm:h-auto pb-24 sm:pb-0">
        <p className="text-lg sm:text-2xl lg:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed font-medium mb-4 sm:mb-6 mt-0 sm:mt-0">
          <span className="text-white">Explore global connectivity</span> with Yolloo – 
          enjoy <span className="text-[#F799A6]"> easy eSIM switching</span>,
          <span className="text-[#F799A6]"> no device limitation</span>, and 
          <span className="text-[#F799A6]"> premium service at unbeatable prices</span>.
        </p>

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-6 justify-center max-w-3xl mx-auto w-full mb-0 sm:mb-14">
          <Link
            href="#pricing"
            onClick={(e) => {
              e.preventDefault();
              const section = document.getElementById('pricing');
              if (section) {
                const offset = 20;
                const elementPosition = section.getBoundingClientRect().top;
                const offsetPosition = elementPosition + window.scrollY - offset;

                window.scrollTo({
                  top: offsetPosition,
                  behavior: 'smooth'
                });
              }
            }}
            className="px-8 py-4 sm:py-5 md:h-16 bg-gradient-to-r from-[#B82E4E] to-[#F799A6] hover:from-[#A02745] hover:to-[#E88A97]
            text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5
            text-base sm:text-lg font-semibold w-full sm:w-auto flex items-center justify-center whitespace-nowrap"
          >
            Browse Yolloo Cards
          </Link>
          <Link
            href="/how-it-works"
            className="px-8 py-4 sm:py-5 md:h-16 bg-white/5 backdrop-blur-sm hover:bg-white/10 text-white hover:text-[#F799A6] rounded-full
            border border-[#F799A6]/20 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5
            text-base sm:text-lg font-semibold w-full sm:w-auto flex items-center justify-center whitespace-nowrap"
          >
            How It Works
          </Link>
        </div>
      </div>
    </div>
  )
}