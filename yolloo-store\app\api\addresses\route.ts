import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// GET /api/addresses - 获取用户的所有地址
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const addresses = await prisma.address.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json(addresses)
  } catch (error) {
    console.error("[ADDRESSES_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

// POST /api/addresses - 创建新地址
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await req.json()
    const { name, phone, address1, address2, city, state, postalCode, country } = body

    if (!name || !phone || !address1 || !city || !state || !postalCode || !country) {
      return new NextResponse("Missing required fields", { status: 400 })
    }

    const address = await prisma.address.create({
      data: {
        name,
        phone,
        address1,
        address2,
        city,
        state,
        postalCode,
        country,
        user: {
          connect: {
            id: session.user.id,
          },
        },
      },
    })

    return NextResponse.json(address)
  } catch (error) {
    console.error("[ADDRESSES_POST]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 