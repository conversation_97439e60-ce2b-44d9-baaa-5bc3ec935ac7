"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Icons } from "@/components/icons"
import { toast } from "sonner"
import { DateFormatter, safeParseDate, cleanCardNumber } from "@/lib/utils"

interface CardFormProps {
  initialData?: {
    id: string
    number: string
    status: string
    type: string
    activationDate: Date | null
    expiryDate: Date | null
  }
}

export function CardForm({ initialData }: CardFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [cardNumber, setCardNumber] = useState(initialData?.number || "")
  const [displayCardNumber, setDisplayCardNumber] = useState(initialData?.number || "")

  // 处理卡号输入，自动清理非数字字符
  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value

    // 清理输入，只保留数字
    const cleanedNumber = cleanCardNumber(inputValue)

    // 存储清理后的纯数字
    setCardNumber(cleanedNumber)

    // 显示原始输入（用户可以看到他们输入的内容）
    setDisplayCardNumber(inputValue)
  }

  // 安全格式化日期为HTML date input格式
  const formatDateForInput = (dateInput: unknown): string => {
    const date = safeParseDate(dateInput)
    if (!date) return ''
    return DateFormatter.custom(date, 'yyyy-MM-dd')
  }

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setIsLoading(true)

    try {
      const formData = new FormData(event.currentTarget)
      const data = {
        number: cardNumber, // 使用清理后的纯数字卡号
        type: formData.get("type"),
        status: formData.get("status"),
        activationDate: formData.get("activationDate")
          ? safeParseDate(formData.get("activationDate") as string)
          : null,
        expiryDate: formData.get("expiryDate")
          ? safeParseDate(formData.get("expiryDate") as string)
          : null,
      }

      if (initialData) {
        // Update existing card
        const response = await fetch(`/api/admin/cards/${initialData.id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        })

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(errorText || "Failed to update card")
        }

        toast.success("Card updated successfully")
      } else {
        // Create new card
        const response = await fetch("/api/admin/cards", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        })

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(errorText || "Failed to create card")
        }

        toast.success("Card created successfully")
      }

      router.refresh()
      router.push("/admin/cards")
    } catch (error) {
      console.error(error)
      const errorMessage = error instanceof Error ? error.message : "Something went wrong"
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={onSubmit} className="space-y-8">
      <div className="space-y-2">
        <div className="space-y-2">
          <Label htmlFor="number">Card Number</Label>
          <Input
            id="number"
            name="number"
            value={displayCardNumber}
            onChange={handleCardNumberChange}
            placeholder="Enter card number (e.g., 29901-00000-00000-00025)"
            required
          />
          {cardNumber && cardNumber !== displayCardNumber && (
            <p className="text-sm text-muted-foreground">
              Will be saved as: <span className="font-mono">{cardNumber}</span>
            </p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="type">Type</Label>
          <Select name="type" defaultValue={initialData?.type || "physical"}>
            <SelectTrigger>
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
              <SelectItem value="physical">Physical</SelectItem>
              <SelectItem value="virtual">Virtual</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select name="status" defaultValue={initialData?.status || "Inactive"}>
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
              <SelectItem value="Active">Active</SelectItem>
              <SelectItem value="Inactive">Inactive</SelectItem>
              <SelectItem value="Expired">Expired</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="activationDate">Activation Date</Label>
            <Input
              id="activationDate"
              name="activationDate"
              type="date"
              defaultValue={formatDateForInput(initialData?.activationDate)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="expiryDate">Expiry Date</Label>
            <Input
              id="expiryDate"
              name="expiryDate"
              type="date"
              defaultValue={formatDateForInput(initialData?.expiryDate)}
            />
          </div>
        </div>
      </div>
      <Button type="submit" disabled={isLoading}>
        {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
        {initialData ? "Update" : "Create"} Card
      </Button>
    </form>
  )
}