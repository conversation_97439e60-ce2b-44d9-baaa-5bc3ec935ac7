import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


interface Params {
  params: {
    addressId: string
  }
}

// DELETE /api/addresses/[addressId] - 删除地址
export async function DELETE(req: Request, { params }: Params) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { addressId } = params

    // 验证地址是否属于当前用户
    const address = await prisma.address.findFirst({
      where: {
        AND: [
          { id: addressId },
          { userId: session.user.id }
        ]
      },
    })

    if (!address) {
      return new NextResponse("Address not found", { status: 404 })
    }

    // 删除地址
    await prisma.address.delete({
      where: {
        id: addressId,
      },
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[ADDRESS_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 