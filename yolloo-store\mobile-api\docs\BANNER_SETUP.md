# Mobile API Banner图片设置指南

## 📁 文件夹结构

已为您创建了以下文件夹结构来存放banner图片：

```
mobile-api/
├── public/
│   └── images/
│       ├── banners/
│       │   ├── home/          # 首页轮播Banner
│       │   ├── category/      # 分类页面Banner  
│       │   └── promotion/     # 促销活动Banner
│       └── icons/             # 应用图标和功能图标
└── src/
    └── common/
        └── utils/
            └── image.util.ts  # 图片URL管理工具类
```

## 🖼️ 图片规格要求

### 首页Banner (home/)
- **尺寸**: 750px × 300px (2.5:1 比例)
- **格式**: WebP (首选), JPEG, PNG
- **文件大小**: 建议 < 200KB
- **命名规范**: `banner_home_{id}_{language}.webp`

### 分类页面Banner (category/)
- **尺寸**: 750px × 200px (3.75:1 比例)
- **格式**: WebP (首选), JPEG, PNG
- **文件大小**: 建议 < 150KB
- **命名规范**: `banner_category_{category_id}_{language}.webp`

### 促销活动Banner (promotion/)
- **尺寸**: 750px × 300px (2.5:1 比例)
- **格式**: WebP (首选), JPEG, PNG
- **文件大小**: 建议 < 200KB
- **命名规范**: `banner_promo_{promo_id}_{language}.webp`

### 图标文件 (icons/)
- **应用图标**: 512px × 512px, 256px × 256px, 128px × 128px, 64px × 64px
- **功能图标**: 48px × 48px, 24px × 24px
- **格式**: SVG (首选), PNG
- **命名规范**: `icon_{name}.svg` 或 `icon_{name}_{size}.png`

## 🌐 多语言支持

文件命名中的语言代码：
- 中文: `_zh`
- 英文: `_en`
- 其他语言: 使用对应的ISO语言代码

## 📝 示例文件名

```
# 首页Banner
banner_home_001_zh.webp          # 5G套餐促销 (中文)
banner_home_001_en.webp          # 5G Package Promotion (English)
banner_home_002_zh.webp          # 新用户优惠 (中文)
banner_home_002_en.webp          # New User Discount (English)

# 分类Banner
banner_category_travel_zh.webp   # 旅行分类
banner_category_esim_zh.webp     # eSIM分类

# 促销Banner
banner_promo_summer2024_zh.webp  # 夏季促销
banner_promo_newuser_zh.webp     # 新用户促销

# 图标
icon_app_512.png                 # 应用图标 512px
icon_travel.svg                  # 旅行图标
icon_esim.svg                    # eSIM图标
icon_shield.svg                  # 保号套餐图标
icon_smartphone.svg              # 手机充值图标
```

## 🔗 API访问路径

图片将通过以下URL访问：
```
https://your-domain.com/api/mobile/static/images/banners/home/<USER>
https://your-domain.com/api/mobile/static/images/icons/icon_travel.svg
```

## 🛠️ 技术实现

### 1. 静态文件服务配置
- 已在 `main.ts` 中配置了静态文件服务
- 静态文件通过 `/api/mobile/static/` 路径访问

### 2. 图片URL管理工具
- 创建了 `ImageUtil` 工具类统一管理图片URL
- 支持多语言、多格式、多尺寸的图片URL生成

### 3. Home Service更新
- 已更新 `HomeService` 使用 `ImageUtil` 生成图片URL
- 支持根据用户语言自动选择对应的图片

### 4. 静态资源控制器
- 创建了 `StaticController` 提供图片访问和健康检查
- 支持缓存控制和错误处理

## 📋 使用步骤

1. **准备图片文件**
   - 按照规格要求制作banner图片
   - 使用专业工具压缩图片以优化加载速度

2. **上传图片**
   - 将图片文件放置到对应的文件夹中
   - 确保文件名符合命名规范

3. **验证访问**
   - 启动应用后访问: `http://localhost:4000/api/mobile/static/health`
   - 检查静态资源服务是否正常

4. **测试API**
   - 调用 `/api/mobile/home/<USER>
   - 验证返回的图片URL是否正确

## 🎨 设计建议

### 移动端优化
- 确保图片在高DPI屏幕上清晰显示
- 文字内容要清晰可读，避免过小字体
- 考虑不同屏幕尺寸的适配

### 品牌一致性
- 使用统一的品牌色彩方案
- 保持视觉风格一致
- 遵循品牌设计指南

### 性能优化
- 优先使用WebP格式减少文件大小
- 图片压缩后保持清晰度
- 避免使用过大的图片文件

## 🔧 维护建议

1. **定期清理**: 删除不再使用的图片文件
2. **版本管理**: 建立图片版本管理机制
3. **监控**: 定期检查图片加载性能
4. **备份**: 重要图片资源要有备份

## 📞 技术支持

如果在使用过程中遇到问题，请检查：
1. 文件路径是否正确
2. 文件名是否符合命名规范
3. 图片格式是否支持
4. 静态文件服务是否正常启动
