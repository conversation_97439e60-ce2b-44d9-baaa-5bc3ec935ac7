"use client"

import { cn } from "@/lib/utils"
import { useState } from "react"

interface FormattedDescriptionProps {
  text: string
  className?: string
  plain?: boolean // 纯文本模式
  maxLines?: number // 最大显示行数
}

function renderOperatorLine(line: string, idx: number, showAll: boolean = false) {
  // 每个运营商条目格式：Country-Operator，Network-4G/5G，APN-drei.at
  // 按中文逗号分割字段
  const fields = line.split(/，/).map(f => f.trim()).filter(Boolean);

  // 每个运营商条目通常有3个字段，限制显示数量
  const maxFields = showAll ? fields.length : Math.min(fields.length, 3);
  const displayFields = fields.slice(0, maxFields);
  const hasMoreFields = fields.length > 3;

  return (
    <div key={idx} className="flex flex-wrap gap-2 mb-1">
      {displayFields.map((field, i) => {
        // 处理不同类型的字段
        let displayText = field.trim();
        let bgColor = "bg-primary/10 text-primary";

        // 根据字段内容设置不同的样式
        if (field.includes('-') && i === 0) {
          // 第一个字段通常是 Country-Operator
          bgColor = "bg-blue-100 text-blue-700";
        } else if (field.toLowerCase().includes('network')) {
          // 网络类型字段
          bgColor = "bg-green-100 text-green-700";
        } else if (field.toLowerCase().includes('apn')) {
          // APN字段
          bgColor = "bg-orange-100 text-orange-700";
        }

        return (
          <span key={i} className={`${bgColor} rounded px-2 py-0.5 text-xs font-semibold whitespace-nowrap`}>
            {displayText}
          </span>
        );
      })}
      {!showAll && hasMoreFields && (
        <span className="bg-gray-100 text-gray-600 rounded px-2 py-0.5 text-xs font-semibold whitespace-nowrap">
          +{fields.length - 3} more
        </span>
      )}
    </div>
  );
}

export function FormattedDescription({ text, className, plain, maxLines }: FormattedDescriptionProps) {
  const [showAllOperatorTags, setShowAllOperatorTags] = useState(false);
  
  // 处理空文本
  if (!text) {
    return <div className={cn(className)}>No description</div>;
  }

  // 只渲染纯文本（无高亮、无粗体、无主题色）
  if (plain) {
    // 只保留文本内容，按行分割
    const lines = text.replace(/<br\s*\/??\s*>/gi, '\n').split('\n').map(line => line.trim()).filter(Boolean);

    // 如果指定了最大行数，则限制行数
    const displayLines = maxLines ? lines.slice(0, maxLines) : lines;

    return (
      <div className={cn("space-y-1", className)}>
        {displayLines.map((line, idx) => (
          <p key={idx} className="text-sm text-gray-600 dark:text-gray-300 font-normal leading-snug">{line}</p>
        ))}
        {maxLines && lines.length > maxLines && (
          <p className="text-xs text-primary cursor-pointer hover:underline">Show more...</p>
        )}
      </div>
    );
  }

  // 1. 先将 <br> 及其所有变体完整替换为换行符
  const textWithLineBreaks = text.replace(/<br\s*\/??\s*>/gi, '\n');
  // 2. 按行分割
  const lines = textWithLineBreaks.split('\n').map(line => line.trim()).filter(Boolean);

  // 3. 识别运营商信息 - 重新设计逻辑
  let operatorLines: string[] = [];
  let otherLines: string[] = [];

  // 查找包含 "Coverage and Operator" 的行
  const operatorLineIdx = lines.findIndex(line => line.toLowerCase().includes('coverage and operator'));

  if (operatorLineIdx >= 0) {
    const operatorLine = lines[operatorLineIdx];

    // 分离标题和数据
    const parts = operatorLine.split('：');
    if (parts.length > 1) {
      const title = parts[0] + '：';
      const data = parts.slice(1).join('：');

      // 按分号分割运营商数据
      operatorLines = data.split(/；/).map(entry => entry.trim()).filter(Boolean);

      // 其他行包括标题行（只有标题部分）
      otherLines = [...lines.slice(0, operatorLineIdx), title, ...lines.slice(operatorLineIdx + 1)];
    } else {
      // 如果没有找到冒号，保持原样
      otherLines = lines;
    }
  } else {
    otherLines = lines;
  }

  // 如果指定了最大行数，则限制普通文本的行数
  const displayOtherLines = maxLines ? otherLines.slice(0, maxLines) : otherLines;
  const hasMoreLines = maxLines && otherLines.length > maxLines;

  // 如果有运营商信息，则显示运营商信息
  const showOperatorInfo = operatorLines.length > 0;

  // 默认显示前2个运营商条目，其余通过View More显示
  const defaultShowCount = 2;
  const displayOperatorLines = showAllOperatorTags ? operatorLines : operatorLines.slice(0, defaultShowCount);
  const hasMoreOperators = operatorLines.length > defaultShowCount;

  // 创建一个包含所有内容的容器，但限制其高度
  if (maxLines) {
    // 使用CSS限制高度的方法
    const lineHeight = 1.5; // 假设行高为1.5em
    const maxHeight = maxLines * lineHeight; // 计算最大高度

    return (
      <div className={cn("space-y-2", className)}>
        {/* 主要内容区域 */}
        <div className="relative">
          <div className={cn("space-y-2 overflow-hidden")} style={{ maxHeight: `${maxHeight}em` }}>
            {displayOtherLines.map((line, idx) => {
              if (line.startsWith('*')) {
                // 星号开头的文本加粗强调
                return (
                  <p key={idx} className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-start">
                    <span className="mr-1">*</span>{line.slice(1).trim()}
                  </p>
                );
              }
              return (
                <p key={idx} className="text-sm text-gray-700 dark:text-gray-200">{line}</p>
              );
            })}
          </div>

          {/* 只在有更多行且没有运营商信息时显示渐变遮罩效果 */}
          {hasMoreLines && !showOperatorInfo && (
            <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white dark:from-gray-950 to-transparent pointer-events-none"></div>
          )}

          {/* 显示更多按钮 */}
          {hasMoreLines && !showOperatorInfo && (
            <p className="text-xs text-primary cursor-pointer hover:underline text-right mt-1">Show more...</p>
          )}
        </div>

        {/* Coverage and Operator 部分只显示前2行 - 不受maxHeight限制 */}
        {showOperatorInfo && (
          <div className="mt-4">
            <p className="font-bold text-base text-gray-900 dark:text-gray-100 mb-2">Coverage and Operator</p>
            <div className="space-y-1">
              {displayOperatorLines.map((line, idx) => renderOperatorLine(line, idx, true))}
            </div>
            {hasMoreOperators && (
              <button
                onClick={() => {
                  // 查找Description标签并点击
                  const descriptionTab = document.querySelector('[value="description"]') as HTMLElement;
                  if (descriptionTab) {
                    descriptionTab.click();
                    // 滚动到标签页区域
                    setTimeout(() => {
                      const tabsContainer = document.querySelector('.mt-12');
                      if (tabsContainer) {
                        tabsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                      }
                    }, 100);
                  }
                }}
                className="text-xs text-primary cursor-pointer hover:underline mt-2 relative z-10"
              >
                View More
              </button>
            )}
          </div>
        )}
      </div>
    );
  }

  // 不限制行数时的正常渲染
  return (
    <div className={cn("space-y-2", className)}>
      {displayOtherLines.map((line, idx) => {
        if (line.startsWith('*')) {
          // 星号开头的文本加粗强调
          return (
            <p key={idx} className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-start">
              <span className="mr-1">*</span>{line.slice(1).trim()}
            </p>
          );
        }
        return (
          <p key={idx} className="text-sm text-gray-700 dark:text-gray-200">{line}</p>
        );
      })}

      {showOperatorInfo && (
        <div className="mt-4">
          <p className="font-bold text-base text-gray-900 dark:text-gray-100 mb-2">Coverage and Operator</p>
          <div className="space-y-1">
            {/* 在Description标签页中显示所有运营商信息 */}
            {operatorLines.map((line, idx) => renderOperatorLine(line, idx, true))}
          </div>
        </div>
      )}
    </div>
  );
}
