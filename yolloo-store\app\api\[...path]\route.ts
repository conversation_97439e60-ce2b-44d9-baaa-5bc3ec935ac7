// 为所有API路由设置动态模式
export const dynamic = 'force-dynamic';

// 空的GET处理函数，不会实际处理请求
export async function GET() {
  return new Response(null, { status: 404 });
}

// 空的POST处理函数，不会实际处理请求
export async function POST() {
  return new Response(null, { status: 404 });
}

// 空的PUT处理函数，不会实际处理请求
export async function PUT() {
  return new Response(null, { status: 404 });
}

// 空的DELETE处理函数，不会实际处理请求
export async function DELETE() {
  return new Response(null, { status: 404 });
} 