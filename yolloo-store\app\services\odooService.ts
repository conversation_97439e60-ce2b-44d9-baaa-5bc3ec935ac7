import axios, { AxiosInstance } from 'axios';
import { createHash } from 'crypto';
import {
    OdooProduct,
    OdooOrder,
    OdooResponse,
    OdooOrderLine,
    OdooInvoiceResponse,
    OdooProductPrice,
    OdooQRCodeResponse,
    OdooOrderResponse
} from '../types/odoo';
import { ODOO_CONFIG, SYNC_PARAMS } from '../config/odoo';
import { prisma } from '@/lib/prisma';

export class OdooService {
    private client: AxiosInstance;
    private config: typeof ODOO_CONFIG;

    constructor(config: typeof ODOO_CONFIG) {
        this.config = config;
        this.client = axios.create({
            baseURL: '',  // 不使用baseURL，而是使用完整URL
            headers: {
                'Content-Type': 'application/json',
                'X-Channel-Id': config.channelId,
                'X-Channel-Language': config.channelLanguage,
            },
            timeout: 300000, // 设置超时为5分钟 (300,000毫秒)
        });
    }

    //生成签名
    private generateSignature(body: string): string {
        const digestStr = `${this.config.authSecret}${body}${this.config.channelId}`.replace(/\s/g, '');

        const signature = createHash('md5').update(digestStr, 'utf8').digest('hex').toLowerCase();

        return signature;
    }

    // 验证签名
    public verifySignature(body: string, signature?: string | null): boolean {
        if (!signature) return false;
        const expectedSignature = this.generateSignature(body);
        return expectedSignature === signature.toLowerCase();
    }

    private async request<T>(method: string, url: string, data?: any): Promise<OdooResponse<T>> {
        const requestData = data || {};

        // 使用JSON.stringify的replacer函数来处理空白字符
        const body = JSON.stringify(requestData, null, 0);

        console.log('[Odoo] req Body: ', body);

        const signature = this.generateSignature(body);
        const fullUrl = `${this.config.address}${url}`;

        const headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Channel-Id': this.config.channelId,
            'X-Channel-Language': this.config.channelLanguage,
            'X-sign-Method': this.config.signMethod.toLowerCase(),
            'X-Sign-Value': signature
        };

        try {
            const response = await this.client.request({
                method,
                url: fullUrl,
                data: body,
                headers
            });

            return response.data;
        } catch (error) {
            if (axios.isAxiosError(error)) {
                console.error('Odoo API Error:', {
                    status: error.response?.status,
                    statusText: error.response?.statusText,
                    data: error.response?.data,
                    config: {
                        url: error.config?.url,
                        method: error.config?.method,
                        headers: error.config?.headers,
                        data: error.config?.data
                    }
                });
                throw new Error(`Odoo API Error: ${error.response?.data?.error || error.message}`);
            }
            console.error('Unexpected error:', error);
            throw error;
        }
    }

    // 拉取商品信息
    async pullProducts(productType: string = 'esim'): Promise<OdooResponse<OdooProduct[]>> {
        let allProducts: OdooProduct[] = [];
        let start = SYNC_PARAMS.defaultStart;
        let hasMoreData = true;
        let lastResponse: OdooResponse<OdooProduct[]> | null = null;
        let batchCount = 0;

        console.log(`[Odoo] Fetching products=${productType}`);

        while (hasMoreData && start < SYNC_PARAMS.maxLength) {
            batchCount++;

            const data = {
                product_type: productType,
                start: start,
                length: SYNC_PARAMS.defaultLength
            };

            const productsResponse = await this.request<OdooProduct[]>('POST', '/openapi/v3/get_proudct_list', data);
            lastResponse = productsResponse;

            // 增强的响应验证 - Odoo返回"ok"表示成功
            if (
                productsResponse.status !== 'success' &&
                productsResponse.status !== '200' &&
                (!productsResponse.result ||
                    (productsResponse.result.status !== 'success' &&
                    productsResponse.result.status !== 'ok' &&
                    productsResponse.result.message !== '操作成功'))
            ) {
                throw new Error(
                    `Failed to fetch products: ${productsResponse.result?.message || 'Unknown error'}`,
                );
            }

            if (
                !productsResponse.result?.data ||
                !Array.isArray(productsResponse.result.data)
            ) {
                throw new Error('No product data returned from API');
            }

            const batchProducts = productsResponse.result.data;

            // 添加到总产品列表
            allProducts = [...allProducts, ...batchProducts];

            // 如果返回的产品数量小于请求的数量，说明没有更多数据了
            if (batchProducts.length < SYNC_PARAMS.defaultLength) {
                hasMoreData = false;
            }

            // 更新起始位置
            start += SYNC_PARAMS.defaultLength;
        }

        console.log(`[Odoo] Fetched ${allProducts.length} products of type=${productType} in ${batchCount} batches`);

        // 确保有响应对象
        if (!lastResponse) {
            throw new Error('No response received from Odoo API');
        }

        // 构造与原来相同格式的响应，但包含所有获取的产品
        return {
            ...lastResponse,
            result: {
                ...lastResponse.result,
                data: allProducts
            }
        };
    }

    // 获取产品价格
    async getProductPrices(params?: {
        product_code?: string[];
        start?: number;
        length?: number;
    }): Promise<OdooResponse<OdooProductPrice[]>> {
        const data = {
            product_code: params?.product_code,
            start: params?.start || SYNC_PARAMS.defaultStart,
            length: params?.length || SYNC_PARAMS.defaultLength
        };
        return this.request<OdooProductPrice[]>('POST', '/openapi/v3/get_proudct_price', data);
    }

    // 创建订单
    async createOrder(order: OdooOrder): Promise<OdooOrderResponse> {
        return this.request('POST', '/openapi/v3/create_purchase_order', order);
    }

    // 查询订单状态
    async queryOrderStatus(orderSn: string): Promise<OdooResponse<any>> {
        const data = {
            customer_order_ref: [orderSn]
        };
        console.log('Sending request to Odoo:', {
            url: '/openapi/v3/get_sale_order_status',
            data
        });
        return this.request('POST', '/openapi/v3/get_sale_order_status', data);
    }

    // 查询多个订单状态
    async queryOrderStatusMultiple(customerOrderRefs: string[]): Promise<OdooResponse<any>> {
        const data = {
            customer_order_ref: customerOrderRefs
        };
        console.log('Sending request to Odoo for multiple orders:', {
            url: '/openapi/v3/get_sale_order_status',
            data
        });
        return this.request('POST', '/openapi/v3/get_sale_order_status', data);
    }

    // 获取结算单列表
    async getInvoiceOrders(params: {
        customer_order_ref?: string[];
        start_invoice_date?: string;
        end_invoice_date?: string;
        summarize?: boolean;
    }): Promise<OdooResponse<OdooInvoiceResponse>> {
        return this.request('POST', '/openapi/v3/get_invoice_order_list', params);
    }

    // 处理产品信息推送
    async handleProductPush(data: any[]): Promise<void> {
        console.log('Processing product push:', data);
        try {
            for (const productData of data) {
                const { product_code, name, description_sale, website_description, off_shelve, variants } = productData;

                // 查找现有产品
                const existingProduct = await prisma.product.findFirst({
                    where: {
                        specifications: {
                            path: ['odooProductCode'],
                            equals: product_code
                        }
                    }
                });

                if (existingProduct) {
                    // 更新现有产品
                    await prisma.product.update({
                        where: { id: existingProduct.id },
                        data: {
                            name,
                            description: description_sale,
                            websiteDescription: website_description,
                            off_shelve: off_shelve ?? false,
                            // 如果有变体价格，更新主产品价格为第一个变体的价格
                            ...(variants?.[0]?.supply_price && {
                                price: variants[0].supply_price
                            })
                        }
                    });

                    // 更新或创建变体
                    if (variants && variants.length > 0) {
                        for (const variant of variants) {
                            // 检查变体属性中是否有code为"card"的属性
                            const hasCardAttribute = variant.variant_attributes?.some((attr: any) => attr.code === 'card');

                            // 如果有card属性，跳过这个变体
                            if (hasCardAttribute) {
                                console.log(`Skipping variant with card attribute: ${variant.variant_code}`);
                                continue;
                            }

                            const variantAttributes = variant.variant_attributes?.reduce((acc: any, attr: any) => {
                                acc[attr.code] = attr.value;
                                return acc;
                            }, {}) || {};

                            // 首先尝试查找现有变体
                            const existingVariant = await prisma.productVariant.findFirst({
                                where: {
                                    AND: [
                                        { productId: existingProduct.id },
                                        { variantCode: variant.variant_code }
                                    ]
                                }
                            });

                            if (existingVariant) {
                                // 更新现有变体
                                await prisma.productVariant.update({
                                    where: { id: existingVariant.id },
                                    data: {
                                        price: variant.supply_price,
                                        currency: variant.currency,
                                        attributes: variantAttributes
                                    }
                                });
                            } else {
                                // 创建新变体
                                await prisma.productVariant.create({
                                    data: {
                                        price: variant.supply_price,
                                        currency: variant.currency,
                                        variantCode: variant.variant_code,
                                        attributes: variantAttributes,
                                        productId: existingProduct.id
                                    }
                                });
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error processing product push:', error);
            throw error;
        }
    }

    // 处理订单状态推送
    async handleOrderStatusPush(data: any[]): Promise<void> {
        console.log('Processing order status push:', data);
        try {
            for (const orderData of data) {
                const { customer_order_ref, order_lines } = orderData;

                if (!order_lines || order_lines.length === 0) continue;

                const orderLine = order_lines[0]; // 获取第一个订单行

                // 处理UID数据 - 可能是单个UID或多个UID的数组
                let uids: string[] = [];
                let uidString: string | null = null;

                if (orderLine.data) {
                    if (Array.isArray(orderLine.data)) {
                        // 从所有data项中提取uid
                        uids = orderLine.data
                            .filter((item: any) => item && item.uid)
                            .map((item: any) => item.uid);

                        if (uids.length > 0) {
                            uidString = uids.join(',');
                        }
                    } else if (typeof orderLine.data === 'object' && orderLine.data.uid) {
                        // 处理单个对象的情况
                        uidString = orderLine.data.uid;
                    }
                }

                console.log(`[ORDER_STATUS_PUSH] Extracted UIDs for order ${customer_order_ref}:`, uids);

                // 更新或创建订单状态
                // 使用产品的variantCode（UUID格式）或默认值
                const variantCode = orderLine.variant_code || "default";

                // 确保UID是正确的格式（数字字符串）
                const formattedUid = uidString ? uidString.replace(/[^0-9,]/g, '') : '';

                console.log(`[ORDER_STATUS_PUSH] Using variantCode: ${variantCode}, formatted UID: ${formattedUid}`);

                // 查找是否已存在相同 orderId, variantCode, uid 的记录
                const existingStatus = await prisma.odooOrderStatus.findFirst({
                    where: {
                        orderId: customer_order_ref,
                        variantCode: variantCode,
                        uid: formattedUid || null
                    }
                });

                if (existingStatus) {
                    // 更新现有记录
                    await prisma.odooOrderStatus.update({
                        where: { id: existingStatus.id },
                        data: {
                            status: orderLine.status,
                            description: orderLine.description,
                            productName: orderLine.product_name,
                            isDigital: orderLine.is_digital || false,
                            deliveredQty: orderLine.delivered_qty || 0,
                            trackingNumber: orderLine.tracking_number,
                            planState: orderLine.data?.[0]?.plan_state,
                            lastCheckedAt: new Date()
                        }
                    });
                } else {
                    // 创建新记录
                    await prisma.odooOrderStatus.create({
                        data: {
                            orderId: customer_order_ref,
                            variantCode: variantCode,
                            status: orderLine.status,
                            description: orderLine.description,
                            productName: orderLine.product_name,
                            isDigital: orderLine.is_digital || false,
                            deliveredQty: orderLine.delivered_qty || 0,
                            trackingNumber: orderLine.tracking_number,
                            planState: orderLine.data?.[0]?.plan_state,
                            uid: formattedUid || null, // 存储格式化后的UID
                            lastCheckedAt: new Date()
                        }
                    });
                }

                // 如果订单状态为已发货，更新主订单状态
                if (orderLine.status === 'delivered') {
                    // 更新订单状态
                    await prisma.order.update({
                        where: { id: customer_order_ref },
                        data: { status: 'DELIVERED' }
                    });

                    // 查找订单关联的推广记录
                    const referral = await prisma.affiliateReferral.findFirst({
                        where: {
                            orderId: customer_order_ref,
                            status: 'PENDING'
                        },
                        include: {
                            organizationCommission: true
                        }
                    });

                    // 如果找到推广记录，更新其状态为APPROVED
                    if (referral) {
                        console.log(`Updating affiliate referral status for order: ${customer_order_ref}`);

                        // 更新AffiliateReferral状态
                        await prisma.affiliateReferral.update({
                            where: { id: referral.id },
                            data: { status: 'APPROVED' }
                        });

                        // 如果有关联的组织佣金记录，同时更新其状态
                        if (referral.organizationCommission) {
                            console.log(`Updating organization commission status for order: ${customer_order_ref}`);
                            await prisma.organizationCommission.update({
                                where: { id: referral.organizationCommissionId! },
                                data: { status: 'APPROVED' }
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error processing order status push:', error);
            throw error;
        }
    }

    // 获取订单二维码
    async getOrderQRCode(customerOrderRef: string | string[]): Promise<OdooResponse<OdooQRCodeResponse[]>> {
        const data = {
            customer_order_ref: Array.isArray(customerOrderRef) ? customerOrderRef.join(',') : customerOrderRef
        };
        return this.request('POST', '/openapi/v3/get_sale_order_esim_qrcode', data);
    }
}

// 导出服务实例创建函数
export const createOdooService = (config: typeof ODOO_CONFIG) => {
    return new OdooService(config);
};