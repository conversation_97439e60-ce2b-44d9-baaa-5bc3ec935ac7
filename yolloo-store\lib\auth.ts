import { PrismaAdapter } from "@auth/prisma-adapter"
import { NextAuthOptions } from "next-auth"
import { prisma } from "@/lib/prisma"
import GoogleProvider from "next-auth/providers/google"
import CredentialsProvider from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import { Adapter } from "next-auth/adapters"
import { getVerificationCode, deleteVerificationCode } from "@/lib/redis"

const adapter = PrismaAdapter(prisma);

// Extend the adapter to include the role field
const extendedAdapter = {
  ...adapter,
  getUser: async (id: string) => {
    const user = await prisma.user.findUnique({
      where: { id }
    });
    if (!user) return null;
    return {
      ...user,
      role: user.role
    };
  },
  getUserByEmail: async (email: string) => {
    const user = await prisma.user.findUnique({
      where: { email }
    });
    if (!user) return null;
    return {
      ...user,
      role: user.role
    };
  },
  createUser: async (data: any) => {
    const user = await prisma.user.create({
      data: {
        ...data,
        role: "CUSTOMER"
      }
    });
    return {
      ...user,
      role: user.role
    };
  }
} as Adapter;

export const authOptions: NextAuthOptions = {
  adapter: extendedAdapter,
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: "/auth/signin",
    error: '/auth/error',
  },
  providers: [
    CredentialsProvider({
      id: "credentials",
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Invalid credentials")
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          }
        })

        if (!user || !user.hashedPassword) {
          throw new Error("Invalid credentials")
        }

        const isCorrectPassword = await bcrypt.compare(
          credentials.password,
          user.hashedPassword
        )

        if (!isCorrectPassword) {
          throw new Error("Invalid credentials")
        }

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          image: user.image,
          role: user.role
        }
      }
    }),
    CredentialsProvider({
      id: "email-code",
      name: "Email Code",
      credentials: {
        email: { label: "Email", type: "email" },
        code: { label: "Verification Code", type: "text" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.code) return null;

        // 验证验证码
        const storedCode = await getVerificationCode(credentials.email);
        if (!storedCode || storedCode !== credentials.code) return null;

        // 删除已使用的验证码
        await deleteVerificationCode(credentials.email);

        // 查找或创建用户
        let user = await prisma.user.findUnique({
          where: { email: credentials.email }
        });

        if (!user) {
          // 创建新用户
          const name = credentials.email.split('@')[0];
          user = await prisma.user.create({
            data: {
              email: credentials.email,
              name,
              role: 'CUSTOMER',
              emailVerified: new Date(),
            }
          });
        } else if (!user.emailVerified) {
          // 更新邮箱验证状态
          user = await prisma.user.update({
            where: { id: user.id },
            data: { emailVerified: new Date() }
          });
        }

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          image: user.image,
          role: user.role
        };
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "select_account",
          access_type: "offline",
          response_type: "code",
          scope: "openid email profile"
        }
      },
      httpOptions: {
        timeout: 40000,
        agent: undefined,
        headers: {
          "Accept": "application/json",
          "User-Agent": "next-auth"
        }
      }
    }),
  ],
  callbacks: {
    async signIn({ account, profile, user, credentials, request }) {
      try {
        // 记录登录历史
        if (user && user.id) {
          // 获取请求头信息
          const reqHeaders = request?.headers || new Headers();

          // 获取用户代理和IP地址
          const userAgent = reqHeaders.get("user-agent") || "";
          const forwarded = reqHeaders.get("x-forwarded-for");
          const ip = forwarded ? forwarded.split(/, /)[0] : reqHeaders.get("REMOTE_ADDR") || "";

          // 确定登录方法
          let loginMethod = "unknown";
          if (credentials) {
            // 检查是否是验证码登录（通过检查credentials中是否有code字段）
            if (credentials.code && !credentials.password) {
              loginMethod = "email_code";
            } else {
              loginMethod = "password";
            }
          } else if (account) {
            loginMethod = account.provider;
          }

          // 记录登录历史
          await prisma.userLoginHistory.create({
            data: {
              userId: user.id,
              ipAddress: ip || null,
              userAgent: userAgent || null,
              loginMethod,
              additionalInfo: {}
            }
          });
        }

        // 如果是Google登录，确保有email
        if (account?.provider === "google") {
          return !!(profile?.email);
        }

        return true;
      } catch (error) {
        console.error("Error recording login history:", error);
        // 即使记录登录历史失败，也允许用户登录
        return true;
      }
    },
    async redirect({ url, baseUrl }) {
      try {
        // 允许的域名列表
        const allowedDomains = [
          'esim.yolloo.com',
          'yolloo.com',
          'www.yolloo.com',
          'localhost'
        ];

        // 如果 url 是相对路径，添加 baseUrl
        const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url.startsWith('/') ? url : `/${url}`}`;
        const parsedUrl = new URL(fullUrl);
        const callbackUrlParam = parsedUrl.searchParams.get('callbackUrl');

        // 如果有 callbackUrl 参数
        if (callbackUrlParam) {
          // 尝试解码 callbackUrl
          const decodedCallbackUrl = decodeURIComponent(callbackUrlParam);

          // 如果是相对路径
          if (decodedCallbackUrl.startsWith('/')) {
            // 直接返回基础URL+相对路径
            return `${baseUrl}${decodedCallbackUrl.startsWith('/') ? decodedCallbackUrl : `/${decodedCallbackUrl}`}`;
          }

          // 如果是完整 URL
          try {
            const parsedCallback = new URL(decodedCallbackUrl);
            // 检查是否是允许的域名
            if (allowedDomains.some(domain =>
              parsedCallback.hostname === domain ||
              parsedCallback.hostname.includes(domain) ||
              parsedCallback.hostname === new URL(baseUrl).hostname
            )) {
              return decodedCallbackUrl;
            }
          } catch (e) {
            console.error('Invalid callback URL:', e);
            // 如果解析失败但看起来是相对路径，尝试使用baseUrl
            if (!decodedCallbackUrl.includes('://')) {
              return `${baseUrl}${decodedCallbackUrl.startsWith('/') ? decodedCallbackUrl : `/${decodedCallbackUrl}`}`;
            }
          }
        }

        // url 本身可能已经是最终重定向目标（不含callbackUrl参数）
        // 检查url自身是否是相对路径
        if (url.startsWith('/')) {
          return `${baseUrl}${url}`;
        }

        // 如果是允许的完整URL
        try {
          const parsed = new URL(fullUrl);
          if (allowedDomains.some(domain =>
            parsed.hostname === domain ||
            parsed.hostname.includes(domain) ||
            parsed.hostname === new URL(baseUrl).hostname
          )) {
            return fullUrl;
          }
        } catch (e) {
          console.error('URL parse error:', e);
        }

        // 默认重定向到首页
        return baseUrl;
      } catch (error) {
        // 如果 URL 解析失败，返回首页
        console.error('Redirect URL parse error:', error);
        return baseUrl;
      }
    },
    async session({ token, session }) {
      if (token) {
        session.user.id = token.id
        session.user.name = token.name
        session.user.email = token.email
        session.user.image = token.picture
        session.user.role = token.role
      }

      return session
    },
    async jwt({ token, user, trigger, session }) {
      if (trigger === "update" && session) {
        return { ...token, ...session.user }
      }

      const dbUser = await prisma.user.findFirst({
        where: {
          email: token.email,
        },
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          role: true,
        },
      })

      if (!dbUser) {
        if (user) {
          token.id = user?.id
          token.role = user?.role || "CUSTOMER"
        }
        return token
      }

      return {
        id: dbUser.id,
        name: dbUser.name,
        email: dbUser.email,
        picture: dbUser.image,
        role: dbUser.role,
      }
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
}
