import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function GET() {
  try {
    // 查找 category.name 为 external_data 的商品及其变体
    const product = await prisma.product.findFirst({
      where: {
        category: {
          name: "external_data"
        },
        off_shelve: false
      },
      include: {
        variants: {
          take: 1,
          select: {
            id: true,
            variantCode: true,
            price: true,
            currency: true,
            duration: true,
            durationType: true
          }
        }
      }
    })

    if (!product) {
      return new NextResponse("External data product not found", { status: 404 })
    }

    // 使用商品本身的价格，如果有变体则使用变体的价格和货币
    const [variant] = product.variants
    return NextResponse.json({
      id: product.id,
      price: variant?.price ?? product.price,
      currency: variant?.currency ?? "USD",
      variant: variant || null
    })
  } catch (error) {
    console.error("Failed to fetch external data product:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}