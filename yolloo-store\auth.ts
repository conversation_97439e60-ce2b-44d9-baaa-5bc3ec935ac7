import NextAuth from "next-auth"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "@/lib/prisma"
import authConfig from "@/auth.config"
import { Adapter } from "next-auth/adapters"

const adapter = PrismaAdapter(prisma);

// Cast the adapter to unknown first to avoid type errors
const baseAdapter = adapter as unknown;

// Extend the adapter to include the role field
const extendedAdapter = {
  ...baseAdapter as any,
  getUser: async (id: string) => {
    const user = await prisma.user.findUnique({
      where: { id }
    });
    if (!user) return null;
    return {
      ...user,
      role: user.role
    };
  },
  getUserByEmail: async (email: string) => {
    const user = await prisma.user.findUnique({
      where: { email }
    });
    if (!user) return null;
    return {
      ...user,
      role: user.role
    };
  },
  createUser: async (data: any) => {
    const user = await prisma.user.create({
      data: {
        ...data,
        role: "CUSTOMER"
      }
    });
    return {
      ...user,
      role: user.role
    };
  }
} as Adapter;

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  adapter: extendedAdapter,
  session: { strategy: "jwt" },
  ...authConfig,
}); 