import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { PagesService } from './pages.service';
import { PageConfigQueryDto } from './dto/page-config-query.dto';
import { PageContentQueryDto } from './dto/page-content-query.dto';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { Public } from '../common/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller('pages')
export class PagesController {
  constructor(private readonly pagesService: PagesService) {}

  @Public()
  @Get('configs')
  getPageConfigs(
    @Query() query: PageConfigQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.pagesService.getPageConfigs(query, ctx);
  }

  @Public()
  @Get('content')
  getPageContent(
    @Query() query: PageContentQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.pagesService.getPageContent(query, ctx);
  }
}
