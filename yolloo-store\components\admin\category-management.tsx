"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Icons } from "@/components/icons"
import { toast } from "sonner"
import { CategoryDialog } from "@/components/admin/category-dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"

interface Category {
  id: string
  name: string
  description: string | null
  _count?: {
    products: number
  }
}

export function CategoryManagement() {
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [deleteDialogO<PERSON>, setDeleteDialogOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [productCounts, setProductCounts] = useState<Record<string, number>>({})

  const fetchCategories = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/admin/categories")
      if (!response.ok) {
        throw new Error("Failed to fetch categories")
      }
      const data = await response.json()
      setCategories(data)

      // Fetch product counts for each category
      const counts: Record<string, number> = {}
      await Promise.all(
        data.map(async (category: Category) => {
          const countResponse = await fetch(`/api/admin/categories/${category.id}`)
          if (countResponse.ok) {
            const categoryData = await countResponse.json()
            counts[category.id] = categoryData._count.products
          }
        })
      )
      setProductCounts(counts)
    } catch (error) {
      console.error(error)
      toast.error("Failed to load categories")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  const handleDeleteCategory = async () => {
    if (!selectedCategory) return

    try {
      const response = await fetch(`/api/admin/categories/${selectedCategory.id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(errorText || "Failed to delete category")
      }

      toast.success("Category deleted successfully")
      fetchCategories()
    } catch (error) {
      console.error(error)
      toast.error(error instanceof Error ? error.message : "Something went wrong")
    } finally {
      setDeleteDialogOpen(false)
      setSelectedCategory(null)
    }
  }

  const openDeleteDialog = (category: Category) => {
    setSelectedCategory(category)
    setDeleteDialogOpen(true)
  }

  return (
    <Card className="p-6 mt-8">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h4 className="text-sm font-medium">Category Management</h4>
            <p className="text-sm text-muted-foreground">
              Manage product categories for your store
            </p>
          </div>
          <Button onClick={() => setIsDialogOpen(true)} size="sm">
            <Icons.add className="mr-2 h-4 w-4" />
            Add Category
          </Button>
        </div>

        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Icons.spinner className="h-6 w-6 animate-spin" />
            </div>
          ) : categories.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Icons.category className="h-10 w-10 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">No categories found</p>
              <Button
                onClick={() => setIsDialogOpen(true)}
                variant="outline"
                size="sm"
                className="mt-2"
              >
                Add your first category
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="pl-6">Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-center">Products</TableHead>
                    <TableHead className="text-right pr-6">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {categories.map((category) => (
                    <TableRow key={category.id}>
                      <TableCell className="font-medium pl-6">{category.name}</TableCell>
                      <TableCell className="max-w-[300px] truncate">
                        {category.description || "—"}
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge variant="outline">
                          {productCounts[category.id] || 0}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right pr-6">
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => openDeleteDialog(category)}
                          disabled={productCounts[category.id] > 0}
                          title={
                            productCounts[category.id] > 0
                              ? "Cannot delete category with associated products"
                              : "Delete category"
                          }
                        >
                          <Icons.trash className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </div>

      <CategoryDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onSuccess={fetchCategories}
      />

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the category "{selectedCategory?.name}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteCategory}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  )
}
