'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export default function ActivateESIM() {
  const [activationCode, setActivationCode] = useState('')
  const [status, setStatus] = useState('')

  const handleActivation = async (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically send a request to your backend API to activate the eSIM
    setStatus('Activating...')
    await new Promise(resolve => setTimeout(resolve, 2000)) // Simulating API call
    setStatus('eSIM activated successfully!')
    setActivationCode('')
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold mb-8">Activate Your eSIM</h1>
      <form onSubmit={handleActivation} className="max-w-md mx-auto">
        <div className="mb-4">
          <Label htmlFor="activationCode">Activation Code</Label>
          <Input
            id="activationCode"
            value={activationCode}
            onChange={(e) => setActivationCode(e.target.value)}
            required
          />
        </div>
        <Button type="submit">Activate eSIM</Button>
      </form>
      {status && (
        <p className="mt-4 text-center font-semibold">{status}</p>
      )}
    </div>
  )
}

