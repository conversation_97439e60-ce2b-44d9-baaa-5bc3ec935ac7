"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Icons } from "@/components/icons"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination"
import { toast } from "sonner"
import { useOrders } from "./orders-context"
import { DateFormatter } from "@/lib/utils"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Checkbox } from "@/components/ui/checkbox"

const ITEMS_PER_PAGE = 20

const ORDER_STATUS_MAP = {
  PENDING: { label: "Pending", variant: "warning" as const },
  PROCESSING: { label: "Processing", variant: "secondary" as const },
  SHIPPED: { label: "Shipped", variant: "info" as const },
  DELIVERED: { label: "Delivered", variant: "success" as const },
  CANCELLED: { label: "Cancelled", variant: "destructive" as const },
  PAID: { label: "Paid", variant: "default" as const },
} as const

export default function OrdersPage() {
  const orders = useOrders()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [debouncedSearch, setDebouncedSearch] = useState("")
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  // 处理搜索防抖
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery)
      setCurrentPage(1) // 重置页码
    }, 500)
    return () => clearTimeout(timer)
  }, [searchQuery])

  // 过滤订单
  const filteredOrders = orders?.filter(order => {
    const searchLower = debouncedSearch.toLowerCase()
    const matchesSearch = debouncedSearch === "" ||
      order.id.toLowerCase().includes(searchLower) ||
      (order.user?.name || "").toLowerCase().includes(searchLower) ||
      (order.user?.email || "").toLowerCase().includes(searchLower) ||
      (order.shippingAddress?.fullName || "").toLowerCase().includes(searchLower)

    const matchesStatus = statusFilter === "all" || order.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const totalPages = Math.ceil((filteredOrders?.length || 0) / ITEMS_PER_PAGE)
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const paginatedOrders = filteredOrders?.slice(startIndex, startIndex + ITEMS_PER_PAGE)

  async function updateOrderStatus(orderId: string, status: string) {
    setIsLoading(orderId)

    try {
      const response = await fetch(`/api/admin/orders/${orderId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      })

      if (!response.ok) {
        throw new Error("Failed to update order status")
      }

      toast.success("Order status updated successfully")
      router.refresh()
    } catch (error) {
      console.error(error)
      toast.error("Something went wrong")
    } finally {
      setIsLoading(null)
    }
  }

  // 使用统一的日期格式化工具
  function formatDate(date: Date) {
    return DateFormatter.withTimezone(date);
  }

  // 计算统计信息
  const stats = {
    total: filteredOrders?.length || 0,
    status: Object.keys(ORDER_STATUS_MAP).reduce((acc, status) => {
      acc[status] = filteredOrders?.filter(order => order.status === status).length || 0
      return acc
    }, {} as Record<string, number>)
  }

  async function deleteSelectedOrders() {
    try {
      const deletePromises = selectedOrders.map(orderId =>
        fetch(`/api/admin/orders/${orderId}`, {
          method: "DELETE",
        }).then(response => {
          if (!response.ok) {
            throw new Error(`Failed to delete order ${orderId}`)
          }
          return response
        })
      )

      await Promise.all(deletePromises)
      toast.success("Selected orders have been deleted")
      // 强制刷新页面以重新获取数据
      window.location.reload()
      setSelectedOrders([])
      setShowDeleteDialog(false)
    } catch (error) {
      console.error(error)
      toast.error("Failed to delete selected orders")
    }
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Orders</h2>
        <p className="text-muted-foreground">
          Here&apos;s a list of all orders
        </p>
      </div>

      {/* 搜索和筛选区域 */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 gap-4">
          <div className="max-w-sm flex-1">
            <Input
              placeholder="Search by order ID, customer name or email..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
            />
          </div>
          <Select
            value={statusFilter}
            onValueChange={setStatusFilter}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent className="max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible">
              <SelectItem value="all">All Status</SelectItem>
              {Object.entries(ORDER_STATUS_MAP).map(([value, { label }]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {selectedOrders.length > 0 && (
            <Button
              variant="destructive"
              onClick={() => setShowDeleteDialog(true)}
            >
              <Icons.trash className="mr-2 h-4 w-4" />
              Delete Selected ({selectedOrders.length})
            </Button>
          )}
        </div>

        {/* 统计信息 */}
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>Total: {stats.total}</span>
          {Object.entries(stats.status).map(([status, count]) => count > 0 && (
            <span key={status}>
              {ORDER_STATUS_MAP[status as keyof typeof ORDER_STATUS_MAP]?.label}: {count}
            </span>
          ))}
        </div>
      </div>

      {!paginatedOrders || paginatedOrders.length === 0 ? (
        <Card className="p-12">
          <div className="flex flex-col items-center justify-center text-center">
            <Icons.package className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Orders Found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery || statusFilter !== "all"
                ? "No orders match your search criteria. Try adjusting your filters."
                : "There are no orders in the system yet. Orders will appear here when customers make purchases."}
            </p>
          </div>
        </Card>
      ) : (
        <>
          <div className="grid gap-4">
            <div className="flex items-center gap-2 px-6 py-2 bg-muted/50 rounded-md">
              <Checkbox
                checked={paginatedOrders.length > 0 && paginatedOrders.every(order => selectedOrders.includes(order.id))}
                onCheckedChange={(checked) => {
                  if (checked) {
                    // Add all current page orders that are not already selected
                    const newSelectedOrders = [...selectedOrders]
                    paginatedOrders.forEach(order => {
                      if (!newSelectedOrders.includes(order.id)) {
                        newSelectedOrders.push(order.id)
                      }
                    })
                    setSelectedOrders(newSelectedOrders)
                  } else {
                    // Remove all current page orders from selection
                    setSelectedOrders(prev => prev.filter(id => !paginatedOrders.find(order => order.id === id)))
                  }
                }}
              />
              <span className="text-sm font-medium">Select All</span>
            </div>
            {paginatedOrders.map((order) => (
              <Card key={order.id} className="p-6 hover:bg-accent/5 transition-colors">
                <div className="grid gap-6 md:grid-cols-5 items-center">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Checkbox
                        checked={selectedOrders.includes(order.id)}
                        onCheckedChange={(checked) => {
                          setSelectedOrders(prev =>
                            checked
                              ? [...prev, order.id]
                              : prev.filter(id => id !== order.id)
                          )
                        }}
                      />
                      <div>
                        <p className="text-sm font-medium">Order #{order.id.slice(0, 8)}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(order.createdAt)}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Customer</p>
                    <p className="text-sm font-medium">
                      {order.user?.name || order.shippingAddress?.fullName || "N/A"}
                      {order.user?.email && (
                        <span className="block text-xs text-muted-foreground">
                          {order.user.email}
                        </span>
                      )}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Items</p>
                    <p className="text-sm font-medium">
                      {order.items.length} items
                      <span className="block">${order.total.toFixed(2)}</span>
                    </p>
                  </div>
                  <div>
                    <Badge variant={ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP]?.variant || "secondary"}>
                      {ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP]?.label || order.status}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={isLoading === order.id}
                      onClick={() =>
                        updateOrderStatus(
                          order.id,
                          order.status === "PENDING" ? "PAID" : "PENDING"
                        )
                      }
                    >
                      {isLoading === order.id && (
                        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      {order.status === "PENDING" ? "Mark as Paid" : "Mark as Pending"}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push(`/admin/orders/${order.id}`)}
                    >
                      <Icons.edit className="h-4 w-4 mr-2" />
                      Details
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          {totalPages > 1 && (
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    onClick={(e) => {
                      e.preventDefault()
                      setCurrentPage(p => Math.max(1, p - 1))
                    }}
                  />
                </PaginationItem>

                {/* First page */}
                {totalPages > 5 && currentPage > 3 && (
                  <PaginationItem>
                    <PaginationLink
                      href="#"
                      onClick={(e) => {
                        e.preventDefault()
                        setCurrentPage(1)
                      }}
                    >
                      1
                    </PaginationLink>
                  </PaginationItem>
                )}

                {/* Ellipsis at the beginning */}
                {totalPages > 5 && currentPage > 3 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}

                {/* Page numbers */}
                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter(page => {
                    if (totalPages <= 5) {
                      // Show all pages if 5 or fewer
                      return true;
                    } else {
                      // Show pages around current page
                      if (currentPage <= 3) {
                        // Near the beginning
                        return page <= 5;
                      } else if (currentPage >= totalPages - 2) {
                        // Near the end
                        return page > totalPages - 5;
                      } else {
                        // In the middle
                        return Math.abs(page - currentPage) <= 1;
                      }
                    }
                  })
                  .map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault()
                          setCurrentPage(page)
                        }}
                        isActive={currentPage === page}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                {/* Ellipsis at the end */}
                {totalPages > 5 && currentPage < totalPages - 2 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}

                {/* Last page */}
                {totalPages > 5 && currentPage < totalPages - 2 && (
                  <PaginationItem>
                    <PaginationLink
                      href="#"
                      onClick={(e) => {
                        e.preventDefault()
                        setCurrentPage(totalPages)
                      }}
                    >
                      {totalPages}
                    </PaginationLink>
                  </PaginationItem>
                )}

                <PaginationItem>
                  <PaginationNext
                    href="#"
                    onClick={(e) => {
                      e.preventDefault()
                      setCurrentPage(p => Math.min(totalPages, p + 1))
                    }}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </>
      )}

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              You are about to delete {selectedOrders.length} orders.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                deleteSelectedOrders()
              }}
            >
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}