{"name": "mobile-api", "version": "0.0.1", "description": "<p align=\"center\">\r   <a href=\"http://nestjs.com/\" target=\"blank\"><img src=\"https://nestjs.com/img/logo-small.svg\" width=\"120\" alt=\"Nest Logo\" /></a>\r </p>", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "prisma generate && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "postinstall": "prisma generate", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "prisma:seed": "ts-node scripts/seed-real-data.ts"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.19", "@prisma/client": "4.16.2", "bcrypt": "^5.1.1", "class-transformer": "^0.5.0", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "ioredis": "^5.3.2", "multer": "^2.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@eslint/eslintrc": "^2.1.4", "@eslint/js": "^8.56.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@swc/cli": "^0.1.62", "@swc/core": "^1.3.64", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.1", "@types/multer": "^1.4.13", "@types/node": "20.2.5", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "cross-env": "^7.0.3", "eslint": "8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "globals": "^13.20.0", "jest": "^29.7.0", "prettier": "^2.8.8", "prisma": "4.16.2", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.0.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "main": "index.js", "directories": {"test": "test"}, "keywords": [], "resolutions": {"@prisma/client": "4.16.2", "prisma": "4.16.2"}}