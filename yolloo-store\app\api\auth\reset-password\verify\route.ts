import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// GET - Verify a password reset token
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const token = searchParams.get("token");
    
    if (!token) {
      return NextResponse.json(
        { valid: false, error: "Token is required" },
        { status: 400 }
      );
    }
    
    // Find the reset token
    const resetToken = await prisma.passwordResetToken.findFirst({
      where: { 
        token,
        expiresAt: { gt: new Date() }, // Token must not be expired
      },
      include: { user: true },
    });
    
    if (!resetToken) {
      return NextResponse.json(
        { valid: false, error: "Invalid or expired token" },
        { status: 200 } // Return 200 to avoid leaking information
      );
    }
    
    return NextResponse.json({
      valid: true,
      email: resetToken.user.email,
    });
  } catch (error) {
    console.error("Error verifying reset token:", error);
    return NextResponse.json(
      { valid: false, error: "Failed to verify token" },
      { status: 500 }
    );
  }
} 