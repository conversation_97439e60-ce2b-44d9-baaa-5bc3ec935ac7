const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  const password = "admin123"
  const hashedPassword = await bcrypt.hash(password, 12)

  const admin = await prisma.user.upsert({
    where: {
      email: "<EMAIL>",
    },
    update: {
      hashedPassword,
      role: "ADMIN",
    },
    create: {
      email: "<EMAIL>",
      name: "admin",
      hashedPassword,
      role: "ADMIN",
    },
  })

  console.log("Admin user created:", admin)
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 