import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function POST(req: NextRequest) {
  try {
    const { referralCode, source, path, userAgent, referrer } = await req.json();
    
    if (!referralCode) {
      return NextResponse.json({ error: "Referral code is required" }, { status: 400 });
    }
    
    // 获取推广员信息
    const affiliate = await prisma.affiliateProfile.findUnique({
      where: { code: referralCode },
    });
    
    if (!affiliate) {
      return NextResponse.json({ error: "Affiliate not found" }, { status: 404 });
    }
    
    // 创建访问记录
    const visit = await prisma.affiliateVisit.create({
      data: {
        affiliateId: affiliate.id,
        organizationId: affiliate.organizationId || undefined,
        source: source || "direct",
        path: path || "/",
        userAgent: userAgent || "",
        referrer: referrer || "",
        convertedToOrder: false,
      },
    });
    
    return NextResponse.json({ success: true, visit });
  } catch (error) {
    console.error("[AFFILIATE_VISIT]", error);
    return NextResponse.json({ error: "Failed to record visit" }, { status: 500 });
  }
} 