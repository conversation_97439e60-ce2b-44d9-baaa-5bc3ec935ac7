import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { DateUtils } from "@/lib/utils";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for updating an organization
const updateOrgSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters").optional(),
  description: z.string().optional(),
  logo: z.string().optional(),
  commissionRate: z.number().min(0).max(1).optional(),
  discountRate: z.number().min(0).max(1).optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED"]).optional(),
});

// GET - 获取单个组织详细信息
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 检查用户是否为管理员
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    // 获取组织信息
    const organization = await prisma.affiliateOrganization.findUnique({
      where: {
        id: params.id
      },
      include: {
        _count: {
          select: {
            members: true,
            visits: true,
            commissions: true,
            withdrawals: true,
          }
        }
      }
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // 获取额外的统计信息
    const stats = await getOrganizationStats(params.id);

    // 不同步获取月度收益数据，改为在analytics接口中获取
    // 返回组织信息和额外的统计数据
    return NextResponse.json({
      ...organization,
      additionalStats: stats
    });

  } catch (error) {
    console.error("Error fetching organization:", error);
    return NextResponse.json({ error: "Failed to fetch organization" }, { status: 500 });
  }
}

// PATCH - Update organization (admin only)
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const organizationId = params.id;

    const body = await req.json();

    // Validate input
    const validationResult = updateOrgSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }

    // Update organization
    const updatedOrganization = await prisma.affiliateOrganization.update({
      where: { id: organizationId },
      data: validationResult.data,
    });

    return NextResponse.json(updatedOrganization);
  } catch (error) {
    console.error("Error updating organization:", error);
    return NextResponse.json(
      { error: "Failed to update organization" },
      { status: 500 }
    );
  }
}

// DELETE - Delete organization (admin only)
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const organizationId = params.id;

    // First update all members to remove organization association
    await prisma.affiliateProfile.updateMany({
      where: { organizationId },
      data: {
        organizationId: null,
        isAdmin: false,
      },
    });

    // Delete all invites for this organization
    await prisma.organizationInvite.deleteMany({
      where: { organizationId },
    });

    // Delete the organization
    await prisma.affiliateOrganization.delete({
      where: { id: organizationId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting organization:", error);
    return NextResponse.json(
      { error: "Failed to delete organization" },
      { status: 500 }
    );
  }
}

// 获取组织统计信息的辅助函数
async function getOrganizationStats(organizationId: string) {
  // 获取组织佣金总额
  const orgCommissions = await prisma.organizationCommission.aggregate({
    where: {
      organizationId: organizationId,
      status: { in: ["APPROVED", "PAID"] }
    },
    _sum: {
      commissionAmount: true
    }
  });

  // 获取成员佣金总额
  const memberCommissions = await prisma.affiliateReferral.aggregate({
    where: {
      affiliate: {
        organizationId: organizationId
      },
      status: { in: ["APPROVED", "PAID"] }
    },
    _sum: {
      commissionAmount: true
    }
  });

  const totalCommissions = orgCommissions._sum.commissionAmount || 0;
  const totalMemberCommissions = memberCommissions._sum.commissionAmount || 0;

  // 组织实际收益 = 总佣金 - 成员佣金
  const organizationActualEarnings = totalCommissions - totalMemberCommissions;

  return {
    totalCommissions,
    memberCommissions: totalMemberCommissions,
    organizationActualEarnings
  };
}

// 获取月度收益数据的辅助函数
async function calculateMonthlyEarnings(organizationId: string) {
  // Get current date and set to beginning of month
  const currentDate = new Date();
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const monthsData = [];

  // Calculate data for the last 6 months
  for (let i = 0; i < 6; i++) {
    // Create date for this month (0-5 months ago)
    const targetDate = new Date();
    targetDate.setMonth(currentDate.getMonth() - i);

    const startOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);
    const endOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0, 23, 59, 59);

    // Get approved organization commissions for this month
    const monthlyCommissions = await prisma.organizationCommission.aggregate({
      where: {
        organizationId,
        status: { in: ["APPROVED", "PAID"] },
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      },
      _sum: {
        commissionAmount: true
      }
    });

    // Get approved member commissions for this month
    const memberMonthlyCommissions = await prisma.affiliateReferral.aggregate({
      where: {
        affiliate: {
          organizationId
        },
        status: { in: ["APPROVED", "PAID"] },
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      },
      _sum: {
        commissionAmount: true
      }
    });

    const totalCommissions = monthlyCommissions._sum.commissionAmount || 0;
    const memberCommissions = memberMonthlyCommissions._sum.commissionAmount || 0;

    // Calculate organization's actual earnings for the month
    const actualEarnings = totalCommissions - memberCommissions;

    // Add month data
    monthsData.push({
      month: months[targetDate.getMonth()],
      year: targetDate.getFullYear(),
      amount: actualEarnings,
      totalCommissions,
      memberCommissions
    });
  }

  // Sort from oldest to newest
  return monthsData.reverse();
}