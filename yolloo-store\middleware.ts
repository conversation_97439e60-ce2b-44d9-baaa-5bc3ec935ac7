import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

// 定义公开路由
const publicRoutes = [
  '/',
  '/landing',
  '/auth/signin',
  '/auth/signup',
  '/auth/reset-password',
  '/auth/forgot-password',
  '/cart',
  '/presale',
  '/about',
  '/contact',
  '/privacy',
  '/terms',
  '/help',
  '/faq',
  '/how-it-works'
]

// 定义公开API路由
const publicApiRoutes = [
  '/api/payments/webhook'
]

// 检查路径是否是公开路由
function isPublicPath(pathname: string): boolean {
  // 检查是否是公开API路由（精确匹配）
  if (publicApiRoutes.includes(pathname)) return true;

  return publicRoutes.some(route => {
    // 精确匹配
    if (route === pathname) return true;
    // 通配符匹配（例如 /product/*）
    if (route.endsWith('/*') && pathname.startsWith(route.slice(0, -2))) return true;
    return false;
  });
}

// 合并中间件功能
export default withAuth(
    function middleware(req) {
        // 调试信息
        console.log(`Middleware processing: ${req.nextUrl.pathname}`);
        console.log(`Auth token exists: ${!!req.nextauth.token}`);

        // 处理推广链接
        const { searchParams } = req.nextUrl;
        const ref = searchParams.get('ref');
        const response = NextResponse.next();

        // 为所有请求设置缓存控制头，确保动态渲染
        response.headers.set('Cache-Control', 'no-store, max-age=0');
        response.headers.set('x-middleware-cache', 'no-cache');
        response.headers.set('x-dynamic-rendering', 'true');

        // 为API请求设置额外的缓存控制头
        if (req.nextUrl.pathname.startsWith('/api/')) {
            response.headers.set('Pragma', 'no-cache');
        }

        // 如果有推广码，保存到 cookie 中
        if (ref) {
            // 设置 cookie，有效期 30 天
            response.cookies.set('referralCode', ref, {
                maxAge: 60 * 60 * 24 * 30, // 30 days
                path: '/',
                httpOnly: true,
                sameSite: 'lax'
            });
        }

        // 只有已登录用户访问登录/注册页面时才重定向
        if (
            (req.nextUrl.pathname.startsWith("/auth/signin") ||
             req.nextUrl.pathname.startsWith("/auth/signup")) &&
            req.nextauth.token  // 确保用户已登录
        ) {
            return NextResponse.redirect(new URL("/account", req.url));
        }

        // 检查是否是公开路由
        const publicPath = isPublicPath(req.nextUrl.pathname);

        // 如果不是公开路由且用户未登录，重定向到登录页面
        if (!publicPath && !req.nextauth.token) {
            // 如果是API请求，应该返回JSON格式的401错误，而不是重定向
            if (req.nextUrl.pathname.startsWith('/api/')) {
                return new NextResponse(
                    JSON.stringify({
                        success: false,
                        message: "Unauthorized"
                    }),
                    {
                        status: 401,
                        headers: { 'Content-Type': 'application/json' }
                    }
                );
            }

            const callbackUrl = req.nextUrl.pathname + req.nextUrl.search;
            // 确保callbackUrl正确编码，防止特殊字符问题
            const encodedCallbackUrl = encodeURIComponent(callbackUrl);
            return NextResponse.redirect(
                new URL(`/auth/signin?callbackUrl=${encodedCallbackUrl}`, req.url)
            );
        }

        return response;
    },
    {
        callbacks: {
            authorized: ({ token, req }) => {
                // 检查是否是公开路由
                const publicPath = isPublicPath(req.nextUrl.pathname);

                // 公开路由不需要认证，其他路由需要认证
                return publicPath || !!token;
            },
        },
    }
);

// 修改匹配规则，匹配所有路由但排除 webhook
export const config = {
    matcher: [
        '/((?!api|_next/static|_next/image|favicon.ico).*)',
        // 匹配API路由但排除webhook
        '/api/:path*',
        // 排除webhook路径
        {
            source: '/api/payments/webhook',
            missing: [
                { type: 'header', key: 'Stripe-Signature' }
            ]
        }
    ],
};