"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import axios from "axios";
import { <PERSON>evron<PERSON>eft, <PERSON>ader2, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Refresh<PERSON>w } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";

interface AnalyticsData {
  totalMembers: number;
  totalReferrals: number;
  totalEarnings: number;
  monthlyEarnings: {
    month: string;
    year: number;
    amount: number;
    totalCommissions: number;
    memberCommissions: number;
  }[];
  topPerformers: {
    id: string;
    user: {
      id: string;
      name: string;
      image?: string;
    };
    referrals: number;
    earnings: number;
  }[];
}

interface Stats {
  totalVisits: number;
  totalConversions: number;
  conversionRate: number;
  totalCommissions: number;
  memberCommissions: number;
  organizationActualEarnings: number;
}

export default function AnalyticsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [period, setPeriod] = useState("30days");
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [stats, setStats] = useState<Stats | null>(null);
  const [memberOrders, setMemberOrders] = useState<any[]>([]);
  const [organization, setOrganization] = useState<any>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        
        // 首先检查用户是否为管理员
        const orgResponse = await axios.get(`/api/affiliate/organizations/${params.id}`);
        setIsAdmin(orgResponse.data.isAdmin || false);
        
        // 如果用户不是管理员，立即重定向并返回
        if (!orgResponse.data.isAdmin) {
          toast.error("您没有权限查看分析数据");
          router.push(`/affiliate/organization/${params.id}`);
          return;
        }
        
        // 只有管理员才能获取分析数据
        const response = await axios.get(`/api/affiliate/organizations/${params.id}/analytics?period=${period}`);
        
        if (response.data.analytics) {
          setData(response.data.analytics);
        }
        
        // 设置管理后台风格的数据
        if (response.data.stats) {
          setStats(response.data.stats);
        }
        
        // 设置组织信息
        if (response.data.organization) {
          setOrganization(response.data.organization);
        }
        
        // 设置成员订单数据
        if (response.data.memberOrders) {
          setMemberOrders(response.data.memberOrders);
        }
        
        // 如果需要刷新数据，自动调用刷新
        if (response.data.needsRefresh) {
          refreshStats();
        }
      } catch (error) {
        console.error("Error fetching analytics data:", error);
        toast.error("获取分析数据失败");
        router.push(`/affiliate/organization/${params.id}`);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [params.id, period, router]);

  // 刷新统计数据
  const refreshStats = async () => {
    try {
      setIsRefreshing(true);
      const response = await axios.post(`/api/affiliate/organizations/${params.id}/refresh-stats`);
      
      if (response.data.success) {
        toast.success("Statistics refreshed successfully");
        
        // 重新获取分析数据
        const analyticsResponse = await axios.get(`/api/affiliate/organizations/${params.id}/analytics?period=${period}`);
        
        if (analyticsResponse.data.analytics) {
          setData(analyticsResponse.data.analytics);
        }
        
        if (analyticsResponse.data.stats) {
          setStats(analyticsResponse.data.stats);
        }
        
        if (analyticsResponse.data.memberOrders) {
          setMemberOrders(analyticsResponse.data.memberOrders);
        }
      } else {
        toast.error(response.data.error || "Failed to refresh stats");
      }
    } catch (error) {
      console.error("Error refreshing stats:", error);
      toast.error("Failed to refresh statistics");
    } finally {
      setIsRefreshing(false);
    }
  };

  const handlePeriodChange = (value: string) => {
    setPeriod(value);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!data || !stats) {
    return (
      <div className="container mx-auto py-8">
        <div className="mb-6">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/affiliate/organization/${params.id}`}>
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Organization
            </Link>
          </Button>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Analytics</CardTitle>
            <CardDescription>No data available</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              There is no analytics data available for this period. Try changing the time period or check back later.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6 flex items-center justify-between">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/affiliate/organization/${params.id}`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Organization
          </Link>
        </Button>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Time period:</span>
          <Select onValueChange={handlePeriodChange} value={period}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="year">Last year</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Organization Analytics Card */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Organization Analytics</CardTitle>
              <CardDescription>
                View performance metrics for this organization
              </CardDescription>
            </div>
            <Button 
              onClick={refreshStats} 
              size="sm" 
              disabled={isRefreshing}
              variant="outline"
            >
              {isRefreshing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Refreshing...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh Stats
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Traffic</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 rounded-lg border">
                    <div className="text-sm text-muted-foreground">Total Visits</div>
                    <div className="text-2xl font-bold">{stats.totalVisits}</div>
                  </div>
                  <div className="p-4 rounded-lg border">
                    <div className="text-sm text-muted-foreground">Conversions</div>
                    <div className="text-2xl font-bold">{stats.totalConversions}</div>
                  </div>
                </div>
              </div>
                    
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Performance</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 rounded-lg border">
                    <div className="text-sm text-muted-foreground">Conversion Rate</div>
                    <div className="text-2xl font-bold">{stats.conversionRate?.toFixed(2) || '0.00'}%</div>
                  </div>
                  <div className="p-4 rounded-lg border">
                    <div className="text-sm text-muted-foreground">Total Orders</div>
                    <div className="text-2xl font-bold">{stats.totalConversions}</div>
                  </div>
                </div>
              </div>
            </div>
                  
            {organization && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Organization Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 rounded-lg border">
                    <div className="text-sm text-muted-foreground">Commission Rate</div>
                    <div className="text-xl font-bold">{(organization.commissionRate * 100)?.toFixed(0) || '0'}%</div>
                  </div>
                  <div className="p-4 rounded-lg border">
                    <div className="text-sm text-muted-foreground">Discount Rate</div>
                    <div className="text-xl font-bold">{(organization.discountRate * 100)?.toFixed(0) || '0'}%</div>
                  </div>
                  <div className="p-4 rounded-lg border">
                    <div className="text-sm text-muted-foreground">Affiliate Code</div>
                    <div className="text-xl font-bold font-mono">{organization.code}</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Commission Distribution Card */}
      <div className="space-y-2 mb-8">
        <h3 className="text-sm font-medium text-muted-foreground">Commission Distribution</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 rounded-lg border">
            <div className="text-sm text-muted-foreground">Organization Gross Earnings</div>
            <div className="text-2xl font-bold">${stats.totalCommissions?.toFixed(2) || '0.00'}</div>
            <div className="text-xs text-muted-foreground mt-1">Total commission generated</div>
          </div>
          <div className="p-4 rounded-lg border">
            <div className="text-sm text-muted-foreground">Member Commissions</div>
            <div className="text-2xl font-bold">${stats.memberCommissions?.toFixed(2) || '0.00'}</div>
            <div className="text-xs text-muted-foreground mt-1">Distributed to members</div>
          </div>
          <div className="p-4 rounded-lg border bg-green-50/40">
            <div className="text-sm text-muted-foreground">Organization Net Earnings</div>
            <div className="text-2xl font-bold">${stats.organizationActualEarnings?.toFixed(2) || '0.00'}</div>
            <div className="text-xs text-muted-foreground mt-1">Actual organization profit</div>
          </div>
        </div>
      </div>

      {/* Overall Stats */}
      <div className="grid gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Performance Overview</CardTitle>
            <CardDescription>
              Summary of organization performance for the selected period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-primary/10 rounded-full">
                  <User className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Members</p>
                  <h3 className="text-2xl font-bold">{data.totalMembers}</h3>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-primary/10 rounded-full">
                  <BarChart className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Referrals</p>
                  <h3 className="text-2xl font-bold">{data.totalReferrals}</h3>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-primary/10 rounded-full">
                  <BarChart className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Earnings</p>
                  <h3 className="text-2xl font-bold">${data.totalEarnings.toFixed(2)}</h3>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Earnings */}
      <div className="grid gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Monthly Earnings</CardTitle>
            <CardDescription>
              Revenue trends over the last 6 months
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative">
              {/* Chart would go here - we're showing a simple table instead for now */}
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mt-2">
                {data.monthlyEarnings.map((month, index) => (
                  <Card key={index} className="p-2">
                    <CardContent className="p-2">
                      <div className="flex justify-between items-center mb-1">
                        <p className="text-sm font-medium">
                          {month.month} {month.year}
                        </p>
                      </div>
                      <p className="text-xl font-bold text-green-600">
                        ${month.amount.toFixed(2)}
                      </p>
                      <div className="mt-2 space-y-1">
                        <div className="flex justify-between items-center text-xs">
                          <span className="text-muted-foreground">Total:</span>
                          <span className="font-medium">${month.totalCommissions.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between items-center text-xs">
                          <span className="text-muted-foreground">Members:</span>
                          <span className="font-medium">${month.memberCommissions.toFixed(2)}</span>
                        </div>
                      </div>
                      {month.amount > 0 && (
                        <div className="w-full bg-green-100 rounded-full h-1.5 mt-2">
                          <div 
                            className="bg-green-600 h-1.5 rounded-full" 
                            style={{ 
                              width: `${Math.min(100, (month.amount / month.totalCommissions) * 100)}%` 
                            }}
                          ></div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      <div className="grid gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Members</CardTitle>
            <CardDescription>
              Members with the highest referrals and earnings
            </CardDescription>
          </CardHeader>
          <CardContent>
            {data.topPerformers.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                No performer data available for this period.
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {data.topPerformers.map((performer, index) => (
                  <Card key={performer.id} className="overflow-hidden">
                    <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-4">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10 border-2 border-background">
                          <AvatarImage src={performer.user.image} alt={performer.user.name} />
                          <AvatarFallback>{performer.user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{performer.user.name}</p>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <BarChart className="h-3 w-3 mr-1" />
                            <span>Top {index + 1} Performer</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <CardContent className="p-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground">Referrals</h4>
                          <p className="text-xl font-bold">{performer.referrals}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground">Earnings</h4>
                          <p className="text-xl font-bold">${performer.earnings.toFixed(2)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Member Orders */}
      <div className="mt-8">
        <h3 className="text-sm font-medium text-muted-foreground mb-4">Member Orders</h3>
        {memberOrders.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed">
            <p className="text-sm text-muted-foreground">No orders found for this organization</p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Member</TableHead>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Order Amount</TableHead>
                  <TableHead>Member Commission</TableHead>
                  <TableHead>Organization Commission</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {memberOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-7 w-7">
                          <AvatarImage src={order.affiliate?.user?.image} alt={order.affiliate?.user?.name} />
                          <AvatarFallback>{order.affiliate?.user?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="text-sm font-medium">{order.affiliate?.user?.name}</div>
                          <div className="text-xs text-muted-foreground">{order.affiliate?.code}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-xs">{order.orderId.substring(0, 8)}...</TableCell>
                    <TableCell>${order.order?.total?.toFixed(2) || '0.00'}</TableCell>
                    <TableCell>${order.commissionAmount?.toFixed(2) || '0.00'}</TableCell>
                    <TableCell>
                      {order.organizationCommission?.commissionAmount 
                        ? `$${(order.organizationCommission.commissionAmount - order.commissionAmount).toFixed(2)}`
                        : '-'}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={
                        order.status === "APPROVED" 
                          ? "bg-green-50 text-green-700 border-green-200"
                          : order.status === "PENDING"
                          ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                          : order.status === "PAID"
                          ? "bg-blue-50 text-blue-700 border-blue-200"
                          : "bg-red-50 text-red-700 border-red-200"
                      }>
                        {order.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{format(new Date(order.createdAt), "MMM d, yyyy")}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
    </div>
  );
} 