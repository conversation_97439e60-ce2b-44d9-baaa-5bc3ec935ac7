"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { toast } from "sonner"

interface CompletePaymentButtonProps {
  orderId: string
}

export function CompletePaymentButton({ orderId }: CompletePaymentButtonProps) {
  const [isLoading, setIsLoading] = useState(false)

  async function handlePayment() {
    setIsLoading(true)

    try {
      const response = await fetch("/api/payments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ orderId }),
      })

      if (!response.ok) {
        throw new Error("Failed to create payment session")
      }

      const data = await response.json()
      
      // 重定向到 Stripe 支付页面
      window.location.href = data.url
    } catch (error) {
      toast.error("Something went wrong. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button 
      className="w-full" 
      size="lg" 
      onClick={handlePayment}
      disabled={isLoading}
    >
      {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
      Complete Payment
    </Button>
  )
} 