# Boss 服务使用指南

本文档提供了如何在应用程序中使用 Boss 服务的详细说明。

## 1. 服务概述

Boss 服务提供了设备订单与套餐管理的功能，包括：

- 套餐分页查询
- 创建套餐订单
- 创建 RPM 订单
- 取消套餐（退款）
- 提前停用套餐
- 套餐用量查询
- 套餐详情查询
- 套餐置顶切换

## 2. 直接使用 Boss 服务

### 导入服务

```typescript
import { bossService } from '@/lib/boss';
```

### 使用示例

#### 2.1 查询套餐分页数据

```typescript
const response = await bossService.getDeviceOrderPage({
  pageNum: 0,
  pageSize: 10,
  queryParam: {
    uid: 'user123',
    // 其他可选过滤条件
  }
});
```

#### 2.2 创建套餐订单

```typescript
const response = await bossService.createProductOrder({
  customerOrderSn: 'ORDER123',
  productSkuId: 'SKU456',
  uid: 'user123'
});
```

#### 2.3 创建 RPM 订单

```typescript
const response = await bossService.createRpmOrder({
  customerOrderSn: 'ORDER123',
  lpaString: 'lpa-config-string',
  uid: 'user123',
  alias: '我的eSIM'
});
```

#### 2.4 取消套餐（退款）

```typescript
const response = await bossService.cancelOrderPlan('ORDER123');
```

#### 2.5 提前停用套餐

```typescript
const response = await bossService.closeOrderPlan('ORDER123');
```

#### 2.6 套餐用量查询

```typescript
const response = await bossService.queryUsageOrderPlan('ORDER123');
```

#### 2.7 套餐详情查询

```typescript
const response = await bossService.queryOrderPlan('ORDER123');
```

#### 2.8 套餐置顶切换

```typescript
const response = await bossService.toppingOrderPlan('ORDER123', 'user123');
```

## 3. 通过 API 使用 Boss 服务

### 3.1 获取订单分页数据

**GET /api/boss/orders**

查询参数:
- `pageNum`: 页码（从0开始）
- `pageSize`: 每页数量
- `uid`: 用户唯一标识（可选）
- `orderSn`: 订单号（可选）
- `packageStatus`: 套餐状态（可选）
- `esimProfileStatus`: eSIM配置文件状态（可选）

示例:
```typescript
const response = await fetch('/api/boss/orders?pageNum=0&pageSize=10&uid=user123');
const data = await response.json();
```

### 3.2 创建订单或执行订单操作

**POST /api/boss/orders**

请求体:
```typescript
{
  "action": "createProductOrder", // 操作类型
  "customerOrderSn": "ORDER123",  // 参数根据操作类型不同而不同
  "productSkuId": "SKU456",
  "uid": "user123"
}
```

支持的 action 类型:
- `createProductOrder`: 创建套餐订单
- `createRpmOrder`: 创建 RPM 订单
- `cancelOrderPlan`: 取消套餐
- `closeOrderPlan`: 提前停用套餐
- `queryUsageOrderPlan`: 套餐用量查询
- `queryOrderPlan`: 套餐详情查询
- `toppingOrderPlan`: 套餐置顶切换

示例:
```typescript
const response = await fetch('/api/boss/orders', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    action: 'createProductOrder',
    customerOrderSn: 'ORDER123',
    productSkuId: 'SKU456',
    uid: 'user123'
  })
});
const data = await response.json();
```

## 4. 响应格式

所有 Boss 服务的响应都遵循以下格式:

```typescript
{
  "data": {
    // 响应数据，根据接口不同而不同
  },
  "resultCode": "200", // 响应状态码
  "resultMsg": "Success" // 响应描述
}
```

## 5. 错误处理

当发生错误时，服务将返回以下格式的响应:

```typescript
{
  "data": null,
  "resultCode": "错误代码",
  "resultMsg": "错误描述"
}
```

常见错误代码:
- `400`: 请求参数错误
- `401`: 身份验证失败
- `403`: 无操作权限
- `404`: 资源不存在
- `500`: 服务器内部错误
