import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";
import { createOdooService } from "@/app/services/odooService";
import { ODOO_CONFIG } from "@/app/config/odoo";
import { OdooOrder } from "@/app/types/odoo";
import { cookies } from "next/headers";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


const odooService = createOdooService(ODOO_CONFIG);

/**
 * 无需支付直接添加第三方eSIM
 * 参考mobile版本实现，设置订单为已支付状态
 */
export async function POST(request: Request) {
  try {
    // 验证用户登录状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // 从cookies中获取推广码
    const cookieStore = cookies();
    const referralCode = cookieStore.get('referralCode')?.value;
    console.log(`[EXTERNAL_ESIM_ACTIVATION] Referral code from cookie: ${referralCode || 'none'}`);

    // 获取请求数据
    const json = await request.json();
    const { cardNumber, lpaString } = json;

    if (!cardNumber || !lpaString) {
      return new NextResponse("Missing required fields", { status: 400 });
    }

    // 验证卡属于当前用户
    const card = await prisma.yollooCard.findFirst({
      where: {
        number: cardNumber,
        userId: session.user.id
      }
    });

    if (!card) {
      return new NextResponse("Card not found or unauthorized", { status: 404 });
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    });

    if (!user || !user.email) {
      return new NextResponse("User not found", { status: 404 });
    }

    // 获取external-data商品信息
    const externalProduct = await prisma.product.findFirst({
      where: {
        category: {
          name: "external_data"
        },
        status: "ACTIVE",
        off_shelve: false
      },
      include: {
        variants: {
          take: 1,
          select: {
            id: true,
            variantCode: true,
            price: true,
            currency: true,
            duration: true,
            durationType: true
          }
        }
      }
    });

    if (!externalProduct) {
      return new NextResponse("External data product not found", { status: 404 });
    }

    const variant = externalProduct.variants[0];
    const price = variant?.price ?? externalProduct.price;
    const priceAsNumber = Number(price);

    // 获取系统默认地址
    let address = await prisma.address.findFirst({
        where: {
          userId: null,
          name: 'System Default Address',
        },
    });

    // 获取odooProductCode
    const specifications = externalProduct.specifications as { odooProductCode?: string };
    const odooProductCode = specifications.odooProductCode;

    // 创建本地订单（直接设置为已支付）
    let variantText = externalProduct.name;
    if (variant?.duration && variant?.durationType) {
      variantText += ` ${variant.duration} ${variant.durationType}`;
    }

    const order = await prisma.order.create({
      data: {
        userId: session.user.id,
        addressId: address.id,
        status: "PAID", // 直接设置为已支付状态
        total: priceAsNumber,
        ...(referralCode && { referralCode }), // 如果有推广码，添加到订单
        shippingAddressSnapshot: {
          name: address.name,
          phone: address.phone,
          address1: address.address1,
          address2: address.address2,
          city: address.city,
          state: address.state,
          postalCode: address.postalCode,
          country: address.country,
        },
        items: {
          create: {
            productCode: externalProduct.sku,
            variantCode: variant?.variantCode,
            variantText: variantText,
            quantity: 1,
            price: priceAsNumber,
            uid: cardNumber,
            lpaString: lpaString
          }
        }
      },
      include: {
        items: true
      }
    });

    // 创建Odoo状态记录
    // 确保UID是数字字符串格式
    const formattedUid = cardNumber ? cardNumber.replace(/[^0-9,]/g, '') : null;

    // 使用与订单项相同的variantCode，确保查询时能正确匹配
    const orderItemVariantCode = variant?.variantCode || "default";

    await prisma.odooOrderStatus.create({
      data: {
        orderId: order.id,
        variantCode: orderItemVariantCode,
        status: "processing",
        description: "External eSIM activation initiated",
        isDigital: true,
        deliveredQty: 0,
        uid: formattedUid,
        lastCheckedAt: new Date(),
      }
    });

    // 处理推广佣金 - 如果有推广码
    if (referralCode) {
      console.log(`Processing affiliate commission for order: ${order.id}, referral code: ${referralCode}`);
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/affiliate/track`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            orderId: order.id,
            referralCode: referralCode
          }),
        });

        if (response.ok) {
          const result = await response.json();
          console.log(`Commission processing result: ${JSON.stringify(result)}`);
        } else {
          console.error(`Failed to process commission: ${response.statusText}`);
        }
      } catch (error) {
        console.error('Error processing commission:', error);
        // 佣金处理失败不应阻止订单处理
      }
    }

    // 创建Odoo订单
    const odooOrder: OdooOrder = {
      customer_order_ref: order.id,
      shipping_address: {
        name: address.name,
        country: address.country,
        city: address.city,
        zip: address.postalCode,
        address: `${address.address1} ${address.address2 || ''}`.trim(),
        phone: address.phone
      },
      payment_method: "prepaid",
      email: user.email,
      order_lines: [{
        customer_order_line_ref: order.items[0]?.id || `web-order-${order.id}`,
        product_code: (variant && variant.variantCode) || odooProductCode || "",
        product_uom_qty: 1,
        card_uid: cardNumber,
        lpa_string: lpaString
      }]
    };

    try {
      const odooResponse = await odooService.createOrder(odooOrder);
      console.log("[EXTERNAL_ESIM_ACTIVATION] Odoo create order response:", JSON.stringify(odooResponse, null, 2));

      // 获取 Odoo 响应中的状态和消息
      const responseStatus = odooResponse.result?.status || "unknown";
      const responseMessage = odooResponse.result?.message || "No response message";

      // 设置 OdooOrderStatus 的状态 - Odoo返回"ok"表示成功
      const isSuccess = responseStatus === "ok" || responseStatus === "success";
      const odooStatus = isSuccess ? "processing" : "error";

      // 设置 OdooOrderStatus 的描述
      const odooDescription = isSuccess
        ? `Successful: ${responseMessage}`
        : `Failed: ${responseMessage}`;

      // 更新 OdooOrderStatus 记录，将 Odoo 响应信息存储到数据库中
      // 使用与订单项相同的variantCode，确保查询时能正确匹配
      const orderItemVariantCode = variant?.variantCode || "default";

      // 确保UID是数字字符串格式
      const formattedUid = cardNumber ? cardNumber.replace(/[^0-9,]/g, '') : null;

      // 查找是否已存在相同 orderId, variantCode, uid 的记录
      const existingStatus = await prisma.odooOrderStatus.findFirst({
        where: {
          orderId: order.id,
          variantCode: orderItemVariantCode,
          uid: formattedUid
        }
      });

      if (existingStatus) {
        // 更新现有记录
        await prisma.odooOrderStatus.update({
          where: { id: existingStatus.id },
          data: {
            status: odooStatus,
            description: odooDescription,
            lastCheckedAt: new Date(),
          }
        });
      } else {
        // 创建新记录
        await prisma.odooOrderStatus.create({
          data: {
            orderId: order.id,
            variantCode: orderItemVariantCode,
            uid: formattedUid,
            status: odooStatus,
            description: odooDescription,
            lastCheckedAt: new Date(),
          }
        });
      }

      // 如果 Odoo 返回 error 状态，将订单状态从 PAID 更新为 PROCESSING
      if (!isSuccess) {
        console.log(`[EXTERNAL_ESIM_ACTIVATION] Odoo returned error status, updating order ${order.id} status from PAID to PROCESSING`);
        await prisma.order.update({
          where: { id: order.id },
          data: { status: "PROCESSING" },
        });
      }

      return NextResponse.json({
        success: true,
        orderId: order.id,
        message: "eSIM activation initiated successfully",
        status: odooResponse.result.status,
        data: {
          cardNumber: cardNumber,
          lpaString: lpaString
        }
      });
    } catch (odooError) {
      console.error("[EXTERNAL_ESIM_ACTIVATION] Odoo API error:", odooError);

      // 记录错误到 OdooOrderStatus 表
      try {
        // 使用与订单项相同的variantCode，确保查询时能正确匹配
        const orderItemVariantCode = variant?.variantCode || "default";

        // 确保UID是数字字符串格式
        const formattedUid = cardNumber ? cardNumber.replace(/[^0-9,]/g, '') : null;

        // 查找是否已存在相同 orderId, variantCode, uid 的记录
        const existingStatus = await prisma.odooOrderStatus.findFirst({
          where: {
            orderId: order.id,
            variantCode: orderItemVariantCode,
            uid: formattedUid
          }
        });

        const errorDescription = `Error creating Odoo order: ${odooError instanceof Error ? odooError.message : 'Unknown error'}`;

        if (existingStatus) {
          // 更新现有记录
          await prisma.odooOrderStatus.update({
            where: { id: existingStatus.id },
            data: {
              status: "error",
              description: errorDescription,
              lastCheckedAt: new Date(),
            }
          });
        } else {
          // 创建新记录
          await prisma.odooOrderStatus.create({
            data: {
              orderId: order.id,
              variantCode: orderItemVariantCode,
              uid: formattedUid,
              status: "error",
              description: errorDescription,
              lastCheckedAt: new Date(),
            }
          });
        }

        // 将订单状态从 PAID 更新为 PROCESSING
        console.log(`[EXTERNAL_ESIM_ACTIVATION] Odoo API call failed, updating order ${order.id} status from PAID to PROCESSING`);
        await prisma.order.update({
          where: { id: order.id },
          data: { status: "PROCESSING" },
        });
      } catch (dbError) {
        console.error("[EXTERNAL_ESIM_ACTIVATION] Failed to record Odoo error in database:", dbError);
      }

      // 尽管Odoo API调用失败，我们仍然返回成功响应，因为本地订单已创建成功
      return NextResponse.json({
        success: true,
        orderId: order.id,
        message: "eSIM activation initiated successfully, but Odoo synchronization failed",
        status: "error",
        data: {
          cardNumber: cardNumber,
          lpaString: lpaString
        }
      });
    }
  } catch (error) {
    console.error("[EXTERNAL_ESIM_ACTIVATION]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}