"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useSession } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { formatPrice, formatUid } from "@/lib/utils"
import { useCart } from "@/lib/hooks/use-cart"
import { CheckoutForm } from "@/components/checkout-form"
import { Address } from "@prisma/client"
import { Icons } from "@/components/icons"
import { useCallback } from "react"

export default function CheckoutPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session, status } = useSession()
  const { items: cartItems } = useCart()
  const [addresses, setAddresses] = useState<Address[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [buyNowItems, setBuyNowItems] = useState<any[] | null>(null)
  const [buyNowLoading, setBuyNowLoading] = useState(false)

  const isBuyNow = searchParams.get("buyNow") === "1"
  const buyNowProductId = searchParams.get("productId")
  const buyNowVariantId = searchParams.get("variantId")
  const buyNowQuantity = Number(searchParams.get("quantity") || 1)

  useEffect(() => {
    if (isBuyNow && buyNowProductId) {
      setBuyNowLoading(true)
      fetch(`/api/products/${buyNowProductId}`)
        .then(res => res.json())
        .then(product => {
          let price = product.price;
          let variant = undefined;
          if (buyNowVariantId && product.variants) {
            variant = product.variants.find((v: any) => v.id === buyNowVariantId);
            if (variant) {
              price = variant.price;
            }
          }
          setBuyNowItems([
            {
              id: product.id,
              productId: product.id,
              name: product.name,
              price,
              quantity: buyNowQuantity,
              image: product.images?.[0],
              stock: product.stock,
              variant,
            },
          ])
        })
        .finally(() => setBuyNowLoading(false))
    }
  }, [isBuyNow, buyNowProductId, buyNowVariantId, buyNowQuantity])

  useEffect(() => {
    if (!isBuyNow && cartItems.length === 0) {
      router.push("/cart")
    }
  }, [cartItems.length, router, isBuyNow])

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push(`/auth/signin?callbackUrl=${encodeURIComponent("/checkout")}`)
    }
  }, [status, router])

  useEffect(() => {
    async function loadAddresses() {
      if (session) {
        console.log("开始加载地址...")
        try {
          const res = await fetch("/api/addresses")
          if (res.ok) {
            const data = await res.json()
            console.log("地址加载成功:", data)
            setAddresses(data)
          }
        } catch (error) {
          console.error("加载地址失败:", error)
        } finally {
          setIsLoading(false)
        }
      }
    }

    loadAddresses()
  }, [session])

  if (!isBuyNow && cartItems.length === 0) {
    return null
  }
  if (isBuyNow && (!buyNowItems || buyNowLoading)) {
    return (
      <div className="flex items-center justify-center h-64">
        <Icons.spinner className="h-8 w-8 animate-spin" />
        <span className="ml-4 text-lg">Loading product...</span>
      </div>
    )
  }

  if (status === "loading") {
    console.log("正在加载认证状态")
    return null
  }

  if (status === "unauthenticated") {
    console.log("用户未认证")
    return null
  }

  const orderItems = isBuyNow ? buyNowItems || [] : cartItems
  const total = orderItems.reduce((acc, item) => acc + item.price * item.quantity, 0)

  return (
    <div className="container py-8">
      <div className="grid gap-8 lg:grid-cols-12">
        <div className="lg:col-span-7">
          <div className="space-y-6">
            <div>
              <h1 className="text-2xl font-bold">Checkout</h1>
              <p className="text-muted-foreground">
                Complete your order by providing your shipping details.
              </p>
            </div>
            {session ? (
              isLoading ? (
                <Card className="p-6">
                  <div className="flex items-center justify-center">
                    <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-gray-900" />
                  </div>
                </Card>
              ) : (
                <CheckoutForm addresses={addresses} items={orderItems} />
              )
            ) : (
              <Card className="p-6">
                <div className="space-y-4">
                  <h2 className="text-lg font-semibold">Sign In Required</h2>
                  <p className="text-sm text-muted-foreground">
                    Please sign in to continue with checkout.
                  </p>
                  <Button asChild>
                    <a href={`/auth/signin?callbackUrl=${encodeURIComponent("/checkout")}`}>
                      Sign In
                    </a>
                  </Button>
                </div>
              </Card>
            )}
          </div>
        </div>
        <div className="lg:col-span-5">
          <Card className="p-6">
            <h2 className="mb-4 text-lg font-semibold">Order Summary</h2>
            <div className="space-y-4">
              {orderItems.map((item) => (
                <div key={item.id} className="flex items-center gap-4">
                  <div className="relative aspect-square h-16 w-16 min-w-fit overflow-hidden rounded-lg border">
                    {item.image ? (
                      // eslint-disable-next-line @next/next/no-img-element
                      <img
                        src={item.image}
                        alt={item.name}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full items-center justify-center bg-secondary">
                        <span className="text-muted-foreground">No image</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="line-clamp-1 font-medium">{item.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {item.quantity} × {formatPrice(item.price)}
                    </p>
                    {item.variant && (
                      <div className="mt-1 space-y-0.5">
                        {item.variant.duration && item.variant.durationType && (
                          <div className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs text-muted-foreground">
                            <span className="font-medium">Duration:</span>
                            <span className="ml-1">{item.variant.duration} {item.variant.durationType}</span>
                          </div>
                        )}
                      </div>
                    )}
                    {item.uid && (
                      <div className="mt-1">
                        <div className="inline-flex flex-col sm:flex-row rounded-full border-primary/20 bg-primary/10 px-2.5 py-1 text-xs text-primary max-w-full">
                          <div className="flex items-center">
                            <Icons.billing className="mr-1 h-3 w-3 flex-shrink-0" />
                            <span className="font-medium">UID:</span>
                          </div>
                          <div className="mt-0.5 sm:mt-0 sm:ml-1 font-mono break-all">
                            {formatUid(item.uid)}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  <p className="font-medium">{formatPrice(item.price * item.quantity)}</p>
                </div>
              ))}
              <div className="border-t pt-4">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Subtotal</span>
                  <span className="font-medium">{formatPrice(total)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Shipping</span>
                  <span className="font-medium">Free</span>
                </div>
                <div className="flex items-center justify-between border-t pt-4">
                  <span className="font-medium">Total</span>
                  <span className="font-bold">{formatPrice(total)}</span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}