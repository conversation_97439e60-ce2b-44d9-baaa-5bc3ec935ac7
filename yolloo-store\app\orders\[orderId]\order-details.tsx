
import { formatPrice, formatUid, DateFormatter } from "@/lib/utils"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Icons } from "@/components/icons"
import { BackButton } from "@/components/back-button"
import { CompletePaymentButton } from "@/components/complete-payment-button"
import { ProductLink } from "@/components/product-link"
import { QRCodeDisplay } from "@/components/orders/qr-code-display"
import type { Order, OrderItem, Product, ProductVariant, Address, OdooOrderStatus } from "@prisma/client"

type OrderWithRelations = Order & {
  items: (OrderItem & {
    product?: Product | null;
    variant?: ProductVariant | null;
  })[];
  shippingAddress?: Address | null;
  shippingAddressSnapshot?: {
    name: string;
    phone: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  } | null;
  odooStatuses: OdooOrderStatus[];
  odooStatus?: OdooOrderStatus | null; // 为了兼容性保留，现在是可选的
}

interface OrderDetailsProps {
  order: OrderWithRelations
  searchParams: {
    success?: string
    canceled?: string
  }
}

// 获取状态显示信息
const getStatusDisplay = (status: string) => {
  const statusMap: Record<string, { label: string; variant: "success" | "warning" | "destructive" | "default" }> = {
    PENDING: { label: "Pending", variant: "warning" },
    PAID: { label: "Paid", variant: "success" },
    CANCELLED: { label: "Cancelled", variant: "destructive" },
    todelivery: { label: "To Delivery", variant: "warning" },
    delivered: { label: "Delivered", variant: "success" },
    activated: { label: "Activated", variant: "success" },
    failed: { label: "Failed", variant: "destructive" },
  }
  return statusMap[status] || { label: status, variant: "default" }
}

// 获取计划状态显示信息
const getPlanStateDisplay = (state: string) => {
  const stateMap: Record<string, { label: string; variant: "success" | "warning" | "destructive" | "default" }> = {
    waiting: { label: "Waiting", variant: "warning" },
    active: { label: "Active", variant: "success" },
    expired: { label: "Expired", variant: "destructive" },
    suspended: { label: "Suspended", variant: "destructive" },
  }
  return stateMap[state] || { label: state, variant: "default" }
}

// 从 variantText 中提取 duration 和 durationType 信息
const extractDurationInfo = (variantText: string | null | undefined) => {
  if (!variantText) return null;

  // 尝试匹配格式：产品名称 + 数字 + 单位(day/month)
  const regex = /(.+?)\s+(\d+)\s+(day|month)s?$/i;
  const match = variantText.match(regex);

  if (match) {
    return {
      productName: match[1].trim(),
      duration: parseInt(match[2], 10),
      durationType: match[3].toLowerCase()
    };
  }

  return null;
}

// 检查地址是否是系统默认地址
const isSystemDefaultAddress = (
  address: Address | null | undefined,
  addressSnapshot: Record<string, any> | null | undefined
): boolean => {
  // 检查实际地址
  if (address?.name === 'System Default Address' && address?.userId === null) {
    return true;
  }

  // 检查地址快照
  if (
    addressSnapshot &&
    typeof addressSnapshot === 'object' &&
    'name' in addressSnapshot &&
    addressSnapshot.name === 'System Default Address'
  ) {
    return true;
  }

  return false;
}


export function OrderDetails({ order, searchParams }: OrderDetailsProps) {
  return (
    <div className="container py-8">
      <div className="space-y-8">
        <div className="flex items-center gap-4">
          <BackButton />
        </div>

        {searchParams.success && (
          <Card className="p-6 bg-green-50">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-full bg-green-500 flex items-center justify-center">
                <Icons.check className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-green-800">
                  Payment Successful
                </h2>
                <p className="text-green-700">
                  Thank you for your purchase! Your order has been confirmed.
                </p>
              </div>
            </div>
          </Card>
        )}

        {searchParams.canceled && (
          <Card className="p-6 bg-red-50">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-full bg-red-500 flex items-center justify-center">
                <Icons.close className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-red-800">
                  Payment Canceled
                </h2>
                <p className="text-red-700">
                  Your payment was canceled. Please try again.
                </p>
              </div>
            </div>
          </Card>
        )}

        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Order Details</h1>
            <p className="text-muted-foreground">
              Order #{order.id} • Placed on{" "}
              {DateFormatter.forUserSafe(order.createdAt)}
            </p>
          </div>
          <Badge
            variant={getStatusDisplay(order.status).variant}
            className="h-7 px-3 text-sm"
          >
            {getStatusDisplay(order.status).label}
          </Badge>
        </div>

        {order.status === "PENDING" && (
          <Card className="p-6 bg-yellow-50 border-yellow-300">
            <div className="flex flex-col items-start gap-4">
              <h2 className="text-lg font-semibold text-yellow-800 mb-2">Complete Your Order</h2>
              <CompletePaymentButton orderId={order.id} />
            </div>
          </Card>
        )}

        <div className="grid gap-8 lg:grid-cols-12">
          <div className="lg:col-span-8 space-y-6">
            <Card>
              <div className="divide-y">
                {order.items.map((item: any) => {
                  // 由于我们移除了外键关系，不再有 product 字段
                  // 使用 variantText 作为主要显示内容
                  console.log(`[ORDER_DETAILS] Item ${item.id}: variantText=${item.variantText}`);

                  return (
                    <div key={item.id} className="flex gap-4 p-6">
                      <div className="relative aspect-square h-24 w-24 overflow-hidden rounded-lg bg-gray-200 flex items-center justify-center">
                        <Icons.package className="h-12 w-12 text-gray-400" />
                      </div>
                      <div className="flex flex-1 flex-col justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <ProductLink productCode={item.productCode}>
                              {item.variantText || "Unknown Product"}
                            </ProductLink>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Quantity: {item.quantity}
                          </p>
                          <div className="mt-2 flex flex-wrap gap-2">
                            {/* Duration Tag */}
                            {(() => {
                              const durationInfo = extractDurationInfo(item.variantText);
                              if (durationInfo?.duration && durationInfo?.durationType) {
                                return (
                                  <div className="inline-flex flex-row rounded-lg bg-blue-100 px-3 py-2 text-sm text-blue-700 max-w-full">
                                    <div className="flex items-center">
                                      <Icons.zap className="mr-1 h-3 w-3 flex-shrink-0" />
                                      <span className="font-medium">{durationInfo.duration} {durationInfo.durationType}</span>
                                    </div>
                                  </div>
                                );
                              }
                              return null;
                            })()}

                            {/* UID Tag */}
                            {item.uid && (
                              <div className="inline-flex flex-col sm:flex-row rounded-lg bg-purple-100 px-3 py-2 text-sm text-purple-700 max-w-full">
                                <div className="flex items-center">
                                  <Icons.creditCard className="mr-1 h-3 w-3 flex-shrink-0" />
                                  <span className="font-medium">UID:</span>
                                </div>
                                <div className="mt-1 sm:mt-0 sm:ml-1 font-mono text-xs break-all">
                                  {formatUid(item.uid)}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="mt-2">
                          <p className="font-medium">
                            {formatPrice(item.price)}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </Card>

            {/* QR Code Display for QR Code Products - Moved to Left Side */}
            {(() => {
              // 检查是否有QR code商品
              const hasQrCodeProducts = order.items.some((item: any) => {
                return item.product?.category?.name?.toLowerCase() === 'qr_code';
              });

              if (!hasQrCodeProducts) return null;

              // 获取第一个QR code产品的Odoo状态记录
              const qrCodeStatus = order.odooStatuses.find(status => {
                // 查找对应的订单项目
                const matchingItem = order.items.find(item =>
                  item.variantCode === status.variantCode &&
                  (status.uid === null || status.uid === item.uid)
                );
                // 检查是否是QR code产品
                return matchingItem?.product?.category?.name?.toLowerCase() === 'qr_code';
              });

              if (!qrCodeStatus) return null;

              // 构建正确的customer_order_ref
              const variantCode = qrCodeStatus.variantCode || 'default';
              const uid = qrCodeStatus.uid || 'no-uid';
              const customerOrderRef = `${order.id}-${variantCode}:::${uid}`;

              return (
                <QRCodeDisplay
                  orderId={order.id}
                  orderRef={customerOrderRef}
                  status={qrCodeStatus.status}
                  isQrCodeProduct={hasQrCodeProducts}
                />
              );
            })()}
          </div>

          <div className="space-y-6 lg:col-span-4">
            {/* Order Summary */}
            <Card>
              <div className="p-6">
                <h2 className="font-semibold">Order Summary</h2>
                <div className="mt-4 space-y-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Subtotal</span>
                    <span>{formatPrice(order.total)}</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span>Total</span>
                    <span>{formatPrice(order.total)}</span>
                  </div>
                </div>
              </div>
            </Card>

            {/* Shipping Address - Only show if not system default address */}
            {(order.shippingAddress || order.shippingAddressSnapshot) &&
              !isSystemDefaultAddress(order.shippingAddress, order.shippingAddressSnapshot) && (
                <Card>
                  <div className="p-6">
                    <h2 className="font-semibold">Shipping Address</h2>
                    <div className="mt-4 text-sm text-muted-foreground">
                      {order.shippingAddress ? (
                        <>
                          <p>{order.shippingAddress.name}</p>
                          <p>{order.shippingAddress.phone}</p>
                          <p>{order.shippingAddress.address1}</p>
                          {order.shippingAddress.address2 && (
                            <p>{order.shippingAddress.address2}</p>
                          )}
                          <p>
                            {order.shippingAddress.city},{" "}
                            {order.shippingAddress.state}{" "}
                            {order.shippingAddress.postalCode}
                          </p>
                          <p>{order.shippingAddress.country}</p>
                        </>
                      ) : order.shippingAddressSnapshot ? (
                        <>
                          <p>{order.shippingAddressSnapshot.name}</p>
                          <p>{order.shippingAddressSnapshot.phone}</p>
                          <p>{order.shippingAddressSnapshot.address1}</p>
                          {order.shippingAddressSnapshot.address2 && (
                            <p>{order.shippingAddressSnapshot.address2}</p>
                          )}
                          <p>
                            {order.shippingAddressSnapshot.city},{" "}
                            {order.shippingAddressSnapshot.state}{" "}
                            {order.shippingAddressSnapshot.postalCode}
                          </p>
                          <p>{order.shippingAddressSnapshot.country}</p>
                        </>
                      ) : null}
                    </div>
                  </div>
                </Card>
              )}

            {/* Shipping Status */}
            {(() => {
              // 过滤掉默认状态，只显示真实的Odoo订单状态
              const realStatuses = order.odooStatuses.filter(status => status.variantCode !== "default");

              // 获取商品项目信息的辅助函数
              const getItemInfo = (variantCode: string, uid: string | null) => {
                return order.items.find(item =>
                  item.variantCode === variantCode &&
                  (uid === null || item.uid === uid)
                );
              };

              // 获取产品显示名称的辅助函数
              const getProductDisplayName = (status: any) => {
                const item = getItemInfo(status.variantCode, status.uid);
                if (item?.variantText) {
                  return item.variantText;
                }
                return status.productName || status.variantCode;
              };

              // 简化方案：按商品顺序遍历，为每个商品查找对应的状态记录
              const orderedStatuses = order.items.map((item, itemIndex) => {
                const matchingStatus = realStatuses.find(status =>
                  status.variantCode === item.variantCode &&
                  (status.uid === null || status.uid === item.uid)
                );
                return matchingStatus ? { ...matchingStatus, itemIndex } : null;
              }).filter(Boolean); // 过滤掉没有状态记录的商品

              // 如果没有真实的Odoo状态记录，不显示发货状态卡片
              if (orderedStatuses.length === 0) {
                return null;
              }

              return (
                <div className="space-y-4">
                  {orderedStatuses.map((statusWithIndex) => {
                    const productName = getProductDisplayName(statusWithIndex);

                    return (
                      <Card key={statusWithIndex.id}>
                        <div className="p-6">
                          <div className="flex items-center justify-between mb-4">
                            <div>
                              <h2 className="font-semibold">Shipping Status</h2>
                              {orderedStatuses.length > 1 && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  Item {statusWithIndex.itemIndex + 1} of {order.items.length}
                                </p>
                              )}
                            </div>
                            {statusWithIndex.lastCheckedAt && (
                              <span className="text-xs text-muted-foreground">
                                Updated: {DateFormatter.forUserSafe(statusWithIndex.lastCheckedAt)}
                              </span>
                            )}
                          </div>
                          <div className="space-y-4">
                            {orderedStatuses.length > 1 && (
                              <div>
                                <p className="text-sm text-muted-foreground mb-1">Product</p>
                                <p className="text-sm font-medium">{productName}</p>
                              </div>
                            )}

                            <div>
                              <p className="text-sm text-muted-foreground mb-1">Status</p>
                              <Badge
                                variant={getStatusDisplay(statusWithIndex.status).variant}
                                className="h-6 px-2 text-xs"
                              >
                                {getStatusDisplay(statusWithIndex.status).label}
                              </Badge>
                            </div>

                            {statusWithIndex.description && (
                              <div>
                                <p className="text-sm text-muted-foreground mb-1">Description</p>
                                <p className="text-sm">{statusWithIndex.description}</p>
                              </div>
                            )}

                            {statusWithIndex.planState && (
                              <div>
                                <p className="text-sm text-muted-foreground mb-1">Plan State</p>
                                <Badge
                                  variant={getPlanStateDisplay(statusWithIndex.planState).variant}
                                  className="h-6 px-2 text-xs"
                                >
                                  {getPlanStateDisplay(statusWithIndex.planState).label}
                                </Badge>
                              </div>
                            )}

                            {statusWithIndex.trackingNumber && (
                              <div>
                                <p className="text-sm text-muted-foreground mb-1">Tracking Number</p>
                                <p className="text-sm font-medium">{statusWithIndex.trackingNumber}</p>
                              </div>
                            )}

                            {statusWithIndex.uid && (
                              <div>
                                <p className="text-sm text-muted-foreground mb-1">Card UIDs</p>
                                <div className="flex flex-wrap gap-2">
                                  {statusWithIndex.uid.split(',')
                                    .map((uid: string) => uid.trim())
                                    .filter((uid: string) => uid.length > 0)
                                    .map((uid: string, uidIndex: number) => (
                                      <Badge
                                        key={uidIndex}
                                        variant="outline"
                                        className="flex flex-col px-2 py-1.5 h-auto text-xs bg-purple-50 max-w-full"
                                      >
                                        <span className="font-medium">UID:</span>
                                        <span className="mt-0.5 font-mono break-all">
                                          {formatUid(uid)}
                                        </span>
                                      </Badge>
                                    ))
                                  }
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </Card>
                    );
                  })}
                </div>
              );
            })()}
          </div>
        </div>
      </div>
    </div>
  )
}