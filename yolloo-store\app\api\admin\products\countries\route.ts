import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// GET /api/admin/products/countries - 获取所有产品中的唯一国家列表
export async function GET() {
  try {
    // 获取所有产品的国家字段
    const products = await prisma.product.findMany({
      select: {
        country: true,
      },
      where: {
        country: {
          not: null,
        },
      },
    });

    // 提取唯一的国家列表并按逗号或分号拆分
    const countriesSet = new Set<string>();
    
    products
      .filter((p) => p.country)
      .forEach((p) => {
        // 拆分国家字符串并添加到Set中
        p.country!.split(/[,;]/)
          .map(c => c.trim())
          .filter(Boolean)
          .forEach(country => {
            countriesSet.add(country);
          });
      });
    
    // 转换为数组并排序
    const countries = Array.from(countriesSet).sort();

    return NextResponse.json({
      countries,
    });
  } catch (error) {
    console.error("[COUNTRIES_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
} 