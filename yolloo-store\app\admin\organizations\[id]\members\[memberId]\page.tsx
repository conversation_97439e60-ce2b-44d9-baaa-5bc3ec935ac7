"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import axios from "axios";
import { Loader2 } from "lucide-react";
import { Icons } from "@/components/icons";
import { OrganizationIcons } from "@/components/organization-icons";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { format } from "date-fns"
import { DateFormatter } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";

// Define the form schema for updating a member
const updateMemberSchema = z.object({
  commissionRate: z.number().min(0).max(1),
  isAdmin: z.boolean(),
});

type UpdateMemberFormValues = z.infer<typeof updateMemberSchema>;

// Define types for the member data
interface Member {
  id: string;
  code: string;
  commissionRate: number;
  totalEarnings: number;
  isAdmin: boolean;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
  organization: {
    id: string;
    name: string;
  };
}

interface Commission {
  id: string;
  amount: number;
  status: string;
  createdAt: string;
  order: {
    id: string;
    orderNumber: string;
    total: number;
  };
}

interface Stats {
  totalVisits: number;
  totalConversions: number;
  conversionRate: number;
  totalCommissions: number;
}

export default function AdminOrganizationMemberDetailPage({ params }: { params: { id: string; memberId: string } }) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [member, setMember] = useState<Member | null>(null);
  const [commissions, setCommissions] = useState<Commission[]>([]);
  const [stats, setStats] = useState<Stats | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const { register, handleSubmit, formState: { errors }, reset, setValue, watch } = useForm<UpdateMemberFormValues>({
    resolver: zodResolver(updateMemberSchema),
  });

  const isAdminValue = watch("isAdmin");

  // Fetch member details on component mount
  useEffect(() => {
    const fetchMemberDetails = async () => {
      try {
        setLoading(true);

        // Fetch member details
        const memberResponse = await axios.get(`/api/admin/organizations/${params.id}/members/${params.memberId}`);
        setMember(memberResponse.data);

        // Fetch commissions
        const commissionsResponse = await axios.get(`/api/admin/organizations/${params.id}/members/${params.memberId}/commissions`);
        setCommissions(commissionsResponse.data);

        // Fetch analytics
        const analyticsResponse = await axios.get(`/api/admin/organizations/${params.id}/members/${params.memberId}/analytics`);
        setStats(analyticsResponse.data.stats);

        // Set form defaults
        reset({
          commissionRate: memberResponse.data.commissionRate,
          isAdmin: memberResponse.data.isAdmin,
        });
      } catch (error) {
        console.error("Error fetching member details:", error);
        toast.error("Failed to fetch member details");
      } finally {
        setLoading(false);
      }
    };

    fetchMemberDetails();
  }, [params.id, params.memberId, reset]);

  // Handle member update
  const onUpdateMember = async (data: UpdateMemberFormValues) => {
    try {
      setIsUpdating(true);
      const response = await axios.patch(`/api/admin/organizations/${params.id}/members/${params.memberId}`, data);
      setMember(response.data);
      toast.success("Member updated successfully");
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error("Error updating member:", error);
      toast.error("Failed to update member");
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle member deletion
  const onDeleteMember = async () => {
    try {
      setIsDeleting(true);
      await axios.delete(`/api/admin/organizations/${params.id}/members/${params.memberId}`);
      toast.success("Member removed from organization");
      router.push(`/admin/organizations/${params.id}`);
    } catch (error) {
      console.error("Error removing member:", error);
      toast.error("Failed to remove member");
      setIsDeleting(false);
    }
  };

  // Get status badge for commission
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>;
      case "PAID":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Paid</Badge>;
      case "REJECTED":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!member) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <h3 className="text-lg font-medium">Member not found</h3>
        <p className="text-sm text-muted-foreground">
          The member you are looking for does not exist.
        </p>
        <Button asChild className="mt-4">
          <Link href={`/admin/organizations/${params.id}`}>
            <OrganizationIcons.arrowLeft className="mr-2 h-4 w-4" />
            Back to Organization
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <Button asChild variant="outline" size="sm" className="mb-4">
          <Link href={`/admin/organizations/${params.id}`}>
            <OrganizationIcons.arrowLeft className="mr-2 h-4 w-4" />
            Back to {member.organization.name}
          </Link>
        </Button>

        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={member.user.image} alt={member.user.name} />
              <AvatarFallback>{member.user.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold">{member.user.name}</h1>
                {member.isAdmin && (
                  <Badge variant="outline" className="bg-primary/10 text-primary">Admin</Badge>
                )}
              </div>
              <p className="text-sm text-muted-foreground">{member.user.email}</p>
            </div>
          </div>

          <div className="flex gap-2">
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Icons.edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              </DialogTrigger>
              <DialogContent>
                <form onSubmit={handleSubmit(onUpdateMember)}>
                  <DialogHeader>
                    <DialogTitle>Edit Member</DialogTitle>
                    <DialogDescription>
                      Update member settings and permissions.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="commissionRate">Commission Rate</Label>
                      <div className="relative">
                        <Input
                          id="commissionRate"
                          type="number"
                          step="0.01"
                          min="0"
                          max="1"
                          placeholder="0.10"
                          {...register("commissionRate", { valueAsNumber: true })}
                        />
                        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                          {(watch("commissionRate") * 100).toFixed(0)}%
                        </div>
                      </div>
                      {errors.commissionRate && (
                        <p className="text-sm text-red-500">{errors.commissionRate.message}</p>
                      )}
                      <p className="text-xs text-muted-foreground">
                        The commission rate determines how much of the product price the member will earn. This comes from the organization's commission. For example, if the organization earns 30% and member rate is 20%, the member gets 20% and the organization keeps 10%.
                      </p>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="isAdmin" className="text-base">Admin Privileges</Label>
                        <p className="text-sm text-muted-foreground">
                          Grant admin privileges to this member
                        </p>
                      </div>
                      <Switch
                        id="isAdmin"
                        checked={isAdminValue}
                        onCheckedChange={(checked) => setValue("isAdmin", checked)}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isUpdating}>
                      {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Save Changes
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive">
                  <Icons.trash className="mr-2 h-4 w-4" />
                  Remove
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Remove Member</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to remove this member from the organization? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={onDeleteMember}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    disabled={isDeleting}
                  >
                    {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Remove
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Member Since</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{DateFormatter.custom(member.createdAt, "MMM d, yyyy")}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Commission Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(member.commissionRate * 100).toFixed(0)}%</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Earnings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${member.totalEarnings.toFixed(2)}</div>
          </CardContent>
        </Card>

        {stats && (
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Conversion Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.conversionRate.toFixed(2)}%</div>
            </CardContent>
          </Card>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Commission History</CardTitle>
          <CardDescription>
            View commission history for this member.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {commissions.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed">
              <p className="text-sm text-muted-foreground">No commissions yet</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Order</TableHead>
                    <TableHead>Order Total</TableHead>
                    <TableHead>Commission</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {commissions.map((commission) => (
                    <TableRow key={commission.id}>
                      <TableCell className="font-medium">
                        {format(new Date(commission.createdAt), "MMM d, yyyy")}
                      </TableCell>
                      <TableCell>#{commission.order.orderNumber}</TableCell>
                      <TableCell>${commission.order.total.toFixed(2)}</TableCell>
                      <TableCell>${commission.amount.toFixed(2)}</TableCell>
                      <TableCell>{getStatusBadge(commission.status)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {stats && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Performance Metrics</CardTitle>
            <CardDescription>
              View performance metrics for this member.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Traffic</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 rounded-lg border">
                    <div className="text-sm text-muted-foreground">Total Visits</div>
                    <div className="text-2xl font-bold">{stats.totalVisits}</div>
                  </div>
                  <div className="p-4 rounded-lg border">
                    <div className="text-sm text-muted-foreground">Conversions</div>
                    <div className="text-2xl font-bold">{stats.totalConversions}</div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Performance</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 rounded-lg border">
                    <div className="text-sm text-muted-foreground">Conversion Rate</div>
                    <div className="text-2xl font-bold">{stats.conversionRate.toFixed(2)}%</div>
                  </div>
                  <div className="p-4 rounded-lg border">
                    <div className="text-sm text-muted-foreground">Total Commissions</div>
                    <div className="text-2xl font-bold">${stats.totalCommissions.toFixed(2)}</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}