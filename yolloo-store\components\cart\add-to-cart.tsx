"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { useToast } from '@/components/ui/use-toast'
import { useCart, CartItem } from '@/lib/hooks/use-cart'

interface ProductVariant {
  id: string
  price: number
  duration?: number | null
  durationType?: string | null
}

interface Product {
  id: string
  name: string
  price: number
  images?: string[]
  stock: number
  variants?: ProductVariant[]
  requiredUID?: boolean
}

interface AddToCartButtonProps {
  product: Product
  selectedVariant?: ProductVariant | null
  disabled?: boolean
  uid?: string
}

export function AddToCartButton({ product, selectedVariant, disabled, uid }: AddToCartButtonProps) {
  const { toast } = useToast()
  const { addItem } = useCart()
  const [isLoading, setIsLoading] = useState(false)

  const handleAddToCart = () => {
    if (product.variants && product.variants.length > 0 && !selectedVariant) {
      toast({
        title: "Please select options",
        description: "You need to select product options before adding to cart",
        variant: "destructive",
      })
      return
    }

    if (product.requiredUID && !uid) {
      toast({
        title: "UID Required",
        description: "Please select or enter a UID before adding to cart",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      // 准备购物车项数据
      const cartItem: CartItem = {
        id: `${product.id}${selectedVariant ? `-${selectedVariant.id}` : ''}${uid ? `-${uid}` : ''}`,
        productId: product.id,
        name: product.name,
        price: selectedVariant?.price || product.price,
        quantity: 1,
        image: product.images?.[0],
        stock: product.stock,
        variant: selectedVariant ? {
          id: selectedVariant.id,
          price: selectedVariant.price,
          duration: selectedVariant.duration,
          durationType: selectedVariant.durationType
        } : undefined,
        uid: uid
      }

      console.log('Adding to cart:', {
        product,
        selectedVariant,
        uid,
        cartItem
      })

      // 添加到本地购物车
      addItem(cartItem)

      toast({
        title: "Added to cart",
        description: `Added ${product.name}${selectedVariant ? ` ${selectedVariant.duration ? selectedVariant.duration : ''}(${selectedVariant.durationType ? selectedVariant.durationType : ''})` : ''} to your cart`,
        duration: 2000,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add the product to cart",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const isDisabled = isLoading || disabled || Boolean(product.variants?.length && !selectedVariant)

  return (
    <Button
      onClick={handleAddToCart}
      disabled={isDisabled}
      className="w-full"
    >
      {isLoading ? "Adding to cart..." : "Add to Cart"}
    </Button>
  )
} 