"use client"

import Link from "next/link"
import { Check } from "lucide-react"
import clsx from "clsx"
import { SectionBackground } from "@/components/ui/section-background"
import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"

const PricingCard = ({ 
  title, 
  description, 
  price, 
  originalPrice, 
  features, 
  isPopular = false,
  productLink,
  buttonText
}: {
  title: string
  description: string
  price: number
  originalPrice: number
  features: string[]
  isPopular?: boolean
  productLink: string
  buttonText: string
}) => {
  const { status } = useSession()
  const isAuthenticated = status === "authenticated"
  
  // Determine the link destination based on authentication status
  const linkHref = isAuthenticated ? productLink : `/auth/signin?callbackUrl=${encodeURIComponent(productLink)}`
  
  return (
    <div className={clsx(
      "relative p-8 rounded-3xl transition-all duration-500 hover:-translate-y-1 flex flex-col backdrop-blur-sm",
      isPopular 
        ? "bg-gradient-to-br from-[#B82E4E] to-[#F799A6] border-2 border-[#F799A6] shadow-[0_8px_32px_rgba(247,153,166,0.3)]" 
        : "bg-gradient-to-br from-[#F799A6]/20 to-[#B82E4E]/20 border border-[#F799A6]/30 hover:border-[#F799A6]/50 hover:shadow-[0_8px_32px_rgba(247,153,166,0.2)]"
    )}>
      {isPopular && (
        <span className="absolute -top-4 left-1/2 -translate-x-1/2 px-6 py-1.5 bg-white 
        text-[#B82E4E] text-sm font-semibold rounded-full shadow-lg shadow-pink-500/30">
          Most Popular
        </span>
      )}
      
      <div className="text-center mb-8">
        <h3 className={clsx(
          "text-2xl font-semibold mb-2",
          isPopular ? "text-white" : "text-gray-900"
        )}>
          {title}
        </h3>
        <p className={clsx(
          "mb-6 text-lg",
          isPopular ? "text-white/90" : "text-gray-600"
        )}>
          {description}
        </p>
        <div className="flex items-center justify-center gap-2">
          <span className={clsx(
            "text-5xl font-bold",
            isPopular ? "text-white" : "text-[#B82E4E]"
          )}>
            ${price}
          </span>
          {originalPrice && (
            <span className={clsx(
              "text-xl line-through",
              isPopular ? "text-white/60" : "text-gray-400"
            )}>
              ${originalPrice}
            </span>
          )}
        </div>
      </div>

      <div className="flex-grow">
        <ul className="space-y-4 mb-8">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start gap-3">
              <div className={clsx(
                "w-5 h-5 flex-shrink-0 flex items-center justify-center rounded-full shadow-sm",
                isPopular 
                  ? "bg-white/20 shadow-white/10" 
                  : "bg-[#F799A6]/20 shadow-pink-500/10"
              )}>
                <Check className={clsx(
                  "w-3 h-3",
                  isPopular ? "text-white" : "text-[#B82E4E]"
                )} />
              </div>
              <span className={clsx(
                "text-lg",
                isPopular ? "text-white" : "text-gray-600"
              )}>
                {feature}
              </span>
            </li>
          ))}
        </ul>
      </div>

      <Link 
        href={linkHref}
        className={clsx(
          "block w-full py-4 px-6 text-center rounded-full font-semibold transition-all duration-500 transform hover:-translate-y-0.5 text-lg",
          isPopular 
            ? "bg-white text-[#B82E4E] shadow-lg hover:shadow-xl shadow-white/30" 
            : "bg-gradient-to-r from-[#B82E4E] to-[#F799A6] text-white hover:from-[#A02745] hover:to-[#E88A97] shadow-lg hover:shadow-xl shadow-pink-500/20"
        )}
      >
        {buttonText}
      </Link>
    </div>
  )
}

export default function PricingSection() {
  const [productLinks, setProductLinks] = useState({
    lite: "",
    plus: "",
    max: ""
  });

  useEffect(() => {
    const fetchProductLinks = async () => {
      try {
        const response = await fetch("/api/products/get-card-links");
        const data = await response.json();
        setProductLinks(data);
      } catch (error) {
        console.error("Error fetching product links:", error);
      }
    };

    fetchProductLinks();
  }, []);

  return (
    <SectionBackground withTopGradient isWhite={false}>
      <div id="pricing" className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-block px-6 py-2 bg-gradient-to-br from-[#F799A6]/20 to-[#B82E4E]/20 backdrop-blur-sm 
          rounded-full border border-[#F799A6]/30 text-[#B82E4E] font-semibold tracking-wide mb-4 shadow-lg shadow-pink-500/10">
            Pricing Cards
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] 
          text-transparent bg-clip-text">
            Choose Your Perfect Card
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed font-medium">
            Select your ideal Yolloo card with eSIM capabilities. Data plans can be purchased separately after activation.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <PricingCard
            title="Lite"
            description="Perfect for light travelers"
            price={12}
            originalPrice={17}
            features={[
              "3 free eSIM downloads",
              "Android device support",
              "15 eSIM profile storage",
              "Global coverage",
              "Basic support"
            ]}
            productLink={productLinks.lite}
            buttonText="Get Card"
          />
          <PricingCard
            title="Plus"
            description="Best for regular travelers"
            price={21}
            originalPrice={30}
            features={[
              "Unlimited eSIM downloads",
              "Android device support",
              "15 eSIM profile storage",
              "Global coverage",
              "Priority support",
              "Free updates"
            ]}
            productLink={productLinks.plus}
            buttonText="Get Card"
          />
          <PricingCard
            title="Max"
            description="Ultimate flexibility"
            price={23}
            originalPrice={33}
            features={[
              "Unlimited eSIM downloads",
              "iOS & Android support",
              "15 eSIM profile storage",
              "Global coverage",
              "24/7 Priority support",
              "Advanced features"
            ]}
            isPopular
            productLink={productLinks.max}
            buttonText="Get Card"
          />
        </div>
        <div className="text-center mt-8 text-sm text-gray-500">
          * Physical card supported with all plans. eSIM data plans are sold separately.
        </div>
      </div>
    </SectionBackground>
  )
} 