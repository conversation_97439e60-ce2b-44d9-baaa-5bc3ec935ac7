import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function PATCH(
  req: Request,
  { params }: { params: { affiliateId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 403 })
    }

    const { commissionRate, discountRate } = await req.json()

    if ((commissionRate !== undefined && (typeof commissionRate !== "number" || commissionRate < 0 || commissionRate > 1)) ||
        (discountRate !== undefined && (typeof discountRate !== "number" || discountRate < 0 || discountRate > 1))) {
      return new NextResponse("Invalid rate value", { status: 400 })
    }

    const updateData: any = {}
    if (commissionRate !== undefined) updateData.commissionRate = commissionRate
    if (discountRate !== undefined) updateData.discountRate = discountRate

    const affiliate = await prisma.affiliateProfile.update({
      where: {
        id: params.affiliateId,
      },
      data: updateData,
    })

    return NextResponse.json(affiliate)
  } catch (error) {
    console.error("[AFFILIATE_PATCH]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 