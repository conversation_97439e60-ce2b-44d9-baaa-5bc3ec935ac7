# Dependencies
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Next.js build output
.next
out

# Environment variables - Allow .env files in Docker
# .env*
# !.env

# Version control
.git
.gitignore

# IDE
.idea
.vscode

# OS generated files
.DS_Store
Thumbs.db

# Docker - Allow these files
# Dockerfile
# docker-compose.yml
# .dockerignore

# Logs
logs
*.log

# Cache
.npm
.eslintcache

# Make sure to keep
!components
!postcss.config.js
!tailwind.config.js
!tsconfig.json 