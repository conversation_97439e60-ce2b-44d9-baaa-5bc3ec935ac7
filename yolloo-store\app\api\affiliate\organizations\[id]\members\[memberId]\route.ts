import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Schema for updating a member
const updateMemberSchema = z.object({
  commissionRate: z.number().min(0).max(1).optional(),
  isAdmin: z.boolean().optional(),
});

// Helper function to check if user has admin access to the organization
async function hasAdminAccess(userId: string, organizationId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { 
      role: true,
      affiliate: {
        select: {
          id: true,
          organizationId: true,
          isAdmin: true,
        },
      },
    },
  });

  if (!user) return false;
  
  // System admin has access to all organizations
  if (user.role === "ADMIN") return true;
  
  // Organization admin has access to their organization
  if (user.affiliate?.organizationId === organizationId && user.affiliate.isAdmin) {
    return true;
  }
  
  return false;
}

// GET - Get member details
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string; memberId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id: organizationId, memberId } = params;
    
    // Check if user has access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);
    
    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    // Get member details
    const member = await prisma.affiliateProfile.findFirst({
      where: {
        id: memberId,
        organizationId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        referrals: {
          take: 10,
          orderBy: { createdAt: "desc" },
          include: {
            order: {
              select: {
                id: true,
                total: true,
                createdAt: true,
              },
            },
          },
        },
        _count: {
          select: {
            referrals: true,
            visits: true,
          },
        },
      },
    });
    
    if (!member) {
      return NextResponse.json({ error: "Member not found" }, { status: 404 });
    }
    
    return NextResponse.json(member);
  } catch (error) {
    console.error("Error fetching member:", error);
    return NextResponse.json(
      { error: "Failed to fetch member" },
      { status: 500 }
    );
  }
}

// PATCH - Update member
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string; memberId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id: organizationId, memberId } = params;
    
    // Check if user has admin access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);
    
    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validationResult = updateMemberSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    // Check if member exists in this organization
    const member = await prisma.affiliateProfile.findFirst({
      where: {
        id: memberId,
        organizationId,
      },
    });
    
    if (!member) {
      return NextResponse.json({ error: "Member not found" }, { status: 404 });
    }
    
    // Prevent changing admin status of an admin member
    if (member.isAdmin && validationResult.data.isAdmin === false) {
      return NextResponse.json(
        { error: "Cannot remove admin status from an admin member" },
        { status: 403 }
      );
    }
    
    // Update member
    const updatedMember = await prisma.affiliateProfile.update({
      where: { id: memberId },
      data: validationResult.data,
    });
    
    return NextResponse.json(updatedMember);
  } catch (error) {
    console.error("Error updating member:", error);
    return NextResponse.json(
      { error: "Failed to update member" },
      { status: 500 }
    );
  }
}

// DELETE - Remove member from organization
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string; memberId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id: organizationId, memberId } = params;
    
    // Check if user has admin access to this organization
    const hasPermission = await hasAdminAccess(session.user.id, organizationId);
    
    if (!hasPermission) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    // Check if member exists in this organization
    const member = await prisma.affiliateProfile.findFirst({
      where: {
        id: memberId,
        organizationId,
      },
    });
    
    if (!member) {
      return NextResponse.json({ error: "Member not found" }, { status: 404 });
    }
    
    // Prevent removing admin members
    if (member.isAdmin) {
      return NextResponse.json(
        { error: "Cannot remove an admin member" },
        { status: 403 }
      );
    }
    
    // Remove member from organization
    const updatedMember = await prisma.affiliateProfile.update({
      where: { id: memberId },
      data: {
        organizationId: null,
        isAdmin: false,
      },
    });
    
    return NextResponse.json(updatedMember);
  } catch (error) {
    console.error("Error removing member:", error);
    return NextResponse.json(
      { error: "Failed to remove member" },
      { status: 500 }
    );
  }
} 