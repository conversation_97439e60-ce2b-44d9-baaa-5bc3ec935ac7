'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'

export default function NewHero() {
  const scrollToGetStart = () => {
    const getStartSection = document.getElementById('get-start');
    if (getStartSection) {
      const offset = 20;
      const elementPosition = getStartSection.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.scrollY - offset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section
      className="relative h-[70vh] min-h-[600px] flex items-center justify-start overflow-hidden"
      style={{
        backgroundImage: "url('/bg_banner.svg')",
        backgroundSize: 'cover',
        backgroundPosition: 'top',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Content Container */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-2xl">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center gap-2 bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 mb-8 shadow-lg"
          >
            <div className="w-2 h-2 bg-[#FF6B9D] rounded-full"></div>
            <span className="text-sm font-medium text-gray-700">One Card, One World</span>
          </motion.div>

          {/* Main Heading */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-6"
          >
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white leading-tight">
              Global travel
            </h1>
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white leading-tight mt-2">
              One card is all you need
            </h2>
          </motion.div>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-lg sm:text-xl text-white/90 mb-10 leading-relaxed max-w-lg"
          >
            Yolloo eSIM makes it easy to stay online wherever you are.
          </motion.p>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4"
          >
            {/* Browse Yolloo Cards Button */}
            <Link
              href="/yolloo-smart"
              className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-[#FF6B9D] to-[#FF8FA3] 
              hover:from-[#FF5A8C] hover:to-[#FF7E92] text-white font-semibold rounded-full 
              shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 
              text-lg min-w-[200px]"
            >
              Browse Yolloo Cards
            </Link>

            {/* How It Works Button */}
            <button
              onClick={scrollToGetStart}
              className="inline-flex items-center justify-center px-8 py-4 bg-white/20 backdrop-blur-sm 
              hover:bg-white/30 text-white font-semibold rounded-full border-2 border-white/30 
              hover:border-white/50 shadow-lg hover:shadow-xl transition-all duration-300 
              transform hover:-translate-y-1 text-lg min-w-[200px]"
            >
              How It Works
            </button>
          </motion.div>
        </div>
      </div>

      {/* Overlay for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-transparent z-0"></div>
    </section>
  )
}
