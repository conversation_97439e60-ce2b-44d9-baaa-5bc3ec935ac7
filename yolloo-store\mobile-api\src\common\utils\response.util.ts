import { RequestContext } from '../interfaces/context.interface';
import { DEFAULT_PAGINATION } from '../constants/app.constants';

export interface PaginationResponse {
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export interface ApiResponse<T> {
  data?: T;
  pagination?: PaginationResponse;
  context?: {
    language: string;
    theme: string;
    currency: string;
  };
  message?: string;
}

export class ResponseUtil {
  static createPaginationResponse(
    total: number,
    page: number = DEFAULT_PAGINATION.PAGE,
    pageSize: number = DEFAULT_PAGINATION.PAGE_SIZE,
  ): PaginationResponse {
    return {
      total,
      page,
      pageSize,
      hasMore: (page - 1) * pageSize + pageSize < total,
    };
  }

  static createContextResponse(ctx: RequestContext) {
    return {
      language: ctx.language,
      theme: ctx.theme,
      currency: ctx.currency,
    };
  }

  static createListResponse<T>(
    items: T[],
    total: number,
    page: number,
    pageSize: number,
    ctx: RequestContext,
    additionalData?: any,
  ) {
    return {
      ...additionalData,
      [Array.isArray(items) ? this.getItemsKey(items) : 'data']: items,
      pagination: this.createPaginationResponse(total, page, pageSize),
      context: this.createContextResponse(ctx),
    };
  }

  static createSuccessResponse<T>(
    data: T,
    message?: string,
    ctx?: RequestContext,
  ): ApiResponse<T> {
    const response: ApiResponse<T> = { data };
    
    if (message) {
      response.message = message;
    }
    
    if (ctx) {
      response.context = this.createContextResponse(ctx);
    }
    
    return response;
  }

  private static getItemsKey(items: any[]): string {
    if (items.length === 0) return 'items';
    
    const firstItem = items[0];
    if (firstItem.boosterType !== undefined) return 'boosters';
    if (firstItem.planType !== undefined) return 'packages';
    if (firstItem.operator !== undefined) return 'rechargeOptions';
    if (firstItem.destination !== undefined) return 'packages';
    
    return 'items';
  }
}
