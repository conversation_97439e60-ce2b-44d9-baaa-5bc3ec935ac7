import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { DateUtils } from "@/lib/utils";

export const dynamic = 'force-dynamic';

// POST - Manually refresh organization statistics
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const organizationId = params.id;

    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Get all members of the organization
    const members = await prisma.affiliateProfile.findMany({
      where: { organizationId },
      select: { id: true },
    });

    const memberIds = members.map(member => member.id);

    // Get all orders promoted by members
    const referrals = await prisma.affiliateReferral.findMany({
      where: {
        affiliateId: { in: memberIds },
      },
      include: {
        order: true,
      },
    });

    // Process all referrals
    for (const referral of referrals) {
      // 1. Check and create visit records if needed
      const existingVisit = await prisma.affiliateVisit.findFirst({
        where: {
          affiliateId: referral.affiliateId,
          orderId: referral.orderId,
        },
      });

      if (!existingVisit) {
        // Create visit record
        await prisma.affiliateVisit.create({
          data: {
            affiliateId: referral.affiliateId,
            organizationId,
            source: "direct",
            path: "/",
            userAgent: "Manual refresh",
            referrer: "Manual refresh",
            convertedToOrder: true,
            orderId: referral.orderId,
          },
        });
      }

      // 2. Check and create organization commission records if needed
      if (!referral.organizationCommissionId) {
        // 确保组织佣金记录存在
        const existingOrgCommission = await prisma.organizationCommission.findFirst({
          where: {
            organizationId,
            referrals: {
              some: {
                orderId: referral.orderId
              }
            }
          }
        });

        if (!existingOrgCommission) {
          // 获取订单信息 - 已在include中加载
          const order = referral.order;

          if (order) {
            // 计算组织应获得的佣金
            const organization = await prisma.affiliateOrganization.findUnique({
              where: { id: organizationId }
            });

            if (organization) {
              // 创建组织佣金记录
              const commissionAmount = order.total * organization.commissionRate;
              const newOrgCommission = await prisma.organizationCommission.create({
                data: {
                  organizationId,
                  commissionAmount,
                  status: order.status === "DELIVERED" ? "APPROVED" : "PENDING",
                }
              });

              // 更新引用关系
              await prisma.affiliateReferral.update({
                where: { id: referral.id },
                data: {
                  organizationCommissionId: newOrgCommission.id
                }
              });
            }
          }
        } else {
          // 如果佣金记录存在但未关联，则更新关联
          await prisma.affiliateReferral.update({
            where: { id: referral.id },
            data: {
              organizationCommissionId: existingOrgCommission.id
            }
          });
        }
      }
    }

    // Update organization commission status
    const updatedCommissions = await prisma.organizationCommission.updateMany({
      where: {
        organizationId,
        status: "PENDING",
        referrals: {
          some: {
            order: {
              status: "DELIVERED",
            },
          },
        },
      },
      data: {
        status: "APPROVED",
      },
    });

    // Update member referral status
    const updatedReferrals = await prisma.affiliateReferral.updateMany({
      where: {
        affiliate: {
          organizationId,
        },
        status: "PENDING",
        order: {
          status: "DELIVERED",
        },
      },
      data: {
        status: "APPROVED",
      },
    });

    // Recalculate organization's total earnings
    const totalCommissions = await prisma.organizationCommission.aggregate({
      where: {
        organizationId,
        status: "APPROVED",
      },
      _sum: {
        commissionAmount: true,
      },
    });

    // Get total commissions earned by members
    const memberCommissions = await prisma.affiliateReferral.aggregate({
      where: {
        affiliate: {
          organizationId,
        },
        status: "APPROVED",
      },
      _sum: {
        commissionAmount: true,
      },
    });

    const orgCommissions = totalCommissions._sum.commissionAmount || 0;
    const memCommissions = memberCommissions._sum.commissionAmount || 0;

    // Organization actual earnings = Total organization commissions - Member commissions
    const orgActualEarnings = orgCommissions - memCommissions;

    // Update organization's totalEarnings
    await prisma.affiliateOrganization.update({
      where: { id: organizationId },
      data: {
        totalEarnings: orgActualEarnings > 0 ? orgActualEarnings : 0,
      },
    });

    // Calculate visit and conversion data
    const totalVisits = await prisma.affiliateVisit.count({ where: { organizationId } });
    const totalConversions = await prisma.affiliateVisit.count({
      where: { organizationId, convertedToOrder: true }
    });
    const conversionRate = totalVisits > 0 ? (totalConversions / totalVisits) * 100 : 0;

    // Calculate monthly earnings data
    const monthlyEarnings = await calculateMonthlyEarnings(organizationId);

    return NextResponse.json({
      success: true,
      message: "Organization statistics refreshed successfully",
      updatedVisits: referrals.length,
      updatedCommissions: updatedCommissions.count,
      updatedReferrals: updatedReferrals.count,
      stats: {
        totalVisits,
        totalConversions,
        conversionRate,
        totalCommissions: orgCommissions,
        memberCommissions: memCommissions,
        organizationActualEarnings: orgActualEarnings
      },
      calculatedAtTimestamp: DateFormatter.iso(new Date())
    });
  } catch (error) {
    console.error("Error refreshing organization statistics:", error);
    return NextResponse.json(
      { error: "Failed to refresh organization statistics" },
      { status: 500 }
    );
  }
}

// Helper function to calculate real monthly earnings data
async function calculateMonthlyEarnings(organizationId: string) {
  // Get current date and set to beginning of month
  const currentDate = new Date();
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const monthsData = [];

  // Calculate data for the last 6 months
  for (let i = 0; i < 6; i++) {
    // Create date for this month (0-5 months ago)
    const targetDate = new Date();
    targetDate.setMonth(currentDate.getMonth() - i);

    const startOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);
    const endOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0, 23, 59, 59);

    // Get approved organization commissions for this month
    const monthlyCommissions = await prisma.organizationCommission.aggregate({
      where: {
        organizationId,
        status: { in: ["APPROVED", "PAID"] },
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      },
      _sum: {
        commissionAmount: true
      }
    });

    // Get approved member commissions for this month
    const memberMonthlyCommissions = await prisma.affiliateReferral.aggregate({
      where: {
        affiliate: {
          organizationId
        },
        status: { in: ["APPROVED", "PAID"] },
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      },
      _sum: {
        commissionAmount: true
      }
    });

    const totalCommissions = monthlyCommissions._sum.commissionAmount || 0;
    const memberCommissions = memberMonthlyCommissions._sum.commissionAmount || 0;

    // Calculate organization's actual earnings for the month
    const actualEarnings = totalCommissions - memberCommissions;

    // Add month data
    monthsData.push({
      month: months[targetDate.getMonth()],
      year: targetDate.getFullYear(),
      amount: actualEarnings,
      totalCommissions,
      memberCommissions
    });
  }

  // Sort from oldest to newest
  return monthsData.reverse();
}