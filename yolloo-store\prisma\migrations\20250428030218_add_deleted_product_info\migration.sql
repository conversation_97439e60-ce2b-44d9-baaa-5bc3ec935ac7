-- AlterTable
ALTER TABLE "OrderItem" ADD COLUMN     "deletedProductId" TEXT;

-- CreateTable
CREATE TABLE "DeletedProductInfo" (
    "id" TEXT NOT NULL,
    "originalProductId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "websiteDescription" TEXT NOT NULL DEFAULT '',
    "price" DOUBLE PRECISION NOT NULL,
    "images" TEXT[],
    "categoryName" TEXT NOT NULL,
    "categoryId" TEXT,
    "sku" TEXT NOT NULL,
    "mcc" TEXT,
    "dataSize" DOUBLE PRECISION,
    "planType" TEXT,
    "country" TEXT,
    "countryCode" TEXT,
    "variantInfo" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "DeletedProductInfo_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "DeletedProductInfo_originalProductId_key" ON "DeletedProductInfo"("originalProductId");

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_deletedProductId_fkey" FOREIGN KEY ("deletedProductId") REFERENCES "DeletedProductInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;
