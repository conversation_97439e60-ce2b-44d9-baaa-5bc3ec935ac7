import { NextResponse } from "next/server";
import { odooService } from "@/lib/odoo";
import { prisma } from "@/lib/prisma";

export async function POST(
  _request: Request,
  { params }: { params: { orderId: string } }
) {
  try {
    // 首先从数据库获取该订单的所有customer_order_ref
    const existingOdooStatuses = await prisma.odooOrderStatus.findMany({
      where: { orderId: params.orderId },
      select: { variantCode: true, uid: true }
    });

    // 构建所有可能的customer_order_ref
    const customerOrderRefs: string[] = [];

    if (existingOdooStatuses.length > 0) {
      // 如果有现有的Odoo状态记录，使用这些记录构建customer_order_ref
      for (const status of existingOdooStatuses) {
        const variantCode = status.variantCode || 'default';
        const uid = status.uid || 'no-uid';
        const customerOrderRef = `${params.orderId}-${variantCode}:::${uid}`;
        customerOrderRefs.push(customerOrderRef);
      }
    } else {
      // 如果没有现有记录，尝试使用简单的订单ID（向后兼容）
      customerOrderRefs.push(params.orderId);
    }

    console.log(`[UPDATE_ODOO_STATUS] Querying Odoo with customer_order_refs:`, customerOrderRefs);

    // 查询Odoo订单状态，传递所有相关的customer_order_ref
    const odooResponse = await odooService.queryOrderStatusMultiple(customerOrderRefs);
    console.log(`[UPDATE_ODOO_STATUS] Response for order ${params.orderId}:`, JSON.stringify(odooResponse, null, 2));

    // 检查响应状态
    if (odooResponse?.status !== 'success' && odooResponse?.status !== 'ok') {
      return NextResponse.json({
        success: false,
        message: `Failed to get order status: ${odooResponse?.result?.message || 'Unknown error'}`,
      }, { status: 400 });
    }

    const responseData = odooResponse.result?.data;

    // 检查是否有订单数据
    if (!responseData || responseData.length === 0) {
      return NextResponse.json({
        success: false,
        message: "No order data returned from Odoo",
      }, { status: 400 });
    }

    // 存储所有更新的Odoo状态记录
    const updatedStatuses = [];

    // 处理所有订单详情
    for (const orderDetail of responseData) {
      // 检查是否有订单行数据
      if (!orderDetail.order_lines || orderDetail.order_lines.length === 0) {
        console.log(`[UPDATE_ODOO_STATUS] No order lines for order detail: ${orderDetail.customer_order_ref}`);
        continue;
      }

      // 从customer_order_ref中提取订单ID和分组键
      // 新格式: orderId-variantCode:::uid 或旧格式: orderId-variantCode-uid
      let orderId: string;
      let variantCode = "default";
      let uid: string | null = null;

      if (orderDetail.customer_order_ref.includes(':::')) {
        // 新格式：使用:::分隔符
        const mainParts = orderDetail.customer_order_ref.split('-');
        orderId = mainParts[0];

        if (mainParts.length > 1) {
          const groupKeyPart = mainParts.slice(1).join('-'); // 重新组合可能包含连字符的部分
          const [extractedVariantCode, extractedUid] = groupKeyPart.split(':::');
          variantCode = extractedVariantCode || "default";
          uid = extractedUid === 'no-uid' ? null : extractedUid;
        }
      } else {
        // 旧格式：使用-分隔符（向后兼容）
        const orderRefParts = orderDetail.customer_order_ref.split('-');
        orderId = orderRefParts[0];

        if (orderRefParts.length > 1) {
          variantCode = orderRefParts[1];
          if (orderRefParts.length > 2) {
            uid = orderRefParts[2] === 'no-uid' ? null : orderRefParts[2];
          }
        }
      }

      // 确保这是我们要查询的订单
      if (orderId !== params.orderId) {
        console.log(`[UPDATE_ODOO_STATUS] Skipping order ${orderDetail.customer_order_ref} as it doesn't match requested ID ${params.orderId}`);
        continue;
      }

      // 处理每个订单行
      for (const orderLine of orderDetail.order_lines) {
        // 处理UID数据 - 可能是单个UID或多个UID的数组
        let uids: string[] = [];
        let uidString: string | null = uid; // 使用从customer_order_ref提取的uid

        // 如果没有从customer_order_ref提取到uid，尝试从orderLine.data中提取
        if (!uidString && orderLine.data) {
          if (Array.isArray(orderLine.data)) {
            // 从所有data项中提取uid
            uids = orderLine.data
              .filter((item: any) => item && item.uid)
              .map((item: any) => item.uid);

            if (uids.length > 0) {
              uidString = uids.join(',');
            }
          } else if (typeof orderLine.data === 'object' && orderLine.data.uid) {
            // 处理单个对象的情况
            uidString = orderLine.data.uid;
          }
        }

        // 确保variantCode是UUID格式
        const formattedVariantCode = variantCode || "default";

        // 确保UID是数字字符串格式
        const formattedUid = uidString ? uidString.replace(/[^0-9,]/g, '') : null;

        console.log(`[UPDATE_ODOO_STATUS] Processing order ${params.orderId}, variant ${formattedVariantCode}, uid=${formattedUid || 'none'}`);

        // 查找是否已存在相同 orderId, variantCode, uid 的记录
        const existingStatus = await prisma.odooOrderStatus.findFirst({
          where: {
            orderId: params.orderId,
            variantCode: formattedVariantCode,
            uid: formattedUid
          }
        });

        let updatedStatus: any;

        if (existingStatus) {
          // 更新现有记录
          updatedStatus = await prisma.odooOrderStatus.update({
            where: { id: existingStatus.id },
            data: {
              status: orderLine.status,
              description: orderLine.description,
              productName: orderLine.product_name,
              isDigital: orderLine.is_digital || false,
              deliveredQty: orderLine.delivered_qty || 0,
              trackingNumber: orderLine.tracking_number,
              planState: orderLine.data?.[0]?.plan_state,
              lastCheckedAt: new Date(),
            }
          });
        } else {
          // 创建新记录
          updatedStatus = await prisma.odooOrderStatus.create({
            data: {
              orderId: params.orderId,
              variantCode: formattedVariantCode,
              status: orderLine.status,
              description: orderLine.description,
              productName: orderLine.product_name,
              isDigital: orderLine.is_digital || false,
              deliveredQty: orderLine.delivered_qty || 0,
              trackingNumber: orderLine.tracking_number,
              planState: orderLine.data?.[0]?.plan_state,
              uid: formattedUid, // 存储格式化后的UID
              lastCheckedAt: new Date(),
            }
          });
        }

        updatedStatuses.push(updatedStatus);

        // 如果订单状态为已发货，更新主订单状态
        if (orderLine.status === 'delivered') {
          await prisma.order.update({
            where: { id: params.orderId },
            data: { status: 'DELIVERED' }
          });
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: `Order status updated successfully. Updated ${updatedStatuses.length} status records.`,
      odooStatuses: updatedStatuses
    });
  } catch (error) {
    console.error("[UPDATE_ODOO_STATUS]", error);
    return NextResponse.json({
      success: false,
      message: `Error updating order status: ${error instanceof Error ? error.message : 'Unknown error'}`,
    }, { status: 500 });
  }
}