import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { DateUtils } from "@/lib/utils";
import { sendOrganizationInviteEmail } from "@/app/services/emailService";

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// GET - List all invites for an organization (admin only)
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: params.id },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Get invites for the organization
    const invites = await prisma.organizationInvite.findMany({
      where: { organizationId: params.id },
      orderBy: { createdAt: "desc" },
      include: {
        affiliate: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json(invites);
  } catch (error) {
    console.error("Error fetching organization invites:", error);
    return NextResponse.json(
      { error: "Failed to fetch organization invites" },
      { status: 500 }
    );
  }
}

// POST - Create a new invite for an organization (admin only)
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    // Check if organization exists
    const organization = await prisma.affiliateOrganization.findUnique({
      where: { id: params.id },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    const body = await req.json();
    const { email, isGeneralInvite } = body;

    // For general invites, we don't need an email
    if (!email && !isGeneralInvite) {
      return NextResponse.json({ error: "Email is required for specific invites" }, { status: 400 });
    }

    // If creating a specific (email-based) invite
    if (email && !isGeneralInvite) {
      // Check if user with this email already exists
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      // Check if there's already an active invite for this email
      const existingInvite = await prisma.organizationInvite.findFirst({
        where: {
          email,
          organizationId: params.id,
          status: "PENDING",
          expiresAt: {
            gt: new Date(),
          },
        },
      });

      if (existingInvite) {
        return NextResponse.json(
          { error: "An active invitation already exists for this email" },
          { status: 400 }
        );
      }
    }

    // Generate a unique invite code
    const inviteCode = `INV-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;

    // Set expiration date (7 days from now)
    const expiresAt = DateUtils.addDays(new Date(), 7);

    // Create the invite
    const invite = await prisma.organizationInvite.create({
      data: {
        email: email || null,  // Allow null for general invites
        inviteCode,
        expiresAt,
        organization: {
          connect: { id: params.id },
        },
      },
    });

    // Send invitation email only for specific invites
    if (organization && email && !isGeneralInvite) {
      const inviteUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invite/${inviteCode}`;

      await sendOrganizationInviteEmail(
        email,
        organization.name,
        inviteUrl,
        expiresAt,
        session?.user?.name || "Admin"
      );
    }

    return NextResponse.json(invite, { status: 201 });
  } catch (error) {
    console.error("Error creating organization invite:", error);
    return NextResponse.json(
      { error: "Failed to create organization invite" },
      { status: 500 }
    );
  }
}