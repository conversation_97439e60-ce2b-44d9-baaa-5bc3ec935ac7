'use client';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { packageStatusMap, orderSourceMap, formatDateTime } from "@/lib/boss-utils";
import { MoreHorizontal, ArrowUp, BarChart, FileText, PowerOff } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface PackageListProps {
  packages: any[];
  onSelectPackage: (pkg: any) => void;
  onViewUsage?: (orderSn: string) => void;
  onViewDetails?: (orderSn: string) => void;
  onSetTop?: (orderSn: string, uid: string) => void;
  onDeactivate?: (orderSn: string) => void;
  uid: string;
}

export function PackageList({
  packages,
  onSelectPackage,
  onViewUsage,
  onViewDetails,
  onSetTop,
  onDeactivate,
  uid
}: PackageListProps) {
  if (!packages || packages.length === 0) {
    return <div className="py-4 text-center text-gray-500">No packages found for this card</div>;
  }

  // 计算不同状态的套餐数量
  const currentActiveCount = packages.filter(pkg => pkg.whetherCurrent === true && pkg.packageStatus !== 800).length;
  const activeCount = packages.filter(pkg => [500, 600, 700].includes(pkg.packageStatus) && pkg.whetherCurrent !== true).length;
  const processingCount = packages.filter(pkg => [300, 400].includes(pkg.packageStatus)).length;
  const terminatedCount = packages.filter(pkg => pkg.packageStatus === 800).length;

  return (
    <div className="overflow-x-auto">
      <div className="mb-2 text-sm text-muted-foreground">
        <span>Total: {packages.length} packages</span>
        <span className="ml-2">(
          {currentActiveCount > 0 && <span className="font-bold text-green-600">{currentActiveCount} current</span>}
          {currentActiveCount > 0 && (activeCount > 0 || processingCount > 0 || terminatedCount > 0) && <span>, </span>}

          {activeCount > 0 && <span className="text-green-600">{activeCount} active</span>}
          {activeCount > 0 && (processingCount > 0 || terminatedCount > 0) && <span>, </span>}

          {processingCount > 0 && <span>{processingCount} processing</span>}
          {processingCount > 0 && terminatedCount > 0 && <span>, </span>}

          {terminatedCount > 0 && <span className="text-red-500">{terminatedCount} terminated</span>}
        )</span>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Order Number</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Priority</TableHead>
            <TableHead>Active</TableHead>
            <TableHead>Start Time</TableHead>
            <TableHead>End Time</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {packages.map((pkg) => (
            <TableRow
              key={pkg.id}
            >
              <TableCell className="font-medium">
                {pkg.orderSn}
              </TableCell>
              <TableCell>
                {pkg.packageStatus !== null && pkg.packageStatus !== undefined ? (
                  <Badge variant={packageStatusMap[pkg.packageStatus]?.variant || 'default'}>
                    {packageStatusMap[pkg.packageStatus]?.label || `Status: ${pkg.packageStatus}`}
                  </Badge>
                ) : '-'}
              </TableCell>
              <TableCell>
                {pkg.uidSort !== undefined ? pkg.uidSort : '-'}
              </TableCell>
              <TableCell>
                {pkg.whetherCurrent ? (
                  <Badge variant="success">Active</Badge>
                ) : (
                  'No'
                )}
              </TableCell>
              <TableCell>{formatDateTime(pkg.planStartTime)}</TableCell>
              <TableCell>{formatDateTime(pkg.planEndTime)}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onSelectPackage(pkg)}
                  >
                    View
                  </Button>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />

                      {onViewUsage && (
                        <DropdownMenuItem onClick={() => onViewUsage(pkg.orderSn)}>
                          <BarChart className="mr-2 h-4 w-4" />
                          <span>View Usage</span>
                        </DropdownMenuItem>
                      )}

                      {onViewDetails && (
                        <DropdownMenuItem onClick={() => onViewDetails(pkg.orderSn)}>
                          <FileText className="mr-2 h-4 w-4" />
                          <span>View Details</span>
                        </DropdownMenuItem>
                      )}

                      {onSetTop && (
                        <DropdownMenuItem onClick={() => onSetTop(pkg.orderSn, uid)}>
                          <ArrowUp className="mr-2 h-4 w-4" />
                          <span>Set as Top</span>
                        </DropdownMenuItem>
                      )}

                      {onDeactivate && pkg.packageStatus !== 800 && (
                        <DropdownMenuItem
                          onClick={() => onDeactivate(pkg.orderSn)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <PowerOff className="mr-2 h-4 w-4" />
                          <span>Deactivate</span>
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
