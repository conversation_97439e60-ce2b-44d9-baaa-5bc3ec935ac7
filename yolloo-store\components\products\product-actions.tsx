'use client'

import { useState } from "react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { AddToCartButton } from "@/components/cart/add-to-cart"
import { CardSelector } from "@/components/products/card-selector"
import { Switch } from "@/components/ui/switch"
import { VariantSelector } from "@/components/products/variant-selector"
import { Icons } from "@/components/icons"

interface ProductVariant {
  id: string
  price: number
  duration?: number | null
  durationType?: string | null
  attributes?: Record<string, string>
}

interface Product {
  id: string
  name: string
  price: number
  images?: string[]
  stock: number
  variants?: ProductVariant[]
  requiredUID?: boolean
  category?: { name: string }
}

interface ProductActionsProps {
  product: Product
}

export function ProductActions({ product }: ProductActionsProps) {
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(
    product.variants?.[0] ?? null
  )
  const [selectedUid, setSelectedUid] = useState<string>()
  const [manualUid, setManualUid] = useState<string>("")
  const [displayUid, setDisplayUid] = useState<string>("")
  const [uidInputType, setUidInputType] = useState<"select" | "manual">("select")
  const [enableUidInput, setEnableUidInput] = useState(product.requiredUID || false)
  const [uidError, setUidError] = useState<string>("")
  const [isValidUid, setIsValidUid] = useState(false)

  // Format UID with hyphens after every 5 digits for display
  const formatUid = (uid: string): string => {
    // Remove all non-digit characters
    const digitsOnly = uid.replace(/\D/g, '')

    // Add hyphens after every 5 digits
    return digitsOnly.replace(/(\d{5})(?=\d)/g, '$1-')
  }

  const validateUid = (uid: string) => {
    // Clear previous error
    setUidError("")

    // Remove any hyphens for validation
    const cleanedUid = uid.replace(/-/g, '')

    // Check if empty
    if (!cleanedUid) {
      setUidError("UID cannot be empty")
      setIsValidUid(false)
      return false
    }

    // Check if contains only numbers
    if (!/^\d+$/.test(cleanedUid)) {
      setUidError("UID must contain only numbers")
      setIsValidUid(false)
      return false
    }

    // Check length (must be exactly 20 digits)
    if (cleanedUid.length !== 20) {
      setUidError("UID must be exactly 20 digits")
      setIsValidUid(false)
      return false
    }

    setIsValidUid(true)
    return true
  }

  const handleUidChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value

    // Only allow digits (0-9) in the input
    // This will prevent spaces, special characters, and other non-digit inputs
    const digitsOnly = inputValue.replace(/\D/g, '')

    // Limit to 20 digits maximum
    const limitedDigits = digitsOnly.slice(0, 20)

    // Store the raw value (digits only)
    setManualUid(limitedDigits)

    // Format for display with hyphens after every 5 digits
    const formattedValue = formatUid(limitedDigits)
    setDisplayUid(formattedValue)

    // Validate the raw value
    if (limitedDigits) {
      validateUid(limitedDigits)
    } else {
      setUidError("")
      setIsValidUid(false)
    }
  }

  const finalUid = enableUidInput
    ? (uidInputType === "select"
      ? selectedUid
      : (isValidUid ? manualUid : undefined))
    : undefined
  const isCardProduct = product.category?.name?.toLowerCase().includes('card') || false
  const showUidSection = !isCardProduct

  return (
    <div className="space-y-6">
      {(!product.variants || product.variants.length === 0) && (
        <div className="text-2xl font-bold">
          ${product.price.toFixed(2)}
        </div>
      )}

      {product.variants && product.variants.length > 0 && (
        <VariantSelector
          variants={product.variants}
          onVariantSelect={(variant) => setSelectedVariant(variant)}
          selectedVariant={selectedVariant}
        />
      )}

      {showUidSection && (
        <div className="space-y-4">
          {!product.requiredUID && (
            <div className="flex items-center space-x-2">
              <Switch
                checked={enableUidInput}
                onCheckedChange={setEnableUidInput}
                id="enable-uid"
              />
              <Label htmlFor="enable-uid">Bind to a card</Label>
            </div>
          )}

          {enableUidInput && (
            <>
              <div className="mb-4">
                <RadioGroup
                  value={uidInputType}
                  onValueChange={(value) => {
                    setUidInputType(value as "select" | "manual")
                    setSelectedUid(undefined)
                    setManualUid("")
                    setDisplayUid("")
                  }}
                  className="grid grid-cols-1 sm:grid-cols-2 gap-3"
                >
                  <div
                    className={`
                      flex items-center p-3 rounded-lg border-2 cursor-pointer transition-all
                      ${uidInputType === "select"
                        ? "border-primary bg-primary/5"
                        : "border-muted hover:border-primary/30"}
                    `}
                    onClick={() => {
                      setUidInputType("select")
                      setSelectedUid(undefined)
                      setManualUid("")
                      setDisplayUid("")
                    }}
                  >
                    <RadioGroupItem value="select" id="select" className="mr-3" />
                    <Label htmlFor="select" className="cursor-pointer font-medium">
                      Select from my cards
                    </Label>
                  </div>
                  <div
                    className={`
                      flex items-center p-3 rounded-lg border-2 cursor-pointer transition-all
                      ${uidInputType === "manual"
                        ? "border-primary bg-primary/5"
                        : "border-muted hover:border-primary/30"}
                    `}
                    onClick={() => {
                      setUidInputType("manual")
                      setSelectedUid(undefined)
                      setManualUid("")
                      setDisplayUid("")
                    }}
                  >
                    <RadioGroupItem value="manual" id="manual" className="mr-3" />
                    <Label htmlFor="manual" className="cursor-pointer font-medium">
                      Enter card UID manually
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {uidInputType === "select" ? (
                <CardSelector onSelect={setSelectedUid} selectedUid={selectedUid} />
              ) : (
                <div className="space-y-3 border-2 rounded-xl p-4 shadow-sm">
                  <div>
                    <Label htmlFor="uid" className="text-base font-medium">Card UID</Label>
                    <p className="text-sm text-muted-foreground mb-3">
                      Enter your 20-digit card number without spaces or special characters
                    </p>
                  </div>
                  <div className="relative">
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground font-mono">
                        UID:
                      </div>
                      <Input
                        id="uid"
                        value={displayUid}
                        onChange={handleUidChange}
                        placeholder="Example: 29901000000000000025"
                        className={`
                          text-base py-6 pl-12 pr-4 font-mono
                          ${uidError ? "border-destructive" : "border-input focus:border-primary"}
                        `}
                      />
                      {displayUid && !uidError && (
                        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-green-600">
                          <Icons.check className="h-5 w-5" />
                        </div>
                      )}
                    </div>
                  </div>
                  {uidError && (
                    <p className="text-sm text-destructive flex items-center gap-1.5">
                      <Icons.warning className="h-4 w-4" />
                      {uidError}
                    </p>
                  )}
                  {!uidError && displayUid && (
                    <p className="text-sm text-green-600 flex items-center gap-1.5">
                      <Icons.check className="h-4 w-4" />
                      Valid card number format
                    </p>
                  )}
                </div>
              )}

              {product.requiredUID && !finalUid && (
                <div className="mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                  <p className="text-sm text-destructive flex items-center gap-2">
                    <Icons.warning className="h-4 w-4 flex-shrink-0" />
                    <span>Please select a card or enter a valid UID to continue</span>
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      )}

      <AddToCartButton
        product={product}
        selectedVariant={selectedVariant}
        disabled={product.requiredUID && !finalUid}
        uid={finalUid}
      />
    </div>
  )
}