import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function PATCH(
  req: Request,
  { params }: { params: { productId: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await req.json()
    const { off_shelve } = body

    // 更新产品状态
    const product = await prisma.product.update({
      where: {
        id: params.productId,
      },
      data: {
        off_shelve,
      },
    })

    return NextResponse.json(product)
  } catch (error) {
    console.error("[PRODUCT_STATUS_PATCH]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 