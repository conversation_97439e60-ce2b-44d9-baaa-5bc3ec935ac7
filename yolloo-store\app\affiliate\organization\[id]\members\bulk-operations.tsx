"use client";

import { useState } from "react";
import { use<PERSON>outer } from "next/navigation";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import axios from "axios";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, Upload, UserPlus, Loader2, AlertCircle, Mail } from "lucide-react";

// Schema for bulk adding existing members
const bulkAddSchema = z.object({
  emails: z.string().min(1, "Please enter at least one email"),
  commissionRate: z.coerce.number().min(0, "Min is 0%").max(1, "Max is 100%").default(0.5),
  isAdmin: z.boolean().default(false),
});

// Schema for sending invites to new users
const bulkInviteSchema = z.object({
  emails: z.string().min(1, "Please enter at least one email"),
  commissionRate: z.coerce.number().min(0, "Min is 0%").max(1, "Max is 100%").default(0.5),
  isAdmin: z.boolean().default(false),
});

// Parse multiple emails from text
const parseEmails = (data: string) => {
  return data
    .split(/[,;\r\n]+/)
    .map(email => email.trim())
    .filter(email => email.length > 0);
};

// Result interfaces
interface SuccessResult {
  email: string;
  name?: string;
  success: true;
  message?: string;
}

interface ErrorResult {
  email: string;
  name?: string;
  success: false;
  error: string;
}

type ProcessingResult = SuccessResult | ErrorResult;

interface BulkOperationsProps {
  organizationId: string;
}

export function BulkOperations({ organizationId }: BulkOperationsProps) {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("add-members");
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<ProcessingResult[]>([]);
  const [showResults, setShowResults] = useState(false);

  // Form for bulk adding existing users
  const addForm = useForm<z.infer<typeof bulkAddSchema>>({
    resolver: zodResolver(bulkAddSchema),
    defaultValues: {
      emails: "",
      commissionRate: 0.5,
      isAdmin: false,
    },
  });

  // Form for sending invites to new users
  const inviteForm = useForm<z.infer<typeof bulkInviteSchema>>({
    resolver: zodResolver(bulkInviteSchema),
    defaultValues: {
      emails: "",
      commissionRate: 0.5,
      isAdmin: false,
    },
  });

  // Handle bulk adding existing users
  const handleBulkAdd = async (values: z.infer<typeof bulkAddSchema>) => {
    try {
      setLoading(true);
      setResults([]);
      setShowResults(false);
      
      // Parse emails
      const emails = parseEmails(values.emails);
      
      if (emails.length === 0) {
        toast.error("No valid emails found");
        return;
      }
      
      // Call API to bulk add members
      const response = await axios.post(`/api/affiliate/organizations/${organizationId}/members/batch`, {
        emails,
        commissionRate: values.commissionRate,
        isAdmin: values.isAdmin,
      });
      
      // Process results
      const processedResults: ProcessingResult[] = [
        ...(response.data.addedMembers || []).map((member: any) => ({
          email: member.user.email,
          name: member.user.name,
          success: true,
          message: "Member added successfully",
        })),
        ...(response.data.errors || []).map((error: any) => ({
          email: error.email,
          success: false,
          error: error.message || "Failed to add member",
        })),
      ];
      
      setResults(processedResults);
      setShowResults(true);
      
      const successCount = processedResults.filter(r => r.success).length;
      if (successCount > 0) {
        toast.success(`Successfully added ${successCount} members`);
        router.refresh();
      }
      
      if (successCount === emails.length) {
        addForm.reset();
      }
    } catch (error) {
      console.error("Error bulk adding members:", error);
      toast.error("Failed to bulk add members");
    } finally {
      setLoading(false);
    }
  };

  // Handle sending invites to new users
  const handleBulkInvite = async (values: z.infer<typeof bulkInviteSchema>) => {
    try {
      setLoading(true);
      setResults([]);
      setShowResults(false);
      
      // Parse emails
      const emails = parseEmails(values.emails);
      
      if (emails.length === 0) {
        toast.error("No valid emails found");
        return;
      }
      
      // Call API to send invites
      const response = await axios.post(`/api/affiliate/organizations/${organizationId}/invites/batch`, {
        emails,
        commissionRate: values.commissionRate,
        isAdmin: values.isAdmin,
      });
      
      // Process results
      const processedResults: ProcessingResult[] = response.data.results || [];
      
      setResults(processedResults);
      setShowResults(true);
      
      const successCount = processedResults.filter(r => r.success).length;
      if (successCount > 0) {
        toast.success(`Successfully sent ${successCount} invitations`);
        router.refresh();
      }
      
      if (successCount === emails.length) {
        inviteForm.reset();
      }
    } catch (error) {
      console.error("Error sending invitations:", error);
      toast.error("Failed to send invitations");
    } finally {
      setLoading(false);
    }
  };

  const resetState = () => {
    setResults([]);
    setShowResults(false);
    addForm.reset();
    inviteForm.reset();
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetState();
    }
    setOpen(open);
  };

  const switchTab = (tab: string) => {
    setActiveTab(tab);
    resetState();
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="default">
          <UserPlus className="mr-2 h-4 w-4" />
          Bulk Operations
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Bulk Member Operations</DialogTitle>
          <DialogDescription>
            Add existing users or invite new users to join your organization.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={switchTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="add-members">Add Existing Users</TabsTrigger>
            <TabsTrigger value="invite-users">Invite New Users</TabsTrigger>
          </TabsList>
          
          {showResults ? (
            <div className="space-y-4 py-4">
              <Alert className={results.every(r => r.success) ? "bg-green-50" : "bg-amber-50"}>
                <AlertDescription>
                  {results.filter(r => r.success).length} of {results.length} operations completed successfully.
                </AlertDescription>
              </Alert>
              
              <div className="max-h-[300px] overflow-y-auto border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Status</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Details</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {results.map((result, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          {result.success ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-500" />
                          )}
                        </TableCell>
                        <TableCell>{result.email}</TableCell>
                        <TableCell>
                          {result.success ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                              {result.message || "Success"}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                              {result.success === false && result.error}
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              
              <DialogFooter>
                <Button 
                  variant="secondary" 
                  onClick={resetState}
                >
                  Start Over
                </Button>
                <Button onClick={() => setOpen(false)}>Done</Button>
              </DialogFooter>
            </div>
          ) : (
            <>
              <TabsContent value="add-members" className="space-y-4 py-4">
                <Form {...addForm}>
                  <form onSubmit={addForm.handleSubmit(handleBulkAdd)} className="space-y-4">
                    <FormField
                      control={addForm.control}
                      name="emails"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Addresses</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter emails (one per line or comma-separated)"
                              rows={6}
                              {...field}
                              disabled={loading}
                            />
                          </FormControl>
                          <FormDescription>
                            Enter email addresses of existing users. Separate multiple emails with commas, semicolons, or new lines.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={addForm.control}
                      name="commissionRate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Commission Rate (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              max="1"
                              placeholder="0.5"
                              {...field}
                              disabled={loading}
                            />
                          </FormControl>
                          <FormDescription>
                            This is the percentage of the product price the member will earn. This comes from the organization's commission (e.g., if organization earns 30% and member rate is 20%, member gets 20% and organization keeps 10%).
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={addForm.control}
                      name="isAdmin"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>Admin Access</FormLabel>
                            <FormDescription>
                              Grant administrator access to these members
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={loading}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    
                    <DialogFooter>
                      <Button type="submit" disabled={loading}>
                        {loading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Upload className="mr-2 h-4 w-4" />
                            Add Members
                          </>
                        )}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </TabsContent>
              
              <TabsContent value="invite-users" className="space-y-4 py-4">
                <Form {...inviteForm}>
                  <form onSubmit={inviteForm.handleSubmit(handleBulkInvite)} className="space-y-4">
                    <FormField
                      control={inviteForm.control}
                      name="emails"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Addresses to Invite</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter emails to invite (one per line or comma-separated)"
                              rows={6}
                              {...field}
                              disabled={loading}
                            />
                          </FormControl>
                          <FormDescription>
                            Enter email addresses to invite. Separate multiple emails with commas, semicolons, or new lines.
                            Each recipient will receive an invitation email with a link to join your organization.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={inviteForm.control}
                      name="commissionRate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Commission Rate (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              max="1"
                              placeholder="0.5"
                              {...field}
                              disabled={loading}
                            />
                          </FormControl>
                          <FormDescription>
                            This is the percentage of the product price the member will earn. This comes from the organization's commission (e.g., if organization earns 30% and member rate is 20%, member gets 20% and organization keeps 10%).
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={inviteForm.control}
                      name="isAdmin"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>Admin Access</FormLabel>
                            <FormDescription>
                              Grant administrator access to invited members
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={loading}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    
                    <Alert className="bg-blue-50 border-blue-200">
                      <AlertCircle className="h-4 w-4 text-blue-600" />
                      <AlertDescription className="text-blue-800 ml-2">
                        Each invited user will receive an email with instructions to join your organization.
                        Invitations are valid for 7 days.
                      </AlertDescription>
                    </Alert>
                    
                    <DialogFooter>
                      <Button type="submit" disabled={loading}>
                        {loading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Mail className="mr-2 h-4 w-4" />
                            Send Invitations
                          </>
                        )}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </TabsContent>
            </>
          )}
        </Tabs>
      </DialogContent>
    </Dialog>
  );
} 