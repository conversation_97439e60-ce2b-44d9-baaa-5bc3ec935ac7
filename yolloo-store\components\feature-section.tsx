'use client'

import { Wifi, Globe, Plane, Smartphone } from "lucide-react"
import { SectionBackground } from "@/components/ui/section-background"

const FeatureCard = ({ icon: Icon, title, description }: {
  icon: any,
  title: string,
  description: string
}) => (
  <div className="flex flex-col items-center p-8 rounded-3xl bg-gradient-to-br from-[#F799A6]/10 to-[#B82E4E]/10 backdrop-blur-sm 
  hover:from-[#F799A6]/15 hover:to-[#B82E4E]/15 transition-all duration-500 hover:shadow-[0_8px_32px_rgba(247,153,166,0.2)] 
  hover:-translate-y-1 border border-[#F799A6]/30 hover:border-[#F799A6]/50 group">
    <div className="w-16 h-16 flex items-center justify-center rounded-2xl bg-gradient-to-br from-[#B82E4E]/20 
    to-[#F799A6]/20 mb-6 shadow-[0_4px_16px_rgba(247,153,166,0.2)]">
      <Icon className="w-8 h-8 text-[#B82E4E] transition-transform duration-500 group-hover:scale-110" />
    </div>
    <h3 className="font-semibold text-xl mb-3 text-gray-900 group-hover:text-[#B82E4E] transition-colors duration-300">
      {title}
    </h3>
    <p className="text-gray-600 text-center leading-relaxed">
      {description}
    </p>
  </div>
)

export default function FeatureSection() {
  return (
    <SectionBackground isWhite={false}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-20">
          <div className="inline-block px-6 py-2 bg-gradient-to-br from-[#F799A6]/20 to-[#B82E4E]/20 backdrop-blur-sm 
          rounded-full border border-[#F799A6]/30 text-[#B82E4E] font-semibold tracking-wide mb-4 shadow-lg shadow-pink-500/10">
            Core Features
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] 
          text-transparent bg-clip-text">
            Digital Connectivity Made Simple
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
            Experience the future of mobile connectivity with our cutting-edge eSIM technology, 
            designed for modern travelers and digital nomads.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          <FeatureCard
            icon={Wifi}
            title="Instant Activation"
            description="Get connected within minutes using our streamlined QR code activation process"
          />
          <FeatureCard
            icon={Globe}
            title="Global Network"
            description="Access premium carriers in 190+ countries with seamless network switching"
          />
          <FeatureCard
            icon={Smartphone}
            title="Smart Compatible"
            description="Works flawlessly with all modern eSIM-enabled devices and tablets"
          />
          <FeatureCard
            icon={Plane}
            title="Travel Freedom"
            description="Say goodbye to physical SIMs and enjoy hassle-free international roaming"
          />
        </div>
      </div>
    </SectionBackground>
  )
} 