import { getServerSession } from "next-auth"
import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


// Get all esims for a specific YollooCard
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const yollooCardId = searchParams.get('yollooCardId')

    if (!yollooCardId) {
      return new NextResponse("Missing yollooCardId", { status: 400 })
    }

    // Verify the YollooCard belongs to the user
    const card = await prisma.yollooCard.findFirst({
      where: {
        id: yollooCardId,
        userId: session.user.id
      }
    })

    if (!card) {
      return new NextResponse("Card not found or unauthorized", { status: 404 })
    }

    const esims = await prisma.esim.findMany({
      where: {
        yollooCardId: yollooCardId
      },
      include: {
        product: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(esims)
  } catch (error) {
    return new NextResponse("Internal error", { status: 500 })
  }
}

// Create a new esim
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const json = await request.json()
    const { iccid, yollooCardId, productId } = json

    if (!iccid || !yollooCardId || !productId) {
      return new NextResponse("Missing required fields", { status: 400 })
    }

    // Verify the YollooCard belongs to the user
    const card = await prisma.yollooCard.findFirst({
      where: {
        id: yollooCardId,
        userId: session.user.id
      }
    })

    if (!card) {
      return new NextResponse("Card not found or unauthorized", { status: 404 })
    }

    // Check if ICCID already exists
    const existingEsim = await prisma.esim.findUnique({
      where: {
        iccid: iccid
      }
    })

    if (existingEsim) {
      return new NextResponse("ICCID already exists", { status: 400 })
    }

    const esim = await prisma.esim.create({
      data: {
        iccid,
        yollooCardId,
        productId,
        status: "Inactive"
      },
      include: {
        product: true
      }
    })

    return NextResponse.json(esim)
  } catch (error) {
    return new NextResponse("Internal error", { status: 500 })
  }
} 