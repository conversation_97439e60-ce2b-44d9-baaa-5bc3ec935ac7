"use client"

import { useRouter } from "next/navigation";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ShoppingCart } from "lucide-react";
import { VariantSelector } from "@/components/products/variant-selector";

interface ProductVariant {
  id: string;
  price: number;
  duration?: number | null;
  durationType?: string | null;
}

interface EsimBuyNowFormProps {
  product: {
    id: string;
    stock: number;
  };
  variants: ProductVariant[];
}

export function EsimBuyNowForm({ product, variants }: EsimBuyNowFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(variants?.[0] ?? null);

  const handleBuyNow = () => {
    setLoading(true);
    const params = new URLSearchParams({
      buyNow: "1",
      productId: product.id,
    });
    if (selectedVariant) {
      params.append("variantId", selectedVariant.id);
    }
    router.push(`/checkout?${params.toString()}`);
  };

  return (
    <div className="flex flex-col gap-4">
      {variants && variants.length > 0 && (
        <VariantSelector
          variants={variants}
          onVariantSelect={setSelectedVariant}
          selectedVariant={selectedVariant}
        />
      )}
      <Button
        size="lg"
        className="w-full md:w-auto bg-gradient-to-r from-blue-500 to-violet-500 text-white font-bold shadow-lg hover:scale-105 transition-transform text-lg py-6 px-10 flex items-center justify-center gap-2"
        onClick={handleBuyNow}
        disabled={loading || product.stock === 0 || (variants.length > 0 && !selectedVariant)}
      >
        <ShoppingCart className="h-6 w-6" /> {loading ? 'Redirecting...' : 'Buy Now'}
      </Button>
    </div>
  );
} 