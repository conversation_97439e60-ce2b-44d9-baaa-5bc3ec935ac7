"use client"

import { User } from "next-auth"
import { signOut } from "next-auth/react"
import Link from "next/link"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { UserAvatar } from "@/components/user-avatar"
import { Icons } from "@/components/icons"

interface UserNavProps {
  user: Pick<User, "name" | "image" | "email"> & {
    id: string
    role?: string
  }
}

export function UserNav({ user }: UserNavProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex items-center gap-2.5 rounded-full px-2 py-1.5 transition-all duration-300 hover:bg-[#F799A6]/10 hover:shadow-sm focus:bg-accent focus:outline-none">
        <UserAvatar
          user={{ name: user.name || null, image: user.image || null }}
          className="h-8 w-8 border-2 border-background transition-transform duration-200 group-hover:scale-105"
        />
        <span className="hidden text-sm font-medium text-muted-foreground transition-colors hover:text-[#B82E4E] md:inline-block">
          {user.name || "Account"}
        </span>
        <Icons.chevronDown className="h-4 w-4 text-muted-foreground transition-colors" />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[280px] p-2 rounded-xl shadow-lg border border-[#F799A6]/20" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-2 p-2">
            <p className="text-sm font-medium leading-none">{user.name}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {user.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="mx-2" />
        <div className="p-2">
          <DropdownMenuItem asChild className="rounded-md transition-all duration-200 hover:bg-[#F799A6]/10">
            <Link href="/account" className="flex items-center py-2 group">
              <Icons.user className="mr-3 h-4 w-4 transition-transform duration-200 group-hover:text-[#B82E4E] group-hover:scale-110" />
              <span className="group-hover:text-[#B82E4E] transition-colors duration-200">Account Settings</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="rounded-md transition-all duration-200 hover:bg-[#F799A6]/10">
            <Link href="/orders" className="flex items-center py-2 group">
              <Icons.billing className="mr-3 h-4 w-4 transition-transform duration-200 group-hover:text-[#B82E4E] group-hover:scale-110" />
              <span className="group-hover:text-[#B82E4E] transition-colors duration-200">My Orders</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="rounded-md transition-all duration-200 hover:bg-[#F799A6]/10">
            <Link href="/affiliate" className="flex items-center py-2 group">
              <Icons.zap className="mr-3 h-4 w-4 transition-transform duration-200 group-hover:text-[#B82E4E] group-hover:scale-110" />
              <span className="group-hover:text-[#B82E4E] transition-colors duration-200">Earn with Yolloo</span>
            </Link>
          </DropdownMenuItem>
          {user.role === "ADMIN" && (
            <DropdownMenuItem asChild className="rounded-md transition-all duration-200 hover:bg-[#F799A6]/10">
              <Link href="/admin" className="flex items-center py-2 group" target="_blank" rel="noopener noreferrer">
                <Icons.settings className="mr-3 h-4 w-4 transition-transform duration-200 group-hover:text-[#B82E4E] group-hover:scale-110" />
                <span className="group-hover:text-[#B82E4E] transition-colors duration-200">Admin Dashboard</span>
              </Link>
            </DropdownMenuItem>
          )}
        </div>
        <DropdownMenuSeparator className="mx-2" />
        <div className="p-2">
          <DropdownMenuItem
            className="rounded-md cursor-pointer text-red-600 hover:bg-red-50/70 hover:text-red-700 focus:bg-red-50 focus:text-red-700 transition-all duration-200 flex items-center py-2 group"
            onSelect={(event) => {
              event.preventDefault()
              signOut({
                callbackUrl: `${window.location.origin}/auth/signin`,
              })
            }}
          >
            <Icons.logout className="mr-3 h-4 w-4 transition-transform duration-200 group-hover:scale-110" />
            <span>Sign out</span>
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}