"use client"

import { useEffect, useState } from "react"
import { Check } from "lucide-react"
import Link from "next/link"
import clsx from "clsx"
import { useSession } from "next-auth/react"
import { SectionBackground } from "@/components/ui/section-background"

// PricingCard component (reused from pricing-section.tsx)
const PricingCard = ({ 
  title, 
  description, 
  price, 
  originalPrice, 
  features, 
  isPopular = false,
  productLink,
  buttonText
}: {
  title: string
  description: string
  price: number
  originalPrice: number
  features: string[]
  isPopular?: boolean
  productLink: string
  buttonText: string
}) => {
  const { status } = useSession()
  const isAuthenticated = status === "authenticated"
  
  // Determine the link destination based on authentication status
  const linkHref = isAuthenticated ? productLink : `/auth/signin?callbackUrl=${encodeURIComponent(productLink)}`
  
  return (
    <div className={clsx(
      "relative p-8 rounded-3xl transition-all duration-500 hover:-translate-y-1 flex flex-col backdrop-blur-sm",
      isPopular 
        ? "bg-gradient-to-br from-[#B82E4E] to-[#F799A6] border-2 border-[#F799A6] shadow-[0_8px_32px_rgba(247,153,166,0.3)]" 
        : "bg-gradient-to-br from-[#F799A6]/20 to-[#B82E4E]/20 border border-[#F799A6]/30 hover:border-[#F799A6]/50 hover:shadow-[0_8px_32px_rgba(247,153,166,0.2)]"
    )}>
      {isPopular && (
        <span className="absolute -top-4 left-1/2 -translate-x-1/2 px-6 py-1.5 bg-white 
        text-[#B82E4E] text-sm font-semibold rounded-full shadow-lg shadow-pink-500/30">
          Most Popular
        </span>
      )}
      
      <div className="text-center mb-8">
        <h3 className={clsx(
          "text-2xl font-semibold mb-2",
          isPopular ? "text-white" : "text-gray-900"
        )}>
          {title}
        </h3>
        <p className={clsx(
          "mb-6 text-lg",
          isPopular ? "text-white/90" : "text-gray-600"
        )}>
          {description}
        </p>
        <div className="flex items-center justify-center gap-2">
          <span className={clsx(
            "text-5xl font-bold",
            isPopular ? "text-white" : "text-[#B82E4E]"
          )}>
            ${price}
          </span>
          {originalPrice && (
            <span className={clsx(
              "text-xl line-through",
              isPopular ? "text-white/60" : "text-gray-400"
            )}>
              ${originalPrice}
            </span>
          )}
        </div>
      </div>

      <div className="flex-grow">
        <ul className="space-y-4 mb-8">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start gap-3">
              <div className={clsx(
                "w-5 h-5 flex-shrink-0 flex items-center justify-center rounded-full shadow-sm",
                isPopular 
                  ? "bg-white/20 shadow-white/10" 
                  : "bg-[#F799A6]/20 shadow-pink-500/10"
              )}>
                <Check className={clsx(
                  "w-3 h-3",
                  isPopular ? "text-white" : "text-[#B82E4E]"
                )} />
              </div>
              <span className={clsx(
                "text-lg",
                isPopular ? "text-white" : "text-gray-600"
              )}>
                {feature}
              </span>
            </li>
          ))}
        </ul>
      </div>

      <Link 
        href={linkHref}
        className={clsx(
          "block w-full py-4 px-6 text-center rounded-full font-semibold transition-all duration-500 transform hover:-translate-y-0.5 text-lg",
          isPopular 
            ? "bg-white text-[#B82E4E] shadow-lg hover:shadow-xl shadow-white/30" 
            : "bg-gradient-to-r from-[#B82E4E] to-[#F799A6] text-white hover:from-[#A02745] hover:to-[#E88A97] shadow-lg hover:shadow-xl shadow-pink-500/20"
        )}
      >
        {buttonText}
      </Link>
    </div>
  )
}

// FAQ Section Component
const FAQSection = () => {
  return (
    <div className="max-w-4xl mx-auto mt-20">
      <div className="text-center mb-12">
        <h2 className="text-3xl sm:text-4xl font-bold mb-4 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] 
        text-transparent bg-clip-text">
          Frequently Asked Questions
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Everything you need to know about our eSIM cards and services
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        {[
          {
            question: "What is an eSIM card?",
            answer: "An eSIM (embedded SIM) is a digital SIM that allows you to activate a cellular plan without having to use a physical SIM card. Our Yolloo cards enable you to store and use multiple eSIM profiles on a single device."
          },
          {
            question: "How many eSIMs can I store?",
            answer: "All our cards support storing up to 15 eSIM profiles, allowing you to switch between different carriers and plans as needed."
          },
          {
            question: "Do I need to buy data plans separately?",
            answer: "Yes, the card purchase gives you the ability to store and manage eSIMs. Data plans are purchased separately based on your travel needs."
          },
          {
            question: "Which devices are compatible?",
            answer: "Our Lite and Plus cards support Android devices with eSIM capability. The Max card supports both iOS and Android devices with eSIM capability."
          },
          {
            question: "How do I activate my card?",
            answer: "After purchasing, you'll receive instructions to activate your card through our app or website. The process is simple and takes just a few minutes."
          },
          {
            question: "Can I use my card internationally?",
            answer: "Yes, all our cards offer global coverage, allowing you to use your eSIMs worldwide where supported by the carrier."
          }
        ].map((faq, index) => (
          <div key={index} className="p-6 rounded-xl bg-white shadow-md border border-gray-100 hover:shadow-lg transition-shadow">
            <h3 className="text-xl font-semibold mb-3 text-gray-900">{faq.question}</h3>
            <p className="text-gray-600">{faq.answer}</p>
          </div>
        ))}
      </div>
    </div>
  )
}

// Comparison Table Component
const ComparisonTable = () => {
  return (
    <div className="max-w-5xl mx-auto mt-20 overflow-x-auto">
      <div className="text-center mb-12">
        <h2 className="text-3xl sm:text-4xl font-bold mb-4 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] 
        text-transparent bg-clip-text">
          Compare Plans
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Find the perfect Yolloo card for your needs
        </p>
      </div>
      
      <div className="min-w-[768px]">
        <table className="w-full border-collapse">
          <thead>
            <tr>
              <th className="p-4 text-left bg-gray-50 border-b-2 border-gray-200"></th>
              <th className="p-4 text-center bg-gray-50 border-b-2 border-gray-200">
                <span className="text-xl font-semibold text-gray-700">Lite</span>
                <p className="text-sm text-gray-500">Basic</p>
              </th>
              <th className="p-4 text-center bg-gray-50 border-b-2 border-gray-200">
                <span className="text-xl font-semibold text-gray-700">Plus</span>
                <p className="text-sm text-gray-500">Popular</p>
              </th>
              <th className="p-4 text-center bg-[#F799A6]/10 border-b-2 border-[#F799A6]">
                <span className="text-xl font-semibold text-[#B82E4E]">Max</span>
                <p className="text-sm text-[#B82E4E]/70">Best Value</p>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="p-4 border-b border-gray-200 font-medium">Price</td>
              <td className="p-4 border-b border-gray-200 text-center">$12</td>
              <td className="p-4 border-b border-gray-200 text-center">$21</td>
              <td className="p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium">$23</td>
            </tr>
            <tr>
              <td className="p-4 border-b border-gray-200 font-medium">eSIM Downloads</td>
              <td className="p-4 border-b border-gray-200 text-center">3 free</td>
              <td className="p-4 border-b border-gray-200 text-center">Unlimited</td>
              <td className="p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium">Unlimited</td>
            </tr>
            <tr>
              <td className="p-4 border-b border-gray-200 font-medium">Device Support</td>
              <td className="p-4 border-b border-gray-200 text-center">Android</td>
              <td className="p-4 border-b border-gray-200 text-center">Android</td>
              <td className="p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium">iOS & Android</td>
            </tr>
            <tr>
              <td className="p-4 border-b border-gray-200 font-medium">eSIM Storage</td>
              <td className="p-4 border-b border-gray-200 text-center">15 profiles</td>
              <td className="p-4 border-b border-gray-200 text-center">15 profiles</td>
              <td className="p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium">15 profiles</td>
            </tr>
            <tr>
              <td className="p-4 border-b border-gray-200 font-medium">Global Coverage</td>
              <td className="p-4 border-b border-gray-200 text-center">✓</td>
              <td className="p-4 border-b border-gray-200 text-center">✓</td>
              <td className="p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium">✓</td>
            </tr>
            <tr>
              <td className="p-4 border-b border-gray-200 font-medium">Support</td>
              <td className="p-4 border-b border-gray-200 text-center">Basic</td>
              <td className="p-4 border-b border-gray-200 text-center">Priority</td>
              <td className="p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium">24/7 Priority</td>
            </tr>
            <tr>
              <td className="p-4 border-b border-gray-200 font-medium">Free Updates</td>
              <td className="p-4 border-b border-gray-200 text-center">-</td>
              <td className="p-4 border-b border-gray-200 text-center">✓</td>
              <td className="p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium">✓</td>
            </tr>
            <tr>
              <td className="p-4 border-b border-gray-200 font-medium">Advanced Features</td>
              <td className="p-4 border-b border-gray-200 text-center">-</td>
              <td className="p-4 border-b border-gray-200 text-center">-</td>
              <td className="p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium">✓</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  )
}

// Testimonials Section
const TestimonialsSection = () => {
  return (
    <div className="max-w-6xl mx-auto mt-20">
      <div className="text-center mb-12">
        <h2 className="text-3xl sm:text-4xl font-bold mb-4 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] 
        text-transparent bg-clip-text">
          What Our Customers Say
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Hear from travelers who have experienced the convenience of Yolloo cards
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-3">
        {[
          {
            name: "Sarah T.",
            location: "Business Traveler, USA",
            quote: "The Max card has been a game-changer for my international business trips. I can easily switch between eSIMs without carrying multiple physical cards.",
            rating: 5
          },
          {
            name: "Michael L.",
            location: "Digital Nomad, Canada",
            quote: "I've been using the Plus card for 6 months now while traveling through Southeast Asia. The unlimited eSIM downloads have saved me so much money on local data plans.",
            rating: 5
          },
          {
            name: "Elena K.",
            location: "Frequent Traveler, Germany",
            quote: "The Lite card is perfect for my occasional trips. Easy to use, great coverage, and the customer support has been excellent when I needed help.",
            rating: 4
          }
        ].map((testimonial, index) => (
          <div key={index} className="p-6 rounded-xl bg-white shadow-md border border-gray-100 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              {[...Array(5)].map((_, i) => (
                <svg 
                  key={i} 
                  className={`w-5 h-5 ${i < testimonial.rating ? "text-yellow-400" : "text-gray-300"}`} 
                  fill="currentColor" 
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <p className="text-gray-600 mb-4 italic">"{testimonial.quote}"</p>
            <div>
              <p className="font-semibold text-gray-900">{testimonial.name}</p>
              <p className="text-sm text-gray-500">{testimonial.location}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// Main Pricing Page Component
export default function PricingPage() {
  const [productLinks, setProductLinks] = useState({
    lite: "",
    plus: "",
    max: ""
  });

  useEffect(() => {
    const fetchProductLinks = async () => {
      try {
        const response = await fetch("/api/products/get-card-links");
        const data = await response.json();
        setProductLinks(data);
      } catch (error) {
        console.error("Error fetching product links:", error);
      }
    };

    fetchProductLinks();
  }, []);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <SectionBackground withTopGradient isWhite={false} className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-block px-6 py-2 bg-gradient-to-br from-[#F799A6]/20 to-[#B82E4E]/20 backdrop-blur-sm 
            rounded-full border border-[#F799A6]/30 text-[#B82E4E] font-semibold tracking-wide mb-4 shadow-lg shadow-pink-500/10">
              Pricing Plans
            </div>
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] 
            text-transparent bg-clip-text">
              Choose Your Perfect Yolloo Card
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
              Select your ideal Yolloo card with eSIM capabilities. Our cards allow you to store and manage multiple eSIM profiles, 
              making international travel seamless and affordable.
            </p>
          </div>

          {/* Pricing Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <PricingCard
              title="Lite"
              description="Perfect for light travelers"
              price={12}
              originalPrice={17}
              features={[
                "3 free eSIM downloads",
                "Android device support",
                "15 eSIM profile storage",
                "Global coverage",
                "Basic support"
              ]}
              productLink={productLinks.lite}
              buttonText="Get Card"
            />
            <PricingCard
              title="Plus"
              description="Best for regular travelers"
              price={21}
              originalPrice={30}
              features={[
                "Unlimited eSIM downloads",
                "Android device support",
                "15 eSIM profile storage",
                "Global coverage",
                "Priority support",
                "Free updates"
              ]}
              productLink={productLinks.plus}
              buttonText="Get Card"
            />
            <PricingCard
              title="Max"
              description="Ultimate flexibility"
              price={23}
              originalPrice={33}
              features={[
                "Unlimited eSIM downloads",
                "iOS & Android support",
                "15 eSIM profile storage",
                "Global coverage",
                "24/7 Priority support",
                "Advanced features"
              ]}
              isPopular
              productLink={productLinks.max}
              buttonText="Get Card"
            />
          </div>
          <div className="text-center mt-8 text-sm text-gray-500">
            * Physical card supported with all plans. eSIM data plans are sold separately.
          </div>
        </div>
      </SectionBackground>

      {/* Comparison Table */}
      <SectionBackground isWhite={true} className="py-20">
        <ComparisonTable />
      </SectionBackground>

      {/* Testimonials */}
      <SectionBackground isWhite={false} className="py-20">
        <TestimonialsSection />
      </SectionBackground>

      {/* FAQ Section */}
      <SectionBackground isWhite={true} className="py-20">
        <FAQSection />
      </SectionBackground>

    </div>
  )
}
