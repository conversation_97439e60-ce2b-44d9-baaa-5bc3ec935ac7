import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { ProductStatus } from '@prisma/client';

// 强制动态渲染
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;


export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url);
        const searchQuery = searchParams.get('search') || '';

        // 构建查询条件
        const whereCondition: any = {
            AND: [
                { status: ProductStatus.ACTIVE },
                { off_shelve: false }
            ]
        };

        // 如果有搜索查询，添加搜索条件
        if (searchQuery) {
            whereCondition.AND.push({
                OR: [
                    { name: { contains: searchQuery, mode: 'insensitive' } },
                    { description: { contains: searchQuery, mode: 'insensitive' } },
                    { websiteDescription: { contains: searchQuery, mode: 'insensitive' } },
                    { country: { contains: searchQuery, mode: 'insensitive' } },
                    // 添加对SKU的搜索支持
                    { sku: { contains: searchQuery, mode: 'insensitive' } },
                    // 添加对specifications中productCode的搜索支持
                    {
                        specifications: {
                            path: ['odooProductCode'],
                            string_contains: searchQuery
                        }
                    }
                ]
            });
        }

        // 从数据库获取商品列表
        const products = await prisma.product.findMany({
            where: whereCondition,
            select: {
                id: true,
                name: true,
                description: true,
                websiteDescription: true,
                price: true,
                off_shelve: true,
                parameters: true,
                category: true,
                country: true,
                countryCode: true,
                dataSize: true,
                planType: true,
            },
            orderBy: [
                {
                    createdAt: 'asc',  // 改为升序，让先保存的产品（单一country）显示在前面
                },
            ],
        });

        return NextResponse.json({ products });
    } catch (error) {
        console.error('Error fetching products:', error);
        return NextResponse.json(
            { error: 'Failed to fetch products' },
            { status: 500 }
        );
    }
}

// 获取单个商品
export async function POST(request: Request) {
    try {
        const { productCode } = await request.json();
        if (!productCode) {
            return NextResponse.json(
                { error: 'Product code is required' },
                { status: 400 }
            );
        }

        const product = await prisma.product.findFirst({
            where: {
                OR: [
                    { sku: productCode },
                    {
                        specifications: {
                            path: ['odooProductCode'],
                            equals: productCode
                        }
                    }
                ]
            },
            select: {
                id: true,
                name: true,
                description: true,
                websiteDescription: true,
                price: true,
                off_shelve: true,
                parameters: true,
                category: true,
                variants: true,
                country: true,
                countryCode: true,
                dataSize: true,
            }
        });

        if (!product) {
            return NextResponse.json(
                { error: 'Product not found' },
                { status: 404 }
            );
        }

        return NextResponse.json({ product });
    } catch (error) {
        console.error('Error in product API:', error);
        return NextResponse.json(
            { error: 'Failed to fetch product' },
            { status: 500 }
        );
    }
}